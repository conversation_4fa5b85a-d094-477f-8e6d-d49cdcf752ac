import { QUERY_CURRENT_STATES } from "@/actions/query/COMM/stock";

export const LIST_STOCK_START = "LIST_STOCK_START";
export const LIST_STOCK_SUCCESS = "LIST_STOCK_SUCCESS";
export const LIST_STOCK_ERROR = "LIST_STOCK_ERROR";

export const LIST_STOCKMEDIA_START = "LIST_STOCKMEDIA_START";
export const LIST_STOCKMEDIA_SUCCESS = "LIST_STOCKMEDIA_SUCCESS";
export const LIST_STOCKMEDIA_ERROR = "LIST_STOCKMEDIA_ERROR";

export const LIST_MYFAVORITE_SUCCESS = "LIST_MYFAVORITE_SUCCESS";
export const LIST_MYFAVORITE_ERROR = "LIST_MYFAVORITE_ERROR";

export const LIST_MARK_STOCK_SUCCESS = "LIST_MARK_STOCK_SUCCESS";
export const LIST_MARK_STOCK_ERROR = "LIST_MARK_STOCK_ERROR";

export const LIST_LANDSEARCH_SUCCESS = "LIST_LANDSEARCH_SUCCESS";
export const LIST_LANDSEARCH_ERROR = "LIST_LANDSEARCH_ERROR";

export const LIST_LANDSEARCH_PDF_SUCCESS = "LIST_LANDSEARCH_PDF_SUCCESS";
export const LIST_LANDSEARCH_PDF_ERROR = "LIST_LANDSEARCH_PDF_ERROR";

export const QUERY_UNIT_VIEWS_SUCCESS = "QUERY_UNIT_VIEWS_SUCCESS";
export const QUERY_UNIT_VIEWS_ERROR = "QUERY_UNIT_VIEWS_ERROR";
export const QUERY_BUSINESS_SUCCESS = "QUERY_BUSINESS_SUCCESS";

export const QUERY_DECORATIONS_SUCCESS = "QUERY_DECORATION_SUCCESS";
export const QUERY_DECORATIONS_ERROR = "QUERY_DECORTAIONS_ERROR";

export const QUERY_POSSESSIONS_SUCCESS = "QUERY_POSSESSIONS_SUCCESS";
export const QUERY_POSSESSIONS_ERROR = "QUERY_POSSESSIONS_ERROR";

export const QUERY_CURRENT_STATES_SUCCESS = "QUERY_CURRENT_STATES_SUCCESS";
export const QUERY_CURRENT_STATES_ERROR = "QUERY_CURRENT_STATES_ERROR";

export const TOGGLE_MYFAVORITE_SUCCESS = "TOGGLE_MYFAVORITE_SUCCESS";
export const TOGGLE_MYFAVORITE_ERROR = "TOGGLE_MYFAVORITE_ERROR";

export const TOGGLE_MARK_STOCK_SUCCESS = "TOGGLE_MARK_STOCK_SUCCESS";
export const TOGGLE_MARK_STOCK_ERROR = "TOGGLE_MARK_STOCK_ERROR";

export const REMOVE_MARK_STOCK_SUCCESS = "REMOVE_MARK_STOCK_SUCCESS";
export const REMOVE_MARK_STOCK_ERROR = "REMOVE_MARK_STOCK_ERROR";

export const CLEAR_STOCK = "CLEAR_STOCK";

export const CHANGE_CURRENT_STOCK = "CHANGE_CURRENT_STOCK";

export const UPDATE_STOCK_MEDIA_BY_INDEX = "UPDATE_STOCK_MEDIA_BY_INDEX";

export const UPDATE_STOCK_MEDIA_LIST = "UPDATE_STOCK_MEDIA_LIST";

export const LIST_MARK_WWW_QUERY_SUCCESS = `LIST_MARK_WWW_QUERY_SUCCESS`;
export const LIST_MARK_WWW_QUERY_ERROR = `LIST_MARK_WWW_QUERY_ERROR`;
