import React from "react";
import { useSelector } from "react-redux";
import PropTypes from "prop-types";
import moment from "moment";
import { withStyles } from "@material-ui/core/styles";
import PropertyTag from "./PropertyTag";
import { injectIntl } from "react-intl";
import { sbu } from "../../config";
import { checkIfSoleagent } from "../Saleskit/helpers";
import { compareDate } from "@/helper/generalHelper";
import _ from "lodash";

// We can inject some CSS into the DOM.
const styles = {
  propertyTags: {
    display: "flex",
    flexWrap: "wrap",
    "& > *:not(:last-child)": {
      // marginRight: "1vw",
    },
  },
  redTag: {
    backgroundColor: "#B00F15",
    color: "#fff",
  },
};

const getIndTags = (detail = {}, isWWW = false) => {
  const validEaaRecord = _.find(_.get(detail, "eaaRecords"), { status: "valid" });

  return [
    {
      id: "search.new",
      value: compareDate(
        _.get(detail, "recordOperation.createDate", ""),
        new Date(_.get(detail, "recordOperation.createDate", ""))
          .getTime() + (2 * 24 * 60 * 60 * 1000),
      ),
    },
    { id: "stock.tag.mortgagee", value: _.get(detail, "mortgagee") },
    { id: "search.form.saleequity", value: _.get(detail, "saleEquity") },
    { id: "stock.advertisment.isNoEditMoney", value: _.get(detail, "isNoEditMoney") },
    { id: "stock.advertisment.isNoAds", value: _.get(detail, "isDoNotAds") },
    {
      id: "search.form.eaa",
      value: compareDate(
        _.get(validEaaRecord, "minDate"),
        _.get(validEaaRecord, "maxDate"),
      ),
    },
    { id: "stock.tag.www", value: isWWW },
    { id: "stock.towngas", value: !!_.get(detail, "haveTownGas") },
    { id: "stock.factory", value: !!_.get(detail, "isFactory") },
    { id: "stock.window", value: !!_.get(detail, "withWindows") },
    { id: "search.form.raisedfloor", value: !!_.get(detail, "isRaisedFloor") },
    { id: "stock.Subdivide", value: !!_.get(detail, "area.divisible") },
    { id: "stock.LPower", value: !!_.get(detail, "hvLPower") },
    { id: "stock.licensed", value: _.get(detail, "isLicensed") },
    { id: "stock.withCarpark", value: _.get(detail, "withParking") },
    { id: "stock.with24hAC", value: _.get(detail, "with24hAC") },
    { id: "stock.is24hEnter", value: _.get(detail, "is24hEnter") },
    { id: "stock.saleWithLicenses", value: _.get(detail, "withSaleWithLicenses") },
    { id: "stock.isIncludeWaterElec", value: _.get(detail, "isIncludeWaterElec") },
    { id: "stock.hvBizTools", value: _.get(detail, "hvBizTools") },
    { id: "stock.toilet", value: !!_.get(detail, "facilities.havePrivateToilets") },
  ];
};

const getCommTags = (detail = {}, isWWW = false) => {
  const validEaaRecord = _.find(_.get(detail, "eaaRecords"), { status: "valid" });

  return [
    {
      id: "search.new",
      value: compareDate(
        _.get(detail, "recordOperation.createDate", ""),
        new Date(
          _.get(detail, "recordOperation.createDate", "")
        ).getTime() + 14 * 24 * 60 * 60 * 1000,
      ),
    },
    { id: "search.header.marketable", value: _.get(detail, "isMarketable") },
    { id: "search.form.investment", value: _.get(detail, "isInvestment") },
    { id: "stock.tag.mortgagee", value: !!_.get(detail, "mortgagee") },
    { id: "search.form.saleequity", value: _.get(detail, "saleEquity") },
    { id: "stock.advertisment.isNoAds", value: _.get(detail, "isDoNotAds") },
    {
      id: "stock.tag.eaa",
      value: compareDate(
        _.get(validEaaRecord, "minDate"),
        _.get(validEaaRecord, "maxDate"),
      ),
    },
    { id: "stock.tag.www", value: isWWW },
    { id: "search.form.raisedfloor", value: _.get(detail, "isRaisedFloor") },
    { id: "stock.tag.carpark", value: _.get(detail, "stockTypeId") === "650000000000000000000015" },
    { id: "search.form.isFacingLiftLB", value: _.get(detail, "isFacingLift") },
    { id: "search.form.haverooftop", value: _.get(detail, "haveRooftop") },
    { id: "stock.terrace", value: !!_.get(detail, "haveTerrace") },
    { id: "column.isProjects", value: _.get(detail, "isProjects") },
    { id: "search.form.isShop", value: _.get(detail, "stockTypeId") === "650000000000000000000009" },
    { id: "stock.inwater", value: _.get(detail, "isInWater") },
    { id: "stock.outwater", value: _.get(detail, "isOutWater") },
    { id: "stock.LPower", value: _.get(detail, "hvLPower") },
    { id: "stock.licensed", value: _.get(detail, "isLicensed") },
    { id: "stock.withCarpark", value: _.get(detail, "withParking") },
    { id: "stock.with24hAC", value: _.get(detail, "with24hAC") },
    { id: "stock.is24hEnter", value: _.get(detail, "is24hEnter") },
    { id: "stock.saleWithLicenses", value: _.get(detail, "withSaleWithLicenses") },
    { id: "stock.isFactory", value: _.get(detail, "isFactory") },
    { id: "stock.isIncludeWaterElec", value: _.get(detail, "isIncludeWaterElec") },
    { id: "stock.hvBizTools", value: _.get(detail, "hvBizTools") },
    { id: "stock.toilet", value: !!_.get(detail, "facilities.havePrivateToilets") },
  ];
};

const getShopsTags = (detail = {}) => {
  const validEaaRecord = _.find(_.get(detail, "eaaRecords"), { status: "valid" });

  return [
    {
      id: "stock.tag.marketable",
      value: _.get(detail, "isMarketableForSale") || _.get(detail, "isMarketableForLease"),
    },
    { id: "stock.tag.mortgagee", value: _.get(detail, "mortgagee") },
    { id: "search.form.transferByCompany", value: _.get(detail, "saleEquity") },
    { id: "search.form.handover", value: _.get(detail, "leasingInfo.isHandOver") },
    {
      id: "search.form.confirmor",
      value: compareDate(null, _.get(detail, "confirmorTransactionDate")),
    },
    { id: "stock.advertisment.isDoNotAllowBoard", value: _.get(validEaaRecord, "isDoNotAllowBoard") },
    { id: "stock.advertisment.isDoNotAllowPoster", value: _.get(validEaaRecord, "isDoNotAllowPoster") },
    { id: "stock.advertisment.isDoNotAllowNewspaper", value: _.get(validEaaRecord, "isDoNotAllowNewspaper") },
    { id: "stock.advertisment.isNoAds", value: _.get(validEaaRecord, "isDoNotAds") },
    { id: "search.form.bigsinglesidestock", value: _.get(detail, "singleSideType") === "Big" },
    { id: "search.form.smallsinglesidestock", value: _.get(detail, "singleSideType") === "Small" },
    { id: "stock.tag.frontandrear", value: _.get(detail, "isFrontAndRearPortion") },
    { id: "stock.tag.shoppingmall", value: _.get(detail, "isShoppingMallStock") },
    { id: "stock.inwater", value: _.get(detail, "leasingInfo.isInWater") },
    { id: "stock.outwater", value: _.get(detail, "leasingInfo.isOutWater") },
    { id: "stock.backdoor", value: _.get(detail, "leasingInfo.haveBackDoor") },
    { id: "stock.towngas", value: _.get(detail, "leasingInfo.haveTownGas") },
    { id: "stock.LPower", value: _.get(detail, "hvLPower") },
    { id: "stock.licensed", value: _.get(detail, "isLicensed") },
    { id: "stock.withCarpark", value: _.get(detail, "withParking") },
    { id: "stock.with24hAC", value: _.get(detail, "with24hAC") },
    { id: "stock.is24hEnter", value: _.get(detail, "is24hEnter") },
    { id: "stock.saleWithLicenses", value: _.get(detail, "withSaleWithLicenses") },
    { id: "stock.isFactory", value: _.get(detail, "isFactory") },
    { id: "stock.isIncludeWaterElec", value: _.get(detail, "isIncludeWaterElec") },
    { id: "stock.bizTools", value: _.get(detail, "hvBizTools") },
    { id: "stock.toilet", value: !!_.get(detail, "facilities.havePrivateToilets") },
  ];
};

/**
 * @param {Array<{ id: string; value: boolean | ((detail: any) => boolean) }>} tagsConfig
 * @returns {{ [id: string]: boolean }}
 */
function generateTagObj(stockDetails = null, tagsConfig = [],) {
  if (!stockDetails || !Array.isArray(tagsConfig) || !tagsConfig.length) {
    return {};
  }

  return tagsConfig.reduce((prev, cur) => {
    const { id, value } = cur;
    const v = typeof value === "function" ? value(stockDetails) : value;
    prev[id] = v || false;
    return prev;
  }, {});
}

function PropertyTagBar(props) {
  const { classes, detail, tagsOverride, intl } = props;
  const markedWWW = useSelector(state => _.get(state, "stock.markedWWW", []));

  const isWWW = React.useMemo(() => {
    if (!markedWWW || !markedWWW.length) { return false; }

    return markedWWW?.filter(
      item => !item.isDeleted
    )?.[0]?.markWWWUsers?.filter(
      item => !item.isDeleted && item.emp_name && item.teamCode,
    ).length > 0;
  }, [markedWWW]);

  const checkFlagAvailable = (flagName, fallback) => {
    let flag = detail[flagName];
    return flag === true || flag === false ? flag : fallback;
  };

  const now = Date.now();
  const tagCar = !!detail.isCarPark;
  const tagSurverorPP = !!detail.haveSurveyorProposal || !!detail.surveyorProposal?.status;
  const tagWww = !!detail.isWWW;
  const tagMtgCalculated = !!(detail.mortgagee && detail.mortgagee.at);
  const tagMtg = checkFlagAvailable("mortgagee", tagMtgCalculated);
  const saleData =
    detail.propertyAdvertisements && detail.propertyAdvertisements.saleData
      ? detail.propertyAdvertisements.saleData
      : {};
  const rentData =
    detail.propertyAdvertisements && detail.propertyAdvertisements.rentData
      ? detail.propertyAdvertisements.rentData
      : {};
  const dataArr = [saleData, rentData];
  let tagEaaCalculated = false;
  for (let i = 0; i < dataArr.length; i++) {
    let minDate = dataArr[i].minDate
      ? moment(dataArr[i].minDate, "YYYY-MM-DD").format("x")
      : null;
    let maxDate = dataArr[i].maxDate
      ? moment(dataArr[i].maxDate, "YYYY-MM-DD").format("x")
      : null;

    if (minDate && maxDate) {
      tagEaaCalculated = minDate < now && now < maxDate;
    } else if (minDate) {
      tagEaaCalculated = minDate < now;
    } else if (maxDate) {
      tagEaaCalculated = now < maxDate;
    }
  }
  const tagEaa = checkFlagAvailable(
    "havePropertyAdvertisements",
    tagEaaCalculated,
  );
  const createTimestamp =
    detail.recordOperation && detail.recordOperation.createDate
      ? moment(detail.recordOperation.createDate, "YYYY-MM-DD").format("x")
      : null;

  let definedNewdays;
  switch (sbu) {
    case "COMM":
      definedNewdays = 15;
      break;
    case "IND":
      definedNewdays = 3;
      break;
    case "SHOPS":
      definedNewdays = 15;
      break;
    default:
      definedNewdays = 15;
      break;
  }

  const tagNewCalculated =
    createTimestamp &&
    createTimestamp > now - definedNewdays * 24 * 60 * 60 * 1000;
  const tagNew = checkFlagAvailable("isNew", tagNewCalculated);
  
  const wwwDetail = _.get(detail, "wwwDetail", null);
  const tagSoleCalculated = !_.isNil(wwwDetail) && checkIfSoleagent(wwwDetail);
  // const tagSoleCalculated = !!(
  //   detail.soleagent &&
  //   detail.soleagent.periodStart != null &&
  //   detail.soleagent.periodEnd != null
  // );
  const tagSole = checkFlagAvailable("isSoleAgent", tagSoleCalculated);
  const tagEqCalculated = !!detail.saleEquity;
  const tagEq = checkFlagAvailable("isSaleEquity", tagEqCalculated);

  /* 
  const tags = {
    [intl.formatMessage({ id: "stock.tag.sole" })]: tagSole,
    [intl.formatMessage({ id: "stock.tag.carpark" })]: tagCar,
    [intl.formatMessage({ id: "stock.tag.www" })]: tagWww,
    [intl.formatMessage({ id: "stock.tag.mortgagee" })]: tagMtg,
    [intl.formatMessage({ id: "stock.tag.eaa" })]: tagEaa,
    [intl.formatMessage({ id: "stock.tag.new" })]: tagNew,
    [intl.formatMessage({ id: "stock.tag.equaity" })]: tagEq,
    [intl.formatMessage({ id: "stock.tag.haveSurverorPP" })]: tagSurverorPP,
  };
  */

  const tagArr = React.useMemo(() => {
    const tagsConfig = [];
    if (sbu === "COMM") {
      tagsConfig.push(...getCommTags(detail, isWWW));
    } else if (sbu === "IND") {
      tagsConfig.push(...getIndTags(detail, isWWW));
    } else if (sbu === "SHOPS") {
      tagsConfig.push(...getShopsTags(detail));
    }
    return tagsConfig.map(v => {
      return { ...v, id: intl.formatMessage({ id: v.id }) };
    });
  }, [intl, detail, isWWW]);

  const tags = React.useMemo(() => {
    return generateTagObj(detail, tagArr);
  }, [detail, tagArr]);

  if (typeof tagsOverride === "object") {
    Object.keys(tagsOverride).forEach((v) => {
      delete tags[v];
      tags[v] = tagsOverride[v];
    });
  }

  let tagsClass = {
    [intl.formatMessage({ id: "stock.tag.sole" })]: tagSole
      ? classes.redTag
      : "",
  };

  return (
    <div className={classes.propertyTags}>
      {tagArr
        .filter((v) => tags[v.id] !== null)
        .map((v) => (
          <PropertyTag className={tagsClass[v.id]} on={tags[v.id]} key={v.id}>
            {v.id}
          </PropertyTag>
        ))}
    </div>
  );
}

PropertyTagBar.propTypes = {
  classes: PropTypes.object.isRequired,
  detail: PropTypes.object,
  tagsOverride: PropTypes.object,
};

export default withStyles(styles)(injectIntl(PropertyTagBar));
