import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import FieldVal from "../../../../common/FieldVal";
import clsx from "clsx";
import { injectIntl } from "react-intl";

const styles = theme => ({
  root: {
    padding: "1vh 0"
  },
  gridContent: {
    padding: "1vw 2vw"
  },
  lmrAlign: {
    "& > :nth-child(3n+2)": {
      textAlign: "center"
    },
    "& > :nth-child(3n)": {
      textAlign: "right"
    }
  }
});

class Facilities extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;

    let facilityMapping = {
      [intl.formatMessage({
        id: "stock.conferencerm"
      })]: "conferenceRoom",
      [intl.formatMessage({
        id: "stock.meetingrm"
      })]: "meetingRoom",
      [intl.formatMessage({
        id: "stock.room"
      })]: "room",
      [intl.formatMessage({
        id: "stock.utilityrm"
      })]: "utilityRoom",
      [intl.formatMessage({
        id: "stock.storerm"
      })]: "storeRoom",
      [intl.formatMessage({
        id: "stock.workstation"
      })]: "workstation",
      [intl.formatMessage({
        id: "stock.windowblinds"
      })]: "windowBlinds",
      [intl.formatMessage({
        id: "stock.computerrm"
      })]: "computerRoom",
      [intl.formatMessage({
        id: "stock.desk"
      })]: "desk"
    };
    const facilities = detail.facilities || {};

    return (
      <div className={classes.root}>
        <DetailBoxSection
          text={intl.formatMessage({
            id: "stock.facilities"
          })}
          expandable={true}
        >
          <Grid
            container
            spacing={2}
            className={clsx(classes.lmrAlign, classes.gridContent)}
          >
            {Object.keys(facilityMapping).map((v, i) => (
              <Grid item xs={4} key={v}>
                <FieldVal field={v}>
                  {facilities[facilityMapping[v]] === true
                    ? 1
                    : facilities[facilityMapping[v]] || "---"}
                </FieldVal>
              </Grid>
            ))}
          </Grid>
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(Facilities));
