import {
  C<PERSON>AR_CREATE_PROPOSAL,
  CREATE_PROPOSAL_START,
  CREATE_PROPOSAL_SUCCESS,
  CREATE_PROPOSAL_ERROR,
  <PERSON><PERSON>AR_PROPOSALS,
  LIST_PROPOSALS_START,
  LIST_MORE_PROPOSALS_START,
  LIST_PROPOSALS_SUCCESS,
  LIST_PROPOSALS_NULL_SUCCESS,
  LIST_PROPOSALS_ERROR,
  REMOVE_PROPOSAL_START,
  REMOVE_PROPOSAL_SUCCESS,
  REMOVE_PROPOSAL_ERROR,
  GET_PROPOSALCOUNT_SUCCESS,
  GET_PROPOSALCOUNT_ERROR,
  QUERY_PROPOSAL_RES_START,
  QUERY_PROPOSAL_RES_SUCCESS,
  QUERY_PROPOSAL_RES_ERROR,
  CREATE_PREVIEW_ERROR,
  FETCH_RECREATE_PP,
  FETCH_RECREATE_PP_SUCCESS,
  CLEAR_CREATED_PP_PREVIEW,
  CREATE_PP_PREVIEW_SUCCESS,
  SET_IS_RECREATE_PP,
} from "../constants/proposal";

const initialState = {
  creatingProposal: false,
  createdProposal: false,
  listing: false,
  listed: false,
  createdPdf: [],

  perviewUrl: null,
  width: null,
  height: null,

  queryingProposalRes: false,
  queryProposalResSuccess: false,
  queryProposalResError: false,
};

export default function medium(state = initialState, action) {
  switch (action.type) {
    case CREATE_PP_PREVIEW_SUCCESS:
      return {
        ...state,
        perviewUrl: action.payload.perviewUrl,
        width: action.payload.width,
        height: action.payload.height,
      };
    case CLEAR_CREATED_PP_PREVIEW:
      return {
        ...state,
        perviewUrl: null,
        width: null,
        height: null,
        createPreviewError: null,
      };
    case CLEAR_CREATE_PROPOSAL:
      return {
        ...state,
        creatingProposal: false,
        createdProposal: false,
        createProposalError: null,
      };
    case CREATE_PROPOSAL_START:
      return {
        ...state,
        creatingProposal: true,
        createdProposal: false,
        // proposal: action.payload.proposal,
      };
    case CREATE_PROPOSAL_SUCCESS:
      return {
        ...state,
        creatingProposal: false,
        createdProposal: true,
        proposal: action.payload.data.createProposal,
      };
    case CREATE_PROPOSAL_ERROR:
      return {
        ...state,
        creatingProposal: false,
        createdProposal: false,
        createProposalError: action.payload.error,
      };
    case CREATE_PREVIEW_ERROR:
      return {
        ...state,
        createPreviewError: action.payload.error,
      };

    case CLEAR_PROPOSALS:
      return {
        ...state,
        listed: false,
        listing: false,
        proposals: [],
        queryvariables: null,
        listProposalError: null,
      };
    case LIST_PROPOSALS_START:
      return {
        ...state,
        listed: false,
        listing: true,
        proposals: [],
        hasMore: true,
        queryvariables: action.payload.variables,
      };
    case LIST_MORE_PROPOSALS_START:
      return {
        ...state,
        listing: true,
        hasMore: true,
        queryvariables: action.payload.variables,
      };
    case LIST_PROPOSALS_SUCCESS:
      return {
        ...state,
        listed: true,
        listing: false,
        hasMore: true,
        proposals: state.proposals.concat(action.payload.data.proposals || []),
        proposalsCount: action.payload.data.proposalsCount,
      };
    case LIST_PROPOSALS_NULL_SUCCESS:
      return {
        ...state,
        listing: false,
        listed: true,
        hasMore: false,
        proposals: state.proposals.concat(action.payload.data.proposals || []),
      };
    case LIST_PROPOSALS_ERROR:
      return {
        ...state,
        listed: false,
        listing: false,
        listProposalError: action.payload.error,
      };
    case REMOVE_PROPOSAL_START:
      return {
        ...state,
        removingProposal: true,
        removedProposal: false,
        removeProposalError: null,
      };
    case REMOVE_PROPOSAL_SUCCESS:
      return {
        ...state,
        removingProposal: false,
        removedProposal: true,
        removeProposalError: null,
      };
    case REMOVE_PROPOSAL_ERROR:
      return {
        ...state,
        removingProposal: false,
        removedProposal: false,
        removeProposalError: "Failed to remove",
      };
    case GET_PROPOSALCOUNT_SUCCESS:
      return {
        ...state,
        createdPdf: action.payload.data.data.length,
      };
    case GET_PROPOSALCOUNT_ERROR:
      return {
        ...state,
        createdPdf: 0,
        listProposalCountError: action.payload.error,
      };
    case QUERY_PROPOSAL_RES_START:
      return {
        ...state,
        queryingProposalRes: true,
        queryProposalResSuccess: false,
        queryProposalResError: false,
      };
    case QUERY_PROPOSAL_RES_SUCCESS:
      return {
        ...state,
        queryingProposalRes: false,
        queryProposalResSuccess: true,
      };

    case QUERY_PROPOSAL_RES_ERROR:
      return {
        ...state,
        queryingProposalRes: false,
        queryProposalResError: true,
      };
    case FETCH_RECREATE_PP:
      return {
        ...state,
        reCreatePPStocks: [],
      };
    case FETCH_RECREATE_PP_SUCCESS:
      return {
        ...state,
        reCreatePPStocks: action.payload || [],
      };
    case SET_IS_RECREATE_PP:
      return {
        ...state,
        isReCreatePP: action.payload,
      };
    default:
      return state;
  }
}
