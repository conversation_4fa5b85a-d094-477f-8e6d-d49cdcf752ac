import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import InfiniteList from "../common/InfiniteList";
import ReportCard from "./ReportCard";
import LoadingOverlay from "../LoadingOverlay";

const styles = (theme) => ({
  card: {
    marginBottom: "1vh",
  },
  notFound: {
    margin: "1em 0",
    textAlign: "center",
    fontWeight: "bold",
  },
});

class List extends React.Component {
  static propTypes = {
    reports: PropTypes.array,
    classes: PropTypes.object.isRequired,
    listed: PropTypes.bool,
    listing: PropTypes.bool,
  };

  render() {
    const {
      classes,
      reports,
      listed,
      listing
    } = this.props;
    const hasData = reports.length > 0;

    return (
      <div className={classes.root}>
        {listed && hasData && (
          <InfiniteList
            list={reports}
            hasMore={true}
            fetchMoreData={() => { }}
          >
            {({ ...props }) => (
              <ReportCard
                className={classes.card}
                {...props}
              />
            )}
          </InfiniteList>)}

        {(listing || !listed) && <LoadingOverlay />}

        {!listing && listed && !hasData && (
          <div className={classes.notFound}>Report not found</div>
        )}
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  reports: state.listTxReport ? state.listTxReport.listTxReports : [],
  listed: state.listTxReport.listed ? state.listTxReport.listed : false,
  listing: state.listTxReport.listing ? state.listTxReport.listing : false,
});

export default connect(mapStateToProps)(withStyles(styles)(List));