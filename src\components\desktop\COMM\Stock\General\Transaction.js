import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import Button from "@material-ui/core/Button";
import { injectIntl } from "react-intl";
import { listTransactions } from "../../../../../actions/transaction";
import TransactionCard from "../../Transaction/TransactionCard";
import { goToTxResult } from "../../../../../helper/generalHelper";

const styles = (theme) => ({
  generalProp: {
    padding: "2vh 0",
  },
  lmrAlign: {
    paddingLeft: "2vw",
  },
  card: {
    padding: 0,
    backgroundColor: "transparent",
  },
  moreButton: {
    textAlign: "center",
    width: "100%",
  },
  buttonText: {
    textTransform: "none",
  },
  noData: {
    fontSize: "1.125em",
    paddingLeft: "2vw",
  },
});

class Transaction extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.state = {
      isExpanding: false,
      latestTxdata: [],
    };
  }

  componentDidMount() {
    const { detail } = this.props;
    const buildingId =
      detail.building && detail.building._id ? detail.building._id : null;
    this.props.listTransactions({ building: [buildingId] });
  }

  componentDidUpdate(prevProps) {
    if (this.props.transactions && this.props.transactions.length > 0) {
      if (prevProps.transactions !== this.props.transactions) {
        const { transactions } = this.props;
        // sort by date in desc order
        transactions.sort(function compare(a, b) {
          let dateA = new Date(a.date);
          let dateB = new Date(b.date);
          return dateB - dateA;
        });
        const slicedTransaction = transactions.slice(0, 5);
        this.setState({
          latestTxdata: slicedTransaction,
        });
      }
    }
  }

  componentWillUnmount() {}

  handleChange = (value) => {
    // get the value returned by child component
    this.setState({ isExpanding: value });
  };

  render() {
    const { classes, detail, transactions, intl } = this.props;
    const { latestTxdata } = this.state;

    const building = detail.building ? detail.building : {};
    const _id = detail.building && building._id ? building._id : null;
    const buildingNameZh =
      detail.building && building.nameZh ? building.nameZh : null;

    const query = {
      building: [_id],
      street: [],
      district: [],
      limit: 50,
      offset: 0,
    };

    const selectedData = {
      building: [
        {
          value: _id,
          label: buildingNameZh,
        },
      ],
    };

    const goToSearchTxResult = () => {
      goToTxResult(query, selectedData);
    };

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          isExpanding={this.state.isExpanding}
          callback={this.handleChange}
          text={intl.formatMessage({ id: "home.transaction" })}
        >
          {transactions && transactions.length > 0 ? (
            <Grid container spacing={3} className={classes.lmrAlign}>
              {latestTxdata && latestTxdata.map((v, i) => {
                return (
                  <Grid item xs={12} key={i}>
                    <TransactionCard
                      className={classes.card}
                      isStockDetail={true}
                      detail={v}
                    />
                  </Grid>
                );
              })}

              <div className={classes.moreButton}>
                <Button
                  variant="outlined"
                  style={{ borderRadius: 25 }}
                  onClick={goToSearchTxResult}
                  className={classes.buttonText}
                >
                  {intl.formatMessage({ id: "stock.transaction.more" })}
                </Button>
              </div>
            </Grid>
          ) : (
            <div className={classes.noData}>{intl.formatMessage({ id: "stock.transaction.null" })}</div>
          )}
        </DetailBoxSection>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  transactions: state.transaction.transactions
    ? state.transaction.transactions
    : [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    listTransactions: (...args) => dispatch(listTransactions(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(Transaction)));
