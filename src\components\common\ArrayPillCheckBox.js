import React, { useState } from "react";
// import { makeStyles } from "@material-ui/core/styles";
import { withStyles } from "@material-ui/styles";
import Checkbox from "@material-ui/core/Checkbox";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import Grid from "@material-ui/core/Grid";
import clsx from 'clsx';

// We can inject some CSS into the DOM.
const styles = theme => ({
  formControl: {
    margin: 0,
    width: "100%",
    "& > span": {
      width: "100%",
    }
  },
  root: {
    display: "none"
  },
  pill: {
    // height: 25,
    lineHeight: "34px",
    color: "#717171",
    borderRadius: "17px",
    padding: "0 10px",
    backgroundColor: "#DFDFDF",
    textAlign: "center"
  },
  checked: {
    color: "#FFF",
    backgroundColor: "#33CCCC"
  },
  disabled: {
    backgroundColor: "#e5e5e587",
    color: "#8d8d8d63",
  },
});

class ArrayPillCheckBox extends React.Component {
  // componentDidMount() {
  //   this.props.fields.push("Follow Up");
  //   this.props.fields.push("Lease");
  //   this.props.fields.push("Sale");
  //   this.props.fields.push("Sale+Lease");
  //   this.props.fields.push("Surrend");
  //   this.props.fields.push("Tenanted");
  // }

  render() {
    const { classes, fields, options, disabled } = this.props;
    const parsedOptions = options.map(v =>
      (typeof v === "string") ? { label: v, value: v } : v
    );
    const fieldsarray = fields.getAll() ? fields.getAll() : [];

    const toggle = (event, option) => {
      if (fieldsarray.indexOf(option) == -1) {
        fields.push(option);
      } else if (fieldsarray.indexOf(option) !== -1) {
        fields.remove(fieldsarray.indexOf(option));
      }
    };

    return (
      <Grid container spacing={1}>
        {parsedOptions.map(option => (
          <Grid key={option.value} item xs={4}>
            <FormControlLabel
              className={`${classes.formControl} ${option.value}`}
              control={
                <Checkbox
                  classes={{
                    root: classes.root
                  }}
                  value={option.value}
                  onChange={event => toggle(event, option.value)}
                  disabled={disabled}
                />
              }
              label={
                <div
                  className={clsx(classes.pill, {
                    [classes.checked]: fieldsarray.indexOf(option.value) !== -1,
                    [classes.disabled]: disabled,
                  })}
                >
                  {option.label}
                </div>
              }
            />
          </Grid>
        ))}
      </Grid>
    );
  }
}

export default withStyles(styles)(ArrayPillCheckBox);
