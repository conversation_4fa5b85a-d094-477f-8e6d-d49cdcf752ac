/* eslint-disable global-require */

// The top-level (parent) route
const routes = {
  path: "",

  // Keep in mind, routes are evaluated in order
  children: [
    {
      path: "",
      load: () => import(/* webpackChunkName: 'home' */ "../../home")
    },
    {
      path: "/login",
      load: () => import(/* webpackChunkName: 'login' */ "../../login")
    },
    {
      path: "/detailsearch",
      load: () => import(/* webpackChunkName: 'search' */ "./search")
    },
    // {
    //   path: "/search",
    //   load: () => import(/* webpackChunkName: 'keyword' */ "./keyword")
    // },
    {
      path: "/result/:some?",
      load: () => import(/* webpackChunkName: 'stocklist' */ "./stocklist")
    },
    {
      path: "/stock/:id",
      load: () => import(/* webpackChunkName: 'stock' */ "./stock")
    },
    {
      path: "/building/:id",
      load: () => import(/* webpackChunkName: 'building' */ "./building")
    },
    {
      path: "/transaction",
      load: () => import(/* webpackChunkName: 'transaction' */ "./transaction")
    },
    {
      path: "/messageCenter",
      load: () =>
        import(/* webpackChunkName: 'messageCenter' */ "../../messageCenter")
    },
    {
      path: "/myFavorite",
      load: () =>
        import(/* webpackChunkName: 'myFavorite' */ "./myFavorite")
    },
    {
      path: "/help",
      load: () => import(/* webpackChunkName: 'tips' */ "../../tips")
    },
    {
      path: "/unlocked",
      load: () =>
        import(
          /* webpackChunkName: 'unlockedStocklist' */ "./unlockedStocklist"
        )
    },
    {
      path: "/proposal/list",
      load: () => import(/* webpackChunkName: 'proposal' */ "./proposal/list")
    },
    // Wildcard routes, e.g. { path: '(.*)', ... } (must go last)
    {
      path: "(.*)",
      load: () => import(/* webpackChunkName: 'not-found' */ "../../not-found")
    }
  ],

  async action({ next }) {
    // Execute each child route until one of them return the result
    const route = await next();

    // Provide default values for title, description etc.
    route.title = `MSearch 搵盤易 - ${route.title || "Untitled Page"}`;
    route.description = route.description || "";

    return route;
  }
};

// The error page is available by permanent url for development mode
// if (__DEV__) {
routes.children.unshift({
  path: "/error",
  action: require("../../error").default
});
// }

export default routes;
