import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { Field, FieldArray, change } from "redux-form";
import { injectIntl, FormattedMessage } from "react-intl";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import Grid from "@material-ui/core/Grid";
import ProposalFormPaper from "../../../../common/ProposalFormPaper";
import MuiSelectArrayOutput from "../../../../common/MuiSelectArrayOutput";
import EditableNormalAndBottomPrice from "../../../../common/EditableNormalAndBottomPrice";
import InlineTextField from "../../../../common/InlineTextField";
import InlineTextInput from "../../../../common/InlineTextInput";
import InlineTextInputField from "../../../../common/InlineTextInputField";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import InputWithCheckBox from "../../../../common/InputWithCheckBox";
import InputWithCheckBoxCustom from "../../../../common/InputWithCheckBoxCustom";
import ShowHiddenCheckBox from "../../../../common/ShowHiddenCheckBox";
import ProposalTenantField from "../../../../common/ProposalTenantField";
import Media from "./Media";
import BuildingInfo from "./BuildingInfo";
import { numberComma } from "../../../../../helper/generalHelper";
import {
  floorTypeLangIdMapping,
  getTypeOptions,
  getFloorTypeOptions,
  getPossessionOptions,
  getDefaultRemarks,
} from "./selectOptions";

// We can inject some CSS into the DOM.
const styles = {
  section: {
    paddingTop: "1vh",
  },
  right: {
    textAlign: "right",
  },
  twoLevelItem: {
    marginBottom: "1vh",
    "& > *:not(:first-child)": {
      color: "rgba(255, 255, 255, 0.75)",
    },
    padding: "0 4px",
    borderRadius: 4,
    color: "#fff",
  },
  rentItem: {
    backgroundColor: "rgba(0, 197, 197, .75)",
  },
  priceItem: {
    backgroundColor: "rgba(232, 0, 0, .75)",
  },
  greyItem: {
    backgroundColor: "rgba(132, 132, 132, .1)",
  },
  rentItemNoValue: {
    backgroundColor: "rgba(140, 190, 190, 0.2)",
  },
  priceItemNoValue: {
    backgroundColor: "rgba(200, 170, 170, 0.2)",
  },
  checkboxNoPadding: {
    padding: 0,
  },
  hidden: {
    visibility: "hidden",
  },
  basicInfo: {
    display: "flex",
    justifyContent: "space-around",
    margin: "1vh 0 0 0",
  },
  remarksFieldMargin: {
    marginBottom: 3,
  },
};

function FieldSection(props) {
  const {
    classes,
    intl,
    formFields,
    changeFieldValue,
    initialize,
    initialValues,
  } = props;

  const [isExpanding, setisExpanding] = useState(true);

  let priceContainer = classes.priceItem; // TODO: need grey color?
  let leaseContainer = classes.rentItem;
  const hasPrice = formFields.avgPrice && formFields.avgPrice[0]&& formFields.avgPrice[0].value && parseFloat(formFields.avgPrice[0].value) !== 0 ||
    formFields.totalPrice && formFields.totalPrice[0] && formFields.totalPrice[0].value && parseFloat(formFields.totalPrice[0].value) !== 0;
  const hasRent = formFields.avgRent && formFields.avgRent[0] && formFields.avgRent[0].value && parseFloat(formFields.avgRent[0].value) !== 0 ||
    formFields.totalRent && formFields.totalRent[0] && formFields.totalRent[0].value && parseFloat(formFields.totalRent[0].value) !== 0;
  const disablePriceBox = formFields.type === "Lease";
  const disableRentBox = formFields.type === "Sale";

  useEffect(() => {
    // code to run on component mount
    initialize(
      initialValues
      //   {
      //   testingone: [{ value: "123", isShow: false }],
      //   testingtwo: [{ value: "444", isShow: false }]
      // }
    )
  }, [])

  const typeOptions = getTypeOptions(intl);
  const actualFloor = formFields.floor && formFields.floor[0] && formFields.floor[0].value ? formFields.floor[0].value : "---";
  const floorTypeOptions = getFloorTypeOptions(intl, actualFloor);
  const possessionOptions = getPossessionOptions(intl);

  const inputToDisplayUnit = (v) => {
    if (intl.locale === "zh")
      return v ? v + intl.formatMessage({ id: "proposal.form.unit" }) : "---";
    else
      return v ? intl.formatMessage({ id: "proposal.form.unit" }) + " " + v : "---";
  };
  const inputToDisplayAreaNet = (v) => v && parseFloat(v) !== 0 ? numberComma(v, 0) : "---";
  const inputToDisplayAreaGross = (v) => v && parseFloat(v) !== 0 ? numberComma(v, 0) : "---";
  const inputToDisplayAreaEff = (v) => v && parseFloat(v) !== 0 ? numberComma(v, 2) + "%" : "---";
  const inputToDisplayFee = (v) => v && parseFloat(v) !== 0 ? "$" + numberComma(v, 2) : "---";
  const inputToDisplayYield = (v) => v && parseFloat(v) !== 0 ? numberComma(v, 2) + "%" : "---";
  const inputToDisplayTitle = (v) => v || "---";

  const setPriceBoxIsShow = (isShow) => {
    changeFieldValue("totalPrice[0]", { ...formFields["totalPrice"][0], isShow: isShow });
    changeFieldValue("avgPrice[0]", { ...formFields["avgPrice"][0], isShow: isShow });
  };
  const setRentBoxIsShow = (isShow) => {
    changeFieldValue("totalRent[0]", { ...formFields["totalRent"][0], isShow: isShow });
    changeFieldValue("avgRent[0]", { ...formFields["avgRent"][0], isShow: isShow });
  };
  const handleTypeChange = (v) => {
    switch (v.value) {
      case "Sale":
        setPriceBoxIsShow(true);
        setRentBoxIsShow(false);
        changeFieldValue("remarks", getDefaultRemarks("Sale"));
        break;
      case "Lease":
        setPriceBoxIsShow(false);
        setRentBoxIsShow(true);
        changeFieldValue("remarks", getDefaultRemarks("Lease"));
        break;
      default:
        setPriceBoxIsShow(true);
        setRentBoxIsShow(true);
        changeFieldValue("remarks", getDefaultRemarks("SaleAndLease"));
    }
  };

  return (
    <div>
      <DetailBoxSection
        text={intl.formatMessage({
          id: "proposal.form.stockinfo",
        })}
        expandable={true}
      >
        <ProposalFormPaper>
          <Grid container spacing={1}>
            <Grid item xs={12}>
              <InputWithCheckBoxCustom
                name="type"
                label={intl.formatMessage({
                  id: "proposal.form.type",
                })}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={1}
                fakeCheckbox={true}
                renderComponent={MuiSelectArrayOutput}
                ranges={typeOptions}
                isArrayOutput={false}
                extraHandleChange={handleTypeChange}
                fullWidth
              />
            </Grid>
          </Grid>
        </ProposalFormPaper>

        <ProposalFormPaper>
          <Grid container spacing={1}>
            <Grid item xs={6}>
              <InputWithCheckBoxCustom
                name="floorType"
                label={intl.formatMessage({
                  id: "search.common.floor",
                })}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={2}
                fakeCheckbox={true}
                renderComponent={MuiSelectArrayOutput}
                ranges={floorTypeOptions}
                isArrayOutput={false}
                fullWidth
              />
            </Grid>
            <Grid item xs={6}>
              <FieldArray
                name={"unit"}
                label={intl.formatMessage({
                  id: "search.common.unit",
                })}
                component={InputWithCheckBox}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                inputToDisplay={inputToDisplayUnit}
                checkboxProps={{
                  className: classes.checkboxNoPadding,
                }}
                checkboxInFront={true}
                checkboxXs={2}
                disabled
              />
            </Grid>
          </Grid>

          {/* building info */}
          <Grid container spacing={1}>
            <ShowHiddenCheckBox
              className={classes.hidden}
              input={{
                value: { isShow: true }
              }}
              changeFieldValue={() => {}}
              aligntoLabel={true}
              disabled
            />
            <Grid item xs={11} style={{ "margin": "auto" }}>
              <div>
                {initialValues.customBuilding}
              </div>

              {/* street info */}
              <div>
                {initialValues.customStreetNo} {initialValues.customStreet}, {initialValues.customDistrict}
              </div>
            </Grid>
          </Grid>

          {/* basic info 3 */}
          <div className={classes.basicInfo}>
            <Grid container spacing={1}>
              <Grid item xs={4}>
                <FieldArray
                  name={"areaNet"}
                  label={intl.formatMessage({ id: "proposal.form.net" })}
                  component={InputWithCheckBox}
                  renderComponent={InlineTextField}
                  changeFieldValue={changeFieldValue}
                  type="number"
                  inputToDisplay={inputToDisplayAreaNet}
                  min={0}
                  checkboxProps={{
                    className: classes.checkboxNoPadding,
                  }}
                  checkboxInFront={true}
                  checkboxXs={3}
                  disabled
                  noBottomBorder
                />
              </Grid>
              <Grid item xs={4}>
                <FieldArray
                  name={"areaEfficiency"}
                  label={intl.formatMessage({ id: "stock.area.effcy" })}
                  component={InputWithCheckBox}
                  renderComponent={InlineTextField}
                  changeFieldValue={changeFieldValue}
                  type="number"
                  inputToDisplay={inputToDisplayAreaEff}
                  min={0}
                  checkboxProps={{
                    className: classes.checkboxNoPadding,
                  }}
                  checkboxInFront={true}
                  checkboxXs={3}
                  disabled
                  noBottomBorder
                />
              </Grid>
              <Grid item xs={4}>
                <FieldArray
                  name={"areaGross"}
                  label={intl.formatMessage({ id: "proposal.form.gross" })}
                  component={InputWithCheckBox}
                  renderComponent={InlineTextField}
                  changeFieldValue={changeFieldValue}
                  type="number"
                  inputToDisplay={inputToDisplayAreaGross}
                  min={0}
                  checkboxProps={{
                    className: classes.checkboxNoPadding,
                  }}
                  checkboxInFront={true}
                  checkboxXs={3}
                  disabled
                  noBottomBorder
                />
              </Grid>
            </Grid>
          </div>
        </ProposalFormPaper>


        {/* prcie rent box */}
        <div className={classes.right}>
          <div
            className={clsx(
              classes.twoLevelItem,
              leaseContainer,
              (!hasRent || disableRentBox) && classes.rentItemNoValue
            )}
          >
            <EditableNormalAndBottomPrice
              avgDecimal={2}
              label="Rent"
              changeFieldValue={changeFieldValue}
              formFields={formFields}
              disabled={disableRentBox}
            />
          </div>

          <div
            className={clsx(
              classes.twoLevelItem,
              priceContainer,
              (!hasPrice || disablePriceBox) && classes.priceItemNoValue
            )}
          >
            <EditableNormalAndBottomPrice
              label="Price"
              changeFieldValue={changeFieldValue}
              formFields={formFields}
              disabled={disablePriceBox}
              updateYield
            />
          </div>
        </div>



        {/* unit section */}
        <ProposalFormPaper>
          <Grid container spacing={1}>
            <Grid item xs={6}>
              <FieldArray
                name="decoration"
                label={intl.formatMessage({
                  id: "search.form.decoration",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={2}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                disabled
              />
            </Grid>

            <Grid item xs={6}>
              <FieldArray
                name="unitView"
                label={intl.formatMessage({
                  id: "search.form.view",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={2}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                disabled
              />
            </Grid>

            <Grid item xs={6}>
              <FieldArray
                name="possession"
                label={intl.formatMessage({
                  id: "search.form.possession",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={2}
                renderComponent={MuiSelectArrayOutput}
                changeFieldValue={changeFieldValue}
                ranges={possessionOptions}
                isArrayOutput={false}
              />
            </Grid>

            <Grid item xs={6}>
              <FieldArray
                name="availability"
                label={intl.formatMessage({
                  id: "proposal.form.availabilitydate",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={2}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                disabled
              />
            </Grid>

            <Grid item xs={6}>
              <FieldArray
                name={"managementFee"}
                label={intl.formatMessage({
                  id: "stock.mgtfee",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={2}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                inputToDisplay={inputToDisplayFee}
                type="number"
                min={0}
              />
            </Grid>

            <Grid item xs={6}>
              <FieldArray
                name="yield"
                label={intl.formatMessage({
                  id: "stock.yield",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={2}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                inputToDisplay={inputToDisplayYield}
                type="number"
                min={0}
                disabled
              />
            </Grid>
          </Grid>
        </ProposalFormPaper>

        <Grid container spacing={1}>
          <Grid item xs={12}>
            <FieldArray
              name={"currentTenants"}
              component={ProposalTenantField}
              changeFieldValue={changeFieldValue}
            />
          </Grid>
        </Grid>

        <ProposalFormPaper>
          <Grid container spacing={1}>
            <Grid item xs={12}>
              <InputWithCheckBoxCustom
                name="remarks"
                className={classes.remarksFieldMargin}
                label={intl.formatMessage({
                  id: "stock.remarks",
                })}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={1}
                fakeCheckbox={true}
                renderComponent={InlineTextInputField}
                multiline
                fullWidth
              />
            </Grid>
          </Grid>
        </ProposalFormPaper>

      </DetailBoxSection>

      <BuildingInfo changeFieldValue={changeFieldValue} />

      <div className={classes.section}>
        <DetailBoxSection
          text={intl.formatMessage({
            id: "proposal.form.otherinfo",
          })}
          expandable={true}
        >
          <ProposalFormPaper>
            <Grid container spacing={1}>
              {/*<Grid item xs={12}>*/}
              {/*  <FieldArray*/}
              {/*    name="customTitle"*/}
              {/*    label={intl.formatMessage({*/}
              {/*      id: "proposal.form.customtitle",*/}
              {/*    })}*/}
              {/*    component={InputWithCheckBox}*/}
              {/*    checkboxInFront={true}*/}
              {/*    aligntoLabel={true}*/}
              {/*    checkboxXs={1}*/}
              {/*    renderComponent={InlineTextField}*/}
              {/*    changeFieldValue={changeFieldValue}*/}
              {/*    inputToDisplay={inputToDisplayTitle}*/}
              {/*  />*/}
              {/*</Grid>*/}

              <Grid item xs={12}>
                <FieldArray
                  name="hideEmployeePhoto"
                  checkboxInFront={true}
                  aligntoLabel={true}
                  checkboxXs={1}
                  component={InputWithCheckBox}
                  renderComponent={InlineTextInput}
                  changeFieldValue={changeFieldValue}
                  fullWidth
                  disabled
                  noBottomBorder
                />
              </Grid>

              <Grid item xs={12}>
                <FieldArray
                  name="hideContact"
                  checkboxInFront={true}
                  aligntoLabel={true}
                  checkboxXs={1}
                  component={InputWithCheckBox}
                  renderComponent={InlineTextInput}
                  changeFieldValue={changeFieldValue}
                  fullWidth
                  disabled
                  noBottomBorder
                />
              </Grid>
            </Grid>
          </ProposalFormPaper>
        </DetailBoxSection>
      </div>

      <Media />

      {props.children}

    </div>
  );
}

const mapStateToProps = (state) => ({
});

const mapDispatchToProps = (dispatch) => {
  return {

  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(FieldSection)));
