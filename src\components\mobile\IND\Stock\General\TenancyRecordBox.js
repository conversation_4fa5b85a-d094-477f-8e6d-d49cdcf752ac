import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import FieldValArrBox from "../../../../common/FieldValArrBox";
import { injectIntl } from "react-intl";
import TenancyRecordBoxPerson from "../../../../common/TenancyRecordBoxPerson";

// We can inject some CSS into the DOM.
const styles = {
  root: {

  },
  currentBox: {
    backgroundColor: "rgba(255, 255, 100, .4)"
  },
};

function TenancyRecordBox(props) {
  const {
    classes,
    status,
    persons = [],
    unit = "---",
    tenancyPeriod = "---",
    rent = "---",
    options = [],
    updateDate = "---",
    mongoId,
    stockId,
    intl,
    ...others
  } = props;

  let displayStatus;
  if (status === "Current")
    displayStatus = intl.formatMessage({
      id: "stock.currenttenancy",
    });
  if (status === "Former")
    displayStatus = intl.formatMessage({
      id: "stock.previoustenancy",
    });
  const tenantField = intl.formatMessage(
    { id: "stock.tenant" },
    { status: displayStatus },
  );

  // make sure there are exactly three options
  let optionsAppended = options.concat({}, {}, {}).slice(0, 3);
  let optionItems = [];
  optionsAppended.forEach((v, i) => {
    optionItems.push({
      field: intl.formatMessage({ id: "stock.option" }) + " (" + (i + 1)+ ")",
      val: optionsAppended[i].period || "---",
      xs: 6,
    });
    optionItems.push({
      field: intl.formatMessage({ id: "stock.rent" }) + " (" + (i + 1)+ ")",
      val: optionsAppended[i].rent || "---",
      xs: 6,
    });
  });

  let tenants = JSON.parse(JSON.stringify(persons));
  if (tenants.length === 0) tenants.push({});
  tenants = tenants.map((v, i) => {
    return {
      extra: <TenancyRecordBoxPerson field={tenantField + (tenants.length > 1 ? ` (${i + 1})` : "")} tenant={v || {}} mongoId={mongoId} stockId={stockId} />
    };
  });

  const items = [
    {
      field: intl.formatMessage({ id: "stock.unit" }),
      val: unit,
    },
    {
      field: intl.formatMessage({ id: "stock.tenancyperiod" }),
      val: tenancyPeriod,
    },
    {
      field: intl.formatMessage({ id: "stock.latestrent" }),
      val: rent,
    },
    ...optionItems,
    {
      field: intl.formatMessage({ id: "stock.updatedate" }),
      val: updateDate,
    },
    ...tenants,
  ];

  return (
    <FieldValArrBox
      className={`${classes.root} ${status === "Current" ? classes.currentBox : ""}`}
      items={items}
      {...others}
    />
  );
}

TenancyRecordBox.propTypes = {
  classes: PropTypes.object.isRequired,
  status: PropTypes.string.isRequired,
  persons: PropTypes.arrayOf(PropTypes.object),
  unit: PropTypes.string,
  tenancyPeriod: PropTypes.string,
  rent: PropTypes.string,
  options: PropTypes.arrayOf(PropTypes.object),
  updateDate: PropTypes.string,
  mongoId: PropTypes.string,
  stockId: PropTypes.number,
};

export default withStyles(styles)(injectIntl(TenancyRecordBox));
