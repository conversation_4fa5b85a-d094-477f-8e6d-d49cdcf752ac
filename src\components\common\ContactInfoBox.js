import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import _ from "lodash";
import { withStyles } from "@material-ui/core/styles";
import SearchIcon from "@material-ui/icons/Search";
import FieldValArrBox from "./FieldValArrBox";
import Grid from "@material-ui/core/Grid";
import { injectIntl } from "react-intl";
import CallItem from "./CallItem";
import FormButtonInline from "./FormButtonInline";
import {
  goToSearchResult,
  consolidateType,
  parseNameWithBr,
  parseNameWithDash,
  getLangKey,
  getReverseLangKey,
} from "../../helper/generalHelper";
import { clearStock } from "../../actions/stock";
import { clearStockList } from "../../actions/stocklist";
import { enableConsolidLandSearch } from "../../config";

// We can inject some CSS into the DOM.
const styles = {
  root: {},
  phonesContainer: {
    paddingTop: 8,
  },
  contact: {
    display: "flex",
    alignItems: "center",
    "& > *:nth-child(2)": {
      marginLeft: "2vw",
      flex: "0 0 auto",
    },
  },
  bottomLine: {
    "&:not(:last-child)": {
      borderBottom: "1px solid #777",
    }
  }
};

function ContactInfoBox(props) {
  const {
    classes,
    contact,
    mongoId,
    stockId,
    clearStockDetail,
    clearStockList,
    deniedCalls,
    intl,
    ...others
  } = props;
  const langKey = getLangKey(intl);
  const reverseLangKey = getReverseLangKey(intl);

  const persons = contact?.contactsPersons || [];
  const companies = contact?.contactsCompanies || [];
  const allPhones = [];
  persons.forEach(v => allPhones.push(...(v?.contact?.phones || [])));
  companies.forEach(v => allPhones.push(...(v?.contact?.phones || [])));

  const query = {
    contactsCompany: companies
      .map((v) => ({
        value: v._id,
        label: parseNameWithDash(v.nameEn, v.nameZh, intl),
      }))
      .filter((v) => v.value && v.label)
      .map((v) => v.value),
    contactsPerson: persons
      .map(v => v[langKey] || v[reverseLangKey])
      .filter((v) => v && v !== "false"),
    contactsPhone: _.uniqBy(allPhones, "number").map((v) => v.number),
    limit: 50,
    offset: 0,
    ...consolidateType,
  };
  const selectedData = {
    contactsCompany: companies
      .map((v) => ({
        value: v._id,
        label: parseNameWithDash(v.nameEn, v.nameZh, intl),
      }))
      .filter((v) => v.value && v.label),
    contactsPerson: persons
      .map(v => v[langKey] || v[reverseLangKey])
      .filter((v) => v && v !== "false")
      .map((v) => ({
        value: v,
        label: v,
      })),
    contactsPhone: _.uniqBy(allPhones, "number").map((v) => ({
      value: v.number,
      label: v.number,
    })),
  };
  const goToSearchContactResult = () => {
    // console.log(query)
    // console.log(selectedData)
    clearStockDetail();
    clearStockList(false);
    goToSearchResult(query, selectedData, true, "consolidate");
  };

  const consolidateSearchBtn = enableConsolidLandSearch == "true" &&
    (query.contactsCompany.length > 0 ||
      query.contactsPerson.length > 0 ||
      query.contactsPhone.length > 0) && (
      <FormButtonInline onClick={goToSearchContactResult} icon={<SearchIcon />}>
        {intl.formatMessage({
          id: "stock.consosearch",
        })}
      </FormButtonInline>
    );

  const items = [
    {
      field: "",
      val: "",
      extra: consolidateSearchBtn,
    },
  ];

  const titleMapping = {
    Mr: intl.formatMessage({ id: "contact.title.mr" }),
    Ms: intl.formatMessage({ id: "contact.title.ms" }),
    Mrs: intl.formatMessage({ id: "contact.title.mrs" }),
    Miss: intl.formatMessage({ id: "contact.title.miss" }),
    Dr: intl.formatMessage({ id: "contact.title.dr" }),
  };

  persons.forEach(person => {
    const contactName = person[langKey] || person[reverseLangKey];
    const contactTitle = titleMapping[person.title];
    const phones = person.contact?.phones || [];
    let contactNameFull =
      contactTitle && contactName
        ? intl.locale === "zh"
          ? `${contactName} ${contactTitle}`
          : `${contactTitle} ${contactName}`
        : contactName || "---";
    if (contactName === "false") contactNameFull = "---";
    let CallItems = phones.length > 0 && (
      <Grid container spacing={1} className={classes.phonesContainer}>
        {phones.map((v, i) => (
          <Grid item xs={12} key={i}>
            <CallItem
              type={v.type}
              number={v.number}
              mongoId={mongoId}
              stockId={stockId}
              isDeniedCall={_.get(deniedCalls, v.number) || false}
              phone={v}
            />
          </Grid>
        ))}
      </Grid>
    );

    items.push(
      {
        field: intl.formatMessage({ id: "stock.contact" }),
        val: contactNameFull,
        extra: CallItems,
      },
      {
        field: intl.formatMessage({ id: "stock.remarks" }),
        val: person.remarks || "---",
        className: classes.bottomLine,
      }
    );
  });

  companies.forEach(company => {
    const phones = company.contact?.phones || [];
    let CallItems = phones.length > 0 && (
      <Grid container spacing={1} className={classes.phonesContainer}>
        {phones.map((v, i) => (
          <Grid item xs={12} key={i}>
            <CallItem
              type={v.type}
              number={v.number}
              mongoId={mongoId}
              stockId={stockId}
              isDeniedCall={_.get(deniedCalls, v.number) || false}
            />
          </Grid>
        ))}
      </Grid>
    );

    items.push(
      {
        field: intl.formatMessage({ id: "stock.company" }),
        val: parseNameWithBr(company.nameEn, company.nameZh, intl),
        extra: CallItems,
      },
      {
        field: intl.formatMessage({ id: "stock.remarks" }),
        val: company.remarks || "---",
        className: classes.bottomLine,
      }
    );
  });

  return <FieldValArrBox className={classes.root} items={items} {...others} />;
}

ContactInfoBox.propTypes = {
  classes: PropTypes.object.isRequired,
  contact: PropTypes.object,
  mongoId: PropTypes.string,
  stockId: PropTypes.number,
  deniedCalls: PropTypes.object,
};

const mapDispatchToProps = (dispatch) => {
  return {
    clearStockDetail: () => dispatch(clearStock()),
    clearStockList: (...args) => dispatch(clearStockList(...args)),
  };
};

export default connect(
  null,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(ContactInfoBox)));
