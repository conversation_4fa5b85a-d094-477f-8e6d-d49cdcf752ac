/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import Layout from "../../../../components/Layout";

const title = "Stock Detail";

async function action({ store, params, query }) {
  const { auth } = store.getState();
  if (!auth.user) {
    return { redirect: "/login" };
  } else if (auth.user.authorized == false) {
    return { redirect: "/login" };
  }

  const Stock = await require.ensure(
    [],
    require => require("./Stock").default,
    "stock"
  );

  let defaultTab = parseInt(query.defaultTab);
  if (isNaN(defaultTab) || defaultTab < 0 || defaultTab > 2)
    defaultTab = 0;

  let headerRef = React.createRef();

  return {
    chunks: ["stock"],
    title,
    component: (
      <Layout headerRef={headerRef} backToListStep={1}>
        <Stock stockid={params} headerRef={headerRef} defaultTab={defaultTab} />
      </Layout>
    )
  };
}

export default action;
