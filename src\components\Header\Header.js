/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import { withStyles } from "@material-ui/styles";

import clsx from "clsx";
import { style } from "./style.js";
import Link from "../Link";
/*
import logoUrl from "./logo-small.png";
import logoUrl2x from "./<EMAIL>";
*/
import RightButton from "./RightButton";
import SearchIcon from "@material-ui/icons/Search";
import HomeWorkRoundedIcon from "@material-ui/icons/HomeWorkRounded";
import CloseIcon from "@material-ui/icons/Close";
import ListIcon from "@material-ui/icons/List";
import SignOutButton from "./SignOutButton";
import MitaClubButton from "./MitaClubButton.js";
import { getDefaultSearchResultURL } from "../../helper/generalHelper";
import { FormattedMessage } from "react-intl";
import { PERMISSIONS } from "@/constants/auth";
import history from "../../core/history";
import { connect } from "react-redux";
import { updateHeader, updateHeaderTitle, updateHeaderSubTitle } from "@/actions/header";
import { addActivityLog } from "@/actions/log";
import { logout } from "@/actions/auth";
import _ from "lodash";

const { VIEW_MITA_CLUB_INFO } = PERMISSIONS;
class Header extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      // title: "MSearch 搵盤易",
      // subTitle: "",
      expanded: false,
      // isAdvanced: false
    };
  }

  changeHeader = (v) => {
    this.props.updateHeader(v);
  }

  changeTitle = (v) => {
    // this.setState({ title: v });
    this.props.updateHeaderTitle(v);
  };

  changeSubTitle = (v) => {
    // this.setState({ subTitle: v });
    this.props.updateHeaderSubTitle(v);
  };

  changeIcon = (v) => {
    this.setState({ isAdvanced: !this.state.isAdvanced });
  };

  goBack = (step) => () => {
    if (!isNaN(parseInt(step))) history.go(-parseInt(step));
  };

  handleAddActivityLog = async (functionName, actionName) => {
    await this.props.addActivityLog(functionName, actionName);
  }

  handleLogout = async (e) => {
    e.preventDefault();
    await this.handleAddActivityLog("header.home.logout", "logout")
    await this.props.logout();
  }

  render() {
    // let { title, subTitle = "" } = this.state;
    const {
      classes,
      header,
      hideHomeIcon,
      hideSearchIcon,
      toggleContent,
      isHome,
      backToListStep,
      isSticky,
      isExpanded,
      hideMoreAction,
      multipleStocks,
      title,
      subTitle,
    } = this.props;
    const isAdvanced = this.props.isAdvanced ? this.props.isAdvanced : false;

    // if (!title) title = "MSearch 搵盤易";

    const defaultSearchResultURL = getDefaultSearchResultURL();

    return (
      <div className={clsx(classes.root, isSticky && classes.sticky)}>
        <div className={classes.container}>
          <Link to="/" className={hideHomeIcon ? classes.hidden : ""}>
            <RightButton>
              <HomeWorkRoundedIcon />
            </RightButton>
          </Link>

          {subTitle ? (
            <div className={classes.titleContainer}>
              {multipleStocks ? (
                <div className={classes.brandTxt}>
                  <FormattedMessage id="proposal.listProposal.create" />
                </div>
              ) : (
                <>
                  <div className={classes.brandTxt} id={header ? header : title}>
                    {header ? header : title}
                  </div>
                  <div className={classes.subTitle} id={"stockId"}>
                    {subTitle}
                  </div>
                </>
              )}
            </div>
          ) : (
            <span
              className={isHome ? classes.welcomebrandTxt : classes.brandTxt}
              id={header ? header : title}
            >
              {header ? header : title}
            </span>
          )}

          {!hideMoreAction && (
            <>
              {isAdvanced && !isExpanded ? (
                <RightButton className={hideSearchIcon ? classes.hidden : ""}>
                  <SearchIcon onClick={toggleContent} />
                </RightButton>
              ) : backToListStep ? (
                <RightButton>
                  <ListIcon onClick={this.goBack(backToListStep)} />
                </RightButton>
              ) : !isAdvanced ? (
                <Link
                  to={defaultSearchResultURL}
                  className={hideSearchIcon ? classes.hidden : ""}
                >
                  <RightButton>
                    <SearchIcon
                      onClick={this.changeIcon}
                      className={hideSearchIcon ? classes.hidden : ""}
                    />
                  </RightButton>
                </Link>
              ) : (
                <RightButton className={hideSearchIcon ? classes.hidden : ""}>
                  <CloseIcon onClick={toggleContent} />
                </RightButton>
              )}
            </>
          )}
          {isHome && (
            <div className={classes.logout} style={{ display: "flex", gap: "3vw" }}>
              {this.props.VIEW_MITA_CLUB_INFO && <Link to="/mita-club" onClick={() => {
                this.handleAddActivityLog("header.home.mitaClub", "read");
              }}>
                <MitaClubButton className={classes.mitaClub}>
                  <FormattedMessage id="home.mitaClub" />
                </MitaClubButton>
              </Link>}
              <div>
                <a className={classes.link} onClick={this.handleLogout}>
                  <SignOutButton className={classes.logoutbtn}>
                    <FormattedMessage id="home.signout" />
                  </SignOutButton>
                </a>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }
}

const StyledHeader = React.forwardRef((props, ref) => {
  const Styled = () => <Header ref={ref} {...props} />;

  return <Styled />;
});

const mapStateToProps = (state) => ({
  // employee: _.get(state, "employee.employees.0"),
  title: _.get(state, "header.title", ""),
  subTitle: _.get(state, "header.subTitle", ""),
  VIEW_MITA_CLUB_INFO: _.get(state, `employee.permissions[${VIEW_MITA_CLUB_INFO}]`),
});

const mapDispatchToProps = (dispatch) => {
  return {
    updateHeader: (...args) => dispatch(updateHeader(...args)),
    updateHeaderTitle: (...args) => dispatch(updateHeaderTitle(...args)),
    updateHeaderSubTitle: (...args) => dispatch(updateHeaderSubTitle(...args)),
    addActivityLog: (...args) => dispatch(addActivityLog(...args)),
    logout: (...args) => dispatch(logout(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
  null,
  { forwardRef: true }
)(withStyles(style)(StyledHeader));
