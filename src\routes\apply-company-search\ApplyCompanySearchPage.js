import React, { useEffect, useMemo } from "react";
import PropTypes from "prop-types";
import { FormattedMessage, injectIntl } from "react-intl";
import { connect } from "react-redux";
import { makeStyles } from "@material-ui/core/styles";

import Layout from "@/components/Layout/Layout";
import ApplyCompanySearch from "@/components/Company/ApplyCompanySearch";


function ApplyCompanySearchPage({
  queryFromUri,
  intl,
}) {
  return (
    <Layout
      header={<FormattedMessage id="home.companySearch" />}
      hideMoreAction
      isSticky
    >
      <ApplyCompanySearch isListApply/>
    </Layout>
  );
}

ApplyCompanySearchPage.propTypes = {
  queryFromUri: PropTypes.object.isRequired,

  intl: PropTypes.object.isRequired,
};


const mapStateToProps = (state) => ({
});

export default connect(
  mapStateToProps,
)(injectIntl(ApplyCompanySearchPage));
