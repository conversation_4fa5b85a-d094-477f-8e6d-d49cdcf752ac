import {
  LIST_TRANSACTIONS_START,
  LIST_MORE_TRANSACTIONS_START,
  LIST_TRANSACTIONS_SUCCESS,
  LIST_TRANSACTIONS_NULL_SUCCESS,
  LIST_TRANSACTIONS_ERROR,
  CLEAR_TRANSACTIONS
} from "../constants/transaction";
import { addActivityLog } from "./log";
import _ from "lodash";

export function listTransactions(variables, isFetchingMore, selectedData) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest }
  ) => {
    variables = _.defaults(variables, { isDeleted: false });

    if (isFetchingMore) {
      dispatch({
        type: LIST_MORE_TRANSACTIONS_START,
        payload: {
          variables,
          selectedData
        },
        checkrefreshToken: true
      });
    } else {
      dispatch({
        type: LIST_TRANSACTIONS_START,
        payload: {
          variables,
          selectedData
        },
        checkrefreshToken: true
      });
    }

    dispatch(addActivityLog("transaction.search", "read", {
      ...variables
    }));

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          "Authorization": getState().auth.user.oauth,
          "CAS-Authorization":  localStorage.getItem('casAccessToken')
        }//getState().auth.user.casAccessToken }
      };

      const query = await getQuery("LIST_TRANSACTIONS_QUERY");

      const resp = await universalRequest("/transaction/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: variables,
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (resp.errors) {
        throw new Error(resp.errors[0].message);
      }

      const { data } = resp;

      if (data.transactions && data.transactions.length > 0) {
        dispatch({
          type: LIST_TRANSACTIONS_SUCCESS,
          payload: {
            data
          }
        });
      } else {
        dispatch({
          type: LIST_TRANSACTIONS_NULL_SUCCESS,
          payload: {
            data
          }
        });
      }
    } catch (error) {
      dispatch({
        type: LIST_TRANSACTIONS_ERROR,
        payload: {
          error
        }
      });
      // throw new Error(error);
    }
  };
}

export function clearTransactions() {
  return async dispatch => {
    dispatch({
      type: CLEAR_TRANSACTIONS
    });
  };
}
