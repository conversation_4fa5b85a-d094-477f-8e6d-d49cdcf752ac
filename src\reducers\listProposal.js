import {
  CLEAR_CREATE_LIST_PROPOSAL,
  CREATE_LIST_PROPOSAL_ERROR,
  CREATE_LIST_PROPOSAL_START,
  CREATE_LIST_PROPOSAL_SUCCESS,
} from "../constants/listProposal";

const initialState = {
  creatingListProposal: false,
  createdListProposal: false,
  listing: false,
  listed: false,
};

export default function listProposalReducer(state = initialState, action) {
  switch (action.type) {
    case CREATE_LIST_PROPOSAL_START:
      return {
        ...state,
        creatingListProposal: true,
        createdListProposal: false,
        listProposal: action.payload.listProposal,
      };

    case CREATE_LIST_PROPOSAL_SUCCESS:
      return {
        ...state,
        creatingListProposal: false,
        createdListProposal: true,
        listProposal: action.payload.data.createListProposal,
      };
    case CREATE_LIST_PROPOSAL_ERROR:
      return {
        ...state,
        creatingListProposal: false,
        createdListProposal: false,
        createListProposalError: action.payload.error,
      };
    case CLEAR_CREATE_LIST_PROPOSAL:
      return {
        ...state,
        creatingListProposal: false,
        createdListProposal: false,
        createListProposalError: null,
      };
    default:
      return state;
  }
}
