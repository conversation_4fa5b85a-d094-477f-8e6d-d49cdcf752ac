import React from 'react';
import PropTypes from "prop-types";
import { withStyles } from '@material-ui/core/styles';
import MuiDialogActions from '@material-ui/core/DialogActions';
import DialogButton from "./DialogButton";

const DialogActions = withStyles(theme => ({
  root: {
    margin: 0,
    padding: "2vh 0",
    display: "flex",
    justifyContent: "center"
  },
}))(MuiDialogActions);

const styles = {
  buttonBack: {
    minWidth: 130,
    border: "none",
  },
  buttonMain: {
    minWidth: 130,
  }
};

function DialogFrame(props) {
  const { classes, children, buttonBack, buttonMain, handleBack, handleMain, buttonBackProps, buttonMainProps } = props;

  return (
    <>
      {children}
      {(buttonBack || buttonMain) && <DialogActions>
        {buttonBack && <DialogButton className={classes.buttonBack} onClick={handleBack} {...buttonBackProps}>
          {buttonBack}
        </DialogButton>}
        {buttonMain && <DialogButton className={classes.buttonMain} onClick={handleMain} {...buttonMainProps}>
          {buttonMain}
        </DialogButton>}
      </DialogActions>}
    </>
  );
}

DialogFrame.propTypes = {
  children: PropTypes.node,
  classes: PropTypes.object.isRequired,
  buttonBack: PropTypes.node,
  buttonMain: PropTypes.node,
  handleBack: PropTypes.func,
  handleMain: PropTypes.func,
  buttonMainProps: PropTypes.object,
  buttonBackProps: PropTypes.object,
};

export default withStyles(styles)(DialogFrame);
