import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import ContactInfoBox from "./ContactInfoBox";
import { injectIntl } from "react-intl";
import { getLangKey, convertNewlineToBr } from "../../../../../helper/generalHelper";

const styles = theme => ({
  root: {
    padding: "1vh 0"
  },
  notFound: {}
});

class ContactInfo extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langKey = getLangKey(intl);

    const mongoId = detail._id ? detail._id : null;
    const stockId =
      detail.unicorn && Number.isInteger(detail.unicorn.id)
        ? detail.unicorn.id
        : null;

    const contact = detail.contact || {};
    let companies = contact.companies || [];
    let companyIds = companies.map(v => v.companyId);
    const remarks = contact.remarks ? convertNewlineToBr(contact.remarks) : "---";
    const people = contact.person || [];
    let contactInfo = [];
    for (let i = 0; i < people.length; i++) {
      if (!people[i]) continue;

      let contactName = people[i][langKey];
      let contactTitle = people[i].title;
      let phones = people[i] && people[i].phones ? people[i].phones : [];

      contactInfo.push({
        companies,
        companyIds,
        remarks,
        contactName,
        contactTitle,
        phones,
        mongoId,
        stockId,
      });
    }
    if (contactInfo.length === 0) contactInfo = [{}];

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.contact"
          })}
        >
          {contactInfo.map((v, i) => (
            <ContactInfoBox
              {...v}
              key={i}
            />
          ))}
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(ContactInfo));
