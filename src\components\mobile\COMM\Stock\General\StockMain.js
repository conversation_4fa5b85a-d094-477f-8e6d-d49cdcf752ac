import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import DetailBoxTitle from "../../../../common/DetailBoxTitle";
import {
  numberComma,
  getLang<PERSON>ey,
  convertNewlineToBr,
  paresFloorUnit,
} from "../../../../../helper/generalHelper";
import clsx from "clsx";
import { injectIntl } from "react-intl";
import FormButton from "../../../../common/FormButton";
import Link from "../../../../Link";
import PropertyTagBar from "../../../../common/PropertyTagBar";
import NormalAndBottomPrice from "../../../../common/NormalAndBottomPrice";
import InfoIcon from "@material-ui/icons/Info";
import Grid from "@material-ui/core/Grid";
import FieldVal from "../../../../common/FieldVal";
import FieldValHorizontal from "../../../../common/FieldValHorizontal";

const styles = (theme) => ({
  root: {
    // padding: "0 2vw"
  },
  textClass: {
    fontSize: "1.175em",
  },
  nameAndBtn: {
    display: "flex",
    alignItems: "center",
    "& > *:last-child": {
      flex: "0 0 auto",
    },
  },
  right: {
    textAlign: "right",
  },
  twoLevelItem: {
    marginBottom: "1vh",
    "& > *:not(:first-child)": {
      color: "rgba(255, 255, 255, 0.75)",
    },
    padding: "0 4px",
    borderRadius: 4,
    color: "#fff",
  },
  rentItem: {
    backgroundColor: "rgba(0, 197, 197, .75)",
  },
  priceItem: {
    backgroundColor: "rgba(232, 0, 0, .75)",
  },
  greyItem: {
    backgroundColor: "rgba(132, 132, 132, .1)",
  },
  rentItemNoValue: {
    backgroundColor: "rgba(140, 190, 190, 0.2)",
  },
  priceItemNoValue: {
    backgroundColor: "rgba(200, 170, 170, 0.2)",
  },
  sepText: {
    fontSize: "1.4em",
    textAlign: "right",
    "& > *:not(:last-child)": {
      marginRight: "5vw",
    },
  },
  btn: {
    height: 22,
    lineHeight: "22px",
    // fontSize: ".875em",
    padding: "0 10px",
    textTransform: "none",
    background: "#9932CC",
    minWidth: 0,
  },
  btnIcon: {
    width: 16,
    height: 16,
    marginLeft: 4,
  },
  link: {
    textDecoration: "none",
  },
  section: {
    display: 'flex',
    marginBottom: "2vh",
    justifyContent: "space-between",
  },
  areaSmallFont: {
    fontSize: ".75em",
  },
  area: {
    display: "inline-flex",
    flexDirection: "column",
    fontWeight: "bold"
  },
  lmrAlign: {
    paddingLeft: "2vw",
  },
  commentBox: {
    padding: "8px",
    marginLeft: "-8px",
    borderRadius: "4px",
    backgroundColor: "rgba(255, 255, 100, .4)",
    lineHeight: "1.5",
  },
  updateDateFields: {
    padding: "0 2vw",
    margin: "5vh 0 3vh",
    textAlign: "right",
  },
  text1: {
    width: "100%",
    display: 'flex',
    justifyContent: 'space-between',
  }
});

class StockDetail extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
    btnController: PropTypes.node
  };

  render() {
    const { classes, detail, intl, btnController } = this.props;
    const langKey = getLangKey(intl);

    const statusName =
    detail.status && detail.status[langKey]
      ? detail.status[langKey]
      : "---";

    const status = detail.status || null;
    let priceContainer, leaseContainer;
    switch (status) {
      case "Leased":
        priceContainer = classes.greyItem;
        leaseContainer = classes.greyItem;
        break;
      case "Selfuse":
        priceContainer = classes.greyItem;
        leaseContainer = classes.greyItem;
        break;
      case "Pending":
        priceContainer = classes.greyItem;
        leaseContainer = classes.greyItem;
        break;
      case "Search":
        priceContainer = classes.greyItem;
        leaseContainer = classes.greyItem;
        break;
      case "Tenanted":
        priceContainer = classes.priceItem;
        leaseContainer = classes.greyItem;
        break;
      case "Sold":
        priceContainer = classes.greyItem;
        leaseContainer = classes.greyItem;
        break;
      case "Lease":
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Sale":
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Sale+Lease":
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      default:
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
    }

    const hasPrice =
      detail.askingPrice &&
      (detail.askingPrice.total || detail.askingPrice.average);
    const hasRent =
      detail.askingRent &&
      (detail.askingRent.total || detail.askingRent.average);

    const unit = detail.unit || "---";
    const floor = detail.floor || "---";
    const buildingName =
      detail.building && detail.building[langKey]
        ? detail.building[langKey]
        : "---";
    const streetNo =
      detail.building && detail.building.street && detail.building.street.number
        ? detail.building.street.number + " "
        : "";
    const streetName =
      detail.building &&
      detail.building.street &&
      detail.building.street.street &&
      detail.building.street.street.nameEn
        ? detail.building.street.street.nameEn
        : "---";
    const district =
      detail.building &&
      detail.building.district &&
      detail.building.district.nameEn
        ? detail.building.district.nameEn
        : "---";
    const buildingId =
      detail.building && detail.building._id ? detail.building._id : null;
    const buildingunicornId =
      detail.building && detail.building.unicorn && detail.building.unicorn.id
        ? detail.building.unicorn.id
        : null;
    let sizeWithTypes = detail?.area?.sizes?.map(size => {
      if (size.value) {
        const type = size.type.toLowerCase();
        const message = intl.formatMessage({ id: `stock.area.${type}` });
        const formattedValue = numberComma(size.value);
        return intl.locale === "zh" ? `${message} ${formattedValue}` : `${formattedValue} ${message}`;
      }
      return null;
    }) ?? null;
    const sizeProving =
      detail.area && detail.area.sizeProving === true ? "S.P." : null;
    const isSubDivide = detail.isSubDivide === true ? "Sub-D" : null;
    // const size = (detail.area && detail.area.size) ? <NumberFormat value={detail.area.size} displayType={'text'} thousandSeparator={true} suffix={' /G'} /> : "---";
    let sizeNet = null;
    if (
      detail.area &&
      detail.area.sizes &&
      detail.area.sizes.filter((v) => v.type === "NET").length > 0
    )
      sizeNet = numberComma(
        detail.area.sizes.filter((v) => v.type === "NET")[0].value
      );
    let efficiency =
      detail.area && detail.area.efficiency
        ? (Math.floor(detail.area.efficiency * 100) / 100).toFixed(2) + "%"
        : null;

    sizeNet = sizeNet
      ? intl.locale == "zh"
        ? (sizeNet =
          intl.formatMessage({ id: "stock.area.netLong" }) + " " + sizeNet)
        : (sizeNet =
          sizeNet + " " + intl.formatMessage({ id: "stock.area.netLong" }))
      : null;

    efficiency = efficiency
      ? intl.locale == "zh"
        ? (efficiency =
            intl.formatMessage({ id: "stock.area.effcy" }) + " " + efficiency)
        : (efficiency =
            efficiency + " " + intl.formatMessage({ id: "stock.area.effcy" }))
      : null;

    const remarksQuick =
      detail.remarks && detail.remarks.internal
        ? convertNewlineToBr(detail.remarks.internal)
        : "---";
    const createDate =
      detail.recordOperation && detail.recordOperation.createDate
        ? detail.recordOperation.createDate
        : "---";
    const lastUpdateDate =
      detail.recordOperation && detail.recordOperation.lastUpdateDate
        ? detail.recordOperation.lastUpdateDate
        : "---";

    const buildingInfoBtn = buildingId && (
      <Link className={classes.link} to={"/building/" + buildingId}>
        <FormButton
          className={classes.btn}
          onClick={() => {
            window.dataLayer.push({ buildingId: buildingunicornId });
          }}
        >
          {intl.formatMessage({
            id: "stock.building",
          })}
          <InfoIcon className={classes.btnIcon} />
        </FormButton>
      </Link>
    );

    return (
      <div className={classes.root}>
        <DetailBoxTitle
          text={
            <div className={classes.text1}>
              <div>{paresFloorUnit(floor, unit, intl)}</div>
              <div>{btnController}</div>
            </div>
          }
          text2={
            <div className={classes.nameAndBtn}>
              <div>{buildingName}</div>
            </div>
          }
          textClass={classes.textClass}
          subtitle2={
            <div className={classes.text1}>
              <div style={{lineHeight: '2em'}}>{streetNo + streetName + ", " + district}</div>
              <div style={{fontSize: "1.4em", fontWeight: 'bold'}}>{statusName}</div>
            </div>
            }
        >
          <div className={classes.section}>
            <div style={{paddingTop: "5px"}}>{buildingInfoBtn}</div>
            <div className={classes.sepText}>
              {sizeNet && (
                <span className={classes.areaSmallFont}>{sizeNet}</span>
              )}
              {sizeProving && (
                <span className={classes.areaSmallFont}>{sizeProving}</span>
              )}
              {efficiency && (
                <span className={classes.areaSmallFont}>{efficiency}</span>
              )}
              {sizeWithTypes && (<span className={classes.area}>
                {sizeWithTypes.map(size => size && (<span>{size}</span>))}
              </span>)}
              {isSubDivide && <span>{isSubDivide}</span>}
            </div>
          </div>

          <div className={classes.right}>
            <div
              className={clsx(
                classes.twoLevelItem,
                leaseContainer,
                !hasRent && classes.rentItemNoValue
              )}
            >
              <NormalAndBottomPrice
                data={detail.askingRent}
                avgDecimal={2}
                label="Rent"
              />
            </div>

            <div
              className={clsx(
                classes.twoLevelItem,
                priceContainer,
                !hasPrice && classes.priceItemNoValue
              )}
            >
              <NormalAndBottomPrice data={detail.askingPrice} label="Price" />
            </div>
          </div>

          <div className={classes.section}>
            <PropertyTagBar detail={detail} />
          </div>

          <Grid container spacing={2} className={classes.lmrAlign}>
            <Grid item xs={12}>
              <FieldVal
                field={intl.formatMessage({
                  id: "stock.comment",
                })}
              />
              <FieldVal className={classes.commentBox}>{remarksQuick}</FieldVal>
            </Grid>
          </Grid>

          <Grid container className={classes.updateDateFields}>
            <Grid item xs={12}>
              <FieldValHorizontal
                field={intl.formatMessage({ id: "stock.stockcreate" })}
              >
                {createDate}
              </FieldValHorizontal>
            </Grid>
            <Grid item xs={12}>
              <FieldValHorizontal
                field={intl.formatMessage({ id: "stock.stockupdate" })}
              >
                {lastUpdateDate}
              </FieldValHorizontal>
            </Grid>
          </Grid>
        </DetailBoxTitle>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(StockDetail));
