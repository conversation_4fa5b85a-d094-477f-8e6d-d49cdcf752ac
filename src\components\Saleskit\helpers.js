import _ from "lodash";
import moment from "moment";
import langFile from "../../lang/proposal/message";

import { sbu } from "../../config";
import { checkExpiredDate, paresFloor } from "../../helper/generalHelper";

export const getStockStatus = status => {
  const statusMap = {
    "Sale": "Sale",
    "Lease": "Lease",
    "Sale+Lease": "SaleAndLease",
    "Tenanted": "Sale",
    "Surrend": "Lease"
  };
  
  return statusMap[status] || "SaleAndLease";
};

export const getNotNullIsShow = (isShow, defaultValue = false) => {
  if (isShow === null || isShow === undefined) {
    return defaultValue;
  }
  return isShow;
};

export const hasIsShowTrue = (obj) => {
  for (let key in obj) {
    if (key.endsWith('IsShow') && obj[key]) {
      return true;
    } else if (typeof obj[key] === 'object') {
      if (hasIsShowTrue(obj[key])) {
        return true;
      }
    }
  }
  return false;
};

export const getICSCode = () => {
  let ics;
  switch (sbu) {
    case "COMM":
      ics = "O";
      break;
    case "SHOPS":
      ics = "S";
      break;
    case "IND":
      ics = "I";
      break;
    default:
      ics = "";
      break;
  }
  return ics;
}

export const getDefaultRemarks = (type) => {
  let sbuStrEn = "";
  let sbuStrZh = "";
  let rentCommissionZh = "半個月";
  let rentCommissionEn = "0.5 month's";
  // let extraRemarksZh = "全包=管理費、地租、差餉。";
  // let extraRemarksEn = "All Included = Management Fee, Rates and G Rent.";
  switch (sbu) {
    case "COMM":
      sbuStrZh = "商業";
      sbuStrEn = "(Comm.)";
      break;
    case "IND":
      sbuStrZh = "工商";
      sbuStrEn = "(Comm. & Ind.)";
      rentCommissionZh = "一個月";
      rentCommissionEn = "1 month's";
      break;
    case "SHOPS":
      sbuStrZh = "商舖";
      sbuStrEn = "(Shop)";
      // extraRemarksEn = "";
      // extraRemarksZh = "";
      break;
    default:
  }
  const mapping = {
    Sale: {
      isShow: true,
      nameZh: sbu === 'IND' ? '成功簽約後, 買家需支付樓價之1 %給予本公司作為佣金。' : `買家需支付樓價之1 % 給予美聯物業代理(${sbuStrZh})有限公司作佣金。`,
      nameEn: sbu === 'IND' ? 'Upon signing a binding agreement, the Purchaser needs to pay an agency fee equivalent to one percent of the transaction price to our company.' : `1% of purchase price shall be paid by the purchaser to Midland Realty ${sbuStrEn} Limited.`,
    },
    Lease: {
      isShow: true,
      nameZh: sbu === 'IND' ? `成功簽約後, 租客需支付${rentCommissionZh}租金給予本公司作為佣金。` : `租客需支付${rentCommissionZh}租金給予美聯物業代理(${sbuStrZh})有限公司作佣金。`,
      nameEn: sbu === 'IND' ? `Upon signing a binding agreement, the Tenant needs to pay an agency fee equivalent to ${rentCommissionEn} rent to our company.` : `${rentCommissionEn} rental shall be paid by the tenant to Midland Realty ${sbuStrEn} Limited.`,
    },
    SaleAndLease: {
      isShow: true,
      nameZh: sbu === 'IND' ? `成功簽約後, 買家需支付樓價之1 %及租客需支付${rentCommissionZh}租金給予本公司作為佣金。` : `買家需支付樓價之1 % 及租客需支付${rentCommissionZh}租金給予美聯物業代理(${sbuStrZh})有限公司作佣金。`,
      nameEn: sbu === 'IND' ? `Upon signing any binding agreement,the Purchaser needs to pay an agency fee equivalent to one percent of the transaction price to our company and the Tenant need to pay an agency fee equivalent to ${rentCommissionEn} rental to our company.` : `1% of purchase price and ${rentCommissionEn} rental shall be paid to Midland Realty ${sbuStrEn} Limited.`,
    },
  };
  return mapping[type] || { nameZh: "", nameEn: "", isShow: true };
};

export const getTypeOptions = (intl) => [
  {
    value: "Sale",
    label: intl.formatMessage({
      id: "proposal.form.forsale",
    }),
  },
  {
    value: "Lease",
    label: intl.formatMessage({
      id: "proposal.form.forlease",
    }),
  },
  {
    value: "SaleAndLease",
    label: intl.formatMessage({
      id: "search.status.salesandlease",
    }),
  },
];

export const getAreaTypeOptions = (intl) => [
  {
    value: "areaGross",
    label: intl.formatMessage({
      id: "stock.area.gross",
    }),
  },
  {
    value: "areaNet",
    label: intl.formatMessage({
      id: "stock.area.net",
    }),
  },
  {
    value: "areaSaleable",
    label: intl.formatMessage({
      id: "stock.area.saleable",
    }),
  },
  {
    value: "areaLettable",
    label: intl.formatMessage({
      id: "stock.area.lettable",
    }),
  },
];

export const getAreaType = (areas) => {
  let areaType = "areaGross";

  if (_.get(areas, "GROSS")) {
    areaType = "areaGross";
  } else if (_.get(areas, "NET")) {
    areaType = "areaNet";
  } else if (_.get(areas, "SALEABLE")) {
    areaType = "areaSaleable";
  } else if (_.get(areas, "LETTABLE")) {
    areaType = "areaLettable";
  }

  return areaType;
}

export const floorTypeLangIdMapping = {
  "Very High": "proposal.form.veryhighfloor",
  "High/Mid": "proposal.form.midhigherfloor",
  "Mid/Low": "proposal.form.midlowerfloor",
  Mid: "proposal.form.midfloor",
  High: "proposal.form.highfloor",
  Low: "proposal.form.lowfloor",
};

const reverseFloorTypeLangIdMapping = Object.entries(floorTypeLangIdMapping)
  .reduce((acc, [key, value]) => {
    acc[langFile.en[value]] = key;
    return acc;
  },
  {}
);

/** 通過數據庫的值恢復得到 form 的原始值 */
export const getOriginalfloorTypeEnValue = (nameEn = "") => reverseFloorTypeLangIdMapping[nameEn] || nameEn;

export const getFloorTypeOptions = (intl, actualFloor) => {
  const options = Object.keys(floorTypeLangIdMapping).map((v) => ({
    value: v,
    label: intl.formatMessage({
      id: floorTypeLangIdMapping[v],
    }),
  }));
  options.unshift({
    value: "Actual Floor",
    label: paresFloor(actualFloor, intl),
  });
  return options;
};

const getBuildingFloorTypes = (floors) =>
  _.reduce(
    floors,
    (floorsObj, { type, name }) => {
      if (!name || !type) return floorsObj;

      const [min, max] = name.split("-");

      if (min && max) {
        return {
          ...floorsObj,
          [type]: {
            ...(floorsObj[type] || {}),
            min: parseInt(min, 10) || null,
            max: parseInt(max, 10) || null,
          },
        };
      }

      return {
        ...floorsObj,
        [type]: {
          ...(floorsObj[type] || {}),
          others: [..._.get(floorsObj, `${type}.others`, []), name],
        },
      };
    },
    {},
  );

export const getStockFloorType = (floors, currFloor) => {
  // not all floor are num, e.g "G", "LB"
  const floor = parseInt(currFloor, 10) || currFloor;
  const isFloorInt = _.isInteger(floor);
  const type = _.reduce(
    Object.entries(getBuildingFloorTypes(floors)),
    (resultStr, typeObj) => {
      // invalid range of floor type
      if (_.isNil(typeObj[1].min) && _.isNil(typeObj[1].max)) return resultStr;

      if (isFloorInt) {
        if (
          _.isNil(typeObj[1].min) &&
          !_.isNil(typeObj[1].max) &&
          floor <= typeObj[1].max
        ) {
          return typeObj[0];
        }

        if (
          !_.isNil(typeObj[1].min) &&
          _.isNil(typeObj[1].max) &&
          floor >= typeObj[1].min
        ) {
          return typeObj[0];
        }
        // floor within the max and min of the floor type
        if (floor >= typeObj[1].min && floor <= typeObj[1].max) {
          return typeObj[0];
        }
      }

      if (_.get(typeObj[1], "others", []).includes(floor)) {
        return typeObj[0];
      }
      return resultStr;
    },
    "---",
  );
  return type;
};

export const titleMapping = {
  unified: {
    nameEn: "Unified",
    nameZh: "統一業權",
  },
  spinoff: {
    nameEn: "Spin-off",
    nameZh: "分散業權",
  },
};

export const yesNoMapping = {
  yes: {
    nameEn: "Yes",
    nameZh: "有",
  },
  no: {
    nameEn: "No",
    nameZh: "無",
  },
};

export const getCompanyTitleOptions = (intl) => [
  {
    value: "midlandici",
    label: intl.formatMessage({
      id: "proposal.form.midlandici",
    }),
  },
  // {
  //   value: "hkpici",
  //   label: intl.formatMessage({
  //     id: "proposal.form.hkp",
  //   }),
  // },
];

export const getStreetTypeOptions = (intl, streets = []) => {
  const options = streets.map((v, i) => ({
    value: i,
    label:
      i === 0
        ? intl.formatMessage({ id: "proposal.form.mainstreet" })
        : `${intl.formatMessage({ id: "proposal.form.substreet" })} ${i}`,
  }));
  return options;
};

export const getLangOptions = (intl) => [
  {
    value: "CHI",
    label: intl.formatMessage({ id: "proposal.general.chinese" }),
  },
  {
    value: "SCHI",
    label: intl.formatMessage({ id: "proposal.general.schinese" }),
  },
  {
    value: "ENG",
    label: intl.formatMessage({ id: "proposal.general.english" }),
  },
  {
    value: "CHI_ENG",
    label: intl.formatMessage({ id: "proposal.general.chiAndEng" }),
  },
  {
    value: "SCHI_ENG",
    label: intl.formatMessage({ id: "proposal.general.schiAndEng" }),
  },
];

export const getFontFamilyOptions = intl => [
  {
    value: "default",
    label: intl.formatMessage({ id: "proposal.form.default" }),
  },
  {
    value: "mingliu",
    label: intl.formatMessage({ id: "proposal.form.mingliu" }),
  },
  {
    value: "kaiti",
    label: intl.formatMessage({ id: "proposal.form.kaiti" }),
  },
  {
    value: "msjh",
    label: intl.formatMessage({ id: "proposal.form.msjh" }),
  },
];


export const getMultiImgOptions = intl => [
  {
    value: "ONE",
    label: "1:1",
  },
  {
    value: "TWO",
    label: "1:2",
  },
  // {
  //   value: "THREE",
  //   label: "1:3",
  // },
  {
    value: "FOUR",
    label: "1:4",
  },
  // {
  //   value: "SIX",
  //   label: "1:6",
  // },
  // {
  //   value: "EIGHT",
  //   label: "1:8",
  // },
];

// return `{field}{locale}` || locale
export const langKey = (locale, field = "name") =>
  field ? `${field}${_.startCase(locale)}` : locale;

export const twoDec = (n) => Math.round((n + Number.EPSILON) * 100) / 100;

export const createOptions = (locale, arr) => [
  {
    value: "",
    label: "---",
  },
  ...arr.map((item) => ({
    value: item.nameEn,
    label: _.get(item, `name${_.startCase(locale)}`),
  })),
];

export const createMultiLangOptions = (locale, arr) => [
  {
    value: "",
    label: "---",
  },
  ...arr.map((item) => ({
    value: {
      nameEn: item.nameEn,
      nameZh: item.nameZh,
    },
    label: _.get(item, `name${_.startCase(locale)}`),
  })),
];

export const findZhEnFromOptions = (options, value) =>
  _.pick(
    options.find((option) => option.nameEn === value) || {
      nameEn: "",
      nameZh: "",
    },
    ["nameZh", "nameEn"],
  );

export const getFloorTypeInfoFromLangFile = (value) => ({
  nameZh:
    value &&
    floorTypeLangIdMapping[value] &&
    langFile.zh[floorTypeLangIdMapping[value]]
      ? langFile.zh[floorTypeLangIdMapping[value]]
      : "",
  nameEn: value === "Actual Floor" ? value :
    value &&
    floorTypeLangIdMapping[value] &&
    langFile.en[floorTypeLangIdMapping[value]]
      ? langFile.en[floorTypeLangIdMapping[value]]
      : ""
});

export const parseMedia = (media, id, multiImg) => {
  const mediaById = media.find(r => r.id === id);
  if (mediaById) {
    const { mediumRoot, photoContent } = mediaById;
    return {
      id,
      mediumRoot,
      multiImg,
      photoContent,
    };
  } else {
    return null;
  }
}

export const parseAddressObj = (stock) => {
  const parseFloor = (floor) => {
    let f = floor.trim();
    while (f.startsWith("0")) f = f.substring(1); // remove leading zeros
    // WB or W/B => 全幢
    if (f === "WB" || f === "W/B") return { zh: "全幢", en: "WB" };
    return { zh: floor, en: floor };
  };

  let unit = _.get(stock, `unit`) || "";
  const building = _.get(stock, `building`);
  let streetNo = _.get(stock, `building.street.number`) || "";
  let street = _.get(stock, `building.street.street`);
  let district = _.get(building, "district");

  let floor;
  if (sbu === "SHOPS") {
    floor = {
      zh: _.get(stock, "floorInChinese") || "",
      en: _.get(stock, "floor") || "",
    };

    if (!district) {
      district = _.get(stock, "district");
    }
    if (!streetNo) {
      streetNo = _.get(stock, "street.number") || "";
    }
    if (!street) {
      street = _.get(stock, "street.street");
    }
  } else {
    floor = parseFloor(_.get(stock, "floor") || "");
  }

  // WF or W/F => 全層
  if (unit === "WF" || unit === "W/F") {
    unit = {
      zh: "全層",
      en: "WF",
    };
  } else {
    unit = {
      zh: unit,
      en: unit,
    };
  }

  return {
    unit,
    floor,
    streetNo: {
      zh: streetNo ? `${streetNo}號` : "",
      en: streetNo || "",
    },
    street: {
      zh: _.get(street, "nameZh") || "",
      en: _.get(street, "nameEn") || "",
    },
    building: {
      zh: _.get(building, "nameZh") || "",
      en: _.get(building, "nameEn") || "",
    },
    district: {
      zh: _.get(district, "nameZh") || "",
      en: _.get(district, "nameEn") || "",
    },
  };
};

const chainAddress = (arr, separator = "") =>
  arr.filter((field) => (field || "").trim()).join(separator);

export const setTitleColor = (stock) => {
  const redColorFont = {
    IND: ['Sale', 'Lease', 'Sale+Lease', 'Tenanted'],
    COMM: ['Sale', 'Lease', 'Sale+Lease', 'Tenanted', 'Surrend'],
    SHOPS: ['Sale', 'Lease', 'Sale+Lease']
  }[sbu] || [];
  return redColorFont.includes(stock?.status?.nameEn || stock?.status) ? {} : {color: "red"};
}

export const getFullAddress = (
  { locale, formatMessage },
  stock,
  floorType = "Actual Floor",
  isUnitShow = true,
  isCustomBuilding = true,
  isShopsTableAddress = false
) => {
  const { unit, floor, streetNo, street, building, district } =
    parseAddressObj(stock);

  if (sbu === "SHOPS") {
    return locale === "zh"
      ? chainAddress(
          [
            ...isShopsTableAddress ? [] : [district.zh],
            street.zh,
            streetNo.zh,
            ...isCustomBuilding ? ([building.zh, building.en]) : [],
            floor.zh,
            ...isUnitShow ? (unit.zh && unit.zh !== "全層" ? [`${unit.zh}號舖`] : [unit.zh]) : [],
          ],
          " ",
        )
      : chainAddress(
          [
            ...isUnitShow ? (unit.en && unit.en !== "WF" && unit.en !== "W/F"
              ? [`Shop No. ${unit.en}`]
              : [unit.en]) : [],
            floor.en && floor.en !== "WB" && floor.en !== "W/B"
              ? `${floor.en}${!floor.en.includes("/F") ? "/F" : ""}`
              : floor.en,
            ...isCustomBuilding ? ([building.en, building.zh]) : [],
            `${streetNo.en} ${street.en}`,
            ...isShopsTableAddress ? [] : [district.en],
          ],
          ", ",
        );
  }

  const isActualFloor = floorType === "Actual Floor";
  if (!isActualFloor) {
    floor[locale] = formatMessage({ id: floorTypeLangIdMapping[floorType] });
  }
  return locale === "zh"
    ? chainAddress(
        [
          // district.zh,
          // street.zh,
          // streetNo.zh,
          building.zh,
          building.en,
          floor.zh && floor.zh !== "全幢" && isActualFloor
            ? `${floor.zh}樓`
            : floor.zh,
          ...isUnitShow ? (unit.zh && unit.zh !== "全層" ? [`${unit.zh}室`] : [unit.zh]) : [],
        ],
        " ",
      )
    : chainAddress(
        [
          ...isUnitShow ? (unit.en && unit.en !== "WF" && unit.en !== "W/F"
            ? [`Unit ${unit.en}`]
            : [unit.en]) : [],
          floor.en && floor.en !== "WB" && floor.en !== "W/B" && isActualFloor
            ? `${floor.en}/F`
            : floor.en,
          building.en,
          building.zh, 
          // `${streetNo.en} ${street.en}`,
          // district.en,
        ],
        ", ",
      );
};

export const getKolAddress = (
  { locale, formatMessage },
  stock,
) => {
  const { unit, floor, streetNo, street, building, district } =
    parseAddressObj(stock);

  if (sbu === "SHOPS") {
    return locale === "zh"
      ? chainAddress(
          [
            district.zh,
            street.zh,
            streetNo.zh,
          ],
          " ",
        )
      : chainAddress(
          [
            `${streetNo.en} ${street.en}`,
            district.en,
          ],
          ", ",
        );
  }

  const floorMap = {
    "High": "高層",
    "Mid": "中層",
    "Low": "低層",
  }
  const floors = _.get(stock, `building.floors`, []);
  const currFloor = _.get(stock, `floor`, "");
  let floorType = getStockFloorType(floors, currFloor).replace("---", "");
  floorType = floorType ? floorMap[floorType] : "";

  return locale === "zh"
    ? chainAddress(
        [
          district.zh,
          // street.zh,
          // streetNo.zh,
          building.zh,
          floorType
        ],
        " ",
      )
    : chainAddress(
        [
          floorType,
          building.en,
          // `${streetNo.en} ${street.en}`,
          district.en,
        ],
        ", ",
      );
};

export const getProposalName = (
  { locale, formatMessage },
  stock,
  floorType,
  isUnitShow = true,
  ppType,
  mode,
  lang,
  type
) => {
  if (sbu === "COMM") {
    const date = moment().format('YYYYMMDD')
    const typeConfig = {
      "Lease": "出租",
      "Sale": "出售",
      "SaleAndLease": "租及售"
    }
    if (ppType !== "OfferLetter") {
      if(lang?.includes("ENG")){
        return `Prime Property Recommendation(${type})${date}`.replace('And', '+')
      } else {
        return `優質商廈推介(${typeConfig[type]})${date}`
      }
    }
  }
  if(!ppType && !mode && !lang) return "default";
  const { unit, floor, streetNo, street, building } = parseAddressObj(stock);
  const title_date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  let formatPPType = '';
  if (ppType === "ListProposal" || mode === "list") {
    formatPPType = `${lang.includes("CHI") ? "多盤建議書" : ""}${lang.includes("ENG") ? "ListPP" : ""}`;
  }
  if (ppType === "Proposal" || mode === "indv") {
    formatPPType = `${lang.includes("CHI") ? "單盤建議書" : ""}${lang.includes("ENG") ? "IndivPP" : ""}`;
  }
  if (ppType === "OfferLetter") {
    formatPPType = `${lang.includes("CHI") ? "還價書" : ""}${lang.includes("ENG") ? "Offer" : ""}`;
  }

  if (sbu === "SHOPS") {
    return `${lang.includes("CHI") ? chainAddress([
          street.zh,
          streetNo.zh,
          floor.zh,
          ...isUnitShow ? (unit.zh && unit.zh !== "全層" ? [`${unit.zh}號舖`] : [unit.zh]) : [],
        ]) : ""}${lang.includes("ENG") ? chainAddress([
            ...isUnitShow ? (unit.en && unit.en !== "WF" && unit.en !== "W/F"
              ? [`Shop no. ${unit.en}`]
              : [unit.en]) : [],
            floor.en && floor.en !== "WB" && floor.en !== "W/B"
              ? `${floor.en}${!floor.en.includes("/F") ? "F" : ""}`
              : floor.en,
            `${streetNo.en} ${street.en}`,
          ],
          ", ",
        ) : ""}_${formatPPType}${title_date}`.replace(/[\s\/:\\*?"<>|%&#]/g, '');
  }

  const isActualFloor = floorType === "Actual Floor";
  if (!isActualFloor) {
    //floor[locale] = formatMessage({ id: floorTypeLangIdMapping[floorType] });
    const temp = {
      "---": {
        zh:"---",
        en:"---"
      },
      "Very High": {
        zh:"極高層",
        en:"Very-High-Floor"
      },
      "High/Mid": {
        zh:"中高層",
        en:"Mid-Higher-Floor"
      },
      "Mid/Low": {
        zh:"中低層",
        en:"Mid-Lower-Floor"
      },
      Mid: {
        zh:"中層",
        en:"Mid-Floor"
      },
      High: {
        zh:"高層",
        en:"High-Floor"
      },
      Low: {
        zh:"低層",
        en:"Low-Floor"
      }
    }
    floor['zh'] = temp[floorType].zh;
    floor['en'] = temp[floorType].en;
  }

  return `${lang.includes("CHI") ? chainAddress([
        building?.zh?.replaceAll(" ", "_"),
        floor.zh && floor.zh !== "全幢" && isActualFloor
          ? `${floor.zh}樓`
          : floor.zh,
        ...isUnitShow ? (unit.zh && unit.zh !== "全層" ? [`${unit.zh}室`] : [unit.zh]) : [],
      ]) : ""}${lang.includes("ENG") ? chainAddress([
          ...isUnitShow ? (unit.en && unit.en !== "WF" && unit.en !== "W/F"
            ? [`Unit${unit.en}`]
            : [unit.en]) : [],
          floor.en && floor.en !== "WB" && floor.en !== "W/B" && isActualFloor
            ? `${floor.en}F`
            : floor.en,
          building?.en?.replaceAll(" ", "_") || "",
        ],
        ", ",
      ) : ""}_${formatPPType}${title_date}`.replace(/[\s\/:\\*?"<>|%&#]/g, '');
};

export const isCoftloft = (areaName) =>
  areaName === "閣樓" || areaName === "自建閣";
export const calcShopsTotalArea = (areas) => ({
  gross: _.sumBy(
    _.filter(areas, (area) => !isCoftloft(area.areaName) && area.grossIsShow),
    "gross",
  ),
  net: _.sumBy(
    _.filter(areas, (area) => !isCoftloft(area.areaName) && area.netIsShow),
    "net",
  ),
});

export function getProposalTenancyDesc(record) {
  const {
    isIncludeGovernmentRent,
    isIncludeRates,
    isIncludeAirConditioning,
    isIncludeManagementFee,
  } = record;

  const fieldChecker = [
    {
      value: isIncludeGovernmentRent,
      nameZh: "差餉",
      nameEn: "G Rent",
    },
    {
      value: isIncludeRates,
      nameZh: "地租",
      nameEn: "rates",
    },
    {
      value: isIncludeAirConditioning,
      nameZh: "冷氣費",
      nameEn: "A/C Fee",
    },
    {
      value: isIncludeManagementFee,
      nameZh: "管理費",
      nameEn: "Management Fee",
    },
  ];

  const includedFeeZhEn = fieldChecker.reduce(
    (tenancyZhEn, { value, nameZh, nameEn }) => {
      if (!value) {
        return {
          ...tenancyZhEn,
          notIncluded: {
            nameZh: [...tenancyZhEn.notIncluded.nameZh, nameZh],
            nameEn: [...tenancyZhEn.notIncluded.nameEn, nameEn],
          },
        };
      }
      return {
        ...tenancyZhEn,
        included: {
          nameZh: [...tenancyZhEn.included.nameZh, nameZh],
          nameEn: [...tenancyZhEn.included.nameEn, nameEn],
        },
      };
    },
    {
      included: { nameZh: [], nameEn: [] },
      notIncluded: { nameZh: [], nameEn: [] },
    },
  );

  const notIncludedText =
    includedFeeZhEn.notIncluded.nameEn.length === 0
      ? null
      : {
          nameZh: `不包括${includedFeeZhEn.notIncluded.nameZh.join("，")}`,
          nameEn: `Not include ${includedFeeZhEn.notIncluded.nameEn.join(
            ", ",
          )}`,
        };

  const includedText =
    includedFeeZhEn.included.nameEn.length === 0
      ? null
      : {
          nameZh: `包括${includedFeeZhEn.included.nameZh.join("，")}`,
          nameEn: `Include ${includedFeeZhEn.included.nameEn.join(", ")}`,
        };

  return {
    nameZh: [includedText, notIncludedText]
      .filter((text) => text)
      .map((text) => text.nameZh)
      .join("。"),
    nameEn: [includedText, notIncludedText]
      .filter((text) => text)
      .map((text) => text.nameEn)
      .join(". "),
  };
}

export const getContainers = (s) => {
  const entrances = s?.building?.entrances || [];
  if (entrances.length === 0) {
    return [{
      nameEn: "",
      nameZh: "",
      haveLoadingBay: false
    }];
  }
  const containers = [];
  for (let i = 0; i < entrances.length; i++) {
    let containerDataEn = entrances?.[i]?.container?.nameEn || "";
    let containerDataZh = entrances?.[i]?.container?.nameZh || "";

    // if (entrances[i].haveLoadingBay) {
    //   if (containerDataEn) containerDataEn += " Loading Bay";
    //   if (containerDataZh) containerDataZh += " 貨台";
    // }
    containers.push({
      nameEn: containerDataEn,
      nameZh: containerDataZh,
      haveLoadingBay: entrances?.[i]?.haveLoadingBay || false
    });
  }
  return containers;
};

export const checkIfSoleagent = (wwwDetails) => {
  const saData = _.find(
    _.get(wwwDetails, "categories") || [],
    (item) => _.get(item, "category") === "soleagent",
  );

  if (saData) {
    const { isEffective, startDate, endDate } = saData;
    // if end date is missing, return false directly
    if (!endDate) return false;

    return isEffective && checkExpiredDate(startDate, endDate);
  }
  return false;
};

export const parseTenancyRecords = (tenancy, intl, tenancyStatus = "Current") => {
  const tenancyStatuses = _.filter(
    tenancy,
    (rec) => rec.status === tenancyStatus && !rec.deleted,
  );

  const lang = langKey(intl.locale, "name");

  const records = tenancyStatuses.reduce((list, item) => {
    let tenantPerson = null;
    let tenantCompany = null;
    let tenantShop = null;
    if (sbu !== "SHOPS") {
      tenantPerson = _.get(_.head(_.get(item, "tenants", [])), "contactsPerson");
      tenantCompany = _.head(_.get(_.head(_.get(item, "tenants", [])), "contactsCompanies", []));
      //if (!tenantPerson) return list;

      // const tenantName =
      //   _.get(tenantPerson, `companies.0.${lang}`) || _.get(tenantPerson, lang);
      // // get tenant's name
      // if (!tenantName) return list;
    } else {
      const tenantShopNameRecords = _.find(_.get(item, "tenants") || [], tenant => _.find(_.get(tenant, "contactsCompanies") || [], v => _.get(v, "type") === "Shop"));
      const tenantNameForShopRecords = _.find(_.get(item, "tenants") || [], tenant => _.find(_.get(tenant, "contactsCompanies") || [], v => _.get(v, "type") !== "Shop"));
      tenantCompany = _.head(_.get(tenantNameForShopRecords, "contactsCompanies", [])) || {};
      tenantShop = _.find(_.get(tenantShopNameRecords, "contactsCompanies") || [],v => _.get(v, "type") === "Shop");
    }

    const minDate = _.get(item, "expiry.minDate");
    const maxDate = _.get(item, "expiry.maxDate");

    return [
      ...list,
      {
        tenant: {
          nameEn:
            _.get(tenantCompany, "nameEn") ||
            _.get(tenantPerson, `companies.0.nameEn`) ||
            _.get(tenantPerson, "nameEn") ||
            "",
          nameZh:
            _.get(tenantCompany, "nameEn") ||
            _.get(tenantPerson, `companies.0.nameZh`) ||
            _.get(tenantPerson, "nameZh") ||
            "",
        },
        tenantIsShow: sbu !== 'SHOPS' ? false : (
          !!_.get(tenantCompany, "nameEn") ||
          !!_.get(tenantCompany, "nameZh") ||
          !!_.get(tenantPerson, "nameEn") ||
          !!_.get(tenantPerson, "nameZh") ||
          !!_.get(tenantPerson, 'companies.0.nameEn') ||
          !!_.get(tenantPerson, 'companies.0.nameZh')
        ) && tenancyStatus === "Current" ? true : false,//!!tenantName,
        tenantShop: {
          nameEn: _.get(tenantShop, "nameEn") || "",
          nameZh: _.get(tenantShop, "nameZh") || "",
        },
        tenantShopIsShow: sbu !== 'SHOPS' ? false : (
          !!_.get(tenantShop, "nameEn") ||
          !!_.get(tenantShop, "nameZh")
        ) && tenancyStatus === "Current" ? true : false,//!!_.get(tenantShop, "nameEn") || !!_.get(tenantShop, "nameZh"),
        rentalFee: _.get(item, "rentalFee") || 0,
        rentalFeeIsShow: sbu !== 'SHOPS' ? false : !!_.get(item, "rentalFee") && tenancyStatus === "Current" ? true : false,//!!_.get(item, "rentalFee"),
        period: {
          min: minDate ? moment(minDate).format("YYYY/MM/DD") : null,
          max: maxDate ? moment(maxDate).format("YYYY/MM/DD") : null,
        },
        periodIsShow: sbu !== 'SHOPS' ? false : !!(minDate || maxDate) && tenancyStatus === "Current" ? true : false,//!!(minDate || maxDate),
        // COMM db does not have isIncludeGovernmentRent, isIncludeRates, isIncludeAirConditioning data
        tenancy:
          sbu !== "COMM"
            ? getProposalTenancyDesc(item)
            : { nameZh: "", nameEn: "" },
        tenancyIsShow: false,
        tenancyRemarks: "",
        tenancyRemarksIsShow: false,
      },
    ];
  }, []);

  return records;
};

const transformTenantData = (data) => {
  return _.map(data, (v) => ({
    tenant: { ...(_.get(v, "tenant") || {}), isShow: v.tenantIsShow },
    rentalFee: { value: v.rentalFee || 0, isShow: v.rentalFeeIsShow },
    period: { ...(_.get(v, "period") || {}), isShow: v.periodIsShow },
    tenancy: { ...(_.get(v, "tenancy") || {}), isShow: v.tenancyIsShow },
    tenancyRemarks: { value: v.tenancyRemarks || "", isShow: v.tenancyRemarksIsShow },
    ...(sbu === "SHOPS" ? { tenantShop: { ...(_.get(v, "tenantShop") || {}), isShow: v.tenantShopIsShow }} : {}),
  }));
};

export const mergeTenants = (tempTenants, historyTenants = []) => {
  // 合并两个数组，以 historyTenants 的成员为准
  const mergedTenants = tempTenants.map(tenant => {
    const matchingHistoryTenant = historyTenants.find(
      ht => ht.tenant.nameZh === tenant.tenant.nameZh && ht.tenant.nameEn === tenant.tenant.nameEn,
    );

    const convertMatchingHistoryTenant = (ht) => {
      if (!ht) { return ht; }

      const {
        tenant: tenantTemp = {},
        // tenantShop: tenantShopTemp = {},
        rentalFee: rentalFeeTemp = {},
        period: periodTemp = {},
        tenancy: tenancyTemp = {},
        tenancyRemarks: tenancyRemarksTemp = {},
      } = ht;

      const { isShow: tenantIsShow = false, ...tenant } = tenantTemp;
      // const { isShow: tenantShopIsShow = false, ...tenantShop } = tenantShopTemp;
      const { isShow: rentalFeeIsShow = false, value: rentalFee } = rentalFeeTemp;
      const { isShow: periodIsShow = false, ...period } = periodTemp;
      const { isShow: tenancyIsShow = false, ...tenancy } = tenancyTemp;
      const { isShow: tenancyRemarksIsShow = false, value: tenancyRemarks } = tenancyRemarksTemp;

      const baseResult = {
        tenant,
        tenantIsShow,
        rentalFee,
        rentalFeeIsShow,
        period,
        periodIsShow,
        tenancy,
        tenancyIsShow,
        tenancyRemarks,
        tenancyRemarksIsShow
      };
      // 只有当sbu为SHOPS时才添加tenantShop相关字段
      if (sbu === "SHOPS") {
        const { tenantShop: tenantShopTemp = {} } = ht;
        const { isShow: tenantShopIsShow = false, ...tenantShop } = tenantShopTemp;
        return {
          ...baseResult,
          tenantShop,
          tenantShopIsShow
        };
      }
      return baseResult;
    };
    return matchingHistoryTenant ? _.merge({}, tenant, convertMatchingHistoryTenant(matchingHistoryTenant)) : tenant;
  });

  // 添加没有匹配到的历史租户
  // historyTenants.forEach(historyTenant => {
  //   if (!mergedTenants.find(tenant => (tenant?.tenant?.nameZh === historyTenant?.tenant?.nameZh && tenant?.tenant?.nameEn === historyTenant?.tenant?.nameEn))) {
  //     const baseTenant = {
  //       tenant: {
  //         nameZh: historyTenant.tenant.nameZh,
  //         nameEn: historyTenant.tenant.nameEn,
  //       },
  //       tenantIsShow: historyTenant.tenant.isShow,
  //       rentalFee: historyTenant.rentalFee.value,
  //       rentalFeeIsShow: historyTenant.rentalFee.isShow,
  //       period: {
  //         min: historyTenant.period.min,
  //         max: historyTenant.period.max,
  //       },
  //       periodIsShow: historyTenant.period.isShow,
  //       tenancy: {
  //         nameZh: historyTenant.tenancy.nameZh,
  //         nameEn: historyTenant.tenancy.nameEn,
  //       },
  //       tenancyIsShow: historyTenant.tenancy.isShow,
  //       tenancyRemarks: historyTenant.tenancyRemarks.value,
  //       tenancyRemarksIsShow: historyTenant.tenancyRemarks.isShow
  //     };
  //     mergedTenants.push(
  //       sbu === "SHOPS"
  //         ? {
  //           ...baseTenant,
  //           tenantShop: {
  //             nameZh: historyTenant.tenantShop.nameZh,
  //             nameEn: historyTenant.tenantShop.nameEn,
  //           },
  //           tenantShopIsShow: historyTenant.tenantShop.isShow,
  //         }
  //         : baseTenant
  //     );
  //   }
  // });

  return mergedTenants;
};

// handle individual pp
export const parseFormToProposal = (form, otherProps = {}) => {
  const { possessions, decorations, unitViews, stocks, media, buildingMedia, streetMedia, currentStates, currentDetail } =
    otherProps;
  const stock = _.find(stocks, (m) => m._id === currentDetail)

  const streetType = _.get(form, "stock.streetType") || 0;

  const wwwDetail = _.get(stock, "wwwDetail", null);
  // handle stock states ...
  const stockVariables = {
    ...form.stock,
    ...form.general,
    // ============ Common fields =============
    ...({
      showEmployeePhoto: _.get(form, "general.showEmployeePhoto.0.isShow"),
      showContact: _.get(form, "general.showContact.0.isShow"),
    }),
    stockId: _.get(stock, "unicorn.id"),
    stockMongoId: _.get(stock, "_id"),
    isSoleagent: !_.isNil(wwwDetail) && checkIfSoleagent(wwwDetail),
    // isSoleagent:
    //   !_.isNil(_.get(stock, "soleagent.periodStart")) &&
    //   !_.isNil(_.get(stock, "soleagent.periodEnd")) &&
    //   checkExpiredDate(
    //     _.get(stock, "soleagent.periodStart"),
    //     _.get(stock, "soleagent.periodEnd"),
    //   ),

    // floorInChinese: _.get(stock, "floorInChinese", ""),
    buildingNameZh: _.get(stock, "building.nameZh") || "",
    buildingNameEn: _.get(stock, "building.nameEn") || "",
    buildingDistrictNameZh: _.get(stock, "building.district.nameZh") || "",
    buildingDistrictNameEn: _.get(stock, "building.district.nameEn") || "",

    exactFloor: _.get(form, "stock.exactFloor.0.value", true) || false,
    avgPrice: {
      ..._.get(form, "stock.avgPrice.0"),
      value: _.get(form, "stock.avgPrice.0.value", 0),
    },
    totalPrice: {
      ..._.get(form, "stock.totalPrice.0"),
      value: _.get(form, "stock.totalPrice.0.value", 0),
    },
    avgRent: {
      ..._.get(form, "stock.avgRent.0"),
      value: _.get(form, "stock.avgRent.0.value", 0),
    },
    totalRent: {
      ..._.get(form, "stock.totalRent.0"),
      value: _.get(form, "stock.totalRent.0.value", 0),
    },
    bottomAvgPrice: {
      value: _.get(form, "stock.bottomAvgPrice.0.value", 0),
      isShow: false,
      ..._.get(form, "stock.bottomAvgPrice.0"),
    },
    bottomTotalPrice: {
      value: _.get(form, "stock.bottomTotalPrice.0.value", 0),
      isShow: false,
      ..._.get(form, "stock.bottomTotalPrice.0"),
    },
    bottomAvgRent: {
      value: _.get(form, "stock.bottomAvgRent.0.value", 0),
      isShow: false,
      ..._.get(form, "stock.bottomAvgRent.0"),
    },
    bottomTotalRent: {
      value: _.get(form, "stock.bottomTotalRent.0.value", 0),
      isShow: false,
      ..._.get(form, "stock.bottomTotalRent.0"),
    },

    floor: _.get(form, "stock.floor.0"),
    unit: {
      ..._.get(form, "stock.unit.0", {}),
      // wf unit display in CHI will be handled on saleskit
      // parse back to wf if CHI wf is used
      value:
        _.get(form, "stock.unit.0.value") === "全層"
          ? "WF"
          : _.get(form, "stock.unit.0.value"),
    },
    possession: {
      value: _.defaultTo(
        findZhEnFromOptions(
          possessions || [],
          _.get(form, "stock.possession.0.value"),
        ),
        null,
      ),
      isShow: _.get(form, "stock.possession.0.isShow"),
    },
    managementFee: _.get(form, "stock.managementFee.0"),
    acFee: _.get(form, "stock.acFee.0"),
    gRent: _.get(form, "stock.gRent.0"),
    rates: _.get(form, "stock.rates.0"),
    includedFee: _.get(form, "stock.includedFee.0"),
    currentTenants: transformTenantData(_.get(form, "stock.currentTenants", [])),
    // =========================================

    ...(sbu === "COMM"
      ? {
          stockType: {
            ...(_.get(form, "stock.stockType.0") || {}),
            value: {
              nameZh: _.get(stock, "stockType.nameZh", "---"),
              nameEn: _.get(stock, "stockType.nameEn", "---"),
            },
          },
          isMgtFeeOpenAC: _.get(form, "stock.isMgtFeeOpenAC.0.isShow"),
          videoUrl: _.get(form, "stock.videoUrl.0"),
          usage: {
            ..._.get(form, "stock.usage.0", {}),
            value: {
              nameZh: _.get(stock, "building.buildingUsageZh") || "",
              nameEn: _.get(stock, "building.buildingUsage") || "",
            },
          },
          haveCarPark: {
            ..._.get(form, "stock.haveCarPark.0", {}),
            value:
              yesNoMapping[
                _.get(stock, "building.haveCarPark", false) ? "yes" : "no"
              ],
          },
          cargoLift: {
            ..._.get(form, "stock.cargoLift.0", {}),
            value:
              yesNoMapping[
                _.get(stock, "building.haveCargoLift", false) ? "yes" : "no"
              ],
          },
          airConditioningType: {
            ..._.get(form, "stock.airConditioningType.0", {}),
            value: {
              nameZh: _.get(stock, "building.airConditioning.typeZh", "---"),
              nameEn: _.get(stock, "building.airConditioning.type", "---"),
            },
          },
          airConditioningOpeningTime: {
            ..._.get(form, "stock.airConditioningOpeningTime.0", {}),
            value: {
              nameZh: _.get(stock, "building.airConditioning.openingTime", "---"),
              nameEn: _.get(stock, "building.airConditioning.openingTime", "---"),
            },
          },
          airConditioningExtraCharges: {
            ..._.get(form, "stock.airConditioningExtraCharges.0", {}),
            value: {
              nameZh: _.get(stock, "building.airConditioning.extraCharges", "---"),
              nameEn: _.get(stock, "building.airConditioning.extraCharges", "---"),
            },
          },
          developers: (_.get(form, "stock.developers") || []).map((v, i) => ({
            ...v,
            value: {
              nameEn: _.get(stock, `building.developers.${i}.nameEn`) || "",
              nameZh: _.get(stock, `building.developers.${i}.nameZh`) || "",
            },
          })),
          currentState: {
            value: _.defaultTo(
              findZhEnFromOptions(
                currentStates || [],
                _.get(form, "stock.currentState.0.value"),
              ),
              null,
            ),
            isShow: _.get(form, "stock.currentState.0.isShow"),
          },
        }
      : {
          ceilingHeight: {
            isShow: _.get(form, "stock.ceilingHeight.0.isShow") || false,
            ft: parseInt(_.get(form, "stock.ceilingHeight.0.ft", 0), 10) || 0,
            in: parseInt(_.get(form, "stock.ceilingHeight.0.in", 0), 10) || 0,
          },
        }),

    ...(sbu === "IND"
      ? {
          usage: {
            ..._.get(form, "stock.usage.0", {}),
            value: {
              nameZh: _.get(stock, "building.buildingUsage.nameZh") || "---",
              nameEn: _.get(stock, "building.buildingUsage.nameEn") || "---",
            },
          },
          stockType: {
            ...(_.get(form, "stock.stockType.0") || {}),
            value: {
              nameZh: _.get(stock, "stockType.nameZh", "---"),
              nameEn: _.get(stock, "stockType.nameEn", "---"),
            },
          },
          airConditioningType: {
            ..._.get(form, "stock.airConditioningType.0", {}),
            value: {
              nameZh:
                _.get(stock, "building.airConditioning.type.nameZh") ?? "---",
              nameEn:
                _.get(stock, "building.airConditioning.type.nameEn") ?? "---",
            },
          },
          airConditioningOpeningTime: {
            ..._.get(form, "stock.airConditioningOpeningTime.0", {}),
            value: {
              nameZh: _.get(stock, "building.airConditioning.openingTime.nameZh") ?? "---",
              nameEn: _.get(stock, "building.airConditioning.openingTime.nameEn") ?? "---",
            },
          },
          airConditioningExtraCharges: {
            ..._.get(form, "stock.airConditioningExtraCharges.0", {}),
            value: {
              nameZh: _.get(stock, "building.airConditioning.extraCharges.nameZh") ?? "---",
              nameEn: _.get(stock, "building.airConditioning.extraCharges.nameEn") ?? "---",
            },
          },
          cargoLift: {
            ..._.get(form, "stock.cargoLift.0", {}),
            value: {
              nameZh: _.get(form, "stock.cargoLift.0.value") || "---",
              nameEn: _.get(form, "stock.cargoLift.0.value") || "---",
            },
          },
          containers: _(_.get(form, "stock.containers", [])).map((v, i) => ({
            isShow: v.isShow,
            value: {..._.get(getContainers(stock), i), haveLoadingBay: v.haveLoadingBay},
          })),
          currentState: {
            value: _.defaultTo(
              findZhEnFromOptions(
                currentStates || [],
                _.get(form, "stock.currentState.0.value"),
              ),
              null,
            ),
            isShow: _.get(form, "stock.currentState.0.isShow"),
          },
          acFee: _.get(form, "stock.acFee.0"),
          gRent: _.get(form, "stock.gRent.0"),
          rates: _.get(form, "stock.rates.0"),
          areaTerrace: _.get(form, "stock.areaTerrace.0"),
          areaRoof: _.get(form, "stock.areaRoof.0"),
          floorLoading: _.get(form, "stock.floorLoading.0"),
        }
      : {}),

    ...(sbu === "SHOPS"
      ? {
          stockType: {
            ...(_.get(form, "stock.stockType.0") || {}),
            value: {
              nameZh: _.get(stock, "stockType.nameZh", "---"),
              nameEn: _.get(stock, "stockType.nameEn", "---"),
            },
          },
          floorType: getFloorTypeInfoFromLangFile(
            _.get(form, "stock.floorType"),
          ),
          suggestedAvgPrice: {
            ..._.get(form, "stock.suggestedAvgPrice.0"),
            value: _.get(form, "stock.suggestedAvgPrice.0.value", 0),
          },
          suggestedTotalPrice: {
            ..._.get(form, "stock.suggestedTotalPrice.0"),
            value: _.get(form, "stock.suggestedTotalPrice.0.value", 0),
          },
          suggestedAvgRent: {
            ..._.get(form, "stock.suggestedAvgRent.0"),
            value: _.get(form, "stock.suggestedAvgRent.0.value", 0),
          },
          suggestedTotalRent: {
            ..._.get(form, "stock.suggestedTotalRent.0"),
            value: _.get(form, "stock.suggestedTotalRent.0.value", 0),
          },
          districtNameZh: _.get(stock, "district.nameZh", ""),
          districtNameEn: _.get(stock, "district.nameEn", ""),

          customBuilding: _.get(form, "stock.customBuilding.0"),
          customStreet:
            _.get(stock, `streets.${streetType}.street.nameEn`) || "",
          customStreetNo: _.get(stock, `streets.${streetType}].number`) || "",
          streetNameZh: _.get(stock, `streets.${streetType}.street.nameZh`, ""),
          streetNameEn: _.get(stock, `streets.${streetType}.street.nameEn`, ""),
          streetNo: _.get(stock, `streets.${streetType}.number`, ""),
          // areas: [
          //   ..._.get(form, "stock.areas.areas"),
          //   {
          //     ..._.get(form, "stock.areas.totalArea"),
          //     ...calcShopsTotalArea(_.get(form, "stock.areas.areas")),
          //   },
          // ],

          areas: _.concat(
            _.get(form, "stock.areas"),
            _.get(form, "stock.area"),
          ).map((v) => ({
            ...v,
            gross: v.gross || 0,
            net: v.net || 0,
          })),
          entranceWidth: {
            isShow: _.get(form, "stock.entranceWidth.0.isShow") || false,
            ft: parseInt(_.get(form, "stock.entranceWidth.0.ft", 0), 10) || 0,
            in: parseInt(_.get(form, "stock.entranceWidth.0.in", 0), 10) || 0,
          },
          unitDepth: {
            isShow: _.get(form, "stock.unitDepth.0.isShow") || false,
            ft: parseInt(_.get(form, "stock.unitDepth.0.ft", 0), 10) || 0,
            in: parseInt(_.get(form, "stock.unitDepth.0.in", 0), 10) || 0,
          },
          currentState: {
            value: _.defaultTo(
              findZhEnFromOptions(
                currentStates || [],
                _.get(form, "stock.currentState.0.value"),
              ),
              null,
            ),
            isShow: _.get(form, "stock.currentState.0.isShow"),
          },
          yield: _.get(form, "stock.yield.0"),
          advanceTenants: transformTenantData(_.get(form, "stock.advanceTenants", [])),
          previousTenants: transformTenantData(_.get(form, "stock.previousTenants", [])),
          formerTenants: transformTenantData(_.get(form, "stock.formerTenants", [])),
          // stockTypeId: _.get(stock, `stockTypeId`, ""),
        }
      : {
          areaEfficiency: _.get(form, "stock.areaEfficiency.0"),
          areaGross: _.get(form, "stock.areaGross.0"),
          areaNet: _.get(form, "stock.areaNet.0"),
          areaSaleable: _.get(form, "stock.areaSaleable.0"),
          areaLettable: _.get(form, "stock.areaLettable.0"),
          availability: _.get(form, "stock.availability.0"),

          streetNameZh: _.get(stock, "building.street.street.nameZh", ""),
          streetNameEn: _.get(stock, "building.street.street.nameEn", ""),
          districtNameZh: _.get(stock, "building.district.nameZh", ""),
          districtNameEn: _.get(stock, "building.district.nameEn", ""),
          streetNo: _.get(stock, "building.street.number", ""),
          floorType: getFloorTypeInfoFromLangFile(
            _.get(form, "stock.floorType"),
          ),
        decoration: sbu === "COMM" ? {
          value: {
            nameZh: _.map(_.get(form, "stock.decoration.0.value", []), v => v?.value?.nameZh).join('，') || "",
            nameEn: _.map(_.get(form, "stock.decoration.0.value", []), v => v?.value?.nameEn).join(', ') || ""
          },
          isShow: _.get(form, "stock.decoration.0.isShow", false),
        } : {
          value: _.defaultTo(
            findZhEnFromOptions(
              decorations || [],
              _.get(form, "stock.decoration.0.value"),
            ),
            null,
          ),
          isShow: _.get(form, "stock.decoration.0.isShow"),
        },
        unitView: sbu === "COMM" ? {
          value: {
            nameZh: _.map(_.get(form, "stock.unitView.0.value", []), v => v?.value?.nameZh).join('，') || "",
            nameEn: _.map(_.get(form, "stock.unitView.0.value", []), v => v?.value?.nameEn).join(', ') || ""
          },
          isShow: _.get(form, "stock.unitView.0.isShow", false),
        } : {
            value: _.defaultTo(
              findZhEnFromOptions(
                unitViews || [],
                _.get(form, "stock.unitView.0.value"),
              ),
              null,
            ),
            isShow: _.get(form, "stock.unitView.0.isShow"),
          },
          title: {
            ..._.get(form, "stock.title.0", {}),
            value: {
              nameZh:
                _.get(
                  _.get(titleMapping, _.get(stock, "building.title", ""), {}),
                  "nameZh",
                ) ?? "---",
              nameEn:
                _.get(
                  _.get(titleMapping, _.get(stock, "building.title", ""), {}),
                  "nameEn",
                ) ?? "---",
            },
          },
          managementCompany: {
            ..._.get(form, "stock.managementCompany.0", {}),
            value: {
              nameZh: _.get(
                stock,
                "building.managementCompany.managementCompany.nameZh",
                "---",
              ),
              nameEn: _.get(
                stock,
                "building.managementCompany.managementCompany.nameEn",
                "---",
              ),
            },
          },
          transport: {
            ..._.get(form, "stock.transport.0", {}),
            value: {
              nameZh: _.get(stock, "building.transportZh", "---"),
              nameEn: _.get(stock, "building.transportEn", "---"),
            },
          },
          passengerLift: {
            ..._.get(form, "stock.passengerLift.0", {}),
            value: {
              nameZh: _.get(form, "stock.passengerLift.0.value", "---"),
              nameEn: _.get(form, "stock.passengerLift.0.value", "---"),
            },
          },
          yield: {
            ..._.get(form, "stock.yield.0", {}),
            value: parseFloat(_.get(form, "stock.yield.0.value") || "0"),
          },
          inTakeDate: _.get(form, "stock.inTakeDate.0"),
          isBuildingFieldsAllHide: !(
            _.get(form, "stock.usage.0.isShow") ||
            _.get(form, "stock.title.0.isShow") ||
            _.get(form, "stock.inTakeDate.0.isShow") ||
            _.some(
              _.map(_.get(form, "stock.developers") || [], (d) => d.isShow),
              Boolean,
            ) ||
            _.get(form, "stock.managementCompany.0.isShow") ||
            _.get(form, "stock.transport.0.isShow") ||
            _.get(form, "stock.passengerLift.0.isShow") ||
            _.get(form, "stock.cargoLift.0.isShow") ||
            _.get(form, "stock.airConditioningType.0.isShow") ||
            _.get(form, "stock.airConditioningOpeningTime.0.isShow") ||
            _.get(form, "stock.airConditioningExtraCharges.0.isShow") ||
            _.some(
              _.map(_.get(form, "stock.containers") || [], (d) => d.isShow),
              Boolean,
            )
          ),
        }),
  };

  delete stockVariables.areaType;
  delete stockVariables.streetType;
  // delete stockVariables.exactFloor;
  delete stockVariables.showUnit;
  if (sbu === "SHOPS") {
    delete stockVariables.area;
    delete stockVariables.showBuilding;
  }
  // handle media state...
  const stockMediaData =
    _.get(
      media.find((m) => m.id === _.get(stock, "unicorn.id", "").toString()),
      "data.photo",
    ) || [];
  const buildingMediaData =
    _.get(
      buildingMedia.find(
        (m) => m.id === _.get(stock, "building.unicorn.id", "").toString(),
      ),
      "data.photo",
    ) || [];
  const streetMediaData =
    _.get(
      streetMedia.find(
        (m) => m.id === _.get(stock, "street.street.unicorn.id", "").toString(),
      ),
      "data.photo",
    ) || [];

  const { media: formMedia } = form;

  const mainPhoto =
    // stockMediaData?.photo?.find((m) => m.tags && m.tags.includes("main")) ||
    buildingMediaData?.find((m) => m.tags && m.tags.includes("main")) ||
    streetMediaData?.find((m) => m.tags && m.tags.includes("main")) || null;

  // let mainPhoto =
  //   _.find(buildingMedia, m => m.tags && m.tags.includes("main")) || null;

  // if there is no main photo defined in building, use stock main
  // if (sbu === "SHOPS" || !mainPhoto) {
  //   mainPhoto =
  //     _.find(stockMedia, m => m.tags && m.tags.includes("main")) || null;
  // }

  const mediaVariables = {
    googleMap: {
      isPP: !!(_.get(formMedia, "selectedMedia", []).includes("map")),
      isMain: _.get(formMedia, "mainPhoto") === "map",
      isMain1: _.get(formMedia, "main1Photo") === "map",
      isMain2: false,
    },
    photos: _.concat(
      [],
      _.get(form, "media.lat") && _.get(form, "media.lng") && _.get(form, "media.selectedMedia", []).includes("map") ?
      [{
        id: "map",
        mediumRoot: `lng:${_.get(form, "media.lng")},lat:${_.get(form, "media.lat")}`,
        photoContent: "map",
        multiImg: _.get(form, "media.attachmentMultiImgConfig.map", "ONE"),
      }]
      : [],
      _.get(form, "media.govLat") && _.get(form, "media.govLng") && _.get(form, "media.selectedMedia", []).includes("govMap") ?
      [{
        id: 'govMap',
        mediumRoot: `lng:${_.get(form, "media.govLng")},lat:${_.get(form, "media.govLat")}`,
        photoContent: 'govMap',
        multiImg: _.get(form, "media.attachmentMultiImgConfig.govMap", "ONE"),
      }]
      : [],
      _.map(_.get(formMedia, "selectedMedia", []), (id) =>
        parseMedia(stockMediaData, id, _.get(form, `media.attachmentMultiImgConfig.${id}`, 'ONE')),
      ).filter(Boolean),
      _.map(_.get(formMedia, "selectedMedia", []), (id) =>
        parseMedia(buildingMediaData, id, _.get(form, `media.attachmentMultiImgConfig.${id}`, 'ONE')),
      ).filter(Boolean),
      _.map(_.get(formMedia, "selectedMedia", []), (id) =>
        parseMedia(streetMediaData, id, _.get(form, `media.attachmentMultiImgConfig.${id}`, 'ONE')),
      ).filter(Boolean),
    ),
    mainPhoto:
      _.get(formMedia, "mainPhoto") && !["map", "govMap"].includes(_.get(form, "media.mainPhoto"))
        ? parseMedia(
            _.concat([], stockMediaData, buildingMediaData, streetMediaData),
            _.get(formMedia, "mainPhoto"),
          )
        : _.get(form, "media.mainPhoto") === "map" ? {
          id: "map",
          mediumRoot: `lng:${_.get(form, "media.lng")},lat:${_.get(form, "media.lat")}`,
          photoContent: "map",
        }
        : _.get(form, "media.mainPhoto") === "govMap" ? {
          id: "govMap",
          mediumRoot: `lng:${_.get(form, "media.govLng")},lat:${_.get(form, "media.govLat")}`,
          photoContent: 'govMap',
        }
        : null,
    main1Photo:
      _.get(formMedia, "main1Photo") && !["map", "govMap"].includes(_.get(form, "media.main1Photo"))
        ? parseMedia(
            _.concat([], stockMediaData, buildingMediaData, streetMediaData),
            _.get(formMedia, "main1Photo"),
          )
        : _.get(form, "media.main1Photo") === "map" ? {
          id: "map",
          mediumRoot: `lng:${_.get(form, "media.lng")},lat:${_.get(form, "media.lat")}`,
          photoContent: "map",
        }
        : _.get(form, "media.main1Photo") === "govMap" ? {
          id: "govMap",
          mediumRoot: `lng:${_.get(form, "media.govLng")},lat:${_.get(form, "media.govLat")}`,
          photoContent: 'govMap',
        }
        : null,
    // mainPhoto: mainPhoto
    //   ? _.pick(mainPhoto, ["id", "mediumRoot", "photoContent"])
    //   : null,
    main2Photo:
      _.get(form, "media.main2Photo") &&
      !["map", "govMap"].includes(_.get(form, "media.main2Photo"))
        ? parseMedia(
            _.concat(
              [],
              stockMediaData,
              buildingMediaData,
              streetMediaData,
            ),
            _.get(form, "media.main2Photo"),
          )
        : _.get(form, "media.main2Photo") === "map" ? {
          id: "map",
          mediumRoot: `lng:${_.get(form, "media.lng")},lat:${_.get(form, "media.lat")}`,
          photoContent: "map",
        }
        : _.get(form, "media.main2Photo") === "govMap" ? {
          id: "govMap",
          mediumRoot: `lng:${_.get(form, "media.govLng")},lat:${_.get(form, "media.govLat")}`,
          photoContent: 'govMap',
        }
        : null,
    videos: [],
    lat: _.get(formMedia, "lat"),
    lng: _.get(formMedia, "lng"),
    govLat: _.get(formMedia, "govLat"),
    govLng: _.get(formMedia, "govLng"),
  };

  return { ...stockVariables, ...mediaVariables, sbu };
};

const parseProposalRecord = (form, stock, otherProps, mode = "list") => {
  const { possessions, decorations, unitViews, media, buildingMedia, streetMedia, currentStates } =
    otherProps;

  const stockMediaData = _.get(
    media.find((m) => m.id === _.get(stock, "unicorn.id", "").toString()),
    "data.photo",
    [],
  );
  const buildingMediaData = _.get(
    buildingMedia.find(
      (m) => m.id === _.get(stock, "building.unicorn.id", "").toString(),
    ),
    "data.photo",
    [],
  );

  const streetMediaData = _.get(
    streetMedia.find(
      (m) => m.id === _.get(stock, "street.street.unicorn.id", "").toString(),
    ),
    "data.photo",
    [],
  );

  const mediaData = [].concat(stockMediaData, buildingMediaData, streetMediaData);

  const building = {
    // ============ Common fields =============
    nameZh: _.get(stock, "building.nameZh", ""),
    nameEn: _.get(stock, "building.nameEn", ""),
    districtNameZh: _.get(stock, "building.district.nameZh", ""),
    districtNameEn: _.get(stock, "building.district.nameEn", ""),
    lat: _.get(stock, "building.coordinates.latitude"),
    lng: _.get(stock, "building.coordinates.longitude"),
    govLat: _.get(stock, "building.coordinates.latitude"),
    govLng: _.get(stock, "building.coordinates.longitude"),
    // =========================================

    ...(sbu === "COMM"
      ? {
          usage: {
            ..._.get(form, "stock.usage.0", {}),
            value: {
              nameZh: _.get(stock, "building.buildingUsageZh") || "",
              nameEn: _.get(stock, "building.buildingUsage") || "",
            },
          },
          airConditioningType: {
            ..._.get(form, "stock.airConditioningType.0", {}),
            value: {
              nameZh: _.get(stock, "building.airConditioning.typeZh") || "---",
              nameEn: _.get(stock, "building.airConditioning.type") || "---",
            },
          },
          airConditioningOpeningTime: {
            ..._.get(form, "stock.airConditioningOpeningTime.0", {}),
            value: {
              nameZh: _.get(stock, "building.airConditioning.openingTime") || "---",
              nameEn: _.get(stock, "building.airConditioning.openingTime") || "---",
            },
          },
          airConditioningExtraCharges: {
            ..._.get(form, "stock.airConditioningExtraCharges.0", {}),
            value: {
              nameZh: _.get(stock, "building.airConditioning.extraCharges") || "---",
              nameEn: _.get(stock, "building.airConditioning.extraCharges") || "---",
            },
          },
          managementCompany: {
            ..._.get(form, "stock.managementCompany.0", {}),
            value: {
              nameZh:
                _.get(
                  stock,
                  "building.managementCompany.managementCompany.nameZh",
                ) || "---",
              nameEn:
                _.get(
                  stock,
                  "building.managementCompany.managementCompany.nameEn",
                ) || "---",
            },
          },
          haveCarPark: {
            ..._.get(form, "stock.haveCarPark.0", {}),
            value:
              yesNoMapping[
                _.get(stock, "building.haveCarPark", false) ? "yes" : "no"
              ],
          },
          cargoLift: {
            ..._.get(form, "stock.cargoLift.0", {}),
            value:
              yesNoMapping[
                _.get(stock, "building.haveCargoLift", false) ? "yes" : "no"
              ],
          },
        }
      : {}),

    ...(sbu === "IND"
      ? {
          usage: {
            ..._.get(form, "stock.usage.0", {}),
            value: {
              nameZh: _.get(stock, "building.buildingUsage.nameZh") || "---",
              nameEn: _.get(stock, "building.buildingUsage.nameEn") || "---",
            },
          },
          containers: _.map(_.get(form, "stock.containers") || [], (v, i) => ({
            isShow: v.isShow,
            value: {..._.get(getContainers(stock), i), haveLoadingBay: v.haveLoadingBay},
          })),
          airConditioningType: {
            ..._.get(form, "stock.airConditioningType.0", {}),
            value: {
              nameZh: _.get(
                stock,
                "building.airConditioning.type.nameZh",
                "---",
              ),
              nameEn: _.get(
                stock,
                "building.airConditioning.type.nameEn",
                "---",
              ),
            },
          },
          airConditioningOpeningTime: {
            ..._.get(form, "stock.airConditioningOpeningTime.0", {}),
            value: {
              nameZh: _.get(stock, "building.airConditioning.openingTime.nameZh", "---"),
              nameEn: _.get(stock, "building.airConditioning.openingTime.nameEn", "---"),
            },
          },
          airConditioningExtraCharges: {
            ..._.get(form, "stock.airConditioningExtraCharges.0", {}),
            value: {
              nameZh: _.get(stock, "building.airConditioning.extraCharges.nameZh", "---"),
              nameEn: _.get(stock, "building.airConditioning.extraCharges.nameEn", "---"),
            },
          },
          managementCompany: {
            ..._.get(form, "stock.managementCompany.0", {}),
            value: {
              nameZh:
                _.get(
                  stock,
                  "building.managementCompany.managementCompany.nameZh",
                ) || "---",
              nameEn:
                _.get(
                  stock,
                  "building.managementCompany.managementCompany.nameEn",
                ) || "---",
            },
          },
          cargoLift: {
            ..._.get(form, "stock.cargoLift.0", {}),
            value: {
              nameZh: _.get(form, "stock.cargoLift.0.value", "---"),
              nameEn: _.get(form, "stock.cargoLift.0.value", "---"),
            },
          },
        }
      : {}),

    ...(sbu === "SHOPS"
      ? {}
      : {
          // these fields are all for both COMM and IND
          title: {
            ..._.get(form, "stock.title.0", {}),
            value: {
              nameZh:
                _.get(
                  _.get(titleMapping, _.get(stock, "building.title", ""), {}),
                  "nameZh",
                ) ?? "---",
              nameEn:
                _.get(
                  _.get(titleMapping, _.get(stock, "building.title", ""), {}),
                  "nameEn",
                ) ?? "---",
            },
          },
          transport: {
            ..._.get(form, "stock.transport.0", {}),
            value: {
              nameZh: _.get(stock, "building.transportZh") || "---",
              nameEn: _.get(stock, "building.transportEn") || "---",
            },
          },
          inTakeDate: _.get(form, "stock.inTakeDate.0"),
          passengerLift: {
            ..._.get(form, "stock.passengerLift.0", {}),
            value: {
              nameZh: _.get(form, "stock.passengerLift.0.value", "---"),
              nameEn: _.get(form, "stock.passengerLift.0.value", "---"),
            },
          },

          ...(mode === "indv"
            ? {
                isBuildingFieldsAllHide: !(
                  _.get(form, "stock.usage.0.isShow") ||
                  _.get(form, "stock.title.0.isShow") ||
                  _.get(form, "stock.inTakeDate.0.isShow") ||
                  _.some(
                    _.map(
                      _.get(form, "stock.developers") || [],
                      (d) => d.isShow,
                    ),
                    Boolean,
                  ) ||
                  _.get(form, "stock.managementCompany.0.isShow") ||
                  _.get(form, "stock.transport.0.isShow") ||
                  _.get(form, "stock.passengerLift.0.isShow") ||
                  _.get(form, "stock.cargoLift.0.isShow") ||
                  _.get(form, "stock.airConditioningOpeningTime.0.isShow") ||
                  _.get(form, "stock.airConditioningExtraCharges.0.isShow") ||
                  _.get(form, "stock.airConditioningType.0.isShow")
                ),
              }
            : {}),
        }),
  };

  const streetType = _.get(form, "stock.streetType") || 0;
  const wwwDetail = _.get(stock, "wwwDetail", null);

  const parsedForm = {
    // ...form.stock,
    // ============ Common fields =============
    stockId: _.get(stock, "unicorn.id"),
    stockMongoId: _.get(stock, "_id"),
    isSoleagent: !_.isNil(wwwDetail) && checkIfSoleagent(wwwDetail),
    // isSoleagent:
    //   !_.isNil(_.get(stock, "soleagent.periodStart")) &&
    //   !_.isNil(_.get(stock, "soleagent.periodEnd")) &&
    //   checkExpiredDate(
    //     _.get(stock, "soleagent.periodStart"),
    //     _.get(stock, "soleagent.periodEnd"),
    //   ),

    avgPrice: {
      ..._.get(form, "stock.avgPrice.0"),
      value: _.get(form, "stock.avgPrice.0.value", 0),
    },
    totalPrice: {
      ..._.get(form, "stock.totalPrice.0"),
      value: _.get(form, "stock.totalPrice.0.value", 0),
    },
    avgRent: {
      ..._.get(form, "stock.avgRent.0"),
      value: _.get(form, "stock.avgRent.0.value", 0),
    },
    totalRent: {
      ..._.get(form, "stock.totalRent.0"),
      value: _.get(form, "stock.totalRent.0.value", 0),
    },
    bottomAvgPrice: {
      value: _.get(form, "stock.bottomAvgPrice.0.value", 0),
      isShow: false,
      ..._.get(form, "stock.bottomAvgPrice.0"),
    },
    bottomTotalPrice: {
      value: _.get(form, "stock.bottomTotalPrice.0.value", 0),
      isShow: false,
      ..._.get(form, "stock.bottomTotalPrice.0"),
    },
    bottomAvgRent: {
      value: _.get(form, "stock.bottomAvgRent.0.value", 0),
      isShow: false,
      ..._.get(form, "stock.bottomAvgRent.0"),
    },
    bottomTotalRent: {
      value: _.get(form, "stock.bottomTotalRent.0.value", 0),
      isShow: false,
      ..._.get(form, "stock.bottomTotalRent.0"),
    },

    floor: _.get(form, "stock.floor.0"),
    unit: {
      ..._.get(form, "stock.unit.0", {}),
      // wf unit display in CHI will be handled on saleskit
      // parse back to wf if CHI wf is used
      value:
        _.get(form, "stock.unit.0.value") === "全層"
          ? "WF"
          : _.get(form, "stock.unit.0.value"),
    },

    possession: {
      value: _.defaultTo(
        findZhEnFromOptions(
          possessions,
          _.get(form, "stock.possession.0.value"),
        ),
        null,
      ),
      isShow: _.get(form, "stock.possession.0.isShow"),
    },
    managementFee: _.get(form, "stock.managementFee.0"),
    acFee: _.get(form, "stock.acFee.0"),
    gRent: _.get(form, "stock.gRent.0"),
    rates: _.get(form, "stock.rates.0"),
    includedFee: _.get(form, "stock.includedFee.0"),
    allInclusive: _.get(form, "stock.allInclusive"),
    remarks: _.get(form, "stock.remarks"),

    currentTenants: transformTenantData(_.get(form, "stock.currentTenants", [])),
    termRemarks: _.get(form, "stock.termRemarks"),
    customTitle: _.get(form, "stock.customTitle"),

    // ================ Media =================
    ...(mode === "list"
      ? {
          mainPhoto:
          _.get(form, "media.mainPhoto") && !["map", "govMap"].includes(_.get(form, "media.mainPhoto"))
              ? parseMedia(mediaData, _.get(form, "media.mainPhoto"))
              : _.get(form, "media.mainPhoto") === "map" ? {
                id: "map",
                mediumRoot: `lng:${_.get(form, "media.lng")},lat:${_.get(form, "media.lat")}`,
                photoContent: "map",
              }
              : _.get(form, "media.mainPhoto") === "govMap" ? {
                id: "govMap",
                mediumRoot: `lng:${_.get(form, "media.govLng")},lat:${_.get(form, "media.govLat")}`,
                photoContent: 'govMap',
              }
              : null,
          photos: _.concat(
            [],
            _.map(_.get(form, "media.selectedMedia", []), (id) =>
              parseMedia(stockMediaData, id, _.get(form, `media.attachmentMultiImgConfig.${id}`, 'ONE')),
            ).filter(Boolean),
            _.map(_.get(form, "media.selectedMedia", []), (id) =>
              parseMedia(buildingMediaData, id, _.get(form, `media.attachmentMultiImgConfig.${id}`, 'ONE')),
            ).filter(Boolean),
            _.map(_.get(form, "media.selectedMedia", []), (id) =>
              parseMedia(streetMediaData, id, _.get(form, `media.attachmentMultiImgConfig.${id}`, 'ONE')),
            ).filter(Boolean),
            _.get(form, "media.lat") && _.get(form, "media.lng") && _.get(form, "media.selectedMedia", []).includes("map") ?
            [{
              id: "map",
              mediumRoot: `lng:${_.get(form, "media.lng")},lat:${_.get(form, "media.lat")}`,
              photoContent: "map",
              multiImg: _.get(form, "media.attachmentMultiImgConfig.map", "ONE"),
            }]
            : [],
            _.get(form, "media.govLat") && _.get(form, "media.govLng") && _.get(form, "media.selectedMedia", []).includes("govMap") ?
            [{
              id: 'govMap',
              mediumRoot: `lng:${_.get(form, "media.govLng")},lat:${_.get(form, "media.govLat")}`,
              photoContent: 'govMap',
              multiImg: _.get(form, "media.attachmentMultiImgConfig.govMap", "ONE"),
            }]
            : [],
          ),
          // includeGGMap: _.get(form, "media.selectedMedia", []).includes("map"),
          // useGGMapPhoto: _.get(form, "media.mainPhoto", "") === "map",
        }
      : {
          googleMap: {
            isPP: !!(_.get(form, "media.selectedMedia", []).includes("map")),
            isMain: _.get(form, "media.mainPhoto") === "map",
            isMain1: _.get(form, "media.main1Photo") === "map",
            isMain2: false,
          },
          photos: _.concat(
            [],
            _.map(_.get(form, "media.selectedMedia", []), (id) =>
              parseMedia(stockMediaData, id, _.get(form, `media.attachmentMultiImgConfig.${id}`, 'ONE')),
            ).filter(Boolean),
            _.map(_.get(form, "media.selectedMedia", []), (id) =>
              parseMedia(buildingMediaData, id, _.get(form, `media.attachmentMultiImgConfig.${id}`, 'ONE')),
            ).filter(Boolean),
            _.map(_.get(form, "media.selectedMedia", []), (id) =>
              parseMedia(streetMediaData, id, _.get(form, `media.attachmentMultiImgConfig.${id}`, 'ONE')),
            ).filter(Boolean),
            _.get(form, "media.lat") && _.get(form, "media.lng") && _.get(form, "media.selectedMedia", []).includes("map") ?
            [{
              id: "map",
              mediumRoot: `lng:${_.get(form, "media.lng")},lat:${_.get(form, "media.lat")}`,
              photoContent: "map",
              multiImg: _.get(form, "media.attachmentMultiImgConfig.map", "ONE"),
            }]
            : [],
            _.get(form, "media.govLat") && _.get(form, "media.govLng") && _.get(form, "media.selectedMedia", []).includes("govMap") ?
            [{
              id: 'govMap',
              mediumRoot: `lng:${_.get(form, "media.govLng")},lat:${_.get(form, "media.govLat")}`,
              photoContent: 'govMap',
              multiImg: _.get(form, "media.attachmentMultiImgConfig.govMap", "ONE"),
            }]
            : [],
          ),
          main1Photo:
            _.get(form, "media.main1Photo") &&
            !["map", "govMap"].includes(_.get(form, "media.main1Photo"))
              ? parseMedia(
                  _.concat(
                    [],
                    stockMediaData,
                    buildingMediaData,
                    streetMediaData,
                  ),
                  _.get(form, "media.main1Photo"),
                )
              : _.get(form, "media.main1Photo") === "map" ? {
                id: "map",
                mediumRoot: `lng:${_.get(form, "media.lng")},lat:${_.get(form, "media.lat")}`,
                photoContent: "map",
              }
              : _.get(form, "media.main1Photo") === "govMap" ? {
                id: "govMap",
                mediumRoot: `lng:${_.get(form, "media.govLng")},lat:${_.get(form, "media.govLat")}`,
                photoContent: 'govMap',
              }
              : null,
          mainPhoto:
            _.get(form, "media.mainPhoto") &&
            !["map", "govMap"].includes(_.get(form, "media.mainPhoto"))
              ? parseMedia(
                  _.concat(
                    [],
                    stockMediaData,
                    buildingMediaData,
                    streetMediaData,
                  ),
                  _.get(form, "media.mainPhoto"),
                )
              : _.get(form, "media.mainPhoto") === "map" ? {
                id: "map",
                mediumRoot: `lng:${_.get(form, "media.lng")},lat:${_.get(form, "media.lat")}`,
                photoContent: "map",
              }
              : _.get(form, "media.mainPhoto") === "govMap" ? {
                id: "govMap",
                mediumRoot: `lng:${_.get(form, "media.govLng")},lat:${_.get(form, "media.govLat")}`,
                photoContent: 'govMap',
              }
              : null,
        }),
    // =========================================

    building,

    ...(sbu === "COMM"
      ? {
          stockType: {
            ...(_.get(form, "stock.stockType.0") || {}),
            value: {
              nameZh: _.get(stock, "stockType.nameZh", "---"),
              nameEn: _.get(stock, "stockType.nameEn", "---"),
            },
          },
          floorInChinese: _.get(form, "stock.floor.0.value", ""),
          isMgtFeeOpenAC: _.get(form, "stock.isMgtFeeOpenAC.0.isShow"),
          videoUrl: _.get(form, "stock.videoUrl.0"),
          currentState: {
            value: _.defaultTo(
              findZhEnFromOptions(
                currentStates || [],
                _.get(form, "stock.currentState.0.value"),
              ),
              null,
            ),
            isShow: _.get(form, "stock.currentState.0.isShow"),
          },
        }
      : {
          ceilingHeight: {
            isShow: _.get(form, "stock.ceilingHeight.0.isShow") || false,
            ft: parseInt(_.get(form, "stock.ceilingHeight.0.ft", 0), 10) || 0,
            in: parseInt(_.get(form, "stock.ceilingHeight.0.in", 0), 10) || 0,
          },
        }),

    ...(sbu === "IND"
      ? {
          stockType: {
            ...(_.get(form, "stock.stockType.0") || {}),
            value: {
              nameZh: _.get(stock, "stockType.nameZh", "---"),
              nameEn: _.get(stock, "stockType.nameEn", "---"),
            },
          },
          currentState: {
            value: _.defaultTo(
              findZhEnFromOptions(
                currentStates || [],
                _.get(form, "stock.currentState.0.value"),
              ),
              null,
            ),
            isShow: _.get(form, "stock.currentState.0.isShow"),
          },
          acFee: _.get(form, "stock.acFee.0"),
          gRent: _.get(form, "stock.gRent.0"),
          rates: _.get(form, "stock.rates.0"),
          areaTerrace: _.get(form, "stock.areaTerrace.0"),
          areaRoof: _.get(form, "stock.areaRoof.0"),
          floorLoading: _.get(form, "stock.floorLoading.0"),
        }
      : {}),

    ...(sbu === "SHOPS"
      ? {
          stockType: {
            ...(_.get(form, "stock.stockType.0") || {}),
            value: {
              nameZh: _.get(stock, "stockType.nameZh", "---"),
              nameEn: _.get(stock, "stockType.nameEn", "---"),
            },
          },
          suggestedAvgPrice: {
            ..._.get(form, "stock.suggestedAvgPrice.0"),
            value: _.get(form, "stock.suggestedAvgPrice.0.value", 0),
          },
          suggestedTotalPrice: {
            ..._.get(form, "stock.suggestedTotalPrice.0"),
            value: _.get(form, "stock.suggestedTotalPrice.0.value", 0),
          },
          suggestedAvgRent: {
            ..._.get(form, "stock.suggestedAvgRent.0"),
            value: _.get(form, "stock.suggestedAvgRent.0.value", 0),
          },
          suggestedTotalRent: {
            ..._.get(form, "stock.suggestedTotalRent.0"),
            value: _.get(form, "stock.suggestedTotalRent.0.value", 0),
          },

          floorInChinese: _.get(stock, "floorInChinese", ""),
          customBuilding: _.get(form, "stock.customBuilding.0"),
          customAddressZh: _.get(form, "stock.customAddressZh"),
          customAddressEn: _.get(form, "stock.customAddressEn"),
          customStreet:
            _.get(stock, `streets.${streetType}.street.nameEn`) || "",
          customStreetNo: _.get(stock, `streets.${streetType}].number`) || "",
          streetNameZh: _.get(stock, `streets[0].street.nameZh`, ""),
          streetNameEn: _.get(stock, `streets[0].street.nameEn`, ""),
          streetNo: _.get(stock, `streets[0].number`, ""),

          districtNameZh: _.get(stock, "district.nameZh", ""),
          districtNameEn: _.get(stock, "district.nameEn", ""),

          areas: _.concat(
            _.get(form, "stock.areas"),
            _.get(form, "stock.area"),
          ).map((v) => ({
            ...v,
            gross: v.gross || 0,
            net: v.net || 0,
          })),
          entranceWidth: {
            isShow: _.get(form, "stock.entranceWidth.0.isShow") || false,
            ft: parseInt(_.get(form, "stock.entranceWidth.0.ft", 0), 10) || 0,
            in: parseInt(_.get(form, "stock.entranceWidth.0.in", 0), 10) || 0,
          },
          unitDepth: {
            isShow: _.get(form, "stock.unitDepth.0.isShow") || false,
            ft: parseInt(_.get(form, "stock.unitDepth.0.ft", 0), 10) || 0,
            in: parseInt(_.get(form, "stock.unitDepth.0.in", 0), 10) || 0,
          },
          currentState: {
            value: _.defaultTo(
              findZhEnFromOptions(
                currentStates || [],
                _.get(form, "stock.currentState.0.value"),
              ),
              null,
            ),
            isShow: _.get(form, "stock.currentState.0.isShow"),
          },
          yield: _.get(form, "stock.yield.0"),
          advanceTenants: transformTenantData(_.get(form, "stock.advanceTenants", [])),
          previousTenants: transformTenantData(_.get(form, "stock.previousTenants", [])),
          formerTenants: transformTenantData(_.get(form, "stock.formerTenants", [])),
        }
      : {
          areaEfficiency: _.get(form, "stock.areaEfficiency.0"),
          areaGross: _.get(form, "stock.areaGross.0"),
          areaNet: _.get(form, "stock.areaNet.0"),
          areaSaleable: _.get(form, "stock.areaSaleable.0"),
          areaLettable: _.get(form, "stock.areaLettable.0"),

          floorType: getFloorTypeInfoFromLangFile(
            _.get(form, "stock.floorType"),
          ),

          districtNameZh: _.get(stock, "building.district.nameZh", ""),
          districtNameEn: _.get(stock, "building.district.nameEn", ""),
          streetNameZh: _.get(stock, "building.street.street.nameZh", ""),
          streetNameEn: _.get(stock, "building.street.street.nameEn", ""),
          streetNo: _.get(stock, "building.street.number", ""),

          availability: _.get(form, "stock.availability.0"),

        decoration: sbu === "COMM" ? {
          value: {
            nameZh: `${mode === 'list' ? '裝修：' : '' }${_.map(_.get(form, "stock.decoration.0.value", []), v => v?.value?.nameZh).join('、')}` || "",
              nameEn: `${mode === 'list' ? 'Decoration: ' : '' }${_.map(_.get(form, "stock.decoration.0.value", []), v => v?.value?.nameEn).join(', ')}` || ""
            },
            isShow: _.get(form, "stock.decoration.0.isShow", false),
          } : {
            value: _.defaultTo(
              findZhEnFromOptions(
                decorations || [],
                _.get(form, "stock.decoration.0.value"),
              ),
              null,
            ),
            isShow: _.get(form, "stock.decoration.0.isShow"),
          },
        unitView: sbu === "COMM" ? {
          value: {
            nameZh: `${mode === 'list' ? '景觀：' : '' }${_.map(_.get(form, "stock.unitView.0.value", []), v => v?.value?.nameZh).join('、')}` || "",
              nameEn: `${mode === 'list' ? 'View: ' : '' }${_.map(_.get(form, "stock.unitView.0.value", []), v => v?.value?.nameEn).join(', ')}` || ""
            },
            isShow: _.get(form, "stock.unitView.0.isShow", false),
          } : {
          value: _.defaultTo(
            findZhEnFromOptions(
              unitViews || [],
              _.get(form, "stock.unitView.0.value"),
            ),
            null,
          ),
          isShow: _.get(form, "stock.unitView.0.isShow"),
        },
          yield: {
            ..._.get(form, "stock.yield.0", {}),
            value: parseFloat(_.get(form, "stock.yield.0.value") || "0"),
          },
        }),
  };

console.log('[ parsedForm ] >', parsedForm)
  return parsedForm;
};

export const parseFormToListPP = (formValue, otherProps = {}) => {
  const { stocks, ...others } = otherProps;
  const { stocks: stocksForm, general, order } = formValue;

  const parsedStockForms = order.reduce((forms, stockId) => {
    const form = stocksForm[stockId];

    if (!_.get(form, "stock.show")) return forms;

    return [
      ...forms,
      parseProposalRecord(
        form,
        stocks.find((s) => s._id === stockId),
        others,
        // can have different print mode for list pp
        // (already implemented in desktop)
        general.mode,
      ),
    ];
  }, []);

  const result = {
    ...general,
    proposals: parsedStockForms,
    ...({
      showEmployeePhoto: _.get(general, "showEmployeePhoto[0].isShow"),
      showContact: _.get(general, "showContact[0].isShow"),
      showUnit: _.get(general, "showUnit[0].isShow"),
      showMainPhoto: _.get(general, "showMainPhoto[0].isShow"),
      showPossession: _.get(general, "showPossession[0].isShow"),
      showTenancy: _.get(general, "showTenancy[0].isShow"),
      showCurrentState: _.get(general, "showCurrentState[0].isShow"),
    }),
    sbu,
  };

  // delete result.exactFloor
  delete result.showBuilding

  return result;
};
