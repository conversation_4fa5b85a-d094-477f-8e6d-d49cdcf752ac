import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import PlayArrowIcon from "@material-ui/icons/PlayArrow";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    fontSize: "1.125em",
    // fontWeight: "700"
  },
  label: {
    textAlign: "left",
    marginRight: "2vw",
    flex: 1,
    display: "flex",
    alignItems: "center",
  },
  icon: {
    fontSize: "1em",
    marginBottom: "5px"
    // marginLeft: "1vw"
  },
  increasing: {
    color: "rgba(255, 255, 255, 0.75)",
    transform: "translateY(3px) rotate(-90deg)"
    // paddingBottom: "0.2em"
  },
  decreasing: {
    color: "rgba(255, 255, 255, 0.75)",
    transform: "translateY(0px) rotate(90deg)"
    // paddingTop: "0.2em"
  },
  unchanged: {},
  pricerentTagText: {
    display: "flex"
  }
};

function VaryingVal(props) {
  const { classes, children, type, className, label, arrowPos, ...other } = props;

  let arrow = null;
  if (type == "increasing" || type == "up")
    arrow = <PlayArrowIcon className={`${classes.icon} ${classes.increasing}`} />;
  else if (type == "decreasing" || type == "down")
    arrow = <PlayArrowIcon className={`${classes.icon} ${classes.decreasing}`} />;

  return (
    <div className={`${classes.root} ${className}`}>
      <div className={classes.label}>
        <div>{label}</div>
        {arrowPos == "label" && arrow}
      </div>
      <div className={classes.pricerentTagText}>
        {arrowPos != "label" && arrow}
        {children}
      </div>
    </div>
  );
}

VaryingVal.propTypes = {
  children: PropTypes.node,
  classes: PropTypes.object.isRequired,
  type: PropTypes.string,
  className: PropTypes.string,
  label: PropTypes.string,
  arrowPos: PropTypes.string
};

export default withStyles(styles)(VaryingVal);
