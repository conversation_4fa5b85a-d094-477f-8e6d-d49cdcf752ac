import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import FieldVal from "../../../../common/FieldVal";
import { injectIntl } from "react-intl";

const styles = theme => ({
  root: {
    padding: "1vh 0"
  },
  gridContent: {
    padding: "1vw 2vw"
  }
});

class Remarks extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;

    let remarkMapping = {
      surroundings: intl.formatMessage({
        id: "stock.surroundings"
      }),
      landlordProvisions: intl.formatMessage({
        id: "stock.landlordprovisions"
      }),
      internal: intl.formatMessage({
        id: "stock.internalremarks"
      })
    };
    const remarks = detail.remarks || {};

    return (
      <div className={classes.root}>
        <DetailBoxSection
          text={intl.formatMessage({
            id: "stock.remarks"
          })}
          expandable={true}
        >
          <Grid container spacing={2} className={classes.gridContent}>
            {Object.keys(remarkMapping).map((v, i) => (
              <Grid item xs={12} key={v}>
                <FieldVal field={remarkMapping[v]}>
                  {remarks[v] || "---"}
                </FieldVal>
              </Grid>
            ))}
          </Grid>
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(Remarks));
