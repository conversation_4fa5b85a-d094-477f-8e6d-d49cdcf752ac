import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import Stroke from "./Stroke";
import FavoriteButton from "./FavoriteButton";
import SelectFieldArrayOutput from "./SelectFieldArrayOutput";
import { injectIntl } from "react-intl";
import MarkStockButton from "./MarkStockButton";
import { sbu } from "../../config";

// We can inject some CSS into the DOM.
const styles = {
  status: {
    fontSize: "1.5em",
    fontWeight: "700",
    textAlign: "right"
  },
  flex: {
    display: "flex",
  },
  usage: {
    padding: "0 2vw",
  },
  source: {
    borderRadius: 4,
    padding: "0 2vw",
    backgroundColor: "#FFFC00",
  },
  handSelect: {
    width: "30vw",
    "& > div": {
      margin: "8px 0",

    },
    "& > div > div > .MuiOutlinedInput-input": {
      padding: "10.5px 14px"
    }
  },
  strokeHidden: {
    visibility: "collapse"
  },
  myfavoriteIcon: {
    padding: "0 1vw"
  }
};

function DetailHandController(props) {
  const {
    classes,
    className,
    ranges,
    hand,
    handleChange,
    status,
    usageOrType, // COMM shows buildingUsage while IND shows stockType
    source,
    mongoid,
    stockid,
    favoriteStockIds,
    markStockIds,
    intl,
    currentHand
  } = props;

  const valueJson = {
    value: hand,
    label: hand == -1 ? intl.formatMessage({ id: "search.form.all" }) : hand + " " + intl.formatMessage({ id: "stock.hand" }),
  };

  const handSelect = (
    <SelectFieldArrayOutput
      ranges={ranges}
      input={{
        value: valueJson,
        onChange: handleChange,
        onBlur: () => { },
      }}
      syncValue={valueJson}
      meta={{}}
      className={classes.handSelect}
      isArrayOutput={false}
      isClearable={false}
    />
  );

  return (
    <Stroke
      className={className}
      left={sbu === "IND" ? handSelect : (<div > {currentHand + " " + intl.formatMessage({ id: "stock.hand" })} </div>)}
      classNameStroke={classes.strokeHidden}
      right={
        <div>
          {/* <div className={classes.status}>
            {status}
            {favoriteStockIds && mongoid && stockid && (
              <FavoriteButton
                favoriteStockIds={favoriteStockIds}
                mongoid={mongoid}
                stockid={stockid}
                isStockDetailPage={true}
                className={classes.myfavoriteIcon}
              />
            )}
            {markStockIds && mongoid && stockid && (
              <MarkStockButton checked={markStockIds.includes(mongoid)} stockId={mongoid} />
            )}
          </div> */}
          <div className={classes.flex}>
            <div className={classes.usage}>{usageOrType}</div>
            <div className={classes.source}>{source}</div>
          </div>
        </div>
      }
    />
  );
}

DetailHandController.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  ranges: PropTypes.array,
  hand: PropTypes.number,
  handleChange: PropTypes.func
};

export default withStyles(styles)(injectIntl(DetailHandController));
