import React from "react";
import PropTypes from "prop-types";
import OneBtnDialog from "./OneBtnDialog";
import { injectIntl } from "react-intl";

function ErrorsDialog(props) {
  const { errors, intl } = props;

  return (
    errors &&
    errors.length > 0 && (
      <OneBtnDialog
        open={true}
        handleClose={() => {
          /* dont let user close the dialog */
        }}
        btn={errors[0].btn}
        btnHandleClick={errors[0].btnHandleClick}
      >
        {intl.formatMessage({
          id: "common.error",
        })}
        : {errors[0].message}
      </OneBtnDialog>
    )
  );
}

ErrorsDialog.propTypes = {
  children: PropTypes.node,
  errors: PropTypes.array.isRequired,
};

export default injectIntl(ErrorsDialog);
