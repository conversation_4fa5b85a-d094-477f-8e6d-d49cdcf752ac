// @ts-nocheck
import React, {useMemo, useState, useEffect} from "react";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/core/styles";
import _ from "lodash";
import { connect, useSelector } from "react-redux";
import { getFormValues } from "redux-form";

import MediaMain from "../common/MediaMain";
import { staticMapUrl } from "../../helper/generalHelper";
import MediaSection from "../common/MediaMain/MediaSection";
import { generateGovMapImage } from "../../helper/generalHelper";
import { sbu } from "../../config";

const useStyles = makeStyles(() => ({
  sectionWrapper: {
    paddingTop: "1vh",
  },
}));

function MediaView({
  stockMedia,
  buildingMedia,
  streetMedia,
  coordinates,
  fieldNamePrefix = "",
  isProposal,
  isListProposal,
  stockId,
  groupSections,
  isGroup,
  hideTypeSelect,
  viewType,
  renderCustomButton,
}) {
  const classes = useStyles();
  const [govMapUrl, setGovMapUrl] = useState(""); // 添加新的状态
  const form = useSelector( state => getFormValues("proposal")(state))
  const [expandConfig, setExpandConfig] = useState({
    selected: false,
    stock: false,
    building: false,
    street: false
  })
  let selectedMedia = {
    photo: []
  }
  selectedMedia.photo = useMemo(()=> {
    // let selectedIds = []
    const photoArr = []
    // const preStr = isListProposal ? `stocks.${stockId}.`: ''
    const preStr = `stocks.${stockId}.`
    console.log('[ MediaView preStr ] >', preStr, _.get(form, `${preStr}media`))
    const {
      mainPhoto = '',
      main1Photo = '',
      selectedMedia: selectedIds = []
    } = _.cloneDeep(_.get(form, `${preStr}media`)) ?? {};
    console.log('[ MediaView mainPhoto ] >', preStr, mainPhoto, _.get(form, `${preStr}media`))
    if(mainPhoto) {
      if(selectedIds.includes(mainPhoto)){
        selectedIds.splice(selectedIds.indexOf(mainPhoto), 1)
      }
      selectedIds.unshift(mainPhoto)
    }
    if(main1Photo){
      if(selectedIds.includes(main1Photo)){
        selectedIds.splice(selectedIds.indexOf(main1Photo), 1)
      }
      selectedIds.splice(selectedIds.includes(mainPhoto) ? selectedIds.indexOf(mainPhoto) + 1 : 0, 0, main1Photo)
    }

    _.forEach(selectedIds, (ele) => {
      if(ele === "map" && coordinates && coordinates.lat && coordinates.lng){
        photoArr.push({
          id: "map",
          type: "photo",
          mediumRoot: staticMapUrl(
            _.get(coordinates, "lat", ""),
            _.get(coordinates, "lng", ""),
          ),
          originalFilename: "Google Map",
          approval: "approved",
          photoContent: "map"
        })
      } else if(ele === "govMap" && coordinates && coordinates.lat && coordinates.lng)   {
        photoArr.push({
          id: "govMap",
          type: "photo",
          mediumRoot: govMapUrl,
          originalFilename: "Government Map",
          approval: "approved",
          photoContent: "govMap"
        })
      } else {
        _.forEach([].concat(stockMedia?.video || [], stockMedia?.photo || [], buildingMedia?.video || [], buildingMedia?.photo || [], streetMedia?.video || [], streetMedia?.photo || []), (item) => {
          if(ele == item.id){
            photoArr.push(item)
          }
        })
      }
    })
    console.log('[ selectedMedia ] >', photoArr, selectedIds)
    return photoArr
  }, [form])

  useEffect(() => {
    let currentUrl = null;
    async function loadGovMapUrl() {
      const govMapUrl = await generateGovMapImage(
        coordinates.lng,
        coordinates.lat,
      );
      currentUrl = govMapUrl
      setGovMapUrl(govMapUrl)
    }
    if (coordinates && coordinates.lat && coordinates.lng) {  
      loadGovMapUrl()
    }
    return () => {
      if (currentUrl) {
        URL.revokeObjectURL(currentUrl);
      }
    };
  }, []);

  const expandChange = (field, expandType) => {
    setExpandConfig({
      selected: false,
      stock: false,
      building: false,
      street: false,
      [field]: expandType
    })
  }

  return (
    <>
      {(isProposal || isListProposal) &&
      <div className={classes.sectionWrapper}>
        <MediaMain
          media={selectedMedia}
          mediaType="selected"
          mediaPath={`${fieldNamePrefix}media.`}
          isProposal={isProposal}
          defaultExpand={expandConfig.selected}
          isListProposal={isListProposal}
          expandChange={expandChange}
          isGroup={isGroup}
          groupSections={groupSections}
          hideTypeSelect={hideTypeSelect}
          viewType={viewType}
          renderCustomButton={renderCustomButton}
        />
      </div>
      }
      <div className={classes.sectionWrapper}>
        <MediaMain
          media={stockMedia}
          mediaType="stock"
          mediaPath={`${fieldNamePrefix}media.`}
          isProposal={isProposal}
          defaultExpand={expandConfig.stock}
          isListProposal={isListProposal}
          expandChange={expandChange}
          isGroup={isGroup}
          groupSections={groupSections}
          hideTypeSelect={hideTypeSelect}
          viewType={viewType}
          renderCustomButton={renderCustomButton}
        />
      </div>
      {sbu !== "SHOPS" ? (
      <div className={classes.sectionWrapper}>
        <MediaMain
          media={buildingMedia}
          mediaType="building"
          mediaPath={`${fieldNamePrefix}media.`}
          isProposal={isProposal}
          defaultExpand={expandConfig.building}
          isListProposal={isListProposal}
          expandChange={expandChange}
          isGroup={isGroup}
          groupSections={groupSections}
          hideTypeSelect={hideTypeSelect}
          viewType={viewType}
          renderCustomButton={renderCustomButton}
        />
      </div>
      ) : (
      <div className={classes.sectionWrapper}>
        <MediaMain
          media={streetMedia}
          mediaType="street"
          mediaPath={`${fieldNamePrefix}media.`}
          isProposal={isProposal}
          defaultExpand={expandConfig.street}
          isListProposal={isListProposal}
          expandChange={expandChange}
          isGroup={isGroup}
          groupSections={groupSections}
          hideTypeSelect={hideTypeSelect}
          viewType={viewType}
          renderCustomButton={renderCustomButton}
        />
      </div>
      )}
      {isProposal && (
        <div className={classes.sectionWrapper}>
          <MediaSection
            media={
              coordinates && coordinates.lat && coordinates.lng
                ? [
                    {
                      id: "map",
                      type: "photo",
                      mediumRoot: staticMapUrl(
                        _.get(coordinates, "lat", ""),
                        _.get(coordinates, "lng", ""),
                      ),
                      originalFilename: "Google Map",
                      approval: "approved",
                      photoContent: "map"
                    },
                    {
                      id: "govMap",
                      type: "photo",
                      mediumRoot: govMapUrl,
                      originalFilename: "Government Map",
                      approval: "approved",
                      photoContent: "govMap"
                    }
                  ]
                : []
            }
            mediaType="map"
            hideTypeSelect
            isProposal
            deletable={false}
            mediaPath={`${fieldNamePrefix}media.`}
            proposalFieldName="googleMapPhoto"
            isListProposal={isListProposal}
            viewType={viewType}
            renderCustomButton={renderCustomButton}
          />
        </div>
      )}
    </>
  );
}

MediaView.defaultProps = {
  isProposal: false,
  isListProposal: false,
  groupSections: ['photo'],
  isGroup: true,
  hideTypeSelect: false,
  viewType: 'grid',
};

MediaView.propTypes = {
  stockMedia: PropTypes.object.isRequired,
  buildingMedia: PropTypes.object.isRequired,
  streetMedia: PropTypes.object.isRequired,
  coordinates: PropTypes.shape({
    lat: PropTypes.number,
    lng: PropTypes.number,
  }),
  isProposal: PropTypes.bool,
  defaultExpand: PropTypes.bool,
  stockId: PropTypes.string,
  /** 是否進行詳細分類, AWS-3937 文檔, VR/影片, 相片 */
  isGroup: PropTypes.bool,
  /** 控制显示哪些分组 */
  groupSections: PropTypes.arrayOf(
    PropTypes.oneOf(['document', 'kol_video', 'video', 'photo']),
  ),
  /** 是否隐藏类型选择器 */
  hideTypeSelect: PropTypes.bool,
  /** 视图类型：网格或列表 */
  viewType: PropTypes.oneOf(['grid', 'list']),
  renderCustomButton: PropTypes.func,
};

const mapStateToProps = (state) => ({
});

export default connect(mapStateToProps)(MediaView);
