import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import _ from "lodash";

import AreaInputItem from "./AreaInputItem";

const styles = {};

class AreaInputItems extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    className: PropTypes.string,
    formFields: PropTypes.array,
    changeFieldValue: PropTypes.func.isRequired,
    netDisabled: PropTypes.bool,
    grossDisabled: PropTypes.bool,
  };

  render() {
    const { classes, className, formFields, changeFieldValue, fields } =
      this.props;

    return (
      <div className={className}>
        {fields.map((v, i) => (
          <AreaInputItem
            key={v}
            name={v}
            areaNameZh={
              formFields[i] && formFields[i].areaName
                ? formFields[i].areaName
                : ""
            }
            areaNameEn={
              formFields[i] && formFields[i].areaNameEn
                ? formFields[i].areaNameEn
                : ""
            }
            changeFieldValue={changeFieldValue}
            netDisabled={this.props.netDisabled}
            grossDisabled={this.props.grossDisabled}
          />
        ))}
      </div>
    );
  }
}
export default withStyles(styles)(AreaInputItems);
