import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import Tab from "@material-ui/core/Tab";
import PlayArrowIcon from '@material-ui/icons/PlayArrow';

// We can inject some CSS into the DOM.
const styles = {
  on: {
    backgroundColor: "#FFD905",
    color: "rgba(0, 0, 0, .6)",
    whiteSpace: "nowrap",
  },
  sortOrderIconAsc: {
    transform: "rotate(-90deg)"
  },
  sortOrderIconDesc: {
    transform: "rotate(90deg)"
  }
};

function SortingTab(props) {
  const { classes, order, field, ...other } = props;

  let tab = <Tab {...other} />;
  if (field === "PP Stock" && order) {
    tab = <Tab className={classes.on} {...other} />
  } else if (order === "ASC") {
    tab = <Tab className={classes.on} icon={<PlayArrowIcon className={classes.sortOrderIconAsc} />} {...other} />
  } else if (order === "DESC") {
    tab = <Tab className={classes.on} icon={<PlayArrowIcon className={classes.sortOrderIconDesc} />} {...other} />
  }
  return tab;
}

SortingTab.propTypes = {
  classes: PropTypes.object.isRequired,
  order: PropTypes.string,
};

export default withStyles(styles)(SortingTab);
