export const LIST_MESSAGES_QUERY = `
  query ($limit: Int, $offset: Int, $sorter: [messageCenterSorter], $isDeleted: Boolean) {
    messageCenter(limit: $limit, offset: $offset, sort: $sorter, isDeleted: $isDeleted) {
      _id
      subject
      message
      recordOperation {
        createDateTime
        createBy {
          name_en
        }
      }
    }
  }
  `;

export const LIST_MESSAGES_PRIVATE_QUERY = `
query($limit: Int, $offset: Int, $sorter: [privateMessageSorter], $isDeleted: Boolean, $createBy: [String], $createDateTimeMin: String, $createDateTimeMax: String, $messageCat: [String]) {
  privateMessage(limit: $limit, offset: $offset, sort: $sorter, isDeleted: $isDeleted, createBy: $createBy, createDateTimeMin: $createDateTimeMin, createDateTimeMax: $createDateTimeMax, messageCat: $messageCat) {
    _id
    sbu
    subject
    message
    messageType
    messageCat
    stock
    recordOperation {
      createDateTime
      createBy {
        _id
        emp_id
        name_zh
        name_en
        dept_code
        nickname
      }
      deleteDate
      deleteBy {
        _id
        emp_id
        name_zh
        name_en
        dept_code
        nickname
      }
    }
  }
}
`;