export const CREATE_LANDSEARCH_DETAIL = `
      mutation($details: DetailsCreate!){
        createDetails(details: $details){
          _id
          stock {
            _id
            sbu
            floor
            unit
            street {
              street {
                _id
                sbu
                nameZh
                nameEn
              }
              number
            }
            building {
              _id
              sbu
              nameZh
              nameEn
              street {
                street {
                  _id
                  sbu
                  nameZh
                  nameEn
                }
                number
              }
              district {
                _id
                sbu
                nameZh
                nameEn
                abbr
              }
            }
          }
          searchType
          landSearch
          applicant {
            emp_id
            name_en
            name_zh
          }
          contactPhone
          memorialNumber
          filmType
          ddType
          ddNumber
          occupationPermitType
          floor
          unit
          item
          team
          shopNumber
          createDate
          lastUpdateDate
          lastUpdateBy
          isDeleted
        }
      }
    `;

export const APPLY_COMPANY_SEARCH = `
  mutation($details: CompanySearchCreate!) {
    applyCompanySearch(details: $details) {
      _id
      search_ref_no
    }
  }`;
