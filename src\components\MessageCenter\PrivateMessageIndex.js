import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import List from "./List";
import LoadingOverlay from "../LoadingOverlay";


const styles = theme => ({
  root: {
    padding: "1vh 2vw",
  },
  notFound: {
    margin: "1em 0",
    textAlign: "center",
    fontWeight: "bold"
  },
});

class PrivateMessageIndex extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {
    const { classes, messages, listed, listing } = this.props;

    const hasData = messages.length > 0;

    return (
      <div className={classes.root}>
        {listed && hasData && <List messages={messages} messageType="private" />}

        {(listing || !listed) && <LoadingOverlay />}

        {!listing && listed && !hasData && (
          <div className={classes.notFound}>
            Message not found
          </div>
        )}
      </div>
    );
  }
}

const mapStateToProps = state => ({
  messages: state.messageCenter.privateMessages ? state.messageCenter.privateMessages : [],
  listed: state.messageCenter.privateListed ? state.messageCenter.privateListed : false,
  listing: state.messageCenter.privateListing ? state.messageCenter.privateListing : false
});

export default connect(mapStateToProps)(withStyles(styles)(PrivateMessageIndex));
