/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { submit, getFormValues, isDirty } from "redux-form";
import withStyles from "isomorphic-style-loader/lib/withStyles";
import _ from "lodash";
import querystring from "querystring";
import { FormattedMessage, injectIntl } from "react-intl";
import TextField from "@material-ui/core/TextField";
import { listUnitViews, listFloorOrdering } from "@/actions/building";
import {
  clearStockList,
  listDecorations,
  listPossessions,
  getHistoryField,
  saveSearch,
  clearSaveSearch,
  listSearch,
  unlockStock,
} from "@/actions/stocklist";
import { listDistricts } from "@/actions/district";
import { listmyFavorite, removeMarkStocks } from "@/actions/stock";
import SearchForm from "@/components/mobile/COMM/Search/SearchForm";
import StockList from "@/components/mobile/COMM/StockList";
import Layout from "@/components/Layout";
import { getSearchQueryString, goToSearchResult } from "@/helper/generalHelper";
import SubmitDialog from "@/components/common/SubmitDialog";
import SelectField from "@/components/common/SelectField";
import BottomButtons from "@/components/common/BottomButtons";
import ConfirmDialog from "@/components/common/ConfirmDialog";
import history from "@/core/history";
import UnlockDialogItem from "@/components/common/UnlockDialogItem";
import s from "./Search.css";

class SearchPage extends React.Component {
  static propTypes = {
    listDistricts: PropTypes.func.isRequired,
    listDecorations: PropTypes.func.isRequired,
    listPossessions: PropTypes.func.isRequired,
    markStockIds: PropTypes.array.isRequired,
    removeMarkStocks: PropTypes.func.isRequired,
    unlockedStockIds: PropTypes.array.isRequired,
    unlockStocks: PropTypes.func.isRequired,
    clearSearchResult: PropTypes.func.isRequired,
    isFormDirty: PropTypes.bool.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      offset: 0,
      selectedData: this.props.selectedData || {},
      expanded: !!this.props.anchorToConsolidate,
      isFirstFetch: true,
      appIsMounted: false,
      init: null,
      saveDialogOpen: false,
      searchCriteriaName: "",
      loadDialogOpen: false,
      loadedSearch: "",
      retainMarkStocks: true,
      retainMarkStocksConfirmOpen: false,
      clearMarkPPStockConfirmOpen: false,
      unlockedstockidsunlockDialogOpen: false,
    };
  }

  componentDidMount() {
    requestAnimationFrame(() => {
      this.setState({ appIsMounted: true });
    });
    this.props.getHistoryField();
    this.props.listmyFavorite();
    if (_.isEmpty(this.props.districts)) {
      // districts is null
      this.props.listDistricts();
    }
    if (_.isEmpty(this.props.decoration)) {
      // decoration is null
      this.props.listDecorations();
    }
    if (_.isEmpty(this.props.possession)) {
      // possession is null
      this.props.listPossessions();
    }
    if (_.isEmpty(this.props.unitview)) {
      // unitview is null
      this.props.listUnitViews();
    }
    if (_.isEmpty(this.props.searches)) {
      // searches is null
      this.props.listSearch();
    }
    if (_.isEmpty(this.props.floorOrdering)) {
      // searches is null
      this.props.listFloorOrdering();
    }

    if (this.props.anchorToConsolidate) {
      // need a little timeout to let it scroll to the bottom or it will scroll to somewhere above
      setTimeout(() => {
        const scrollPx = document.body.scrollHeight;
        window.scroll({
          top: scrollPx,
          behavior: "smooth",
        });
      }, 100);
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.selectedData !== this.props.selectedData) {
      this.setState({
        selectedData: this.props.selectedData,
        isFirstFetch: true,
      });
    }
    if (prevProps.searches !== this.props.searches) {
      const searchItem = this.props.searches.filter(
        (v) => v.name === this.state.searchCriteriaName,
      )[0];
      if (searchItem) {
        this.setState({ loadedSearch: searchItem._id, searchCriteriaName: "" });
      }
    }
  }

  submit = async (values) => {
    this.setState({ expanded: false });
    if (!this.state.retainMarkStocks) {
      this.props.removeMarkStocks();
      this.setState({ retainMarkStocks: true });
    }
    values.limit = 50;
    values.offset = 0;
    values = _.pickBy(values, (v) => v !== "");

    if (this.props.isFormDirty) {
      await this.props.clearSearchResult();
    }

    goToSearchResult(values, this.state.selectedData, true);
  };

  setSelectedData = (field, selectedData) => {
    this.setState({
      selectedData: {
        ...this.state.selectedData,
        [field]: selectedData ?? null,
      },
    });
  };

  toggleRetainMarkStock = (e) => {
    if (!e.target.checked) {
      // need to confirm user behavior
      this.setState({
        retainMarkStocksConfirmOpen: true,
      });
    } else {
      this.setState({
        retainMarkStocks: true,
      });
    }
  };

  clearSelectedData = (excludedValue = {}) => {
    this.setState({ selectedData: { ...excludedValue } });
  };

  toggleContent = () => {
    this.setState({ expanded: !this.state.expanded, isFirstFetch: false });

    // go to back last query when the form is collapsed
    if (this.state.expanded == true) {
      this.setState({
        selectedData: this.props.selectedData,
      });
      this.state.init(this.props.queryvariablesFromUrl);

      this.setState({ searchCriteriaName: "", loadedSearch: "" });
    }
    // window.scrollTo(0, 0);
  };

  onFormInit = (init) => {
    !this.state.init && this.setState({ init });
  };

  handleOpenSaveDialog = () => {
    this.setState({ saveDialogOpen: true });
  };

  handleCloseSaveDialog = () => {
    this.setState({ saveDialogOpen: false });
    this.props.clearSaveSearch();
  };

  handleNameChange = (e) => {
    this.setState({ searchCriteriaName: e.target.value });
  };

  submitSaveDialog = async () => {
    const query = getSearchQueryString(
      this.props.formFields,
      this.state.selectedData,
    );
    // console.log(querystring.parse(query));

    // await saveSearch to finish and the reducer will update searches list automatically
    await this.props.saveSearch({
      name: this.state.searchCriteriaName,
      query,
    });

    // searches list has already been updated here. we can find the upserted/modified item
    // const searchItem = this.props.searches.filter(
    //   (v) => v.name === this.state.searchCriteriaName,
    // )[0];
    // this.setState({ loadedSearch: searchItem._id, searchCriteriaName: "" });
  };

  handleOpenLoadDialog = () => {
    this.setState({ loadDialogOpen: true });
  };

  handleCloseLoadDialog = () => {
    this.setState({ loadDialogOpen: false });
  };

  handleLoadChange = (v) => {
    this.setState({ loadedSearch: v.target.value });
  };

  submitLoadDialog = () => {
    const searchItem = this.props.searches.filter(
      (v) => v._id === this.state.loadedSearch,
    )[0];

    const query = querystring.parse(searchItem.query);
    const queryvariables = JSON.parse(query.param);
    const selectedData = JSON.parse(query.selectedData);

    this.setState({ selectedData });
    this.state.init(queryvariables);

    this.setState({ searchCriteriaName: searchItem.name });
    this.handleCloseLoadDialog();
  };

  goToListProposal = (mode) => {
    history.push(
      `/stock?mode=${mode}&ids=${encodeURIComponent(
        JSON.stringify(this.props.markStockIds),
      )}&defaultTab=3`,
    );
  };

  render() {
    const {
      queryvariablesFromUrl,
      isAdvanced,
      selectedData: selectedDataFromUrl,
      savingSearch,
      savedSearch,
      saveSearchError,
      searches,
      upsertedSearch,
      markStockIds,
      unlockedStockIds,
      removeMarkStocks,
      intl,
    } = this.props;
    const {
      selectedData,
      saveDialogOpen,
      searchCriteriaName,
      loadDialogOpen,
      loadedSearch,
      retainMarkStocks,
      retainMarkStocksConfirmOpen,
      clearMarkPPStockConfirmOpen,
      unlockDialogOpen,
      mode,
    } = this.state;

    const loadedSearchOptions = searches.map((v) => ({
      value: v._id,
      label: v.name,
    }));

    const bottomButtons = [
      {
        label: intl.formatMessage({
          id: "proposal.listProposal.clearPPStocks",
        }),
        onClick: () => {
          this.setState({ clearMarkPPStockConfirmOpen: true });
        },
      },
      {
        label: intl.formatMessage({ id: "proposal.create" }),
        onClick: () => {
          if (!_.isEmpty(markStockIds)) {
            // if not every mark stock is unlocked
            if (
              !_.every(
                markStockIds.map((id) => unlockedStockIds.includes(id)),
                Boolean,
              )
            ) {
              this.setState({ unlockDialogOpen: true, mode: "list" });
            } else {
              this.goToListProposal("list");
            }
          }
        },
      },
      // {
      //   label: intl.formatMessage({ id: "proposal.indvProposal.create" }),
      //   onClick: () => {
      //     if (!_.isEmpty(markStockIds)) {
      //       // if not every mark stock is unlocked
      //       if (
      //         !_.every(
      //           markStockIds.map((id) => unlockedStockIds.includes(id)),
      //           Boolean,
      //         )
      //       ) {
      //         this.setState({ unlockDialogOpen: true, mode: "indv" });
      //       } else {
      //         this.goToListProposal("indv");
      //       }
      //     }
      //   },
      // },
    ];

    return (
      <div>
        {this.state.appIsMounted && (
          <Layout
            path="search"
            header={<FormattedMessage id="home.search" />}
            isAdvanced
            toggleContent={this.toggleContent}
            isExpanded={this.state.expanded}
            isSticky
          >
            <div className={this.state.expanded ? s.root : s.expandedroot}>
              <SearchForm
                onSubmit={this.submit}
                selectedData={selectedData}
                setSelectedData={this.setSelectedData}
                clearSelectedData={this.clearSelectedData}
                initialValues={queryvariablesFromUrl}
                expanded={this.state.expanded}
                fromChildToParentCallback={this.onFormInit}
                handleSaveClick={this.handleOpenSaveDialog}
                handleLoadClick={this.handleOpenLoadDialog}
                retainMarkStocks={retainMarkStocks}
                handleRetainMarkStocksClick={this.toggleRetainMarkStock}
              />
            </div>

            <StockList
              queryvariablesFromUrl={queryvariablesFromUrl}
              selectedDataFromUrl={selectedDataFromUrl}
              isAdvanced={isAdvanced}
              expanded={this.state.expanded}
              isFirstFetch={this.state.isFirstFetch}
            />

            {!this.state.expanded && <BottomButtons buttons={bottomButtons} />}

            <ConfirmDialog
              open={retainMarkStocksConfirmOpen}
              onOkCallback={() => this.setState({ retainMarkStocks: false })}
              handleClose={() =>
                this.setState({ retainMarkStocksConfirmOpen: false })
              }
            >
              <FormattedMessage id="search.checkbox.uncheckMarkedRecord" />
            </ConfirmDialog>

            <ConfirmDialog
              open={clearMarkPPStockConfirmOpen}
              onOkCallback={_.isEmpty(markStockIds) ? null : removeMarkStocks}
              handleClose={() =>
                this.setState({ clearMarkPPStockConfirmOpen: false })
              }
            >
              {_.isEmpty(markStockIds) ? (
                <FormattedMessage id="proposal.listProposal.emptyMarkStocks" />
              ) : (
                <FormattedMessage id="proposal.listProposal.clearPPStocksConfirm" />
              )}
            </ConfirmDialog>

            <UnlockDialogItem
              dialogOpen={unlockDialogOpen}
              handleCloseDialog={() =>
                this.setState({ unlockDialogOpen: false })
              }
              stockid={markStockIds.filter(
                (id) => !unlockedStockIds.includes(id),
              )}
              unlock4ProposalCb={this.goToListProposal}
              mode={mode}
            />

            <SubmitDialog
              dialogOpen={saveDialogOpen}
              handleCloseDialog={this.handleCloseSaveDialog}
              submitting={savingSearch}
              submitted={savedSearch}
              error={saveSearchError}
              submit={this.submitSaveDialog}
              submitBtnText={intl.formatMessage({ id: "search.button.save" })}
              succMsg={
                upsertedSearch
                  ? intl.formatMessage({ id: "search.save.savesuccess" })
                  : intl.formatMessage({ id: "search.save.modifysuccess" })
              }
            >
              <TextField
                label={intl.formatMessage({ id: "search.save.name" })}
                value={searchCriteriaName}
                onChange={this.handleNameChange}
                variant="outlined"
                fullWidth
              />
            </SubmitDialog>

            <SubmitDialog
              dialogOpen={loadDialogOpen}
              handleCloseDialog={this.handleCloseLoadDialog}
              submitting={false}
              submitted={false}
              error={""}
              submit={this.submitLoadDialog}
              submitBtnText={intl.formatMessage({ id: "search.button.load" })}
            >
              <SelectField
                label={intl.formatMessage({ id: "search.save.name" })}
                ranges={loadedSearchOptions}
                input={{
                  value: loadedSearch,
                  onChange: this.handleLoadChange,
                }}
                meta={{}}
                variant="outlined"
                fullWidth
              />
            </SubmitDialog>
          </Layout>
        )}
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  queryvariables: state.stocklist.queryvariables
    ? state.stocklist.queryvariables
    : {},
  districts: state.district.districts ? state.district.districts : [],
  decoration: state.stocklist.decoration ? state.stocklist.decoration : [],
  possession: state.stocklist.possession ? state.stocklist.possession : [],
  unitview: state.building.unitview ? state.building.unitview : [],
  floorOrdering: state.building.floorOrdering ? state.building.floorOrdering : [],
  savingSearch: state.stocklist.savingSearch
    ? state.stocklist.savingSearch
    : false,
  savedSearch: state.stocklist.savedSearch
    ? state.stocklist.savedSearch
    : false,
  saveSearchError: state.stocklist.saveSearchError
    ? state.stocklist.saveSearchError
    : null,
  searches: state.stocklist.searches ? state.stocklist.searches : [],
  upsertedSearch: state.stocklist.upsertedSearch
    ? state.stocklist.upsertedSearch
    : false,
  formFields: getFormValues("searchForm")(state),
  markStockIds: _.get(state, "stock.markStockIds") || [],
  unlockedStockIds: _.get(state, "stocklist.unlockedStockIds") || [],
  isFormDirty: isDirty("searchForm")(state),
});

const mapDispatchToProps = (dispatch) => ({
  dispatchSubmitForm: () => dispatch(submit("searchForm")),
  listDistricts: () => dispatch(listDistricts()),
  listDecorations: () => dispatch(listDecorations()),
  listPossessions: () => dispatch(listPossessions()),
  listUnitViews: () => dispatch(listUnitViews()),
  listFloorOrdering: () => dispatch(listFloorOrdering()),
  getHistoryField: () => dispatch(getHistoryField()),
  listmyFavorite: () => dispatch(listmyFavorite()),
  saveSearch: (...args) => dispatch(saveSearch(...args)),
  clearSaveSearch: () => dispatch(clearSaveSearch()),
  listSearch: (...args) => dispatch(listSearch(...args)),
  removeMarkStocks: () => dispatch(removeMarkStocks()),
  clearSearchResult: () => dispatch(clearStockList(false)),
  unlockStocks: (ids) => dispatch(unlockStock(ids)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(s)(injectIntl(SearchPage)));
