import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import { convertNewlineToBr, getLangKey } from "../../../../../helper/generalHelper";
import FieldVal from "../../../../common/FieldVal";
import { injectIntl } from "react-intl";

const styles = (theme) => ({
  freeText: {
    color: "#4B4B4B",
    fontSize: "0.875em",
  },
  gridContent: {
    padding: "1vw 2vw",
  },
});

class ExtraInformation extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langkey = getLangKey(intl);
    const remarkLangkey = getLangKey(intl, "remark");

    const surroundings = detail.surroundings
      ? convertNewlineToBr(detail.surroundings)
      : "---";
    const remarks =
      detail[remarkLangkey]
        ? convertNewlineToBr(detail[remarkLangkey])
        : "---";

    let extrainfoMapping = {
      [intl.formatMessage({
        id: "building.surroundings",
      })]: { value: surroundings, xs: 12 },
      [intl.formatMessage({
        id: "stock.remarks",
      })]: { value: remarks, xs: 12 },
    };

    return (
      <DetailBoxSection
        expandable={true}
        isExpanding={true}
        text={intl.formatMessage({
          id: "building.extrainfo",
        })}
      >
        <Grid container spacing={2} className={classes.gridContent}>
          {Object.keys(extrainfoMapping).map((v, i) => (
            <Grid item xs={extrainfoMapping[v].xs} key={v}>
              <FieldVal field={v}>{extrainfoMapping[v].value}</FieldVal>
            </Grid>
          ))}
        </Grid>
        {/* <div className={classes.gridContent}>
          <DetailBoxSection
            text={intl.formatMessage({
              id: "building.extrainfo"
            })}
            noStrike={true}
          >
            <span className={classes.freeText}>{surroundings}</span>
          </DetailBoxSection>

          <DetailBoxSection
            text={intl.formatMessage({
              id: "building.liftzone"
            })}
            noStrike={true}
          >
            <span className={classes.freeText}>{liftZone}</span>
          </DetailBoxSection>

          <DetailBoxSection
            text={intl.formatMessage({
              id: "stock.remarks"
            })}
            noStrike={true}
          >
            <span className={classes.freeText}>{remarks}</span>
          </DetailBoxSection>
        </div> */}
      </DetailBoxSection>
    );
  }
}

export default withStyles(styles)(injectIntl(ExtraInformation));
