import React from "react";
import { Grid } from "@material-ui/core";
import { Field } from "redux-form";
import { makeStyles, withStyles } from "@material-ui/core/styles";
import clsx from "clsx";
import MuiInputLabel from "@material-ui/core/InputLabel";

import UnderlineSingleSelect from "./UnderlineSingleSelect";

const useStyles = makeStyles({
  multiInputContainer: {
    marginLeft: "-8px !important",
    marginRight: "-8px !important",
  },
  padInput: {
    paddingTop: 16,
  },
});

const InputLabel = withStyles({
  root: {
    transform: "translate(0, 1.5px) scale(0.75)",
    color: "rgba(0, 0, 0, 0.9)",
    fontSize: 17,
  },
  asterisk: {
    color: "red",
  },
})(MuiInputLabel);

function SelectMultiInput({
  label,
  selectName,
  selectXs,
  selectPosition,
  options,
  spacing,
  InputComponent,
  inputProps,
  required,
  ...custom
}) {
  const classes = useStyles();

  const renderSelect = () => (
    <Grid item xs={selectXs}>
      <Field
        name={selectName}
        component={UnderlineSingleSelect}
        type="select-multiple"
        options={options}
      />
    </Grid>
  );

  return (
    <div>
      {label && <InputLabel required={required}>{label}</InputLabel>}
      <Grid
        item
        container
        spacing={spacing}
        className={classes.multiInputContainer}
      >
        {selectPosition === "left" && renderSelect()}

        <Grid item xs={12 - selectXs}>
          <Field component={InputComponent} {...inputProps} />
        </Grid>

        {selectPosition === "right" && renderSelect()}
      </Grid>
    </div>
  );
}

export default SelectMultiInput;
