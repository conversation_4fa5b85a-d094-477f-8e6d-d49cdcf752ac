import React, { useMemo, useState, useEffect } from "react";
import PropTypes from "prop-types";
import { TextField } from "@material-ui/core";
import { injectIntl } from "react-intl";
import { connect, useDispatch } from "react-redux";
import { Field, formValueSelector, submit, change } from "redux-form";
import _ from "lodash";

import SubmitDialog from "../common/SubmitDialog";
import DialogFrame from "../common/DialogFrame";
import Dialog from "../common/Dialog";
import { goToProposalList } from "../../helper/generalHelper";
import { generalProposalQuota } from "../../config";

const renderTextField = ({ input, label, ...custom }) => (
  <TextField label={label} {...input} {...custom} />
);

function CreateProposalDialog({
  open,
  close,
  proposalName,
  createProposal,
  createdPdf,
  creatingProposal,
  createdProposal,
  createProposalError,
  intl,
}) {
  const dispatch = useDispatch();
  const [validationProposalName, setValidationProposalName] = useState({
    isFormValid: true,
    message: "",
  });

  useEffect(() => {
    if (/[\/:\\*?"<>|%&#]/.test(proposalName)) {
      setValidationProposalName({
        isFormValid: false,
        message: intl.formatMessage({
          id: "proposal.form.validation",
        }),
      });
    } else {
      setValidationProposalName({
        isFormValid: true,
        message: "",
      });
    }
  }, [proposalName]);

  const handleProposalNameChange = () => {
    dispatch(change("proposal", `general.useNameAsPdf`, true))
  };
  return generalProposalQuota <= createdPdf ? (
    <Dialog open={open} handleClose={close}>
      {intl.formatMessage(
        { id: "proposal.create.exceed" },
        { quota: generalProposalQuota },
      )}
      <DialogFrame
        buttonMain={intl.formatMessage({ id: "common.ok" })}
        handleMain={close}
      />
    </Dialog>
  ) : (
    <SubmitDialog
      dialogOpen={open}
      handleCloseDialog={close}
      submit={createProposal}
      succCallback={() => goToProposalList()}
      submitting={creatingProposal}
      submitted={createdProposal}
      error={createProposalError}
      submitBtnText={intl.formatMessage({ id: "proposal.form.save" })}
      succMsg={intl.formatMessage(
        { id: "proposal.form.savesuccess" },
        { filename: proposalName },
      )}
      mainButtonProps={
        validationProposalName.isFormValid
          ? { id: "createProposalButton" }
          : { id: "createProposalButton", disabled: true }
      }
    >
      <Field
        name="general.proposalName"
        label={intl.formatMessage({ id: "proposal.form.proposalname" })}
        component={renderTextField}
        variant="outlined"
        fullWidth
        onChange={handleProposalNameChange}
      />
      {!validationProposalName.isFormValid && (
        <p style={{ color: "#eff506", margin: "0", fontSize: "13px" }}>{validationProposalName.message}</p>
      )}
    </SubmitDialog>
  );
}

CreateProposalDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  close: PropTypes.func.isRequired,
  proposalName: PropTypes.string.isRequired,
  createProposal: PropTypes.func.isRequired,
  createdPdf: PropTypes.number.isRequired,
  creatingProposal: PropTypes.bool.isRequired,
  createdProposal: PropTypes.bool.isRequired,
  createProposalError: PropTypes.string,
  intl: PropTypes.object.isRequired,
};

const selector = formValueSelector("proposal");

const mapStateToProps = (state) => ({
  proposalName: selector(state, "general.proposalName"),
  createdPdf: _.get(state, "proposal.createdPdf.length", 0),
  creatingProposal:
    _.get(state, "proposal.creatingProposal") ||
    _.get(state, "listProposal.creatingListProposal"),
  createdProposal:
    _.get(state, "proposal.createdProposal") ||
    _.get(state, "listProposal.createdListProposal"),
  createProposalError:
    _.get(state, "proposal.createProposalError.message") ||
    _.get(state, "listProposal.createListProposalError.message"),
});

const mapDispatchToProps = (dispatch) => ({
  createProposal: () => dispatch(submit("proposal")),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(CreateProposalDialog));
