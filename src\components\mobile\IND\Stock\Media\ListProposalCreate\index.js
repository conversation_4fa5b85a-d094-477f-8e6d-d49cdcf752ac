import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { injectIntl } from "react-intl";
import { submit, change } from "redux-form";

import Dialog from "../../../../../common/Dialog";
import SubmitDialog from "../../../../../common/SubmitDialog";
import { generalProposalQuota } from "../../../../../../config";
import DialogFrame from "../../../../../common/DialogFrame";
import { clearCreateProposal } from "../../../../../../actions/proposal";
import { goToProposalList } from "../../../../../../helper/generalHelper";

function ListProposalCreate({
  exceedQuota,
  createProposalDialogOpen,
  handleCloseCreateProposalDialog,
  clearCreateProposal,
  creatingProposal,
  createdProposal,
  createProposalError,
  intl,
}) {
  const handleCloseSubmitDialog = () => {
    handleCloseCreateProposalDialog();
    clearCreateProposal();
  };
  return (
    <div>
      {exceedQuota ? (
        <Dialog
          open={createProposalDialogOpen}
          handleClose={handleCloseCreateProposalDialog}
        >
          {intl.formatMessage(
            { id: "proposal.create.exceed" },
            { quota: generalProposalQuota },
          )}
          <DialogFrame
            buttonMain={intl.formatMessage({ id: "common.ok" })}
            handleMain={handleCloseSubmitDialog}
          />
        </Dialog>
      ) : (
        <SubmitDialog
          dialogOpen={createProposalDialogOpen}
          handleCloseDialog={handleCloseSubmitDialog}
          succCallback={() => goToProposalList()}
          submitting={creatingProposal}
          submitted={createdProposal}
          error={createProposalError}
          submit={}
        ></SubmitDialog>
      )}
    </div>
  );
}

ListProposalCreate.propTypes = {
  exceedQuota: PropTypes.bool,
  createProposalDialogOpen: PropTypes.bool,
  handleCloseCreateProposalDialog: PropTypes.func.isRequired,
  clearCreateProposal: PropTypes.func.isRequired,
  creatingProposal: PropTypes.bool,
  createdProposal: PropTypes.bool,
  createProposalError: PropTypes.string,
  intl: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  creatingProposal: state.proposal.creatingProposal
    ? state.proposal.creatingProposal
    : false,
  createdProposal: state.proposal.createdProposal
    ? state.proposal.createdProposal
    : false,
  createProposalError:
    state.proposal.createProposalError &&
    state.proposal.createProposalError.message
      ? state.proposal.createProposalError.message
      : null,
});

const mapDispatchToProps = (dispatch) => {
  return {
    dispatchSubmitForm: () => dispatch(submit("listProposalForm")), //TODO: make action to combine all forms
    clearCreateProposal: (args) => dispatch(clearCreateProposal(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(ListProposalCreate));
