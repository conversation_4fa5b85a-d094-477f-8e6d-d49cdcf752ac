/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import InfiniteScroll from "react-infinite-scroll-component";


const styles = theme => ({

});

class List extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    list: PropTypes.array,
    fetchMoreData: PropTypes.func.isRequired,
    hasMore: PropTypes.bool,
    children: PropTypes.func,
  };

  render() {
    const {
      classes,
      list,
      fetchMoreData,
      hasMore,
      children,
    } = this.props;

    return (
      <div className={classes.root}>
        <InfiniteScroll
          dataLength={list.length}
          next={fetchMoreData}
          hasMore={hasMore}
          style={{ overflowY: "hidden" }}
          endMessage={
            <p style={{ textAlign: "center" }}>
              <b>No results found</b>
            </p>
          }
        >
          {list.map((v, i) =>
            children({ detail: v, key: i })
          )}
        </InfiniteScroll>
      </div>
    );
  }
}

export default withStyles(styles)(List);
