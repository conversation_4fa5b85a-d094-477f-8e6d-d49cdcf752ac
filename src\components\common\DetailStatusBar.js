import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import { injectIntl } from "react-intl";
import FavoriteButton from "./FavoriteButton";
import MarkStockButton from "./MarkStockButton";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
  },
  status: {
    fontSize: "1.5em",
    fontWeight: "700",
    textAlign: "right",
    padding: "0 0 0 2vw",
  },
  usage: {
    padding: "0 2vw",
  },
  source: {
    borderRadius: 4,
    padding: "0 2vw",
    backgroundColor: "#FFFC00",
  },
  hand: {
    padding: "0 1vw",
  },
  myfavoriteIcon: {
    padding: "0 1vw",
  },
};

function DetailStatusBar(props) {
  const {
    classes,
    className,
    currentHand,
    status,
    usageOrType, // COMM shows buildingUsage while IND shows stockType
    source,
    mongoid,
    stockid,
    favoriteStockIds,
    markStockIds,
    intl,
  } = props;

  return (
    <div>
      <div className={`${classes.root} ${className}`}>
        <div className={classes.status}>{status}</div>
        {favoriteStockIds && mongoid && stockid && (
          <FavoriteButton
            favoriteStockIds={favoriteStockIds}
            mongoid={mongoid}
            stockid={stockid}
            isStockDetailPage={true}
            className={classes.myfavoriteIcon}
          />
        )}
        {markStockIds && mongoid && stockid && (
          <MarkStockButton
            checked={markStockIds.includes(mongoid)}
            stockId={mongoid}
          />
        )}
      </div>
      <div className={`${classes.root} ${className}`}>
        <div className={classes.hand}>
          {currentHand + " " + intl.formatMessage({ id: "stock.hand" })}
        </div>
        <div className={classes.source}>{source}</div>
      </div>
      <div className={`${classes.root} ${className}`}>
        <div className={classes.usage}>{usageOrType}</div>
      </div>
    </div>
  );
}

DetailStatusBar.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  currentHand: PropTypes.number,
  usageOrType: PropTypes.string,
  status: PropTypes.string,
  source: PropTypes.string,
};

export default withStyles(styles)(injectIntl(DetailStatusBar));
