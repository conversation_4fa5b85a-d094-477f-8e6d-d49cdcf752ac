import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    minWidth: 48,
    color: "#888",
    fontSize: "0.8em",
    textAlign: "center",
    borderRadius: 4,
    padding: "1px",
    backgroundColor: "#E3E3E3",
    display: "inline-block",
    margin: 2,
  },
  on: {
    color: "#000",
    backgroundColor: "#FFD905",
  }
};

function PropertyTag(props) {
  const { classes, className, children, on,  ...other } = props;

  return (
    <div className={`${classes.root} ${className || (on ? classes.on : "")}`} {...other}>
      {children}
    </div>
  );
}

PropertyTag.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  children: PropTypes.node,
};

export default withStyles(styles)(PropertyTag);
