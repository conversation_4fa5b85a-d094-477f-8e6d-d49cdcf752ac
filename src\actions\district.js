import {
  LIST_DISTRICTS_START,
  LIST_DISTRICTS_SUCCESS,
  LIST_DISTRICTS_ERROR,
  CLEAR_DISTRICTS
} from "../constants/district";

export function listDistricts(variables) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest }
  ) => {
    dispatch({
      type: LIST_DISTRICTS_START,
      checkrefreshToken: true
    });
    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: { 
          "Content-Type": "application/json",
          "Authorization": getState().auth.user.oauth,
          "CAS-Authorization":  localStorage.getItem('casAccessToken')
        }//getState().auth.user.casAccessToken  }
      };

      const query = await getQuery("LIST_DISTRICTS_QUERY");

      const data = await universalRequest("/district/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: {
            sorter: [{ field: "nameEn", order: "ASC" }]
          },
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_DISTRICTS_SUCCESS,
        payload: {
          data
        }
      });
    } catch (error) {
      dispatch({
        type: LIST_DISTRICTS_ERROR,
        payload: {
          error
        }
      });
      // throw new Error(error);
    }
  };
}

export function clearDistricts() {
  return async dispatch => {
    dispatch({
      type: CLEAR_DISTRICTS
    });
  };
}
