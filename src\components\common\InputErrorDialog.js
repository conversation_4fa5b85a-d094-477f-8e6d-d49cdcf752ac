import React from "react";
import { Dialog, DialogContent, Typography } from "@material-ui/core";
import PropTypes from "prop-types";
import { FormattedMessage } from "react-intl";
import { makeStyles } from "@material-ui/core/styles";
import _ from "lodash";
import clsx from "clsx";

const useStyles = makeStyles({
  wrapper: {
    backgroundColor: "#E3E3E3",
  },
  error: {
    marginBottom: 12,
  },
  title: {
    fontWeight: 600,
    lineHeight: 1.6,
    fontSize: 17,
    marginBottom: 5,
  },
});

function InputErrorDialog({ open, handleClose, errors, className }) {
  const classes = useStyles();

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogContent className={clsx(classes.wrapper, className)}>
        {Object.keys(errors).map((errorKey) => (
          <div key={errorKey} className={classes.error}>
            <Typography className={classes.title}>
              <FormattedMessage id={errorKey} />
            </Typography>
            {_.map(_.get(errors, errorKey), (errorField) => (
              <Typography key={`${errorKey}_${errorField.fieldMsgId}`}>
                {_.get(errorField, "fieldMsgId") && (
                  <>
                    [<FormattedMessage id={errorField.fieldMsgId} />]
                  </>
                )}
                {_.get(errorField, "msgContent") && (
                  <>{errorField.msgContent}</>
                )}
              </Typography>
            ))}
          </div>
        ))}
      </DialogContent>
    </Dialog>
  );
}

InputErrorDialog.defaultProps = {
  className: "",
};

InputErrorDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  handleClose: PropTypes.func.isRequired,
  errors: PropTypes.object.isRequired,
  className: PropTypes.string,
};

export default InputErrorDialog;
