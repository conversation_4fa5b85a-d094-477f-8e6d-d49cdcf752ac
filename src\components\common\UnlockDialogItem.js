import React, { useState, useEffect } from "react";
import { connect } from "react-redux";
import { withStyles, makeStyles } from "@material-ui/core/styles";
import MuiDialogActions from "@material-ui/core/DialogActions";
import _ from "lodash";
import { injectIntl } from "react-intl";

import Dialog from "./Dialog";
import FormButton from "./FormButton";
import history from "../../core/history";
import { sbu } from "../../config";
import { unlockStock } from "../../actions/stocklist";
import { permissions, generalDailyQuota } from "../../config";

const DialogActions = withStyles((theme) => ({
  root: {
    margin: 0,
    padding: "2vh 0",
    display: "flex",
    justifyContent: "center",
  },
}))(MuiDialogActions);

const useStyles = makeStyles(() => ({
  root: {
    padding: "10vh 0",
  },
  font18: {
    fontSize: "1.125em",
    textAlign: "center",
  },
  buttonBack: {
    border: "none",
    flex: 1,
  },
  buttonMain: {
    fontSize: "1.125em",
    // color: "#000",
    // backgroundColor: "#27EEEE",
    // border: "none"
  },
  errorMsg: {
    color: "red",
  },
  quotatext: {
    fontSize: "1.5em",
    // lineHeight: "60px",
    fontWeight: "500",
  },
  quotanumber: {
    fontSize: "2em",
  },
}));

function UnlockDialogItem({
  stockid,
  count,
  max,
  dialogOpen,
  handleCloseDialog,
  stockunicornid,
  unlockfinished,
  defaultTab,
  unlockStock,
  unlock4ProposalCb,
  mode = "indv",
  intl,
}) {
  const classes = useStyles();
  const [reachedMax, setReachedMax] = useState(false);
  const [isClicked, setIsClicked] = useState(false);

  useEffect(() => {
    if (isClicked && unlockfinished) {
      if (unlock4ProposalCb) {
        unlock4ProposalCb(mode);
        handleCloseDialog();
      } else {
        const id = [stockid];
        history.push(
          "/stock?ids=" +
            encodeURIComponent(JSON.stringify(id)) +
            (defaultTab ? "&defaultTab=" + defaultTab : "") +
            (mode ? "&mode=" + mode : ""),
        );
      }
    }
  }, [unlockfinished]);

  const handleUnlockStockDetail = (e, stockid) => {
    e.preventDefault();
    setIsClicked(true);
    if (max - count <= 0) {
      setReachedMax(true);
    } else {
      unlockStock(stockid);
    }
  };

  let displaystockId =
    stockunicornid && stockunicornid.toString().length <= 6
      ? sbu.substring(0, 1) + _.padStart(stockunicornid, 7, "0")
      : "";
  dialogOpen && window.dataLayer.push({ stockId: displaystockId });

  return (
    <div>
      <Dialog
        open={dialogOpen}
        handleClose={handleCloseDialog}
        fullWidth
        className={classes.root}
      >
        <div className={classes.font18}>
          <div>
            {intl.formatMessage({
              id: "search.todayQuota",
            })}{" "}
            <span className={classes.quotanumber}>{max - count}</span>/{max}
          </div>
          {/* <div className={classes.quotatext}>
          <span className={classes.quotanumber}>{max - count}</span>/{max}
        </div> */}

          {reachedMax ? (
            <span className={classes.errorMsg}>
              {intl.formatMessage({
                id: "search.unlock.exceed",
              })}
            </span>
          ) : (
            <span></span>
          )}
        </div>

        <div className={classes.font18}>
          {intl.formatMessage({
            id: "search.unlockMessage",
          })}
        </div>

        <DialogActions>
          {/* <DialogButton
          className={classes.buttonBack}
          onClick={handleCloseDialog}
        >
          Back
        </DialogButton> */}
          <FormButton
            className={classes.buttonMain}
            onClick={(e) => handleUnlockStockDetail(e, stockid)}
            disabled={isClicked}
            id="unlockStockButton"
          >
            {intl.formatMessage({
              id: "search.button.view",
            })}
          </FormButton>
        </DialogActions>
      </Dialog>
    </div>
  );
}

const mapStateToProps = (state) => ({
  count: _.get(state, "stocklist.count") || 0,
  max: generalDailyQuota,
  unlockfinished: _.get(state, "stocklist.unlockfinished") || false,
});

const mapDispatchToProps = (dispatch) => ({
  unlockStock: (id) => dispatch(unlockStock(id)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(UnlockDialogItem));
