import React from "react";

const title = "AiText";

async function action({ store, params }) {
  const { auth } = store.getState();
  if (!auth.user || !auth.user.authorized) {
    return { redirect: "/login" };
  }

  const AiText = await require.ensure(
    [],
    (require) => require("./AiText").default,
    "AiText",
  );

  return {
    chunks: ["company"],
    title,
    component: <AiText stockid={params.id} />,
  };
}

export default action;
