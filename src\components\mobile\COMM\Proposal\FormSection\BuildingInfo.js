import React from "react";
import { FieldArray } from "redux-form";
import { injectIntl } from "react-intl";
import { withStyles } from "@material-ui/core/styles";
import Grid from "@material-ui/core/Grid";
import InlineTextField from "../../../../common/InlineTextField";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import InputWithCheckBox from "../../../../common/InputWithCheckBox";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    paddingTop: "1vh",
  },
};

function FieldSection({
  classes,
  intl,
  changeFieldValue,
  fieldNamePrefix,
  isListProposal,
}) {
  return (
    <div className={classes.root}>
      <DetailBoxSection
        text={intl.formatMessage({
          id: "stock.photo.buildinginfo",
        })}
        expandable
      >
        <Grid container spacing={1}>
          {/* {!isListProposal && ( */}
          <Grid item xs={6}>
            <FieldArray
              name={`${fieldNamePrefix}stock.usage`}
              label={intl.formatMessage({
                id: "proposal.form.buildingusage",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              disabled
              noBottomBorder
            />
          </Grid>
          {/* )} */}
          <Grid item xs={6}>
            <FieldArray
              name={`${fieldNamePrefix}stock.title`}
              label={intl.formatMessage({
                id: "proposal.form.title",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              disabled
              noBottomBorder
            />
          </Grid>
          <Grid item xs={6}>
            <FieldArray
              name={`${fieldNamePrefix}stock.inTakeDate`}
              label={intl.formatMessage({
                id: "proposal.form.completiondate",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              disabled
              noBottomBorder
            />
          </Grid>

          {/* {!isListProposal && (
            <FieldArray
              name="stock.developers"
              label={intl.formatMessage({
                id: "search.form.developer",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              xs={6}
              disabled
              noBottomBorder
            />
          )} */}

          <Grid item xs={12}>
            <FieldArray
              name={`${fieldNamePrefix}stock.managementCompany`}
              label={intl.formatMessage({
                id: "building.mgtcompany",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              disabled
              noBottomBorder
            />
          </Grid>

          <Grid item xs={12}>
            <FieldArray
              name={`${fieldNamePrefix}stock.transport`}
              label={intl.formatMessage({
                id: "proposal.form.transport",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              disabled
              noBottomBorder
            />
          </Grid>

          <Grid item xs={6}>
            <FieldArray
              name={`${fieldNamePrefix}stock.passengerLift`}
              label={intl.formatMessage({
                id: "building.passengerlift",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              type="number"
              inputToDisplay={(v) => (v && parseInt(v, 10) !== 0 ? v : "---")}
              min={0}
              disabled
              noBottomBorder
            />
          </Grid>

          <Grid item xs={6}>
            <FieldArray
              name={`${fieldNamePrefix}stock.cargoLift`}
              label={intl.formatMessage({
                id: "building.cargolift",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              disabled
              noBottomBorder
            />
          </Grid>
          <Grid item xs={6}>
            <FieldArray
              name={`${fieldNamePrefix}stock.airConditioningType`}
              label={intl.formatMessage({
                id: "proposal.form.airConditioning",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              disabled
              noBottomBorder
            />
          </Grid>

          <Grid item xs={6}>
            <FieldArray
              name={`${fieldNamePrefix}stock.haveCarPark`}
              label={intl.formatMessage({
                id: "tips.stocktag.carpark",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              disabled
              noBottomBorder
            />
          </Grid>
          <Grid item xs={12}>
            <FieldArray
              name={`${fieldNamePrefix}stock.airConditioningOpeningTime`}
              label={intl.formatMessage({
                id: "proposal.form.airconditioningOpeningTime",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              disabled
              noBottomBorder
              multiline
            />
          </Grid>
          <Grid item xs={12}>
            <FieldArray
              name={`${fieldNamePrefix}stock.airConditioningExtraCharges`}
              label={intl.formatMessage({
                id: "proposal.form.airconditioningOpeningTimeExtracharges",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              disabled
              noBottomBorder
              multiline
            />
          </Grid>
        </Grid>
      </DetailBoxSection>
    </div>
  );
}
export default withStyles(styles)(injectIntl(FieldSection));
