import React from "react";

const title = "Company";

async function action({ store, query }) {
  const { auth } = store.getState();
  if (!auth.user || !auth.user.authorized) {
    return { redirect: "/login" };
  }

  const SearchPage = await require.ensure(
    [],
    (require) => require("./CompanySearchPage").default,
    "companySearchPage",
  );

  return {
    chunks: ["companySearch"],
    title,
    component: <SearchPage queryFromUri={query} />,
  };
}

export default action;
