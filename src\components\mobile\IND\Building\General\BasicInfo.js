import React from "react";
import PropTypes from "prop-types";
import moment from "moment";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import { injectIntl } from "react-intl";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import FieldVal from "../../../../common/FieldVal";
import { getLangKey } from "../../../../../helper/generalHelper";

const styles = (theme) => ({
  root: {
    padding: "2vw",
  },
});

class BasicInfo extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langkey = getLangKey(intl);
    const managementFeeLangkey = getLangKey(intl, "managementFee");

    const usageMapping = {
      "residential": intl.formatMessage({ id: "stock.usage.residential" }),
      "commercial": intl.formatMessage({ id: "stock.usage.commercial" }),
      "shop": intl.formatMessage({ id: "stock.usage.shop" }),
      "industrial": intl.formatMessage({ id: "stock.usage.industrial" }),
      "industrialCommercial": intl.formatMessage({ id: "stock.usage.industrialcommercial" }),
      "godown": intl.formatMessage({ id: "stock.usage.godown" }),
      "office": intl.formatMessage({ id: "stock.usage.office" }),
      "factory": intl.formatMessage({ id: "stock.usage.factory" }),
      "workshop": intl.formatMessage({ id: "stock.usage.workshop" }),
      "retail": intl.formatMessage({ id: "stock.usage.retail" }),
      "hotel": intl.formatMessage({ id: "stock.usage.hotel" }),
      "warehouse": intl.formatMessage({ id: "stock.usage.warehouse" }),
      "nonPolluting": intl.formatMessage({ id: "stock.usage.nonPolluting" }),
      "ancillaryOffice": intl.formatMessage({ id: "stock.usage.ancillaryOffice" }),
      "serviceRoom": intl.formatMessage({ id: "stock.usage.serviceRoom" }),
      "motorvehicles": intl.formatMessage({ id: "stock.usage.motorvehicles" }),
      "carport": intl.formatMessage({ id: "stock.usage.carport" }),
      "coldStorage": intl.formatMessage({ id: "stock.usage.coldStorage" }),
      "canteen": intl.formatMessage({ id: "stock.usage.canteen" }),
      "site": intl.formatMessage({ id: "stock.usage.site" }),
      "commercialRevolutionIndustrialBuilding": intl.formatMessage({ id: "stock.usage.commercialrevolutionindustrialbuilding" }),
    };

    const buildingUsage =
      detail.usage && detail.usage.length
        ? detail.usage.map(v => usageMapping[v]).join(", ")
        : "---";
    const buildingGrade = detail.grade || "---";
    const isCloseToMTR =
      detail.closeToMTR === true
        ? intl.formatMessage({
            id: "common.yes",
          })
        : detail.closeToMTR === false
        ? intl.formatMessage({
            id: "common.no",
          })
        : "---";
    const completionDate = detail.inTakeDate ? moment(parseInt(detail.inTakeDate)).format("YYYY") : "---";
    const managementFee = detail[managementFeeLangkey] || "---";
    const mgtFeeUpdateDate = "---";
    const feeIncludeACBool = detail.isIncludeAirCondCharge;
    const feeIncludeAC =
      feeIncludeACBool === true
        ? intl.formatMessage({
            id: "common.include",
          })
        : feeIncludeACBool === false
        ? intl.formatMessage({
            id: "common.exclude",
          })
        : "---";
    const developers = detail.developers || [];
    let developer = developers.length === 0 ? "---" : "";
    developers.forEach((v, i) => {
      developer += (i > 0 ? ", " : "") + (v[langkey] || "---");
    });

    const managementCompany =
      detail.managementCompanies && detail.managementCompanies[0]
        ? detail.managementCompanies[0]
        : {};
    const managementCompanyName =
      managementCompany[langkey]
        ? managementCompany[langkey]
        : "---";
    // const contactName = detail.managementCompanyPerson || "---";
    // const contactPhones = detail.managementCompanyContact || "---";
    const contactName = detail.managementCompanyPeople || "---";
    const contactPhones = detail.managementCompanyContacts || "---";

    let generalMapping = {
      [intl.formatMessage({
        id: "stock.usage",
      })]: { value: buildingUsage, xs: 6 },
      [intl.formatMessage({
        id: "search.form.grade",
      })]: { value: buildingGrade, xs: 6 },
      [intl.formatMessage({
        id: "building.mtr",
      })]: { value: isCloseToMTR, xs: 6 },
      [intl.formatMessage({
        id: "building.competition",
      })]: { value: completionDate, xs: 6 },
      [intl.formatMessage({
        id: "stock.mgtfee",
      })]: { value: managementFee, xs: 6 },
      [intl.formatMessage({
        id: "building.mgtfeeupdatedate",
      })]: { value: mgtFeeUpdateDate, xs: 6 },
      [intl.formatMessage({
        id: "building.accharge",
      })]: { value: feeIncludeAC, xs: 6 },
      [intl.formatMessage({
        id: "building.mgtcompany",
      })]: { value: managementCompanyName, xs: 12 },
      [intl.formatMessage({
        id: "stock.contact",
      })]: { value: contactName, xs: 12 },
      [intl.formatMessage({
        id: "building.phone",
      })]: { value: contactPhones, xs: 12 },
    };

    let developersArr = [];
    for (let item in generalMapping) {
      developersArr.push([item, generalMapping[item]]);
    }

    if (developers && developers.length > 0) {
      for (let i = developers.length - 1; i >= 0; i--) {
        const itemToPush = [
          intl.formatMessage({
            id: "building.developer",
          }) + ` ${i + 1}`,
          {
            value: developers[i][langkey],
            xs: 12,
          },
        ];
        developersArr.splice(7, 0, itemToPush);
      }
    }

    return (
      <div className={classes.root}>
        <Grid container spacing={2}>
          {developersArr.map((v, i) => {
            return (
              <Grid item xs={v[1].xs} key={v[0]}>
                <FieldVal field={v[0]}>{v[1].value}</FieldVal>
              </Grid>
            );
          })}
        </Grid>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(BasicInfo));
