import React, { useEffect, useState } from "react";
import { injectIntl } from "react-intl";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/core/styles";
import _ from "lodash";

import ConfirmDialog from "./ConfirmDialog";

const useStyles = makeStyles({
  error: {
    color: "#ff3333",
  },
});

function SearchConsentDialog({
  renderChildren,
  open,
  handleClose,
  okMsg,
  cancelMsg,
  okCallback,
  isApplyConsent,
  sendLog,
  intl,
}) {
  const classes = useStyles();

  const [error, setError] = useState(null);
  const [returnLogRes, setReturnLogRes] = useState(null);

  // check log response error
  // if action is ok, run callback after checking
  const checkLogResult = (callback) => (res) => {
    if (_.has(res, "error") && !_.get(res, "log_seq")) {
      setError(
        JSON.stringify(_.get(res, "error") || { error: "no log_seq received" }),
      );
    } else if (callback) callback(res);
  };

  useEffect(() => {
    if (open) {
      sendLog(
        checkLogResult((logRes) => setReturnLogRes(logRes)),
        isApplyConsent,
      );
    }

    return () => {
      if (open) {
        setError(null);
        setReturnLogRes(null);
      }
    };
  }, [open]);

  const handleConsentClose = () => {
    handleClose(false);
    setReturnLogRes(null);
    setError(null);
  };

  return (
    <ConfirmDialog
      open={open && !!(error || returnLogRes)}
      handleClose={handleConsentClose}
      onOkCallback={
        !_.isNull(error)
          ? null
          : () =>
              sendLog(
                checkLogResult(okCallback),
                false,
                _.get(returnLogRes, "log_seq") || "",
                "A",
              )
      }
      onCancelCallback={
        !_.isNull(error)
          ? null
          : () =>
              sendLog(
                checkLogResult(null),
                false,
                _.get(returnLogRes, "log_seq") || "",
                "R",
              )
      }
      okMsg={okMsg}
      cancelMsg={
        !_.isNull(error)
          ? intl.formatMessage({ id: "common.cancel" })
          : cancelMsg
      }
      disableEscapeKeyDown
      disableBackdropClick
    >
      {error ? (
        <p className={classes.error}>{error}</p>
      ) : (
        renderChildren(returnLogRes)
      )}
    </ConfirmDialog>
  );
}

SearchConsentDialog.propTypes = {
  renderChildren: PropTypes.func.isRequired,
  open: PropTypes.bool.isRequired,
  handleClose: PropTypes.func.isRequired,
  okMsg: PropTypes.string.isRequired,
  cancelMsg: PropTypes.string.isRequired,
  okCallback: PropTypes.func.isRequired,
  isApplyConsent: PropTypes.bool.isRequired,

  // sendLog should have the following params:
  // 1. callback: acutal action after take a log
  // 2. requireHkId: require applicant's Hkid for application, default as false
  // 3. log_seq: log_seq if it's exist, default as null
  // 4. status: R/A action from confirmation box, default as null
  // sample usage can be found in ApplyCompanySearch
  sendLog: PropTypes.func.isRequired,

  intl: PropTypes.object.isRequired,
};

export default injectIntl(SearchConsentDialog);
