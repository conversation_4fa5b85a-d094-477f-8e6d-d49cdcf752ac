import React from "react";
import { connect } from "react-redux";
import { Box, makeStyles } from "@material-ui/core";
import { convertCurrency } from "@/helper/generalHelper";
import { getEmployeesMitaclubManagerInfo, getMitaclubEmpsByTeamCode } from "@/actions/employee";
import { AGENT_TARGET, MANAGER_TARGET } from "../MitaClubPage";
import QualificationCard from "./QualificationCard";
import MyTeamListCard from "./MyTeamListCard";
import TeamDetailsCard from "./TeamDetailsCard";
import GoldCard from "./GoldCard";
import _ from "lodash";

const qualificationLevels = [
  {
    badge: "鑽石",
    title: "升級至鑽石級別",
    description: "首 1 名最高盈利",
  },
  {
    badge: "白⾦",
    title: "升級至⽩⾦級別",
    description: "首 2 名最高盈利",
  }
];

const useStyles = makeStyles((theme) => ({
  "@global": {
    body: {
      margin: 0,
      padding: 0,
      height: "100vh",
      backgroundColor: "#F5F5F5",
    }
  },
  root: {
    // paddingBottom: "32px",
  },
  contentStack: {
    display: "flex",
    flexDirection: "column",
    width: "100%",
    gap: theme.spacing(3),
    // marginTop: "-85px",
  },
}));

function CardBox(props) {
  const classes = useStyles();

  const performanceData = React.useMemo(() => {
    return (props.mitaclubInfo || []).reduce((acc, item) => {
      acc.AccumulatedSalesAmount += +item.AccumulatedSalesAmount;
      acc.AccumulatedSalesCase += +item.AccumulatedSalesCase;
      return acc;
    }, {
      AccumulatedSalesAmount: 0,
      AccumulatedSalesCase: 0,
      ExecutionDate: props.mitaclubInfo[0]?.ExecutionDate,
    });
  }, [props.mitaclubInfo]);

  const metricsValue = React.useMemo(() => {
    return convertCurrency(+performanceData.AccumulatedSalesAmount || 0)
  }, [performanceData]);

  return (
    <Box style={{ padding: "0 12px" }}>
      <Box className={classes.contentStack}>
        {/* 业绩卡片 */}
        <GoldCard
          title={"你的年度團隊業績"}
          updateDate={performanceData.ExecutionDate}
          currentPerformance={(+performanceData.AccumulatedSalesAmount || 0)}
          metricsValue={metricsValue}
        />

        {/* 资格卡片 */}
        <QualificationCard
          titleType={"區董"}
          qualificationLevels={qualificationLevels}
        />

        {(!props.mitaclubAgentMap || !props.mitaclubTeams?.length) ? null : <MyTeamListCard
          mitaclubTeams={props.mitaclubTeams}
          mitaclubAgentMap={props.mitaclubAgentMap}
          targetValues={AGENT_TARGET}
        />}

        {!props.mitaclubManagerInfo?.length ? null : <TeamDetailsCard
          isManager
          agents={props.mitaclubManagerInfo}
          targetValues={MANAGER_TARGET}
        />}
        {!(props?.mitaclubAgentMap && props.mitaclubTeams?.length) ? null : props.mitaclubTeams.map((team) => {
          const key = team.TeamCode;
          const agents = props?.mitaclubAgentMap[key] || [];
          return (<TeamDetailsCard key={key} agents={agents} targetValues={AGENT_TARGET} />);
        })}

      </Box>
    </Box>
  );
}

function DistrictDirector(props) {
  const classes = useStyles();

  React.useEffect(() => {
    const teamCodes = props.mitaclubInfo?.map(item => item.TeamCode) || [];
    props.getMitaclubEmpsByTeamCode(teamCodes);
  }, [props.mitaclubInfo]);

  React.useEffect(() => {
    const teamCodes = (props.mitaclubInfo || []).map(v => v.TeamCode) || [];
    props.getEmployeesMitaclubManagerInfo(teamCodes);
  }, [props.getEmployeesMitaclubManagerInfo, props.mitaclubInfo]);

  return (
    <Box className={classes.root}>
      <CardBox
        mitaclubInfo={props.mitaclubInfo}
        mitaclubTeams={props.mitaclubTeams}
        mitaclubAgentMap={props.mitaclubAgentMap}
        mitaclubManagerInfo={props.mitaclubManagerInfo}
      />
    </Box>
  );
}

const mapStateToProps = (state) => ({
  mitaclubInfo: _.get(state, "employee.mitaclubInfo") || [],
  mitaclubTeams: _.get(state, "employee.mitaclubTeams") || [],
  mitaclubManagerInfo: _.get(state, "employee.mitaclubManagerInfo") || [],
  mitaclubAgentMap: _.get(state, "employee.mitaclubAgentMap"),
  // employee: _.get(state, "employee.employees.0"),
});

const mapDispatchToProps = (dispatch) => ({
  getMitaclubEmpsByTeamCode: (teamCode) => dispatch(getMitaclubEmpsByTeamCode({ teamCode })),
  getEmployeesMitaclubManagerInfo: (teamCode = []) => dispatch(getEmployeesMitaclubManagerInfo({ teamCode })),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(DistrictDirector);
