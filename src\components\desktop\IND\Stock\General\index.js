import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import DetailTabPanelFrame from "../../../../common/DetailTabPanelFrame";
import StockMain from "./StockMain";
import BasicProperty from "./BasicProperty";
import Facilities from "./Facilities";
import Remarks from "./Remarks";
import ContactInfo from "./ContactInfo";
import EaaRecord from "./EaaRecord";
import Tenancy from "./Tenancy";
import ExtraCharge from "./ExtraCharge";
import LandSearch from "./LandSearch";
import { enableConsolidLandSearch } from "../../../../../config";

const styles = (theme) => ({});

class General extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    listing: PropTypes.bool,
    detail: PropTypes.object,
    hand: PropTypes.number,
  };

  constructor(props) {
    super(props);
    this.state = { appIsMounted: false };
  }

  componentDidMount() {
    requestAnimationFrame(() => {
      this.setState({ appIsMounted: true });
    });
  }

  render() {
    const {
      classes,
      detail,
      listed,
      listing,
      handController,
      hand,
    } = this.props;

    const hasData = Object.keys(detail).length > 0;

    return (
      <DetailTabPanelFrame
        hasData={hasData}
        listing={listing}
        listed={listed}
        notFoundText="Stock not found"
      >
        {handController}
        <StockMain detail={detail} />
        <BasicProperty detail={detail} />
        {/* only load transaction graph in client side */}
        {this.state.appIsMounted &&
          React.createElement(require("./Transaction").default, {
            detail: detail,
          })}
        { enableConsolidLandSearch == 'true' && <LandSearch detail={detail}/> }
        <ExtraCharge detail={detail} />
        <ContactInfo detail={detail} hand={hand} />
        <Tenancy detail={detail} />
        <EaaRecord detail={detail} />
        <Remarks detail={detail} />
        <Facilities detail={detail} />
      </DetailTabPanelFrame>
    );
  }
}

const mapStateToProps = (state) => ({
  detail: state.stock.detail ? state.stock.detail : {},
  listed: state.stock.listed ? state.stock.listed : false,
  listing: state.stock.listing ? state.stock.listing : false,
});

export default connect(mapStateToProps)(withStyles(styles)(General));
