import fetch from "node-fetch";
import config from "../../config";

export const ppStockCount = async (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    const body = {
      emp_id: req.body.emp_id,
      sbu: config.sbu,
      newStock: req.body.newStock || [],
    };

    try {
      const resp = await fetch(`${config.api.icimsgeneral}/ppStockCount`, {
        method: "POST",
        body: JSON.stringify(body),
        headers: {
          "content-type": "application/json",
        },
      });
      const data = await resp.json();
      res.send(data);
    } catch (e) {
      console.log(e);
      return {};
    }
  }
};
