import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import DeleteMediaDialog from "../DeleteMediaDialog";
import { injectIntl } from "react-intl";
import MediaSection from "./MediaSection";
import VirtualTour from "./VirtualTour";
import _ from "lodash";

const styles = {
  SectionWrapper: {
    paddingTop: "1vh",
  },
};

class MediaMain extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    media: PropTypes.object,
    mediaType: PropTypes.string.isRequired,
    deletable: PropTypes.bool,
    deleteDialogCallback: PropTypes.func,
    titleClass: PropTypes.string,
    numClass: PropTypes.string,
    isProposal: PropTypes.bool,

    /** 是否進行詳細分類, AWS-3937 文檔, VR/影片, 相片 */
    isGroup: PropTypes.bool,
    /** 控制显示哪些分组 */
    groupSections: PropTypes.arrayOf(
      PropTypes.oneOf(['document', 'kol_video', 'video', 'photo']),
    ),
    /** 是否隐藏类型选择器 */
    hideTypeSelect: PropTypes.bool,
    /** 视图类型：网格或列表 */
    viewType: PropTypes.oneOf(['grid', 'list']),
    /** 自定义按钮渲染函数 */
    renderCustomButton: PropTypes.func,
  };

  constructor(props) {
    super(props);
    this.state = {
      deleteDialogOpen: false,
      deleteDialogMediaId: null,
      subMediaSectionExpandKeySet: new Set(props.groupSections),
      typeFilter: "",
    };
  }

  componentDidUpdate(prevProps, prevState) {
    if (!_.isEqual(prevProps.groupSections, this.props.groupSections)) {
      const subMediaSectionExpandKeySet = new Set(this.props.groupSections);
      this.setState({ subMediaSectionExpandKeySet });
    }
  }

  handleTypeFilterChange = (value) => {
    this.setState({ typeFilter: value });
  };

  setSubMediaSectionExpandKey (key = "") {
    return (field = "", expandType = false) => {
      this.setState(prevState => {
        const newSet = new Set([...prevState.subMediaSectionExpandKeySet.keys()]);
        expandType ? newSet.add(key) : newSet.delete(key);
        return { subMediaSectionExpandKeySet: newSet };
      });
    }
  }

  handleOpenDeleteMediaDialog = (id) => {
    this.setState({ deleteDialogOpen: true, deleteDialogMediaId: id });
  };

  handleCloseDeleteDialog = () => {
    this.setState({ deleteDialogOpen: false });
  };

  render() {
    const { deleteDialogOpen, deleteDialogMediaId } = this.state;
    const {
      classes,
      media,
      mediaType,
      mediaPath,
      deletable,
      deleteDialogCallback,
      titleClass,
      numClass,
      isProposal,
      intl,
      defaultExpand = false,
      isListProposal,
      expandChange,
      hideTypeSelect,
      viewType,
      renderCustomButton,
    } = this.props;

    const mediaTypeMapping = {
      stock: intl.formatMessage({ id: "search.header.stock" }),
      building: intl.formatMessage({ id: "search.form.building" }),
      street: intl.formatMessage({ id: "search.form.street" }),
      selected: intl.formatMessage({ id: "proposal.form.media.selected" })
    };
    let prefix =
      mediaType && mediaTypeMapping[mediaType]
        ? mediaTypeMapping[mediaType]
        : "";
    if (intl.locale === "en") {
      prefix = prefix.trim() + " ";
    }

    const virtualTours = media?.virtualTour || [];

    const mixedMedia = [];
    if (this.props.groupSections.includes('video')) {
      mixedMedia.push(...(media?.video || []));
    }
    if (this.props.groupSections.includes('photo')) {
      mixedMedia.push(...(media?.photo || []));
    }
    if (this.props.groupSections.includes('document')) {
      mixedMedia.push(...(media?.document || []));
    }
    if (this.props.groupSections.includes('kol_video')) {
      mixedMedia.push(...(media?.kolVideo || []));
    }

    return (
      <>
        <MediaSection
          media={mixedMedia}
          mediaType={mediaType}
          mediaPath={mediaPath}
          prefix={prefix}
          deletable={deletable}
          handleOpenDeleteMediaDialog={this.handleOpenDeleteMediaDialog}
          titleClass={titleClass}
          numClass={numClass}
          isProposal={isProposal}
          proposalFieldName={mediaType + "Media"}
          defaultExpand={defaultExpand}
          isListProposal={isListProposal}
          expandChange={expandChange}
          isGroup={this.props.isGroup}
          typeFilter={this.state.typeFilter}
          handleTypeFilterChange={this.handleTypeFilterChange}
          hideTypeSelect={hideTypeSelect}
          viewType={viewType}
          renderCustomButton={renderCustomButton}
        />

        {this.props.isGroup && defaultExpand && _.map(this.props.groupSections, (section) => (
          <MediaSection
            key={section}
            hideTypeSelect
            media={mixedMedia}
            mediaPath={mediaPath}
            deletable={deletable}
            handleOpenDeleteMediaDialog={this.handleOpenDeleteMediaDialog}
            titleClass={titleClass}
            isProposal={isProposal}
            proposalFieldName={mediaType + "Media"}
            defaultExpand={this.state.subMediaSectionExpandKeySet.has(section)}
            isListProposal={isListProposal}
            expandChange={this.setSubMediaSectionExpandKey(section)}
            typeFilter={this.state.typeFilter}
            subType={section}
            viewType={viewType}
            renderCustomButton={renderCustomButton}
          />
        ))}

        {virtualTours.length > 0 && !isProposal && (
          <div className={classes.SectionWrapper}>
            <VirtualTour media={media} prefix={prefix} />
          </div>
        )}

        <DeleteMediaDialog
          dialogOpen={deleteDialogOpen}
          handleCloseDialog={this.handleCloseDeleteDialog}
          callback={deleteDialogCallback}
          mediaId={deleteDialogMediaId}
        />
      </>
    );
  }
}

MediaMain.defaultProps = {
  media: {},
  deleteDialogCallback: () => {},
  expandChange: () => {},
  groupSections: [],
  hideTypeSelect: false,
  viewType: 'grid',
};

export default withStyles(styles)(injectIntl(MediaMain));
