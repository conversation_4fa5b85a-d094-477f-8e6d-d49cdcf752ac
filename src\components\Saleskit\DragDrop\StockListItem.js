import React from "react";
import PropTypes from "prop-types";
import {
  Checkbox,
  FormControlLabel,
  Typography,
} from "@material-ui/core";
import { withStyles, makeStyles } from "@material-ui/core/styles";
import MuiExpansionPanel from "@material-ui/core/ExpansionPanel";
import MuiExpansionPanelSummary from "@material-ui/core/ExpansionPanelSummary";
import MuiExpansionPanelDetails from "@material-ui/core/ExpansionPanelDetails";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import { Draggable } from "react-beautiful-dnd";

const ExpansionPanel = withStyles((theme) => ({
  root: {
    backgroundColor: "#ffffff",
    border: "none",
    boxShadow: "none",
    marginBottom: theme.spacing(1),
    "&:not(:last-child)": {
      borderBottom: 0,
    },
    "&:before": {
      display: "none",
    },
    "&$expanded": {
      margin: 0,
      marginBottom: theme.spacing(1),
    },
  },
  expanded: {},
}))(MuiExpansionPanel);

const ExpansionPanelSummary = withStyles({
  root: {
    backgroundColor: (props) => (props.selected ? "#ffffc1" : "#ffffff"),
    // marginBottom: -1,
    minHeight: 35,
    padding: "2px 2px",
    // height: 40,
    "&$expanded": {
      minHeight: 35,
      margin: 0,
    },
  },
  content: {
    margin: 0,
    "&$expanded": {
      margin: 0,
    },
  },
  expandIcon: {
    padding: 0,
    margin: 0,
    width: 35,
    height: 35,
  },
  expanded: {},
})(MuiExpansionPanelSummary);

const ExpansionPanelDetails = withStyles((theme) => ({
  root: {
    padding: 0,
    display: "block",
  },
}))(MuiExpansionPanelDetails);

const useStyles = makeStyles(() => ({
  formControl: {
    margin: 0,
    marginRight: 5,
  },
  checkbox: {
    display: "block",
    padding: 0,
    "&.Mui-checked:not(.Mui-disabled)": {
      color: "#13CE66",
    },
  },
  address: {
    width: "100%",
    lineHeight: 1.3,
  },
}));

function StockListItem({
  stockId,
  idx,
  expanded,
  handleExpand,
  handleSelect,
  selected,
  title,
  titleColor,
  show,
  changeFieldValue,
  ordering,
  children,
}) {
  const classes = useStyles();
  return (
    <Draggable draggableId={stockId} index={idx} isDragDisabled={!ordering}>
      {(draggableProvided) => (
        <ExpansionPanel
          expanded={!ordering && expanded}
          onChange={handleExpand}
          TransitionProps={{ unmountOnExit: true }}
          ref={draggableProvided.innerRef}
          {...draggableProvided.draggableProps}
        >
          <ExpansionPanelSummary
            selected={expanded || selected}
            expandIcon={<ExpandMoreIcon />}
            aria-label="Expand"
            aria-controls={`${title}-actions2-content`}
            id={`${title}-actions2-header`}
            {...draggableProvided.dragHandleProps}
          >
            <FormControlLabel
              className={classes.formControl}
              aria-label="Check for display"
              onClick={(e) => e.stopPropagation()}
              onFocus={(e) => e.stopPropagation()}
              control={
                <Checkbox
                  className={classes.checkbox}
                  disabled={ordering}
                  onChange={(e) =>
                    changeFieldValue("stock.show", e.target.checked)
                  }
                  checked={show}
                />
              }
            />
            <Typography
              onClick={(e) => {
                e.stopPropagation();
                handleSelect();
              }}
              className={classes.address}
              style={titleColor}
            >
              {title}
            </Typography>
          </ExpansionPanelSummary>
          <ExpansionPanelDetails>{children}</ExpansionPanelDetails>
        </ExpansionPanel>
      )}
    </Draggable>
  );
}

StockListItem.propTypes = {
  stockId: PropTypes.string.isRequired,
  idx: PropTypes.number.isRequired,
  expanded: PropTypes.bool.isRequired,
  handleExpand: PropTypes.func.isRequired,
  handleSelect: PropTypes.func.isRequired,
  selected: PropTypes.bool.isRequired,
  title: PropTypes.string.isRequired,
  titleColor: PropTypes.object.isRequired,
  show: PropTypes.bool.isRequired,
  changeFieldValue: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
};

export default StockListItem;
