import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import { convertNewlineToBr } from "../../../../../helper/generalHelper";
import FieldVal from "../../../../common/FieldVal";
import { injectIntl } from "react-intl";

const styles = theme => ({
  gridContent: {
    padding: "1vw 2vw"
  }
});

class AirConditioning extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;

    const airConditioning =
      detail.airConditioning && detail.airConditioning.length
        ? detail.airConditioning.join(", ")
        : "---";
    const acOpeningTime =
      detail.airConditioningOpeningTimeRemark
        ? convertNewlineToBr(detail.airConditioningOpeningTimeRemark)
        : "---";
    const extraCharges =
      detail.airConditioningExtraCharge
        ? convertNewlineToBr(detail.airConditioningExtraCharge)
        : "---";

    let acMapping = {
      [intl.formatMessage({
        id: "building.airconditioning"
      })]: { value: airConditioning, xs: 12 },
      [intl.formatMessage({
        id: "building.airconditioningtime"
      })]: { value: acOpeningTime, xs: 12 },
      [intl.formatMessage({
        id: "building.extracharges"
      })]: { value: extraCharges, xs: 12 }
    };

    return (
      <DetailBoxSection
        text={intl.formatMessage({
          id: "building.airconditioning"
        })}
        expandable={true}
        isExpanding={true}
      >
        <Grid container spacing={2} className={classes.gridContent}>
          {Object.keys(acMapping).map((v, i) => (
            <Grid item xs={acMapping[v].xs} key={v}>
              <FieldVal field={v}>{acMapping[v].value}</FieldVal>
            </Grid>
          ))}
        </Grid>
      </DetailBoxSection>
    );
  }
}

export default withStyles(styles)(injectIntl(AirConditioning));
