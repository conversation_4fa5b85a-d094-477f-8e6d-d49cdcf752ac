import React from "react";
import PropTypes from "prop-types";
import { withStyles, createStyles } from "@material-ui/core/styles";
import { injectIntl } from "react-intl";
import InputWithCheckBoxCustom from "./InputWithCheckBoxCustom";
import InlineTextInput from "./InlineTextInput";
import { numberComma } from "../../helper/generalHelper";
import { InputAdornment } from "@material-ui/core";

// We can inject some CSS into the DOM.
const styles = createStyles({
  root: {
    display: "flex",
    justifyContent: "flex-end",
    alignItems: "center",
  },
  areaName: {
    flex: 1,
    textAlign: "right",
    textOverflow: "ellipsis",
    overflow: "hidden",
    whiteSpace: "nowrap",
  },
  typeItem: {
    width: "37vw",
    textAlign: "right",
    display: "flex",
    justifyContent: "flex-end",
    flexWrap: "nowrap",
  },
  typeItem2: {
    width: "35vw",
    textAlign: "right",
    display: "flex",
    justifyContent: "flex-end",
    flexWrap: "nowrap",
  },
  checkboxSmallPadding: {
    padding: "2px 9px",
    marginBottom: "-25px"
  },
});

const mockCheckbox = (backgroundColor = "") => {
  return (
    <div style={{ width: "24px", height: "24px" }}>
      <span 
        style={{ 
          backgroundColor,
          width: "18px",
          height: "18px",
          display: "inline-block",
          margin: "5px 4px",
          borderRadius: "3px",
        }}
      >&nbsp;</span>
    </div>
  );
};

function AreaInputItem(props) {
  const {
    classes,
    className,
    name,
    areaNameZh,
    areaNameEn,
    changeFieldValue,
    intl,
    netDisabled,
    grossDisabled,
    ...other
  } = props;

  const inputToDisplayNet = (v) => v && parseFloat(v) !== 0 ? numberComma(v, 0) : "---";
  const inputToDisplayGross = (v) => v && parseFloat(v) !== 0 ? numberComma(v, 0) : "---";
  // area of 閣樓, 自建樓 is unselectable
  const disabled = areaNameZh === "閣樓" || areaNameZh === "自建閣";
  const noBottomBorder = areaNameZh === "總面積";

  const fakeCheckboxEle = React.useMemo(() => mockCheckbox("#ababab"), []);

  return (
    <div className={`${classes.root} ${className}`} {...other}>
      <div className={classes.areaName}>{intl.locale === "zh" ? areaNameZh : areaNameEn}</div>
      <InputWithCheckBoxCustom
        fakeCheckbox={netDisabled}
        fakeCheckboxEle={netDisabled ? fakeCheckboxEle : undefined}
        className={classes.typeItem}
        name={name}
        label=""
        jsonField="net"
        renderComponent={InlineTextInput}
        inputToDisplay={inputToDisplayNet}
        changeFieldValue={changeFieldValue}
        type="number"
        min={0}
        checkboxProps={{
          className: classes.checkboxSmallPadding,
          disabled: disabled,
        }}
        checkboxInFront
        disabled={noBottomBorder}
        noBottomBorder={noBottomBorder}
        customInputProps={{
          style: { 
            textAlign: 'right',
            display: 'flex',
            justifyContent: 'space-between',
          },
        }}
        startAdornment={(
          <InputAdornment position="start">
            <span>{intl.formatMessage({ id: "stock.area.net" })}</span>
          </InputAdornment>
        )}
        startAdornmentText={intl.formatMessage({ id: "stock.area.net" })}
      />
      <InputWithCheckBoxCustom 
        fakeCheckbox={grossDisabled}
        fakeCheckboxEle={grossDisabled ? fakeCheckboxEle : undefined}
        className={classes.typeItem2}
        name={name}
        label=""
        jsonField="gross"
        renderComponent={InlineTextInput}
        inputToDisplay={inputToDisplayGross}
        changeFieldValue={changeFieldValue}
        type="number"
        min={0}
        checkboxProps={{
          className: classes.checkboxSmallPadding,
          disabled: disabled,
        }}
        checkboxInFront
        disabled={noBottomBorder}
        noBottomBorder={noBottomBorder}
        customInputProps={{
          style: { 
            textAlign: 'right',
            display: 'flex',
            justifyContent: 'space-between',
          },
        }}
        startAdornment={(
          <InputAdornment position="start">
            <span>{intl.formatMessage({ id: "stock.area.gross" })}</span>
          </InputAdornment>
        )}
        startAdornmentText={intl.formatMessage({ id: "stock.area.gross" })}
      />
    </div>
  );
}

AreaInputItem.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  name: PropTypes.string,
  areaNameZh: PropTypes.string,
  areaNameEn: PropTypes.string,
  changeFieldValue: PropTypes.func.isRequired,
  netDisabled: PropTypes.bool,
  grossDisabled: PropTypes.bool,
};

export default withStyles(styles)(injectIntl(AreaInputItem));
