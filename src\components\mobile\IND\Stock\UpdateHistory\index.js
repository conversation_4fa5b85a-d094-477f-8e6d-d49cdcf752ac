import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import DetailTabPanelFrame from "../../../../common/DetailTabPanelFrame";
import UpdateHistoryMain from "./UpdateHistoryMain";

const styles = (theme) => ({});

class UpdateHistory extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    listing: PropTypes.bool,
    detail: PropTypes.array,
    currentDetail: PropTypes.number,
  };

  constructor(props) {
    super(props);
  }

  render() {
    const {
      classes,
      currentDetail,
      listed,
      listing,
      handController,
      hand,
      handsMapping,
    } = this.props;
    const detail =
      this.props.detail.length > 0 ? this.props.detail.find((d) => d._id === currentDetail) : {};

    const hasData = Object.keys(detail).length > 0;

    return (
      <DetailTabPanelFrame
        hasData={hasData}
        listing={listing}
        listed={listed}
        notFoundText="Stock not found"
      >
        {handController}
        <UpdateHistoryMain detail={detail} hand={hand} handsMapping={handsMapping} />
      </DetailTabPanelFrame>
    );
  }
}

const mapStateToProps = (state) => ({
  detail: state.stock.detail ? state.stock.detail : [],
  currentDetail: state.stock.currentDetail ? state.stock.currentDetail : 0,
  listed: state.stock.listed ? state.stock.listed : false,
  listing: state.stock.listing ? state.stock.listing : false,
});

export default connect(mapStateToProps)(withStyles(styles)(UpdateHistory));
