export const getTypeOptions = (intl) => [
  {
    value: "Sale",
    label: intl.formatMessage({
      id: "proposal.form.forsale",
    }),
  },
  {
    value: "Lease",
    label: intl.formatMessage({
      id: "proposal.form.forlease",
    }),
  },
  {
    value: "SaleAndLease",
    label: intl.formatMessage({
      id: "proposal.form.salesandlease",
    }),
  },
];

export const getStreetTypeOptions = (intl, streets = []) => {
  let options = streets.map((v, i) => {
    return {
      value: i,
      label: i === 0
        ? intl.formatMessage({ id: "proposal.form.mainstreet" })
        : intl.formatMessage({ id: "proposal.form.substreet" }) + " " + i
    };
  });
  return options;
};

export const possessionLangIdMapping = {
  "Vacant": "search.form.vacant",
  "Immediate Vacant": "search.form.immediatevacant",
  "Nego. Vacant": "search.form.negovacant",
  "Vacant Possession": "search.form.vacantpossession",
  "Vacant Transaction": "search.form.vacanttransaction",
  "Tenant Occupies": "search.form.tenant",
  "Owner Occupies": "search.form.occupied",
  "Sale With TA": "search.form.salwwithta",
  "Incomplete": "search.form.incomplete",
  "Sale & Leaseback": "search.form.saleandleaseback",
  "N/A": "common.na",
};

export const getPossessionOptions = (intl) => {
  let options = Object.keys(possessionLangIdMapping).map(v => {
    return {
      value: v,
      label: intl.formatMessage({
        id: possessionLangIdMapping[v],
      }),
    };
  });
  options.unshift({
    value: "",
    label: "---",
  });
  return options;
};

export const getDefaultRemarks = (type) => {
  const mapping = {
    "Sale": "佣金為樓價之1%",
    "Lease": "佣金為半個月的租金",
    "SaleAndLease": "買方支付的佣金為樓價之1 % 及租方支付的佣金為半個月的租金",
  };
  return mapping[type] || "";
};
