import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";

// We can inject some CSS into the DOM.
const styles = (theme) => ({
  root: {
    width: 60,
    height: 60,
    border: `3px solid ${theme.base.portraitBorderColor}`,
    borderRadius: "100%",
    boxSizing: "border-box",
    overflow: "hidden",
  },
  img: {
    width: "100%",
    height: "100%",
  },
});

function ProfilePhoto(props) {
  const { classes, src, className, ...other } = props;

  return (
    <div className={clsx(classes.root, className)} {...other}>
      <img className={classes.img} src={src} />
    </div>
  );
}

ProfilePhoto.propTypes = {
  src: PropTypes.string,
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
};

export default withStyles(styles)(ProfilePhoto);
