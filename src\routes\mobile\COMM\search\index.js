/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import { checkAndParseUrlParam } from "../../../../helper/generalHelper";

const title = "Stock Search";

async function action({ store, params, query }) {
  const { auth } = store.getState();
  if (!auth.user) {
    return { redirect: "/login" };
  }
  if (!auth.user.authorized) {
    return { redirect: "/login" };
  }

  const SearchPage = await require.ensure(
    [],
    (require) => require("./SearchPage").default,
    "searchPage",
  );

  const parsedjson = checkAndParseUrlParam(query.param);
  const selectedData = checkAndParseUrlParam(query.selectedData);
  if (parsedjson === false || selectedData === false)
    return { redirect: "/error" };

  const anchorToConsolidate = query.anchorTo === "consolidate";

  return {
    chunks: ["search"],
    title,
    component: (
      <SearchPage
        selectedData={selectedData}
        queryvariablesFromUrl={parsedjson}
        isAdvanced="true"
        anchorToConsolidate={anchorToConsolidate}
      />
    ),
  };
}

export default action;
