/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import { checkAndParseUrlParam } from "../../../../helper/generalHelper";

const title = "Stock Search";

async function action({ store, params, query }) {
  const { auth } = store.getState();
  if (!auth.user) {
    return { redirect: "/login" };
  } else if (auth.user.authorized == false) {
    return { redirect: "/login" };
  }

  const SearchPage = await require.ensure(
    [],
    (require) => require("./SearchPage").default,
    "searchPage",
  );

  let parsedjson = checkAndParseUrlParam(query.param);
  let selectedData = checkAndParseUrlParam(query.selectedData);

  console.log(query);
  if (parsedjson === false || selectedData === false)
    return { redirect: "/error" };

  let anchorToConsolidate = query.anchorTo === "consolidate";

  return {
    chunks: ["search"],
    title,
    component: (
      <SearchPage
        selectedData={selectedData}
        queryvariablesFromUrl={parsedjson}
        isAdvanced={"true"}
        anchorToConsolidate={anchorToConsolidate}
      />
    ),
  };
}

export default action;
