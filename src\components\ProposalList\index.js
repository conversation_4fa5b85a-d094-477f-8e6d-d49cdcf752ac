import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import List from "./List";
import LoadingOverlay from "../LoadingOverlay";

const styles = (theme) => ({
  root: {
    padding: "1vh 2vw",
  },
  notFound: {
    margin: "1em 0",
    textAlign: "center",
    fontWeight: "bold",
  },
});

class ProposalList extends React.Component {
  static propTypes = {
    proposals: PropTypes.array,
    listed: PropTypes.bool,
    listing: PropTypes.bool,
  };

  render() {
    const { classes, proposals, listed, listing } = this.props;

    const hasData = proposals.length > 0;
    return (
      <div className={classes.root}>
        {listed && hasData && <List proposals={proposals} />}

        {(listing || !listed) && <LoadingOverlay />}

        {!listing && listed && !hasData && (
          <div className={classes.notFound}>Proposal not found</div>
        )}
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  proposals: state.proposal.proposals ? state.proposal.proposals : [],
  listed: state.proposal.listed ? state.proposal.listed : false,
  listing: state.proposal.listing ? state.proposal.listing : false,
});

export default connect(mapStateToProps)(withStyles(styles)(ProposalList));
