import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import FieldVal from "../../../../common/FieldVal";
import { getLangKey, numberComma } from "../../../../../helper/generalHelper";
import { injectIntl } from "react-intl";
import DetailBoxSection from "../../../../common/DetailBoxSection";

const styles = theme => ({
  root: {
    padding: "1vh 0",
  },
  lmrAlign: {
    paddingLeft: "2vw"
  }
});

class BasicProperty extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langKey = getLangKey(intl);
    const inspectionLangKey = intl.locale === "zh" ? "inspectionZh" : "inspection";
    const possessionLangKey = intl.locale === "zh" ? "possessionZh" : "possession";

    const unitViewName =
      detail.unitView && detail.unitView[langKey]
        ? detail.unitView[langKey]
        : "---";
    const decoName =
      detail.decoration && detail.decoration[langKey]
        ? detail.decoration[langKey]
        : "---";
    const possession = detail[possessionLangKey] || "---";
    const availability = detail.availability || "---";
    const managementFee = detail.managementFee
      ? "$" + detail.managementFee + "/ft"
      : "---";
    const isIncludeAirConditioning = detail.isIncludeAirConditioning
      ? " A/C"
      : "";
    const isIncludeManagementFee = detail.isIncludeManagementFee
      ? " Inclusive"
      : "";
    const keyNumber = detail.keyNumber || "---";
    const rates = detail.rates
      ? "$" + numberComma(detail.rates) + "/Qtr"
      : "---";
    const isIncludeRates = detail.isIncludeRates ? " Inclusive" : "";
    const governmentRent = detail.governmentRent
      ? "$" + numberComma(detail.governmentRent) + "/Qtr"
      : "---";
    const isIncludeGovernmentRent = detail.isIncludeGovernmentRent
      ? " Inclusive"
      : "";
    const completionDate = detail.completionDate || "---";
    const inspection = detail[inspectionLangKey] || "---";

    const sbutypeHeader = intl.formatMessage({
      id: "stock.sbutype",
    });
    const possessionHeader = intl.formatMessage({
      id: "stock.possession"
    });
    const viewHeader = intl.formatMessage({
      id: "stock.view"
    });
    const mgtfeeHeader = intl.formatMessage({
      id: "stock.mgtfee"
    });
    const availabilityHeader = intl.formatMessage({
      id: "stock.availability"
    });
    const decorationHeader = intl.formatMessage({
      id: "stock.decoration"
    });
    const keynumpwHeader = intl.formatMessage({
      id: "stock.keynumpw"
    });
    const ratesHeader = intl.formatMessage({
      id: "stock.rates"
    });
    const grentHeader = intl.formatMessage({
      id: "stock.grent"
    });
    const completionHeader = intl.formatMessage({
      id: "stock.completion"
    });
    const inspectionHeader = intl.formatMessage({
      id: "stock.inspection"
    });

    let generalMapping = {
      [sbutypeHeader]: {
        value: intl.formatMessage({
          id: "common.commercial",
        }),
        xs: 6,
      },
      [possessionHeader]: { value: possession, xs: 6 },
      [viewHeader]: { value: unitViewName, xs: 6 },
      [mgtfeeHeader]: {
        value:
          managementFee + isIncludeAirConditioning + isIncludeManagementFee,
        xs: 6
      },
      [availabilityHeader]: { value: availability, xs: 6 },
      [decorationHeader]: { value: decoName, xs: 6 },
      [keynumpwHeader]: { value: keyNumber, xs: 6 },
      [ratesHeader]: { value: rates + isIncludeRates, xs: 6 },
      [grentHeader]: { value: governmentRent + isIncludeGovernmentRent, xs: 6 },
      [completionHeader]: { value: completionDate, xs: 6 },
      [inspectionHeader]: { value: inspection, xs: 6 },
    };

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.unit",
          })}
        >
          <Grid container spacing={2} className={classes.lmrAlign}>
            {Object.keys(generalMapping).map((v, i) => (
              <Grid item xs={generalMapping[v].xs} key={v}>
                <FieldVal field={v}>
                  {/* <div
                    className={
                      generalMapping[v].className
                        ? generalMapping[v].className
                        : null
                    }
                  > */}
                  {generalMapping[v].value}
                  {/* </div> */}
                </FieldVal>
              </Grid>
            ))}
          </Grid>
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(BasicProperty));
