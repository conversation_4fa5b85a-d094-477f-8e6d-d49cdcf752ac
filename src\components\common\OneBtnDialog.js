import React from "react";
import PropTypes from "prop-types";
import Dialog from "./Dialog";
import DialogFrame from "./DialogFrame";

function OneBtnDialog(props) {
  const { children, open, handleClose, btn, btnHandleClick, ...others } = props;

  return (
    <Dialog handleClose={handleClose} open={open} {...others}>
      <DialogFrame buttonMain={btn} handleMain={btnHandleClick}>
        {children}
      </DialogFrame>
    </Dialog>
  );
}

OneBtnDialog.propTypes = {
  children: PropTypes.node,
  open: PropTypes.bool.isRequired,
  handleClose: PropTypes.func.isRequired,
  btn: PropTypes.string,
  btnHandleClick: PropTypes.func,
};

export default OneBtnDialog;
