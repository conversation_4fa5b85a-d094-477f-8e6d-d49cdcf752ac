import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import FormControl from '@material-ui/core/FormControl';
import Select from '@material-ui/core/Select';
import InputLabel from '@material-ui/core/InputLabel';

// We can inject some CSS into the DOM.
const styles = {
    formControlOption: {
        marginTop: "0px !important"
    },
    label: {
        lineHeight: 1.3,
        color: "#777",
        fontSize: "0.875em"
    },
};


function DropDownNativeSelect(props) {
    const {
        classes,
        className,
        label,
        input,
        ranges,
        customLabelProps = {},
        meta: { touched, invalid, error },
        jsonField = "value",
        ...custom
    } = props;

    const handleonBlur = (e) => {
        e.preventDefault();
        input.onBlur()
    }

    const [selected, setSelected] = useState(input.value[jsonField]);

    useEffect(() => {
        setSelected(input.value[jsonField]);
    }, [input.value]);

    const handleonChange = (e) => {
        input.onBlur()
        input.onChange({ ...input.value, [jsonField]: e.target.value })
        setSelected(e.target.value)
    }
    return (
        <FormControl {...custom}>
            <label
                {...customLabelProps}
                className={clsx(classes.label, customLabelProps.className)}
            >
                {label}
            </label>
            <Select
                native
                value={selected}
                onChange={handleonChange}
                className={classes.formControlOption}
            >
                {ranges.map((v) => {
                    return (
                        <option value={v.value} key={v.value}>{v.label}</option>
                    )
                })}
            </Select>
        </FormControl>
    );
}

DropDownNativeSelect.propTypes = {
    classes: PropTypes.object.isRequired,
    className: PropTypes.string,
    label: PropTypes.string,
};

export default withStyles(styles)(DropDownNativeSelect);



// function InlineTextField(props) {
//     const {
//         classes,
//         className,
//         label,
//         customLabelProps = {},
//         ...others
//     } = props;

//     return (
//         <div className={clsx(classes.root, className)}>
            // <label
            //     {...customLabelProps}
            //     className={clsx(classes.label, customLabelProps.className)}
            // >
            //     {label}
            // </label>
//             <InlineTextInput {...others} />
//         </div>
//     );
// }

// InlineTextField.propTypes = {
//     classes: PropTypes.object.isRequired,
//     className: PropTypes.string,
//     label: PropTypes.string,
//     customLabelProps: PropTypes.object,
// };

// export default withStyles(styles)(InlineTextField);
