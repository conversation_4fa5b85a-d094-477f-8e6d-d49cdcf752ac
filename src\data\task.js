import schedule from "node-schedule";
import mongodb from "./mongodb";

const task = schedule.scheduleJob("0 8 * * *", function() {
  console.log(
    "Schedule task for clearing unlock record at 00:00am every day..."
  );
  const col = mongodb.db.collection("unlock");
  col.deleteMany(function(err, res) {
    if (err) {
      reject(err);
    } else {
      console.log("Removed all docs in collection");
    }
  });
});

export default task;
