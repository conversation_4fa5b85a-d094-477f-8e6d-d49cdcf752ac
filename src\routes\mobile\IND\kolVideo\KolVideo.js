/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import withStyles from "isomorphic-style-loader/lib/withStyles";
import { FormattedMessage } from "react-intl";
import _ from "lodash";
import KolVideoList from "../../../../components/mobile/IND/KolVideo/List";
import s from "./KolVideo.css";
import { listTeamsKolVideos, clearTeamsKolVideos, batchApprovedKolMedia, batchRemoveKolMedia } from "../../../../actions/kolVideo";
import Layout from "../../../../components/Layout/Layout";

class KolVideo extends React.Component {
  static propTypes = {
    listTeamsKolVideos: PropTypes.func.isRequired,
    clearTeamsKolVideos: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      appIsMounted: false,
    };
  }

  componentDidMount() {
    requestAnimationFrame(() => {
      this.setState({ appIsMounted: true });
    });
  }

  render() {
    return (
      <div>
        {this.state.appIsMounted && (
          <Layout
            header={<FormattedMessage id="home.kol.video" />}
            hideSearchIcon={true}
          >
            <KolVideoList/>
          </Layout>
        )}
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
});

const mapDispatchToProps = (dispatch) => {
  return {
    listTeamsKolVideos: (...args) => dispatch(listTeamsKolVideos(...args)),
    clearTeamsKolVideos: () => dispatch(clearTeamsKolVideos()),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(s)(KolVideo));
