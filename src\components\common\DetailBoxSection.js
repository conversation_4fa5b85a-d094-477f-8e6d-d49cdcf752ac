import React, { useEffect } from "react";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/core/styles";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import ExpandLessIcon from "@material-ui/icons/ExpandLess";
import clsx from "clsx";
import _ from "lodash";

// We can inject some CSS into the DOM.
const useStyles = makeStyles({
  root: {
    // padding: "1vh 0"
  },
  titleRow: {
    display: "flex",
    alignItems: "center",
  },
  strike: {
    overflow: "hidden",
    whiteSpace: "nowrap",
    flex: 1,
    "& > div": {
      position: "relative",
      display: "inline-flex",
      alignItems: "center",
      "&::after": {
        content: '""',
        position: "absolute",
        top: "calc(50% - 1px)",
        width: 9999,
        height: 1,
        background: "#C1C1C1",
        left: "100%",
      },
    },
  },
  title: {
    color: (props) => (props.isMobile ? "#777" : "inherit"),
    fontSize: (props) => (props.isMobile ? "0.875em" : "1.4em"),
    fontWeight: (props) => (props.isMobile ? "bold" : "normal"),
    marginRight: (props) => (props.isMobile ? "1vw" : 8),
  },
  num: {
    width: 20,
    height: 20,
    lineHeight: "20px",
    color: "#FFF",
    fontSize: "0.688em",
    textAlign: "center",
    marginRight: "1vw",
    borderRadius: "100%",
    backgroundColor: "#13CE66",
  },
  expand: {
    color: "#777",
    marginLeft: "1.5vw",
    cursor: "pointer",
  },
  content: {
    padding: (props) => (props.isMobile ? "1vh 0" : "8px 0"),
  },
});

function DetailBoxSection(props, context = {}) {
  const {
    children,
    text,
    expandable,
    num,
    contentClass,
    noStrike,
    titleClass,
    numClass,
    customRight,
    className,
    handleTypeChange,
  } = props;
  const { browserDetect } = context;
  const classes = useStyles({
    isMobile: browserDetect === "mobile",
  });

  const [isExpanding, setIsExpanding] = React.useState(
    props.isExpanding || false,
  );

  let displayNum = num <= 99 ? num : "99+";

  const handleClick = (event, value) => {
    setIsExpanding(!isExpanding);
    // get control of isExpanding from parent component
    props.callback && props.callback(!isExpanding);
  };

  useEffect(() => {
    if (_.isFunction(handleTypeChange)) {
      handleTypeChange('');
    }
  }, [isExpanding]);

  useEffect(() => {
    setIsExpanding(!!props.isExpanding)
  }, [props.isExpanding])

  return (
    <div className={clsx(classes.root, className)}>
      <div className={classes.titleRow} onClick={handleClick}>
        <div className={!noStrike ? classes.strike : ""}>
          <div>
            {text && (
              <div className={`${classes.title} ${titleClass}`}>{text}</div>
            )}
            {(num || num === 0) && (
              <div className={`${classes.num} ${numClass}`}>{displayNum}</div>
            )}
          </div>
        </div>
        {expandable === true && isExpanding === false && (
          <ExpandMoreIcon className={classes.expand} />
        )}
        {expandable === true && isExpanding === true && (
          <ExpandLessIcon className={classes.expand} />
        )}
        {customRight}
      </div>
      <div className={`${classes.content} ${contentClass}`}>
        {!(expandable === true && isExpanding === false) && children}
      </div>
    </div>
  );
}
DetailBoxSection.defaultProps = {
  className: "",
};

DetailBoxSection.propTypes = {
  children: PropTypes.node,
  text: PropTypes.string,
  num: PropTypes.number,
  expandable: PropTypes.bool,
  contentClass: PropTypes.string,
  noStrike: PropTypes.bool,
  customRight: PropTypes.node,
  className: PropTypes.string,
  handleTypeChange: PropTypes.func,
  titleClass: PropTypes.string,
  numClass: PropTypes.string,
  isExpanding: PropTypes.bool,
  callback: PropTypes.func,
};

DetailBoxSection.contextTypes = {
  browserDetect: PropTypes.string,
};

export default DetailBoxSection;
