import React from "react";
import PropTypes from "prop-types";
import { withStyles, createStyles } from "@material-ui/styles";
import { numberComma, convertCurrency } from "../../../../helper/generalHelper";
import VaryingVal from "../../../common/VaryingVal";
import { FormattedMessage, injectIntl } from "react-intl";

const styles = createStyles((theme) => ({
  root: {
    borderRadius: 4,
    padding: 8,
    backgroundColor: "#FFF",
  },
  sourceRow: {
    fontSize: ".875em",
    color: "#777",
    marginBottom: ".5vh",
    display: "flex",
    justifyContent: "space-between",
  },
  unitRow: {
    fontSize: "1.125em",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  },
  content: {
    fontSize: "1.125em",
    marginBottom: ".5vh",
  },
  pricetype: {
    fontSize: "1.125em",
    fontWeight: "700",
    color: "#000",
  },
  priceRow: {
    fontSize: "1.125em",
    textAlign: "right",
  },
  val: {
    display: "inline-flex",
  },
  alignToright: {
    textAlign: "right",
  },
  cardPriceRow: {
    padding: "0 4px",
    borderRadius: 4,
    backgroundColor: "rgba(140, 190, 190, 0.2)",
  },
  cardRentRow: {
    padding: "0 4px",
    borderRadius: 4,
    backgroundColor: "rgba(200, 170, 170, 0.2)",
  },
  greyBox: {
    display: "inline",
    backgroundColor: "#8A8A8A",
    color: "#fff",
    padding: 4,
    margin: 4,
    borderRadius: 4
  }
}));

class TransactionCard extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
    className: PropTypes.string,
  };

  render() {
    const { classes, detail, className, intl } = this.props;
    const langKey = intl.locale === "zh" ? "zh" : "en";

    const isStockDetail = this.props.isStockDetail
      ? this.props.isStockDetail
      : false;

    const date = detail.date + " " || "";
    const source =
      detail.data_source && detail.data_source[langKey]
        ? detail.data_source[langKey]
        : "";
    const txNature = detail.tx_nature ? detail.tx_nature : null;
    const unit = detail.unit
      ? intl.formatMessage({
        id: "search.common.unit",
      }) +
      " " +
      detail.unit
      : "";
    const floor = detail.floor && detail.floor[langKey]
      ? detail.floor[langKey]
      : "";

    const building = detail.building ? detail.building : null;
    const buildingnameEn = building && building.en ? building.en : "---";
    const buildingnameZh = building && building.zh ? building.zh : "---";
    const streetNo =
      detail.street && detail.street.no ?
        (langKey === "zh" ? detail.street.no + " " + intl.formatMessage({ id: "stock.number" }) : detail.street.no)
        : "";

    const streetName =
      detail.street && detail.street[langKey]
        ? detail.street[langKey]
        : "---";
    const district =
      detail.district && detail.district[langKey]
        ? detail.district[langKey]
        : "";
    let areaData = detail.area && detail.area.length > 0 ? detail.area : [];

    areaData = areaData.map(v => {
      let name = v.areaName || "";
      let shopno = v.shopno || "";
      let value = v.value ? numberComma(v.value) : "";
      if (intl.locale === "zh")
        return name + " " + shopno + " " + value;
      else
        return name + " " + shopno + " " + value;
    });

    const priceType =
      detail.type === "Sale"
        ? intl.formatMessage({
          id: "search.status.sold",
        })
        : intl.formatMessage({
          id: "search.status.leased",
        });
    const priceTotal = detail.sell
      ? " " + "$" + numberComma(detail.sell)
      : "";
    const rentTotal = detail.tenancy && detail.tenancy.rent
      ? " " + "$" + numberComma(detail.tenancy.rent)
      : "";
    const price =
      detail.type === "Sale"
        ? detail.ft_sell
          ? numberComma(detail.ft_sell)
          : ""
        : detail.tenancy && detail.tenancy.ft_rent
          ? numberComma(detail.tenancy.ft_rent, 2)
          : "";
    const priceText = price ? " @" + price : "";

    return (
      <div className={`${classes.root} ${className}`}>
        <div className={classes.sourceRow}>
          <span>
            {date}
            {source}
            {txNature && <span className={classes.greyBox}>{txNature}</span>}
          </span>
        </div>

        <>
          <div className={classes.content}>
            <div>{district}  {streetName} {streetNo} </div>
            <div>{buildingnameZh}</div>
          </div>

          <div className={`${classes.content} ${classes.alignToright}`}>
            {areaData.map((v, i) =>
              <div key={i}>{v}</div>
            )}
          </div>
        </>

        <div className={classes.priceRow}>
          {priceTotal !== "" && (
            <VaryingVal
              className={`${classes.val} ${
                classes.cardPriceRow
                }`}
              label={intl.formatMessage({
                id: "stock.sold",
              })}
            >
              <span>
                {priceTotal} {priceText}
              </span>
            </VaryingVal>
          )}

          {priceTotal == "" && (
            <VaryingVal
              className={`${classes.val} ${
                classes.cardRentRow
                }`}
              label={intl.formatMessage({
                id: "stock.leased",
              })}
            >
              <span>{rentTotal} {priceText}</span>
            </VaryingVal>
          )}
        </div>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(TransactionCard));
