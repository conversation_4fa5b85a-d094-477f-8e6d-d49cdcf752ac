import fetch from "node-fetch";
import url from "url";
import moment from "moment";
import xml2js from "xml2js";
import { Readable } from "stream";
import _ from "lodash";

import config, { api, sbu, publicHost } from "../../config";
import mongodb from "../../data/mongodb";
import { getCompanyInfo } from "./company";

const parser = new xml2js.Parser({ explicitArray: false });
const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("");
const landsearchBaseUrl = api.landsearch;

function resJson(res, status, error, data) {
  res.json({
    status,
    errors: error ? [{ message: error }] : null,
    data,
  });
  res.end();
}

const fetchPdf = async (item) => {
  const request = await fetch(item);
  let url;
  if (
    !request.headers.get("content-length") ||
    request.headers.get("content-type") !== "application/pdf"
  ) {
    url = null;
  } else if (request.headers.get("content-length") < 1100) {
    url = null;
  } else {
    url = request.url;
  }
  return url;
};

// all possible filename for a landsearch doc
const alphabetUrlList = (basepath) =>
  [`${basepath}.pdf`].concat(alphabet.map((i) => `${basepath}-${i}.pdf`));

const getOtherSearchType = async (variables) => {
  let getothertypeURL = new URL(
    `${landsearchBaseUrl}/get_other_search_by_stock_id.asp`,
  );

  Object.keys(variables).forEach((key) =>
    getothertypeURL.searchParams.append(key, variables[key]),
  );

  const resp = await fetch(getothertypeURL, {
    method: "post",
  });

  if (resp.status !== 200 && resp.status !== 400) {
    throw new Error(resp.statusText);
  }
  const data = await resp.text();

  let returnJson = [];
  // convert xml response to json
  parser.parseString(data, (errors, json) => {
    if (errors) throw new Error(errors[0].message);

    delete json["xml"]["$"];
    delete json["xml"]["s:Schema"];

    if (json["xml"]["rs:data"] == "\r\n") {
      // no other search type for this id
    } else {
      const jsondata = json["xml"]["rs:data"]["z:row"];

      // result in array
      if (Array.isArray(jsondata)) {
        jsondata.map((i) => {
          let item = i["$"];
          // trim all the space for each item
          Object.keys(item).map((k) => (item[k] = item[k].trim()));
          returnJson.push({ ...item, stock_id: variables["stock_id"] });
        });
      } else {
        // only 1 result
        let singleItem = jsondata["$"];
        Object.keys(singleItem).forEach(
          (key) => (singleItem[key] = singleItem[key].trim()),
        );
        returnJson.push({ ...singleItem, stock_id: variables["stock_id"] });
      }
    }
  });

  // only get the doc path when there is other search type for this id
  if (Array.isArray(returnJson) && returnJson.length > 0) {
    // get pdf path by returned search_ref_no
    let searchDocURL = new URL(`${landsearchBaseUrl}/get_search_doc_path.asp`);
    searchDocURL.searchParams.append("org_code", "ICI");
    let docPathArray = [];

    // get the file path by iterating the search_ref_no
    await Promise.all(
      returnJson.map(async (i) => {
        // init an empty array for doc path
        i.list = [];
        const search_ref = i.search_ref_no;
        searchDocURL.searchParams.set("search_ref_no", search_ref);

        const resp = await fetch(searchDocURL, {
          method: "post",
        });
        if (resp.status !== 200 && resp.status !== 400) {
          throw new Error(resp.statusText);
        }
        const data = await resp.text();

        parser.parseString(data, (errors, json) => {
          if (errors) throw new Error(errors[0].message);

          delete json["xml"]["$"];
          delete json["xml"]["s:Schema"];

          const temp = json["xml"]["rs:data"]["z:row"];
          // result in array
          if (Array.isArray(temp)) {
            temp.map((j) => {
              let item = j["$"];
              let itemPath = item.filename;
              itemPath = itemPath.replace(/#/i, "%23");
              i.list.push(itemPath);
            });
          } else {
            // only 1 result
            let singleItem = temp["$"];
            let singleItemPath = singleItem.filename;
            singleItemPath = singleItemPath.replace(/#/i, "%23");
            i.list = [singleItemPath];
          }
        });
      }),
    );
  }

  return returnJson;
};

export const listCompanySearch = async (req, res, next, getQuery) => {
  let variables = req.body;
  let companysearchUrl,
    companyRegistrationNumber,
    search_ref_no,
    applicantNameEn,
    applicantNameZh,
    companyNameEn,
    companyId,
    applicantTeamCode;

  const companyQuery = await getQuery("LIST_COMPANY_QUERY");

  const companyData = _.get(
    await getCompanyInfo(
      companyQuery,
      {
        companySearchApplicantId: _.get(variables, "companySearchApplicantId"),
        companyRegistrationNumber: _.get(
          variables,
          "companyRegistrationNumber",
        ),
        companyName: [_.get(variables, "companyName") ?? ""],
      },
      {
        Authorization: req.headers["authorization"],
        "CAS-Authorization": req.headers["cas-authorization"],
      },
    ),
    "data.registeredCompany.0",
  );

  if (!_.isEmpty(companyData)) {
    companyRegistrationNumber = _.get(companyData, "companyRegistrationNumber");
    search_ref_no = _.get(companyData, "companySearch.0.search_ref_no") || "";
    companyId = _.get(companyData, "_id");
    companyNameEn = _.get(companyData, "companyNameEn") || "";
    applicantNameEn =
      _.get(companyData, "companySearch.0.createPersonNameEn") || "";
    applicantNameZh =
      _.get(companyData, "companySearch.0.createPersonNameZh") || "";
    applicantTeamCode =
      _.get(companyData, "companySearch.0.createPersonTeamCode") || "";
  } else {
    resJson(res, 300, "Company Not Found");
    return;
  }

  if (!_.isEmpty(companyRegistrationNumber)) {
    companysearchUrl = new URL(`${landsearchBaseUrl}/get_comp_info.asp`);
    companysearchUrl.searchParams.append("search_type", "CO");
    companysearchUrl.searchParams.append(
      "comp_code",
      companyRegistrationNumber,
    );
  } else {
    companysearchUrl = new URL(`${landsearchBaseUrl}/get_comp_by_name.asp`);
    companysearchUrl.searchParams.append("search_type", "CO");
    companysearchUrl.searchParams.append("comp_name", companyNameEn);
  }

  try {
    const resp = await fetch(companysearchUrl, {
      method: "post",
    });

    if (resp.status !== 200 && resp.status !== 400) {
      throw new Error(resp.statusText);
    }
    const data = await resp.text();
    let companysearchDataJson = [];
    // convert xml response to json
    parser.parseString(data, (errors, json) => {
      if (errors) throw new Error(errors[0].message);
      delete json["xml"]["$"];
      delete json["xml"]["s:Schema"];

      const companysearchData = json["xml"]["rs:data"]["z:row"];

      if (Array.isArray(companysearchData)) {
        companysearchData.map((i) => {
          let item = i["$"];
          // trim all the space for each item
          Object.keys(item).map((k) => (item[k] = item[k].trim()));
          companysearchDataJson.push(item);
        });
      } else {
        const singleItem = companysearchData["$"];
        Object.keys(singleItem).map(
          (k) => (singleItem[k] = singleItem[k].trim()),
        );
        companysearchDataJson.push(singleItem);
      }
    });

    // get file path for IND/COMM/SHOPS
    let finalArr = companysearchDataJson.map((j) => {
      const actionDate = moment(j.action_date, "YYYY-MM-DD");
      const actionDateYear = actionDate.format("YYYY");
      const pdfpathsuffix = "scs_pdf"; //actionDateYear <= 2008 ? "mrms_pdf_2" : "mrms_pdf";
      const searchType = "CO";

      const basepath = `${
        api.landsearchfilepath
      }/pdf/Watermark.aspx?co=ici&filepath=\\\\mssqlpid\\${pdfpathsuffix}\\${searchType}\\${actionDateYear}\\${actionDate.format(
        "MM",
      )}\\${actionDate.format("DD")}\\${j.file_name}`;
      // add a new field for a list of pdf path
      return { ...j, list: [basepath] };
    });

    finalArr.map((j) => {
      j.search_ref_no = search_ref_no;
      j.applicantNameEn = applicantNameEn;
      j.applicantNameZh = applicantNameZh;
      j.applicantTeamCode = applicantTeamCode;

      // --- check create date ---
      let createDateAfterOneDay = moment(j.action_date).add(1, "days");
      if (moment().isSameOrAfter(createDateAfterOneDay)) {
        j.isPrivate = false;
      } else {
        j.isPrivate = true;
      }
    });

    await Promise.all(
      finalArr.map(async (k) => {
        let hasPdfList = [];
        await Promise.all(
          k.list.map(async (pdfurl) => {
            const data = await fetchPdf(pdfurl);
            // only store the data if the url has pdf
            if (data) {
              hasPdfList.push(data);
            }
            k.list = hasPdfList;
          }),
        );
      }),
    );
    const savecompanysearchUrl = url.resolve(publicHost, "saveCompanySearch");
    const getcompanysearchUrl = url.resolve(publicHost, "getCompanySearchDoc");

    await Promise.all(
      finalArr.map(async (i) => {
        let wrappedFilePathList = [];
        if (Array.isArray(i.list) && i.list.length > 0) {
          await Promise.all(
            i.list.map(async (pdfurl) => {
              // wrap doc path list within local db
              // save each link to db (search_ref_no : url)
              const reqbody = {
                companyCode: i.comp_code,
                fileName: i.file_name,
                url: pdfurl,
              };

              const resp = await fetch(savecompanysearchUrl, {
                method: "post",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify(reqbody),
              });
              const data = await resp.json();

              const wrappedFilePath = `${getcompanysearchUrl}/${i.comp_code}/${i.file_name}`;
              wrappedFilePathList.push(wrappedFilePath);
              // replace current 'list' field by the wrapped file path list
              i.list = wrappedFilePathList;
            }),
          );
        }
      }),
    );

    resJson(res, 200, null, finalArr);
  } catch (e) {
    console.log(e);
    resJson(res, 300, e.message);
  }
};

export const listLandSearch = async (req, res, next) => {
  try{
    const url = `${api.icimsgeneral}/listLandSearch`;
    const body = {
      sbu, 
      stock_id: req.body.stock_id
    };
    const data = await fetch(url, {
      method: 'post',
      body: JSON.stringify(body),
      headers: {
        "content-type": "application/json",
        // Authorization: "Bearer I8D89l84JqNBsasyG5JF9WdwbDc2Gzos"
      }
    });
    const result = await data.json()
    console.log('[ listLandSearch result ] >', result)
    resJson(res, 200, null, result.data)
  } catch (e) {
    resJson(res, 300, e.message);
  }
};

export const listLandSearchPdf = async (req, res, next) => {
  try {
    let list = req.body.list;
    const landSearchRefNo = req.body.landSearchRefNo;

    const url = `${api.icimsgeneral}/listLandSearchPdf`;
    const body = {
      sbu, 
      list,
      landSearchRefNo
    };
    const data = await fetch(url, {
      method: 'post',
      body: JSON.stringify(body),
      headers: {
        "content-type": "application/json",
        // Authorization: "Bearer I8D89l84JqNBsasyG5JF9WdwbDc2Gzos"
      }
    });
    const result = await data.json()
    console.log('[ listLandSearchPdf result ] >', result)    
    resJson(res, 200, null, result.data);
  } catch (e) {
    console.error(e);
    resJson(res, 500, e.message);
    return {};
  }
}

export const saveCompanySearch = async (req, res, next) => {
  let variables = req.body;
  const query = {
    companyCode: req.body.companyCode,
    fileName: req.body.fileName,
  };
  const col = mongodb.db.collection("companysearch");
  col.updateOne(
    query,
    {
      $set: { url: req.body.url },
      $setOnInsert: { createDate: new Date() },
    },
    {
      upsert: true,
    },
    function (err, r) {
      if (err) {
        resJson(res, 300, "Failed to save record");
      } else if (r.upsertedCount > 0) {
        res.send({ addLandSearchSuccess: true });
      } else {
        res.send({ addLandSearchSuccess: true });
      }
    },
  );
};

export const getCompanySearchDoc = async (req, res, next) => {
  const query = {
    companyCode: req.params.companyCode,
    fileName: req.params.fileName,
  };
  // console.log(query);

  const col = mongodb.db.collection("companysearch").find(query);
  col.toArray(async (err, docs) => {
    // console.log(docs[0]);
    if (docs[0] && docs[0].url) {
      // as the doc path does not store the actual pdf file
      // call GET request to doc path in order to retrieve the pdf file

      const link = docs[0].url;
      const resp = await fetch(link, {
        method: "get",
      });

      // get the blob response (pdf file)
      const data = await resp.blob();

      const tempArrayBuffer = await data.arrayBuffer();
      const tmpBuffer = toBuffer(tempArrayBuffer);

      console.log("original buffer");
      console.log(tempArrayBuffer);
      console.log("just buffer");
      console.log(tmpBuffer);

      const buffer = new Buffer(tmpBuffer, "base64");
      const stream = new Readable();
      // readable._read = () => {}; // _read is required but you can noop it
      stream.push(buffer);
      stream.push(null);
      // console.log(stream);

      // ready for pdf response
      res.setHeader("Content-Length", tempArrayBuffer.byteLength);
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader("Content-Disposition", "attachment; filename=quote.pdf");
      stream.pipe(res);
      // let stream = await readFile(data.arrayBuffer());
    } else {
      resJson(res, 300, err);
    }
  });
};

export const saveLandSearch = async (req, res, next) => {
  // if (!req.user) {
  //     next();
  // } else {
  // }
  // bypassing auth for testing so that can call api directly by postman

  let variables = req.body;
  const query = {
    search_ref_no: req.body.search_ref_no,
    filename: req.body.filename
  };

  const col = mongodb.db.collection("landsearch");
  col.updateOne(
    query,
    {
      $set: { url: req.body.url },
      $setOnInsert: { createDate: new Date() },
    },
    {
      upsert: true,
    },
    function (err, r) {
      if (err) {
        resJson(res, 300, "Failed to save record");
      } else if (r.upsertedCount > 0) {
        res.send({ addLandSearchSuccess: true });
      } else {
        res.send({ addLandSearchSuccess: true });
      }
    },
  );
};

export const getLandSearchDoc = async (req, res, next) => {
  const query = {
    search_ref_no: req.params.id,
    filename: req.params.name
  };

  // call GET request to internal icims api to retrieve the pdf file
  try {
    const url = `${api.icimsgeneral}/getLandSearchDoc/${query.search_ref_no}/${query.filename}`;
    const resp = await fetch(url, {
      method: "get",
    });
    // get the blob response (pdf file)
    const data = await resp.blob();
    const tempArrayBuffer = await data.arrayBuffer();
    const tmpBuffer = Buffer.from(tempArrayBuffer);

    // console.log("original buffer");
    // console.log(tempArrayBuffer);
    // console.log("just buffer");
    // console.log(tmpBuffer);

    const buffer = new Buffer(tmpBuffer, "base64");
    const stream = new Readable();
    stream.push(buffer);
    stream.push(null);
    // console.log(stream);

    // ready for pdf response
    res.setHeader("Content-Length", tempArrayBuffer.byteLength);
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader("Content-Disposition", `attachment; filename="${encodeURIComponent(query.filename)}"`);
    stream.pipe(res);
  } catch (e) {
    console.error(e);
    resJson(res, 300, e.message);
  }
};

export const takeLandSearchLog = async (req, res, next) => {
  try {
    const args = {
      getHkid: req.body.requireHkid ? 1 : 0,
      logEmpId: _.get(req.body, "user.empId"),
      logSystemName: "MRMS",
      logLocation: req.body.location,
      consentStatus: req.body.status,
      stockId: req.body.stockId,
      searchType: "LD",
      searchRefNo: req.body.refNo,
      openDocPath: req.body.docPath,
      logUser: _.get(req.body, "user.userId"),
      logDept: _.get(req.body, "user.deptId"),
      logSeq: req.body.logSeq,
    };

    const resp = await fetch(config.api.landsearchLog, {
      method: "POST",
      headers: {
        "content-type": "application/json",
      },
      body: JSON.stringify(args),
    });
    const data = await resp.json();

    resJson(res, 200, null, data);
  } catch (e) {
    resJson(res, 300, e.message);
  }
};

function toBuffer(ab) {
  var buf = Buffer.alloc(ab.byteLength);
  var view = new Uint8Array(ab);
  for (var i = 0; i < buf.length; ++i) {
    buf[i] = view[i];
  }
  return buf;
}

export const applyLandSearch = async (req, res, next, getQuery) => {
  if (!req.user) {
    next();
  } else {
    try {
      const query = await getQuery("CREATE_LANDSEARCH_DETAIL");

      const details = { ...req.body.details };
      if (!details.user_id && _.get(req, "user.login.info.user_id", "")) {
        details.user_id = req.user.login.info.user_id;
      }

      const resp = await fetch(api.landSearchDetails, {
        method: "POST",
        headers: {
          "content-type": "application/json",
          Authorization: req.headers["authorization"],
          "CAS-Authorization": req.headers["cas-authorization"],
        },
        body: JSON.stringify({
          query,
          variables: {
            details,
          },
        }),
      }).then((response) => response.json());

      if (!_.isEmpty(_.get(resp, "errors"))) {
        resJson(res, 300, _.get(resp, "errors"));
      } else {
        resJson(res, 200, null, resp);
      }
    } catch (e) {
      console.error(e);
      resJson(res, 300, e);
    }
  }
};
