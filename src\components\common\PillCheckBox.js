import React from "react";
import { makeStyles } from "@material-ui/core/styles";
import Checkbox from "@material-ui/core/Checkbox";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import clsx from "clsx";

// We can inject some CSS into the DOM.
const styles = makeStyles((theme) => ({
  formControl: {
    margin: 0,
    width: "100%",
    "& > span": {
      width: "100%",
    },
  },
  root: {
    display: "none",
  },
  pill: {
    lineHeight: "34px",
    color: "#717171",
    borderRadius: "17px",
    padding: "0 10px",
    backgroundColor: "#DFDFDF",
    textAlign: "center",
  },
  checked: {
    color: "#FFF",
    backgroundColor: "#33CCCC",
  },
  disabled: {
    backgroundColor: "#e5e5e587",
    color: "#8d8d8d63",
  },
}));

function PillCheckBox(props) {
  const classes = styles();
  const { className, text, input, disabled } = props;

  return (
    <FormControlLabel
      className={`${classes.formControl} ${className}`}
      control={
        <Checkbox
          classes={{
            root: classes.root,
          }}
          checked={input.value ? true : false}
          onChange={input.onChange}
          disabled={disabled}
        />
      }
      label={
        <div
          className={clsx(classes.pill, {
            [classes.checked]: input.value,
            [classes.disabled]: disabled,
          })}
        >
          {text}
        </div>
      }
    />
  );
}

export default PillCheckBox;
