/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import Layout from "../../../../components/Layout";
import { checkAndParseUrlParam } from "../../../../helper/generalHelper";
import { FormattedMessage } from "react-intl";

const title = "My WWW";

async function action({ store, params, query }) {
  const { auth } = store.getState();
  if (!auth.user) {
    return { redirect: "/login" };
  } else if (auth.user.authorized === false) {
    return { redirect: "/login" };
  }

  const WWWStockPage = await require.ensure(
    [],
    (require) => require("./WWWStockPage").default,
    "wwwStockPage",
  );

  let parsedJson = checkAndParseUrlParam(query.param);
  let selectedData = checkAndParseUrlParam(query.selectedData);

  if (parsedJson === false || selectedData === false)
    return { redirect: "/error" };

  let headerRef = React.createRef();

  return {
    chunks: ["my-www-stock"],
    title,
    component: (
      <Layout
        headerRef={headerRef}
        path="search"
        header={<FormattedMessage id="home.wwwStock" />}
        isAdvanced={true}
        isSticky={true}
        hideSearchIcon={true}
      >
        <WWWStockPage
          selectedData={selectedData}
          queryvariablesFromUrl={parsedJson}
          isAdvanced={"true"}
          headerRef={headerRef}
        />
      </Layout>
    ),
  };
}

export default action;
