import React, { useState } from "react";
import _ from "lodash";
import { connect } from "react-redux";
import { Field, FieldArray, change } from "redux-form";
import { injectIntl, FormattedMessage } from "react-intl";
import {
  OutlinedInput,
  InputAdornment,
  Grid,
  Collapse,
} from "@material-ui/core";
import { withStyles } from "@material-ui/core/styles";
import DetailBoxSection from "@/components/common/DetailBoxSection";
import TextInputCombined from "@/components/common/TextInputCombined";
import ArrayPillCheckBox from "@/components/common/ArrayPillCheckBox";
import PillCheckBox from "@/components/common/PillCheckBox";
import PillButton from "@/components/common/PillButton";
import SelectFieldArrayOutput from "@/components/common/SelectFieldArrayOutput";
import ReactSelectCreatable from "@/components/common/ReactSelectCreatable";
import ChipsCheckBox from "@/components/common/ChipsCheckBox";
import AutoCompleteSelect from "@/components/common/AutoCompleteSelect";
import Search from "@/components/common/Search";
import Switch from "@/components/common/Switch";
import { minValue, maxValue, number } from "@/core/formValidators";
import { listBuildings } from "@/actions/building";
import { streetSearch } from "@/actions/street";
import { listCompanies } from "@/actions/stocklist";
import { getLangKey } from "@/helper/generalHelper";
import { enableConsolidLandSearch } from "@/config";

// We can inject some CSS into the DOM.
const styles = {
  expendbutton: {
    textTransform: "none",
    display: "flex",
    marginLeft: "auto",
    color: "#33CCCC",
  },
  divider: {
    textAlign: "center",
    margin: "auto 0px",
  },
  Headertitle: {
    color: "rgba(0, 0, 0, 0.54)",
    fontSize: "1em",
    fontWeight: "400",
  },
  DetailBoxSectionContent: {
    paddingLeft: 0,
    paddingRight: 0,
    paddingBottom: "3vh",
  },
  sticky: {
    position: "-webkit-sticky" /* Safari */,
    position: "sticky",
    top: "52px",
  },
  sectionTitleBtnContainer: {
    "& > button": {
      marginLeft: "1vw",
      fontSize: 12,
    },
  },
  fieldsdivider: {
    marginTop: 8,
    marginBottom: 10,
    borderRadius: 0,
    "& input": {
      textAlign: "center",
      transform: "scale(1.6)",
    },
    "& .MuiOutlinedInput-notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23)",
      borderLeft: 0,
      borderRight: 0,
    },
    "&:hover .MuiOutlinedInput-notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23)",
    },
    "& $focused $notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23) !important",
    },
  },
  fieldMargin: {
    marginTop: 8,
    marginBottom: 10,
  },
  paddingSection: {
    paddingBottom: "3vh",
  },
};

const boolranges = [
  {
    value: "",
    label: "All",
  },
  {
    value: true,
    label: "True",
  },
  {
    value: false,
    label: "False",
  },
];

function getstatusOptions(intl) {
  const statusOptions = [
    {
      value: "Sale",
      label: intl.formatMessage({
        id: "search.status.sale",
      }),
    },
    {
      value: "Lease",
      label: intl.formatMessage({
        id: "search.status.lease",
      }),
    },
    {
      value: "Tenanted",
      label: intl.formatMessage({
        id: "search.status.saleslease",
      }),
    },
    {
      value: "Sale+Lease",
      label: intl.formatMessage({
        id: "search.status.salesandlease",
      }),
    },
    {
      value: "Pending",
      label: intl.formatMessage({
        id: "search.status.pending",
      }),
    },
    {
      value: "TEL",
      label: intl.formatMessage({
        id: "search.status.tel",
      }),
    },
    {
      value: "Search",
      label: intl.formatMessage({
        id: "search.status.se",
      }),
    },
    {
      value: "Don't Call",
      label: intl.formatMessage({
        id: "search.status.dontcall",
      }),
    },
    {
      value: "Sold",
      label: intl.formatMessage({
        id: "search.status.sold",
      }),
    },
    {
      value: "Leased",
      label: intl.formatMessage({
        id: "search.status.leased",
      }),
    },
    {
      value: "History",
      label: intl.formatMessage({
        id: "search.status.history",
      }),
    },
    {
      value: "Cancel",
      label: intl.formatMessage({
        id: "search.status.cancel",
      }),
    },
  ];
  return statusOptions;
}

function getpropertyconditionOptions(intl) {
  const miscOptions = [
    {
      value: "isMortgagee",
      label: intl.formatMessage({
        id: "search.form.mortgagee",
      }),
    },
    {
      value: "haveSurveyorProposal",
      label: intl.formatMessage({
        id: "search.form.surveryorPP",
      }),
    },
    {
      value: "underNego",
      label: intl.formatMessage({
        id: "search.form.underNego",
      }),
    },
    {
      value: "isSaleEquity",
      label: intl.formatMessage({
        id: "search.form.saleequity",
      }),
    },
    {
      value: "isWithKey",
      label: intl.formatMessage({
        id: "search.form.withkey",
      }),
    },
    {
      value: "withPassCodes",
      label: intl.formatMessage({
        id: "search.form.passcode",
      }),
    },
    {
      value: "isCarPark",
      label: intl.formatMessage({
        id: "search.form.carpark",
      }),
    },
    {
      value: "isParentStock",
      label: intl.formatMessage({
        id: "search.form.parentstock",
      }),
    },
    {
      value: "isChildStock",
      label: intl.formatMessage({
        id: "search.form.childstock",
      }),
    },
    // {
    //   value: "confirmorStatus",
    //   label: intl.formatMessage({
    //     id: "search.form.confirmor",
    //   }),
    // },
    // {
    //   value: "haveSurveyorProposal",
    //   label: intl.formatMessage({
    //     id: "search.form.surveryorPP",
    //   }),
    // },
    {
      value: "pdfPP",
      label: intl.formatMessage({
        id: "search.form.pdfPP",
      }),
    },
    {
      value: "haveVR",
      label: intl.formatMessage({
        id: "search.form.vr",
      }),
    },
    {
      value: "haveStockVideo",
      label: intl.formatMessage({
        id: "search.form.haveStockVideo",
      }),
    },
    {
      value: "haveBuildingVideo",
      label: intl.formatMessage({
        id: "search.form.haveBuildingVideo",
      }),
    },
    {
      value: "haveStockPhoto",
      label: intl.formatMessage({
        id: "search.form.haveStockPhoto",
      }),
    },
    {
      value: "haveBuildingPhoto",
      label: intl.formatMessage({
        id: "search.form.haveBuildingPhoto",
      }),
    },
    {
      value: "havePropertyAdvertisements",
      label: intl.formatMessage({
        id: "search.form.eaa",
      }),
    },
    {
      value: "haveLandSearchDoc",
      label: intl.formatMessage({
        id: "search.form.landSearch",
      }),
    },
  ];
  return miscOptions;
}

function getMarketableMapping(intl) {
  const marketableMapping = {
    [intl.formatMessage({
      id: "search.new",
    })]: "isNew",
    [intl.formatMessage({
      id: "search.header.marketable",
    })]: "isMarketable",
    [intl.formatMessage({
      id: "search.form.issoleagent",
    })]: "isSoleAgent",
    [intl.formatMessage({
      id: "stock.tag.www",
    })]: "isWWW",
  };
  return marketableMapping;
}

const unitM = {
  endAdornment: (
    <InputAdornment position="end">
      <span>M</span>
    </InputAdornment>
  ),
};

const unitK = {
  endAdornment: (
    <InputAdornment position="end">
      <span>K</span>
    </InputAdornment>
  ),
};

const unitFormatter = (value, unit) => {
  if (!value || value == null) return "";

  let unitValue;
  if (unit == "K") {
    unitValue = 1000;
  } else if (unit == "M") {
    unitValue = 1000000;
  } else {
    unitValue = 1;
  }
  return value / unitValue;
};

const formatMaxLength = (value, length) => {
  if (!value) {
    return value;
  }

  if (value.length <= length) {
    return `${value.slice(0, length)}`;
  }
  return `${value.slice(0, length)}`;
};

const formatUnitandMaxLength = (value, unit) => {
  if (!value) {
    return value;
  }

  value = value.replace(/[^0-9.]/g, "");

  let unitValue;
  if (unit == "K") unitValue = 1000;
  if (unit == "M") unitValue = 1000000;

  let number = value * unitValue;
  const roundnumber = number.toFixed();
  // return the value after rounding
  return `${roundnumber.toString()}`;
};

function createMinvalue(min) {
  return minValue(min, <FormattedMessage id="search.form.invalidinput" />);
}
function createMaxvalue(max) {
  return maxValue(max, <FormattedMessage id="search.form.invalidinput" />);
}
const numberValidate = number(
  <FormattedMessage id="search.form.invalidinput" />,
);
const minvaluezero = createMinvalue(0);
const maxvalue1 = createMaxvalue(200);
const maxvalue2 = createMaxvalue(999);
const maxvalue3 = createMaxvalue(9999);
const maxvalue4 = createMaxvalue(99999);
const maxvalue5 = createMaxvalue(9999999);
const maxvalue6 = createMaxvalue(99999999);
const maxvalue7 = createMaxvalue(999999999);
const maxvalue8 = createMaxvalue(9999999999);

function FieldSection(props) {
  const {
    classes,
    buildings,
    fieldhistory,
    streets,
    districts,
    listBuildings,
    searchStreets,
    decoration,
    possession,
    currentState,
    stockType,
    unitview,
    selectedData,
    setSelectedData,
    expanded,
    intl,
    changeForm,
    initialValues,
    companies,
    listCompanies,
    retainMarkStocks,
    onRetainMarkStocksClick,
  } = props;

  const grades = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "A",
      label: "A",
    },
  ];

  const usages = [
    {
      value: "Site",
      label: intl.locale === 'zh' ? "地皮" : "Site",
    },
    {
      value: "Shop",
      label: intl.locale === 'zh' ? "商舖" : "Shop",
    },
    {
      value: "Commercial",
      label: intl.locale === 'zh' ? "商業" : "Commercial",
    },
    {
      value: "Godown",
      label: intl.locale === 'zh' ? "貨倉" : "Godown",
    },
    {
      value: "I/O",
      label: intl.locale === 'zh' ? "工商綜合" : "I/O",
    },
    {
      value: "Industrial",
      label: intl.locale === 'zh' ? "工業" : "Industrial",
    },
    {
      value: "Motor Vehicles Workshop",
      label: intl.locale === 'zh' ? "汽車工場" : "Motor Vehicles Workshop",
    },
    {
      value: "Carport",
      label: intl.locale === 'zh' ? "停車場" : "Carport",
    },
    {
      value: "Cold Storage",
      label: intl.locale === 'zh' ? "凍倉" : "Cold Storage",
    },
    {
      value: "Hotel",
      label: intl.locale === 'zh' ? "酒店" : "Hotel",
    },
    {
      value: "Canteen",
      label: intl.locale === 'zh' ? "食堂" : "Canteen",
    },
    {
      value: "Office",
      label: intl.locale === 'zh' ? "寫字樓" : "Office",
    },
    {
      value: "Industrial & Carport",
      label: intl.locale === 'zh' ? "工業及停車場" : "Industrial & Carport",
    },
    {
      value: "Godown & Carport",
      label: intl.locale === 'zh' ? "貨倉及停車場" : "Godown & Carport",
    },
    {
      value: "Godown & Workshop",
      label: intl.locale === 'zh' ? "貨倉及工場" : "Godown & Workshop",
    },
    {
      value: "Shop & Office",
      label: intl.locale === 'zh' ? "商舖及寫字樓" : "Shop & Office",
    },
    {
      value: "Ancillary Office",
      label: intl.locale === 'zh' ? "附屬性寫字樓" : "Ancillary Office",
    },
  ];

  const ownerTypes = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "Single Owner",
      label: intl.formatMessage({
        id: "search.form.singleowner",
      }),
    },
    {
      value: "Investor",
      label: intl.formatMessage({
        id: "search.form.investor",
      }),
    },
    {
      value: "Strata Title",
      label: intl.formatMessage({
        id: "search.form.stratatitle",
      }),
    },
    {
      value: "Cooperation",
      label: intl.formatMessage({
        id: "search.form.cooperation",
      }),
    },
    {
      value: "Mortgagee",
      label: intl.formatMessage({
        id: "search.form.mortgagee",
      }),
    },
    {
      value: "Developer",
      label: intl.formatMessage({
        id: "search.form.developer",
      }),
    },
    {
      value: "HandOver",
      label: intl.formatMessage({
        id: "search.form.handover",
      }),
    },
  ];

  const entranceContainer = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "20-foot equivalent unit",
      label: intl.formatMessage({
        id: "search.form.20-foot-equivalent-unit",
      }),
    },
    {
      value: "40-foot equivalent unit",
      label: intl.formatMessage({
        id: "search.form.40-foot-equivalent-unit",
      }),
    },
    {
      value: "H40-foot equivalent unit",
      label: intl.formatMessage({
        id: "search.form.H40-foot-equivalent-unit",
      }),
    },
    {
      value: "16-ton equivalent unit",
      label: intl.formatMessage({
        id: "search.form.16-ton-equivalent-unit",
      }),
    },
    {
      value: "trucks",
      label: intl.formatMessage({
        id: "search.form.trucks",
      }),
    },
  ];

  const createMaxValue = (max) => {
    console.log("creating max value...");
    const maxvalue = maxValue(max);
    return maxvalue;
  };

  let priceType, rentType;

  if (initialValues.priceMinTotal || initialValues.priceMaxTotal)
    priceType = "Total";
  if (initialValues.priceMinAvg || initialValues.priceMaxAvg) priceType = "Avg";
  if (initialValues.rentMinTotal || initialValues.rentMaxTotal)
    rentType = "Avg";
  if (initialValues.rentMinAvg || initialValues.rentMaxAvg) rentType = "Avg";
  const [TogglePriceValue, setTogglePriceValue] = useState(
    priceType ? priceType : "Total",
  );
  const [ToggleRentValue, setToggleRentValue] = useState(
    rentType ? rentType : "Total",
  );

  const langkey = getLangKey(intl);
  const decorationselection = (decoration || []).map((item) => ({
    value: item["_id"],
    label: item[langkey],
  }));

  const possessionselection = (possession || []).map((item) => ({
    value: item["nameEn"],
    label: item[langkey] !== "" ? item[langkey] : item["nameEn"],
  }));

  const currentStateselection = (currentState || []).map((item) => ({
    value: item["_id"],
    label: item[langkey] !== "" ? item[langkey] : item["nameEn"],
  }));

  const stockTypeselection = (stockType || []).map((item) => ({
    value: item["nameEn"],
    label: item[langkey] !== "" ? item[langkey] : item["nameEn"],
  }));

  const viewselection = (unitview || []).map((item) => ({
    value: item["_id"],
    label: item[langkey],
  }));

  const deselectAllStatus = () => {
    changeForm("status", []);
  };

  const onChangePriceTotalAvg = () => {
    if (TogglePriceValue === "Total") {
      changeForm("priceMinTotal", "");
      changeForm("priceMaxTotal", "");
      setTogglePriceValue("Avg");
    } else if (TogglePriceValue === "Avg") {
      changeForm("priceMinAvg", "");
      changeForm("priceMaxAvg", "");
      setTogglePriceValue("Total");
    }
  };

  const onChangeRentTotalAvg = () => {
    if (ToggleRentValue === "Total") {
      changeForm("rentMinTotal", "");
      changeForm("rentMaxTotal", "");
      setToggleRentValue("Avg");
    } else if (ToggleRentValue === "Avg") {
      changeForm("rentMinAvg", "");
      changeForm("rentMaxAvg", "");
      setToggleRentValue("Total");
    }
  };

  const selectAllStatus = () => {
    const statusValue = getstatusOptions(intl).map((v) => v.value);
    changeForm("status", statusValue);
  };

  const deselectAllMisc = () => {
    const miscOptions = getpropertyconditionOptions(intl).map((v) => v.value);
    for (let key in miscOptions) {
      changeForm(miscOptions[key], false);
    }
  };

  const selectAllMisc = () => {
    const miscOptions = getpropertyconditionOptions(intl).map((v) => v.value);
    for (let key in miscOptions) {
      changeForm(miscOptions[key], true);
    }
  };

  const dateonFocus = (e) => {
    e.target.placeholder = "";
    e.target.type = "date";
  };

  const dateonBlur = (e) => (minormax) => {
    e.target.placeholder = intl.formatMessage({
      id: `search.form.common.${minormax}`,
    });
  };

  const unicornIdIsSet = !_.isEmpty(_.get(selectedData, "unicornId"));

  return (
    <div>
      {!expanded ? (
        <div className={classes.sticky}>
          <Field
            name="buildingSourcesId"
            margin="normal"
            label={intl.formatMessage({
              id: "search.form.building",
            })}
            fullWidth
            component={AutoCompleteSelect}
            optionsdata={buildings}
            history={fieldhistory}
            apiaction={listBuildings}
            limit={99}
            selectedData={selectedData && selectedData.buildingSourcesId}
            setSelectedData={setSelectedData}
            placeholder={unicornIdIsSet && "---"}
            disabled={unicornIdIsSet}
          />
        </div>
      ) : (
        <Field
          name="buildingSourcesId"
          margin="normal"
          label={intl.formatMessage({
            id: "search.form.building",
          })}
          fullWidth
          component={AutoCompleteSelect}
          optionsdata={buildings}
          apiaction={listBuildings}
          limit={99}
          selectedData={selectedData && selectedData.buildingSourcesId}
          setSelectedData={setSelectedData}
          customInputProps={{
            className: classes.fieldMargin,
          }}
          placeholder={unicornIdIsSet && "---"}
          disabled={unicornIdIsSet}
        />
      )}

      {/* <Field
          name="building"
          type="text"
          margin="normal"
          label="Building"
          component={TextInput}
          fullWidth
          variant="outlined"
        /> */}

      <Collapse in={expanded}>
        <div className={classes.paddingSection}>
          <Field
            name="district"
            margin="normal"
            label={intl.formatMessage({
              id: "search.form.district",
            })}
            fullWidth
            component={Search}
            searchItems={districts}
            selectedData={selectedData && selectedData.district}
            setSelectedData={setSelectedData}
            customInputProps={{
              className: classes.fieldMargin,
            }}
            placeholder={unicornIdIsSet && "---"}
            disabled={unicornIdIsSet}
          />

          <DetailBoxSection
            text={intl.formatMessage({
              id: "search.header.status",
            })}
            titleClass={classes.Headertitle}
            contentClass={classes.DetailBoxSectionContent}
            customRight={
              <div className={classes.sectionTitleBtnContainer}>
                <PillButton onClick={selectAllStatus} disabled={unicornIdIsSet}>
                  {intl.formatMessage({
                    id: "search.form.all",
                  })}
                </PillButton>
                <PillButton
                  onClick={deselectAllStatus}
                  disabled={unicornIdIsSet}
                >
                  {intl.formatMessage({
                    id: "search.form.none",
                  })}
                </PillButton>
              </div>
            }
          >
            <FieldArray
              name="status"
              component={ArrayPillCheckBox}
              options={getstatusOptions(intl)}
              disabled={unicornIdIsSet}
            />
          </DetailBoxSection>

          <DetailBoxSection
            text={intl.formatMessage({
              id: "search.header.marketable",
            })}
            titleClass={classes.Headertitle}
            contentClass={classes.DetailBoxSectionContent}
          >
            <Grid container spacing={1}>
              {Object.keys(getMarketableMapping(intl)).map((v, i) => (
                <Grid key={v} item xs={4}>
                  <div>
                    <Field
                      name={getMarketableMapping(intl)[v]}
                      text={v}
                      component={PillCheckBox}
                      disabled={unicornIdIsSet}
                    />
                  </div>
                </Grid>
              ))}
            </Grid>
          </DetailBoxSection>

          {/* last update date */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="lastUpdateDateMin"
                  type={unicornIdIsSet ? "text" : "date"}
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.lastupdatedate",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="lastUpdateDateMax"
                  type={unicornIdIsSet ? "text" : "date"}
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          <Grid item xs={12}>
            <Field
              name="unicornId"
              margin="normal"
              label={intl.formatMessage({
                id: "search.header.stockid",
              })}
              fullWidth
              component={ReactSelectCreatable}
              optionsdata={[]}
              customInputProps={{
                className: classes.fieldMargin,
              }}
              selectedData={selectedData && selectedData.unicornId}
              setSelectedData={setSelectedData}
              autoCreateOnBlur
            />
          </Grid>
          {/* <Field
            name="unicornId"
            type="number"
            margin="normal"
            className={classes.fieldMargin}
            label={intl.formatMessage({
              id: "search.header.stockid",
            })}
            component={TextArrayInput}
            fullWidth
            validate={[numberValidate, minvaluezero, maxvalue5]}
            variant="outlined"
          /> */}

          {/* area */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="areaMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.area",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"left"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="areaMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue8]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"right"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          {/* price */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"priceMin" + TogglePriceValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  label={intl.formatMessage({
                    id: "search.common.price",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  validate={
                    TogglePriceValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue8]
                      : [minvaluezero]
                  }
                  InputProps={TogglePriceValue === "Total" ? unitM : null}
                  format={(value) => {
                    let formattedvalue;
                    if (TogglePriceValue === "Total") {
                      formattedvalue = unitFormatter(value, "M");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (TogglePriceValue === "Total") {
                      return formatUnitandMaxLength(value, "M");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"priceMax" + TogglePriceValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  validate={
                    TogglePriceValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue8]
                      : [minvaluezero, maxvalue8]
                  }
                  InputProps={TogglePriceValue == "Total" && unitM}
                  format={(value) => {
                    let formattedvalue;
                    if (TogglePriceValue === "Total") {
                      formattedvalue = unitFormatter(value, "M");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (TogglePriceValue === "Total") {
                      return formatUnitandMaxLength(value, "M");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                  rightcomp={
                    <Switch
                      value={TogglePriceValue}
                      textL={intl.formatMessage({
                        id: "search.common.pricetotal",
                      })}
                      textR={intl.formatMessage({
                        id: "search.common.priceavg",
                      })}
                      valleft="Total"
                      valright="Avg"
                      onChange={onChangePriceTotalAvg}
                      disabled={unicornIdIsSet}
                    />
                  }
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          {/* rent */}
          <Grid container className={classes.paddingSection}>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"rentMin" + ToggleRentValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  label={intl.formatMessage({
                    id: "search.common.rent",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  validate={
                    ToggleRentValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue6]
                      : [minvaluezero]
                  }
                  InputProps={ToggleRentValue === "Total" && unitK}
                  format={(value) => {
                    let formattedvalue;
                    if (ToggleRentValue === "Total") {
                      formattedvalue = unitFormatter(value, "K");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (ToggleRentValue === "Total") {
                      return formatUnitandMaxLength(value, "K");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"rentMax" + ToggleRentValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  validate={
                    ToggleRentValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue6]
                      : [minvaluezero, maxvalue8]
                  }
                  InputProps={ToggleRentValue === "Total" && unitK}
                  format={(value) => {
                    let formattedvalue;
                    if (ToggleRentValue === "Total") {
                      formattedvalue = unitFormatter(value, "K");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (ToggleRentValue === "Total") {
                      return formatUnitandMaxLength(value, "K");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                  rightcomp={
                    <Switch
                      value={ToggleRentValue}
                      textL={intl.formatMessage({
                        id: "search.common.renttotal",
                      })}
                      textR={intl.formatMessage({
                        id: "search.common.rentavg",
                      })}
                      valleft="Total"
                      valright="Avg"
                      onChange={onChangeRentTotalAvg}
                      disabled={unicornIdIsSet}
                    />
                  }
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          {/* street */}
          <Grid container>
            <Grid item xs={12}>
              <Field
                name="street"
                margin="normal"
                label={intl.formatMessage({
                  id: "search.form.street",
                })}
                fullWidth
                component={AutoCompleteSelect}
                optionsdata={streets}
                apiaction={searchStreets}
                selectedData={selectedData && selectedData.street}
                setSelectedData={setSelectedData}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                placeholder={unicornIdIsSet && "---"}
                disabled={unicornIdIsSet}
              />
            </Grid>

            <Grid item xs={5}>
              <div>
                <Field
                  name="streetNoMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.form.streetnumber",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 4)}
                  compPosition={"left"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="streetNoMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue3]}
                  parse={(value) => formatMaxLength(value, 4)}
                  compPosition={"right"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          {/* floor number */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="floorLeft"
                  type="text"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.form.floor",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="floorRight"
                  type="text"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          {/* building grade */}
          <Grid container>
            <Grid item xs={5}>
              <Field
                name="buildingGrade"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.grade",
                })}
                ranges={unicornIdIsSet ? [{ value: "", label: "---" }] : grades}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                disabled={unicornIdIsSet}
              />
            </Grid>
            <Grid item xs={2} className={classes.divider}>
              {""}
            </Grid>
            <Grid item xs={5}></Grid>

            <Grid item xs={12}>
              <Field
                name="buildingUsage"
                margin="normal"
                component={Search}
                valueField="value"
                labelField="label"
                label={intl.formatMessage({
                  id: "search.form.usage",
                })}
                fullWidth
                searchItems={usages}
                selectedData={selectedData && selectedData.buildingUsage}
                setSelectedData={setSelectedData}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                placeholder={unicornIdIsSet && "---"}
                disabled={unicornIdIsSet}
              />
            </Grid>
          </Grid>

          {/* age */}
          <Grid container className={classes.paddingSection}>
            <Grid item xs={5}>
              <div>
                <Field
                  name="ageMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.age",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 3)}
                  compPosition={"left"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="ageMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue2]}
                  parse={(value) => formatMaxLength(value, 3)}
                  compPosition={"right"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          <DetailBoxSection
            text={intl.formatMessage({
              id: "search.header.miscellaneous",
            })}
            titleClass={classes.Headertitle}
            contentClass={classes.DetailBoxSectionContent}
            customRight={
              <div className={classes.sectionTitleBtnContainer}>
                <PillButton onClick={selectAllMisc} disabled={unicornIdIsSet}>
                  {intl.formatMessage({
                    id: "search.form.all",
                  })}
                </PillButton>
                <PillButton onClick={deselectAllMisc} disabled={unicornIdIsSet}>
                  {intl.formatMessage({
                    id: "search.form.none",
                  })}
                </PillButton>
              </div>
            }
          >
            <div>
              <Grid container spacing={1}>
                {getpropertyconditionOptions(intl).map((v, i) => {
                  return (
                    <Grid key={i} item xs={4}>
                      <div>
                        <Field
                          name={v.value}
                          text={v.label}
                          component={PillCheckBox}
                          disabled={unicornIdIsSet}
                        />
                      </div>
                    </Grid>
                  );
                })}
              </Grid>
            </div>
          </DetailBoxSection>

          <Grid container>
            {/* create date */}
            <Grid container>
              <Grid item xs={5}>
                <div>
                  <Field
                    name="createDateMin"
                    type={unicornIdIsSet ? "text" : "date"}
                    margin="normal"
                    className={classes.fieldMargin}
                    label={intl.formatMessage({
                      id: "search.common.createdate",
                    })}
                    placeholder={
                      unicornIdIsSet
                        ? "---"
                        : intl.formatMessage({
                            id: "search.form.common.min",
                          })
                    }
                    component={TextInputCombined}
                    fullWidth
                    variant="outlined"
                    compPosition={"left"}
                    disabled={unicornIdIsSet}
                  />
                </div>
              </Grid>
              <Grid item xs={2}>
                <OutlinedInput
                  disabled
                  readOnly={true}
                  defaultValue="|"
                  variant="outlined"
                  className={classes.fieldsdivider}
                />
              </Grid>
              <Grid item xs={5}>
                <div>
                  <Field
                    name="createDateMax"
                    type={unicornIdIsSet ? "text" : "date"}
                    margin="normal"
                    className={classes.fieldMargin}
                    placeholder={
                      unicornIdIsSet
                        ? "---"
                        : intl.formatMessage({
                            id: "search.form.common.max",
                          })
                    }
                    component={TextInputCombined}
                    fullWidth
                    variant="outlined"
                    compPosition={"right"}
                    disabled={unicornIdIsSet}
                  />
                </div>
              </Grid>
            </Grid>

            {/* tenancy expiry date */}
            <Grid container>
              <Grid item xs={5}>
                <div>
                  <Field
                    name="tenancyExpireDateMin"
                    type={unicornIdIsSet ? "text" : "date"}
                    margin="normal"
                    className={classes.fieldMargin}
                    label={intl.formatMessage({
                      id: "search.common.tenancy",
                    })}
                    placeholder={
                      unicornIdIsSet
                        ? "---"
                        : intl.formatMessage({
                            id: "search.form.common.min",
                          })
                    }
                    component={TextInputCombined}
                    fullWidth
                    variant="outlined"
                    compPosition={"left"}
                    disabled={unicornIdIsSet}
                  />
                </div>
              </Grid>
              <Grid item xs={2}>
                <OutlinedInput
                  disabled
                  readOnly={true}
                  defaultValue="|"
                  variant="outlined"
                  className={classes.fieldsdivider}
                />
              </Grid>
              <Grid item xs={5}>
                <div>
                  <Field
                    name="tenancyExpireDateMax"
                    type={unicornIdIsSet ? "text" : "date"}
                    margin="normal"
                    className={classes.fieldMargin}
                    placeholder={
                      unicornIdIsSet
                        ? "---"
                        : intl.formatMessage({
                            id: "search.form.common.max",
                          })
                    }
                    component={TextInputCombined}
                    fullWidth
                    variant="outlined"
                    compPosition={"right"}
                    disabled={unicornIdIsSet}
                  />
                </div>
              </Grid>
            </Grid>

            <Grid item xs={12}>
              <Field
                name="stockType"
                margin="normal"
                component={Search}
                valueField="value"
                labelField="label"
                label={intl.formatMessage({
                  id: "search.form.stockType",
                })}
                fullWidth
                searchItems={stockTypeselection}
                selectedData={selectedData && selectedData.stockType}
                setSelectedData={setSelectedData}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                placeholder={unicornIdIsSet && "---"}
                disabled={unicornIdIsSet}
              />
            </Grid>
            <Grid item xs={12}>
              <Field
                name="ownerType"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.ownertype",
                })}
                ranges={
                  unicornIdIsSet ? [{ value: "", label: "---" }] : ownerTypes
                }
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                disabled={unicornIdIsSet}
              />
            </Grid>
            <Grid item xs={12}>
              <Field
                name="possession"
                margin="normal"
                component={Search}
                valueField="value"
                labelField="label"
                label={intl.formatMessage({
                  id: "search.form.possession",
                })}
                fullWidth
                searchItems={possessionselection}
                selectedData={selectedData && selectedData.possession}
                setSelectedData={setSelectedData}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                placeholder={unicornIdIsSet && "---"}
                disabled={unicornIdIsSet}
              />
            </Grid>
            <Grid item xs={12}>
              <Field
                name="decoration"
                margin="normal"
                component={Search}
                valueField="value"
                labelField="label"
                label={intl.formatMessage({
                  id: "search.form.decoration",
                })}
                fullWidth
                searchItems={decorationselection}
                selectedData={selectedData && selectedData.decoration}
                setSelectedData={setSelectedData}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                placeholder={unicornIdIsSet && "---"}
                disabled={unicornIdIsSet}
              />
            </Grid>
            <Grid item xs={12}>
              <Field
                name="unitView"
                margin="normal"
                component={Search}
                valueField="value"
                labelField="label"
                label={intl.formatMessage({
                  id: "search.form.view",
                })}
                fullWidth
                searchItems={viewselection}
                selectedData={selectedData && selectedData.unitView}
                setSelectedData={setSelectedData}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                placeholder={unicornIdIsSet && "---"}
                disabled={unicornIdIsSet}
              />
            </Grid>
            <Grid item xs={12}>
              <Field
                name="currentState"
                margin="normal"
                component={Search}
                valueField="value"
                labelField="label"
                label={intl.formatMessage({
                  id: "search.form.currentState",
                })}
                fullWidth
                searchItems={currentStateselection}
                selectedData={selectedData && selectedData.currentState}
                setSelectedData={setSelectedData}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                placeholder={unicornIdIsSet && "---"}
                disabled={unicornIdIsSet}
              />
            </Grid>
            {/* <Grid item xs={12}>
              <Field
                name="entranceContainer"
                margin="normal"
                component={Search}
                valueField="value"
                labelField="label"
                label={intl.formatMessage({
                  id: "building.container",
                })}
                fullWidth
                searchItems={entranceContainer}
                selectedData={selectedData && selectedData.entranceContainer}
                setSelectedData={setSelectedData}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                placeholder={unicornIdIsSet && "---"}
                disabled={unicornIdIsSet}
              />
            </Grid> */}
          </Grid>
        </div>

        {enableConsolidLandSearch == "true" && (
          <DetailBoxSection
            text={intl.formatMessage({
              id: "search.header.consolidate",
            })}
            titleClass={classes.Headertitle}
            contentClass={classes.DetailBoxSectionContent}
          >
            <Grid container>
              <Grid item xs={6}>
                <Field
                  name="isVendor"
                  label={intl.formatMessage({ id: "search.form.owner" })}
                  component={ChipsCheckBox}
                  disabled={unicornIdIsSet}
                />
              </Grid>
              <Grid item xs={6}>
                <Field
                  name="isCurrent"
                  label={intl.formatMessage(
                    { id: "stock.tenant" },
                    {
                      status: intl.formatMessage({
                        id: "stock.currenttenancy",
                      }),
                    },
                  )}
                  component={ChipsCheckBox}
                  disabled={unicornIdIsSet}
                />
              </Grid>
              <Grid item xs={6}>
                <Field
                  name="isFormer"
                  label={intl.formatMessage(
                    { id: "stock.tenant" },
                    {
                      status: intl.formatMessage({
                        id: "stock.previoustenancy",
                      }),
                    },
                  )}
                  component={ChipsCheckBox}
                  disabled={unicornIdIsSet}
                />
              </Grid>

              <Grid item xs={12}>
                <Field
                  name="contactsPerson"
                  margin="normal"
                  label={intl.formatMessage({
                    id: "stock.contact",
                  })}
                  fullWidth
                  component={ReactSelectCreatable}
                  optionsdata={[]}
                  customInputProps={{
                    className: classes.fieldMargin,
                  }}
                  selectedData={selectedData && selectedData.contactsPerson}
                  setSelectedData={setSelectedData}
                  placeholder={unicornIdIsSet && "---"}
                  disabled={unicornIdIsSet}
                />
              </Grid>
              <Grid item xs={12}>
                <Field
                  name="contactsCompany"
                  margin="normal"
                  label={intl.formatMessage({
                    id: "search.form.company",
                  })}
                  fullWidth
                  component={AutoCompleteSelect}
                  optionsdata={companies}
                  apiaction={listCompanies}
                  customInputProps={{
                    className: classes.fieldMargin,
                  }}
                  selectedData={selectedData && selectedData.contactsCompany}
                  setSelectedData={setSelectedData}
                  showZhEnLabel={true}
                  placeholder={unicornIdIsSet && "---"}
                  disabled={unicornIdIsSet}
                />
              </Grid>
              {/*<Grid item xs={12}>*/}
              {/*  <Field*/}
              {/*    name="phone"*/}
              {/*    margin="normal"*/}
              {/*    label={intl.formatMessage({*/}
              {/*      id: "building.phone",*/}
              {/*    })}*/}
              {/*    fullWidth*/}
              {/*    component={ReactSelectCreatable}*/}
              {/*    optionsdata={[]}*/}
              {/*    customInputProps={{*/}
              {/*      className: classes.fieldMargin,*/}
              {/*    }}*/}
              {/*    selectedData={selectedData && selectedData.phone}*/}
              {/*    setSelectedData={setSelectedData}*/}
              {/*  />*/}
              {/*</Grid>*/}
              <Grid item xs={12}>
                <Field
                  name="contactsEmail"
                  margin="normal"
                  label={intl.formatMessage({
                    id: "search.form.email",
                  })}
                  fullWidth
                  component={ReactSelectCreatable}
                  optionsdata={[]}
                  customInputProps={{
                    className: classes.fieldMargin,
                  }}
                  selectedData={selectedData && selectedData.contactsEmail}
                  setSelectedData={setSelectedData}
                  placeholder={unicornIdIsSet && "---"}
                  disabled={unicornIdIsSet}
                />
              </Grid>
              <Grid item xs={12}>
                <Field
                  label={intl.formatMessage({
                    id: "search.checkbox.retainMarkedRecord",
                  })}
                  component={ChipsCheckBox}
                  input={{
                    value: retainMarkStocks,
                    onChange: onRetainMarkStocksClick,
                  }}
                />
              </Grid>
            </Grid>
          </DetailBoxSection>
        )}

        {props.children}
      </Collapse>
    </div>
  );
}

const mapStateToProps = (state) => ({
  buildings: state.building.buildings || [],
  fieldhistory: state.stocklist.fieldhistory || [],
  queryvariables: state.stocklist.queryvariables
    ? state.stocklist.queryvariables
    : {},
  listing: state.building.listing ? state.building.listing : false,
  listed: state.building.listed ? state.building.listed : false,
  // streets: state.street.streets ? state.street.streets : [],
  streets: _.get(state, "street.streetSearch") || [],
  districts: state.district.districts ? state.district.districts : [],
  decoration: state.stocklist.decoration ? state.stocklist.decoration : [],
  possession: state.stocklist.possession ? state.stocklist.possession : [],
  currentState: state.stocklist.currentState ? state.stocklist.currentState : [],
  stockType: state.stocklist.stockType ? state.stocklist.stockType : [],
  unitview: state.building.unitview ? state.building.unitview : [],
  companies: state.stocklist.companies ? state.stocklist.companies : [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    listBuildings: (graphqlvariable) => {
      dispatch(listBuildings(graphqlvariable));
    },
    // listStreets: (graphqlvariable) => dispatch(listStreets(graphqlvariable)),
    searchStreets: (variables) => dispatch(streetSearch(variables)),
    listCompanies: (graphqlvariable) =>
      dispatch(listCompanies(graphqlvariable)),
    changeForm: (field, val) => dispatch(change("searchForm", field, val)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(FieldSection)));
