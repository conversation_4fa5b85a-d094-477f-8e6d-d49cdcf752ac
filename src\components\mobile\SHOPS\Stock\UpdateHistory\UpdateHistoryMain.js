import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import { getLangKey, convertCurrency, convertNewlineToBr } from "../../../../../helper/generalHelper";
import ItemWithIcon from "../../../../common/ItemWithIcon";
import { injectIntl } from "react-intl";

const styles = (theme) => ({
  root: {
    fontSize: "1.125em",
  },
  small: {
    fontSize: ".777em",
  },
  notFound: {},
  flexRow: {
    display: "flex",
    alignItems: "center",
  },
  flexOne: {
    flex: 1,
  },
  invalidEmp: {
    color: "#9900FF",
  },
});

class UpdateHistoryMain extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, hand, handsMapping, intl } = this.props;
    const langKey = getLangKey(intl);

    const updateHistory = detail.updateHistory
      ? JSON.parse(JSON.stringify(detail.updateHistory))
      : [];

    let currHandArr = [];
    for (let i = 0; i < updateHistory.length; i++) {
      if (updateHistory[i].deleted) continue;
      if (
        hand !== -1 &&
        (
          !updateHistory[i].handsId ||
          handsMapping.find(v => v._id === updateHistory[i].handsId)?.hands !== hand
        )
      )
        continue;

      let date = updateHistory[i].datetime
        ? updateHistory[i].datetime.slice(0, 10)
        : "";
      let hands =
        handsMapping.find(v => v._id === updateHistory[i].handsId)?.hands;
      let employeeName =
        updateHistory[i].employee && updateHistory[i].employee.name_en
          ? updateHistory[i].employee.name_en
          : "---";
      let employeeDept =
        updateHistory[i].employee && updateHistory[i].employee.dept_code
          ? updateHistory[i].employee.dept_code
          : "";
      let isInvalid =
        updateHistory[i].employee && updateHistory[i].employee.valid_emp === false;
      let remarks =
        updateHistory[i].description
          ? convertNewlineToBr(updateHistory[i].description)
          : "---";
      let status =
        updateHistory[i].status && updateHistory[i].status[langKey]
          ? updateHistory[i].status[langKey]
          : "---";
      let askingPrice =
        updateHistory[i].askingPrice && updateHistory[i].askingPrice.total
          ? "$" + convertCurrency(updateHistory[i].askingPrice.total, undefined, { decimals: 3 })
          : "---";
      let askingRent =
        updateHistory[i].askingRent && updateHistory[i].askingRent.total
          ? "$" + convertCurrency(updateHistory[i].askingRent.total, undefined, { decimals: 3 })
          : "---";

      let textContent = (
        <>
          <div className={classes.flexRow}>
            <div className={classes.flexOne}>
              {date}
            </div>
            <div>
              {hands}{" "}
              {intl.formatMessage({ id: "stock.hand" })}
            </div>
          </div>
          <div className={classes.flexRow}>
            <div className={classes.flexOne}>
              {employeeName + (employeeDept ? " (" + employeeDept + ")" : "")}
            </div>
            <div>
              {intl.formatMessage({
                id: "search.common.price",
              })}{" "}
              {askingPrice}
            </div>
          </div>
          <div className={classes.flexRow}>
            <div className={classes.flexOne}>{status}</div>
            <div>
              {intl.formatMessage({
                id: "search.common.rent",
              })}{" "}
              {askingRent}
            </div>
          </div>
          <div>{remarks}</div>
        </>
      );
      let item = {
        date,
        hands,
        employeeName,
        employeeDept,
        isInvalid,
        status,
        askingPrice,
        askingRent,
        remarks,
      };

      let j;
      for (j = 0; j < currHandArr.length; j++) {
        if (date < currHandArr[j].date) break;
      }
      currHandArr.splice(j, 0, item);
    }

    return (
      <div className={classes.root}>
        {currHandArr.reverse().map((v, i) => (
          <ItemWithIcon
            textContent={
              <>
                <div className={classes.flexRow}>
                  <div className={classes.flexOne}>
                    {v.date}
                  </div>
                  <div>
                    {v.hands}{" "}
                    {intl.formatMessage({ id: "stock.hand" })}
                  </div>
                </div>
                <div className={classes.flexRow}>
                  <div className={`${classes.flexOne} ${v.isInvalid ? classes.invalidEmp : ""}`}>
                    {v.employeeName + (v.employeeDept ? " (" + v.employeeDept + ")" : "")}
                  </div>
                  <div>
                    {intl.formatMessage({
                      id: "search.common.price",
                    })}{" "}
                    {v.askingPrice}
                  </div>
                </div>
                <div className={classes.flexRow}>
                  <div className={classes.flexOne}>{v.status}</div>
                  <div>
                    {intl.formatMessage({
                      id: "search.common.rent",
                    })}{" "}
                    {v.askingRent}
                  </div>
                </div>
                <div>{v.remarks}</div>
              </>
            }
            key={i}
          />
        ))}
        {currHandArr.length === 0 && (
          <div className={classes.notFound}>
            {intl.formatMessage({
              id: "stock.noupdatehistory",
            })}
          </div>
        )}
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(UpdateHistoryMain));
