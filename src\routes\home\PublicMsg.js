import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import {
  clearMessages,
  listMessages
} from "../../actions/messageCenter";
import MessageCard from "../../components/common/MessageCard";

const styles = theme => ({
  card: {
    maxHeight: "12vh",
    overflowY: "auto"
  },
  title: {
    fontSize: ".875em",
    color: "#CCC",
  },
});

class PublicMsg extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    messages: PropTypes.array,
    listed: PropTypes.bool,
    listing: PropTypes.bool,
  };

  componentDidMount() {
    const variables = {
      limit: 1,
      isDeleted: false,
      sorter: [
        { field: "createDateTime", order: "DESC" },
      ]
    };
    this.props.listMessages(variables);
  }

  componentWillUnmount() {
    this.props.clearMessages();
  }

  render() {
    const { classes, messages, listed, listing } = this.props;
    const detail = messages && messages.length > 0 ? messages[0] : {};

    return (
      <div className={classes.root}>
        <MessageCard
          className={classes.card}
          detail={detail}
          title={<div className={classes.title}>Public Message</div>}
          listing={listing}
        />
      </div>
    );
  }
}

const mapStateToProps = state => ({
  messages: state.messageCenter.messages ? state.messageCenter.messages : [],
  listed: state.messageCenter.listed ? state.messageCenter.listed : false,
  listing: state.messageCenter.listing ? state.messageCenter.listing : false,
});

const mapDispatchToProps = dispatch => {
  return {
    listMessages: (...args) => dispatch(listMessages(...args)),
    clearMessages: () => dispatch(clearMessages())
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(PublicMsg));