import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import InfiniteList from "../common/InfiniteList";
import ProposalCard from "./ProposalCard";
import { listProposals, removeProposal } from "../../actions/proposal";
import ItemCount from "../common/ItemCount";

const styles = (theme) => ({
  card: {
    marginBottom: "1vh",
  },
});

class List extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    proposals: PropTypes.array,
    listProposals: PropTypes.func.isRequired,
    queryvariables: PropTypes.object,
    hasMore: PropTypes.bool,
  };

  fetchMoreData = () => {
    let variables = this.props.queryvariables;
    variables.offset += 50;
    this.props.listProposals(variables, true);
  };

  render() {
    const {
      classes,
      proposals,
      hasMore,
      removeProposal,
      removeProposalError,
      removedProposal,
      removingProposal,
      proposalsCount,
    } = this.props;

    return (
      <div className={classes.root}>
        <ItemCount count={proposalsCount} />
        <InfiniteList
          list={proposals}
          fetchMoreData={this.fetchMoreData}
          hasMore={hasMore}
        >
          {({ ...props }) => (
            <ProposalCard
              className={classes.card}
              removeProposal={removeProposal}
              removeProposalError={removeProposalError}
              removedProposal={removedProposal}
              removingProposal={removingProposal}
              {...props}
            />
          )}
        </InfiniteList>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  queryvariables: state.proposal.queryvariables
    ? state.proposal.queryvariables
    : {},
  hasMore: state.proposal.hasMore ? state.proposal.hasMore : false,
  removedProposal: state.proposal.removedProposal
    ? state.proposal.removedProposal
    : false,
  removingProposal: state.proposal.removingProposal
    ? state.proposal.removingProposal
    : false,
  removeProposalError: state.proposal.removeProposalError
    ? state.proposal.removeProposalError
    : "",
  proposalsCount: state.proposal.proposalsCount
    ? state.proposal.proposalsCount
    : 0,
});

const mapDispatchToProps = (dispatch) => {
  return {
    listProposals: (...args) => dispatch(listProposals(...args)),
    removeProposal: (...args) => dispatch(removeProposal(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(List));
