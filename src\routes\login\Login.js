import React, { Component } from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { reduxForm, Field } from "redux-form";
import { withStyles, MuiThemeProvider } from "@material-ui/core/styles";
import clsx from "clsx";
import {
  GoogleReCaptchaProvider,
  GoogleReCaptcha,
} from "react-google-recaptcha-v3";
import CssBaseline from "@material-ui/core/CssBaseline";
import Paper from "@material-ui/core/Paper";
import Grid from "@material-ui/core/Grid";
import Fade from "@material-ui/core/Fade";
import Typography from "@material-ui/core/Typography";
import CircularProgress from "@material-ui/core/CircularProgress";
import { injectIntl } from "react-intl";
import Cookies from "universal-cookie";

import validate from "./validate";
import { style, theme } from "./style";
import { login, verifycode, clearAuth } from "@/actions/auth";
import { addActivityLog } from "@/actions/log";
import TextInput from "@/components/common/TextInput";
import FormButton from "@/components/common/FormButton";
import Dialog from "@/components/common/Dialog";
import logo from "@/files/icons/logo-bw.png";

import {
  recaptchaSiteKey,
  resendpinCounter,
  enableRecaptcha,
  productionHost,
  publicHost,
  deployDomain,
} from "@/config";
import history from "@/core/history";

import { getCookieDomain } from "@/helper/generalHelper";

class Login extends Component {
  static propTypes = {
    handleSubmit: PropTypes.func.isRequired,
    submitting: PropTypes.bool.isRequired,
    authorized: PropTypes.bool,
    authorizing: PropTypes.bool,
    error: PropTypes.string,
    user: PropTypes.object,

    login: PropTypes.func.isRequired,
    verifycode: PropTypes.func.isRequired,
    clearAuth: PropTypes.func.isRequired,
    addActivityLog: PropTypes.func.isRequired,
  };

  constructor() {
    super();
    this.state = {
      inputtoken: null,
      checked: false,
      dialogOpen: false,
      appIsMounted: false,
      counter: resendpinCounter,
    };
  }

  _handleTextFieldChange = (event) => {
    this.setState({
      inputtoken: event.target.value,
    });
  };

  componentDidMount() {
    this.setState({ appIsMounted: true });
    this.props.clearAuth();
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.props.user !== null) {
      if (prevProps.user !== this.props.user) {
        this.setState({
          counter: resendpinCounter,
        });

        this.interval = setInterval(() => {
          if (this.state.counter !== 0) {
            this.setState({
              counter: this.state.counter - 1,
            });
          } else {
            clearInterval(this.interval);
          }
        }, 1000);

        this.setState({ dialogOpen: true });
      }
    }
  }

  onSubmit = (values) => {
    this.props.login(/*values, */ this.props.intl);
    this.setState({ checked: true });
  };

  checkReCaptcha = (token) => {
    return fetch("/checkReCaptcha", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        response: token,
      }),
    })
      .then((resp) => {
        return resp.json();
      })
      .then((data) => {
        return data;
      })
      .catch((error) => {
        console.log(error);
      });
  };

  handleCloseDialog = () => {
    this.setState({ dialogOpen: false });
  };

  verifyCallback = async (recaptchaToken) => {
    const response = await this.checkReCaptcha(recaptchaToken);
    console.log(response);
    if (response.success && response.score > 0.1) {
    } else {
      const msg = JSON.stringify(response);
      history.push(
        "/error?message=" +
          encodeURIComponent("Invalid request") +
          "&reason=" +
          encodeURIComponent(msg),
      );
    }
  };

  handleVerifyPin = async (e) => {
    // this is set as the submit btn just because the "enter" key press should trigger this btn
    // this btn should not trigger the onSubmit so use preventDefault
    e.preventDefault();
    const { user } = this.props;
    const loginSuccess = await this.props.verifycode(
      {
        token: this.state.inputtoken,
        user,
      },
      this.props.intl,
    );
    if (loginSuccess.valid) {
      // const emp_id = user.info && user.info.emp_id ? user.info.emp_id : null;
      // const short_name =
      //   user.info && user.info.short_name ? user.info.short_name : null;
      // const dept_code =
      //   user.info && user.info.dept_code ? user.info.dept_code : null;
      // // push require info to GTM
      // window.dataLayer.push({
      //   emp_id: emp_id,
      //   short_name: short_name,
      //   dept_code: dept_code
      // });
      this.props.addActivityLog("login", "login");
      const cookies = new Cookies();
      cookies.set("homepageCounter", 0, {
        domain: getCookieDomain(deployDomain),
      });
      window.location.replace("/");
    }
  };

  render() {
    const {
      classes,
      handleSubmit,
      submitting,
      pristine,
      reset,
      authorizing,
      user,
      error,
      intl,
    } = this.props;

    const isProd =
      publicHost === productionHost ||
      /(ms[cis].midlandici.com.hk)/g.test(publicHost);

    return (
      <Grid container component="main" className={classes.root}>


        <CssBaseline />
        {/* <Grid item xs={false} sm={4} md={7} className={classes.image} /> */}

        <Grid item xs={12} sm={12} md={12}>
          <div className={classes.rightContainer}>
            <div className={classes.logoBox}>
              <img className={classes.logo} src={logo} />
            </div>

            <div className={classes.formContainer}>
              <Typography component="h1" className={classes.text}>
                {user ? (
                  <div>
                    <Dialog
                      open={this.state.dialogOpen}
                      handleClose={this.handleCloseDialog}
                      fullWidth={true}
                    >
                      <div className={classes.dialogroot}>
                        <div className={classes.font18}>
                          {intl.formatMessage({
                            id: "login.message.confirmation",
                          })}
                        </div>
                      </div>
                    </Dialog>
                  </div>
                ) : // <>4-digit PIN has been sent to your 開單易</>
                isProd ? (
                  <span>
                    {intl.formatMessage({
                      id: "login.message.welcome",
                    })}
                  </span>
                ) : (
                  <span>
                    {intl.formatMessage({
                      id: "login.message.welcomeuat",
                    })}
                    <br />
                    {intl.formatMessage({
                      id: "login.message.clicktoprod",
                    })}
                    <a href={productionHost}>
                      {intl.formatMessage({
                        id: "login.message.prod",
                      })}
                    </a>
                  </span>
                )}
              </Typography>

              {this.state.appIsMounted && (
                <MuiThemeProvider theme={theme}>
                  <form
                    onSubmit={handleSubmit(this.onSubmit)}
                    className={classes.form}
                    id="login-form"
                    noValidate
                  >
                    {!user && (
                      <div className={classes.btnRow}>
                        <FormButton
                          type="submit"
                          variant="contained"
                          className={classes.submit}
                          disabled={submitting}
                        >
                          {intl.formatMessage({
                            id: "login.button.sendpin",
                          })}
                        </FormButton>
                      </div>
                    )}

                    {user && (
                      <div>
                        <Fade in={true}>
                          <Paper elevation={0}>
                            <Field
                              value={this.state.inputtoken}
                              onChange={this._handleTextFieldChange}
                              name="token"
                              type="token"
                              margin="normal"
                              label="PIN"
                              component={TextInput}
                              fullWidth
                              required
                              variant="outlined"
                              inputProps={{ maxLength: "4" }}
                            />

                            <div className={classes.btnRow}>
                              <FormButton
                                // type="submit"
                                variant="contained"
                                onClick={handleSubmit((values) =>
                                  this.onSubmit(values),
                                )}
                                className={clsx(classes.submit, classes.resend)}
                                disabled={
                                  this.state.counter == 0 ? false : true
                                }
                              >
                                {intl.formatMessage({
                                  id: "login.button.resend",
                                })}

                                {this.state.counter !== 0 && (
                                  <span className={classes.counter}>
                                    ({this.state.counter})
                                  </span>
                                )}
                              </FormButton>

                              <FormButton
                                type="submit"
                                variant="contained"
                                onClick={this.handleVerifyPin}
                                className={classes.submit}
                                id="verifyPinButton"
                              >
                                {intl.formatMessage({
                                  id: "login.button.signin",
                                })}
                              </FormButton>
                            </div>
                          </Paper>
                        </Fade>
                      </div>
                    )}
                  </form>
                </MuiThemeProvider>
              )}

              {error && (
                <div>
                  <Typography color="error">{error}</Typography>
                </div>
              )}

              {authorizing && (
                <div className={classes.circularLoader}>
                  <CircularProgress />
                </div>
              )}
            </div>

            {/* <div>
              <img
                className={classes.banner}
                src="https://www.midlandici.com.hk/ics/property/bannerUnicorn/getBanner/unicorn_mr_c"
              />
            </div> */}
          </div>
        </Grid>

        {/* <GoogleReCaptcha onVerify={this.verifyCallback} /> */}
      </Grid>
    );
  }
}

let LoginRoute = withStyles(style)(Login);

LoginRoute = connect(
  (state) => ({
    authorized: state.auth.authorized,
    authorizing: state.auth.authorizing,
    user: state.auth.user,
    error: state.auth.error,
  }),
  {
    login,
    verifycode,
    clearAuth,
    addActivityLog,
  },
)(LoginRoute);

export default reduxForm({
  form: "login",
  validate,
})(injectIntl(LoginRoute));
