import {
  CLEAR_LIST_WWW_STOCK,
  LIST_MORE_WWW_STOCK_RESULT_START,
  LIST_WWW_STOCK_RESULT_NULL_SUCCESS,
  LIST_WWW_STOCK_RESULT_START,
  LIST_WWW_STOCK_RESULT_SUCCESS,
  LIST_WWW_STOCK_RESULT_ERROR,
  SET_SEARCH_WWW_STOCK_RESULT_ANCHOR,
  <PERSON><PERSON><PERSON>_SEARCH_WWW_STOCK_RESULT_ANCHOR,
  SET_CURRENT_WWW_STOCK_START,
  SET_CURRENT_WWW_STOCK_SUCCESS,
  SET_CURRENT_WWW_STOCK_ERROR,
  LIST_WWW_STOCK_DESC_ERROR,
  LIST_WWW_STOCK_DESC_NULL_SUCCESS,
  LIST_WWW_STOCK_DESC_SUCCESS,
  LIST_WWW_STOCK_DESC_START,
  UPDATE_WWW_STOCK_START,
  UPDATE_WWW_STOCK_SUCCESS,
  UPDATE_WWW_STOCK_ERROR,
  UPDATE_WWW_STOCK_DESC_START,
  UPDATE_WWW_STOCK_DESC_SUCCESS,
  UPDATE_WWW_STOCK_DESC_ERROR,
  INIT_WWW_STOCK,

  WWW_USED_COUNT_QUERY_START,
  WWW_USED_COUNT_QUERY_SUCCESS,
  WWW_USED_COUNT_QUERY_ERROR,
} from "@/constants/wwwStock";
import _ from "lodash";


const initialState = {
  listed: false,
  valid: false,
  wwwStockList: [],
  wwwStockListCount: 0,
  wwwStockDesc: {},
  currentWWWStock: {},

  usedCount: {},
  usedCountError: null,
};

export default function wwwStock(state = initialState, action) {

  switch (action.type) {
    case CLEAR_LIST_WWW_STOCK: {
      return {
        ...state,
        listing: false,
        listed: false,
        hasMore: false,
        wwwStockList: [],
        wwwStockListCount: 0,
      };
    }
    case LIST_WWW_STOCK_RESULT_START: {
      return {
        ...state,
        listing: true,
        wwwStockList: [],
        wwwStockListCount: 0,
        hasMore: true,
        variables: action.payload.variables,
        uuid: action.payload.uuid,
        error: null
      };
    }
    case LIST_MORE_WWW_STOCK_RESULT_START:
      return {
        ...state,
        listing: true,
        hasMore: true,
        variables: action.payload.variables,
        uuid: action.payload.uuid,
      };
    case LIST_WWW_STOCK_RESULT_SUCCESS:
      if (state.uuid !== action.payload.uuid) {
        return state;
      }
      return {
        ...state,
        listing: false,
        listed: true,
        hasMore: state.wwwStockList.length + action.payload.data.wwwStockList.length < action.payload.data.wwwStockListCount,
        wwwStockList: state.wwwStockList.concat(action.payload.data.wwwStockList),
        wwwStockListCount: action.payload.data.wwwStockListCount,
      };
    case LIST_WWW_STOCK_RESULT_NULL_SUCCESS:
      if (state.uuid !== action.payload.uuid) {
        return state;
      }
      return {
        ...state,
        listing: false,
        listed: true,
        hasMore: false,
        wwwStockList: state.wwwStockList.concat(action.payload.data.wwwStockList),
        wwwStockListCount: action.payload.data.wwwStockListCount,
      };
    case LIST_WWW_STOCK_RESULT_ERROR:
      return {
        ...state,
        error: action.payload.error,
      };
    case SET_CURRENT_WWW_STOCK_START:
      return {
        ...state,
        currentWWWStock: {},
      };
    case SET_CURRENT_WWW_STOCK_SUCCESS:
      return {
        ...state,
        currentWWWStock: _.first(action.payload.data.wwwStockList),
      };
    case SET_CURRENT_WWW_STOCK_ERROR:
      return {
        ...state,
        error: action.payload.error,
      };
    case SET_SEARCH_WWW_STOCK_RESULT_ANCHOR: {
      return {
        ...state,
        searchResultAnchor: action.payload.px,
      };
    }
    case CLEAR_SEARCH_WWW_STOCK_RESULT_ANCHOR: {
      return {
        ...state,
        searchResultAnchor: null,
      };
    }
    case LIST_WWW_STOCK_DESC_START:
      return {
        ...state,
        wwwStockDesc: {},
        error: null
      };
    case LIST_WWW_STOCK_DESC_SUCCESS:
      return {
        ...state,
        wwwStockDesc: action.payload.data.wwwStockDesc,
      };
    case LIST_WWW_STOCK_DESC_NULL_SUCCESS:
      return {
        ...state,
        wwwStockDesc: {},
      };
    case LIST_WWW_STOCK_DESC_ERROR:
      return {
        ...state,
        error: action.payload.error,
      };
    case UPDATE_WWW_STOCK_START:
      return {
        ...state,
        wwwStockUpdating: true,
        wwwStockUpdated: false,
        wwwStockUpdateError: null,
      };
    case UPDATE_WWW_STOCK_SUCCESS:
      return {
        ...state,
        wwwStockUpdating: false,
        wwwStockUpdated: true,
        wwwStockUpdateError: null,
      };
    case UPDATE_WWW_STOCK_ERROR:
      return {
        ...state,
        wwwStockUpdating: false,
        wwwStockUpdated: false,
        wwwStockUpdateError: "Network error",
      };
    case INIT_WWW_STOCK:
      return {
        ...state,
        wwwStockUpdating: false,
        wwwStockUpdated: false,
        wwwStockUpdateError: null,
      };
    case UPDATE_WWW_STOCK_DESC_START:
      return {
        ...state,
        wwwStockDescUpdating: true,
        wwwStockDescUpdated: false,
        wwwStockDescUpdateError: null,
      };
    case UPDATE_WWW_STOCK_DESC_SUCCESS:
      return {
        ...state,
        wwwStockDescUpdating: false,
        wwwStockDescUpdated: true,
        wwwStockDescUpdateError: null,
      };
    case UPDATE_WWW_STOCK_DESC_ERROR:
      return {
        ...state,
        wwwStockDescUpdating: false,
        wwwStockDescUpdated: false,
        wwwStockDescUpdateError: "Network error",
      };
    case WWW_USED_COUNT_QUERY_START:
      return {
        ...state,
        usedCount: {},
      };
    case WWW_USED_COUNT_QUERY_SUCCESS:
      return {
        ...state,
        usedCount: _.get(action, "payload.data.getUsedCount", {}),
      };
    case WWW_USED_COUNT_QUERY_ERROR:
      return {
        ...state,
        usedCountError: _.get(action, "payload.data.error", "Network error"),
      };

    default:
      return state;
  }



}
