import React from 'react';
import {
  createStyles,
  withStyles,
} from '@material-ui/core';
import _ from 'lodash';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { injectIntl } from 'react-intl';
import Agent from './components/Agent';
import Manager from './components/Manager';
import DistrictDirector from './components/DistrictDirector';
import SBUDirector from './components/SBUDirector';
import Layout from "../../../../components/Layout";
import Header from "./components/Header";
import Footer from "./components/Footer";
import {
  getEmployeesMitaclubAgent,
  getEmployeesMitaclubManager,
  getEmployeesMitaclubDistrictDirector,
  getEmployeesMitaclubSBUDirector,
  listEmployees,
} from '@/actions/employee';
import jwt from 'jsonwebtoken';
import { jwtSecret } from "@/config";

export const AGENT_TARGET = [
  2_560_000,
  1_280_000,
];

export const MANAGER_TARGET = [Infinity, 7_500_000];

/** @typedef {"SBUdirector" | "DistrictDirector" | "Manager" | "Agent"} PersonalType */

const styles = createStyles({
  "@global": {
    body: {
      margin: 0,
      padding: 0,
      height: "100vh",
      backgroundColor: "#F5F5F5",
    },
    "#layout-header-container>div:nth-child(1)": {
      display: "none",
    },
  },
  root: {
    padding: '20px',
    maxWidth: '1200px',
    margin: '0 auto'
  },
  paper: {
    padding: '20px',
    marginTop: '20px'
  },
  title: {
    marginBottom: '20px',
    color: '#333'
  }
});

function getToken() {
  if (typeof window === 'undefined') {
    return null;
  }
  const query = new URLSearchParams(window.location.search);
  const token = query.get("token");
  return token;
}

const GRADES = Object.freeze({
  manager: ["S5"],
  director: ["S1", "S2", "S3", "S3A", "S4", "S4A", "S4B"],
});

function MitaClubPage(props) {
  /** @type [PersonalType, React.Dispatch<React.SetStateAction<PersonalType>>] */
  const [personalType, setPersonalType] = React.useState();

  /** `url query token`為假數據用於測試 */
  const currentEmployee = React.useMemo(() => {
    // jwt for simulating different roles
    const token = getToken();
    if (!_.isEmpty(token)) {
      try {
        /** @type {any} */
        const decoded = jwt.verify(token, jwtSecret);
        // console.log("Decoded Token:", decoded);
        const { role, empId, teamCode } = decoded;

        const mockEmployee = {
          isMock: true,
          emp_id: empId,
          // teamCode,
          dept_code: teamCode,
          grade_id: "C1",
        }
        if (role === "director") {
          mockEmployee.grade_id = "S4";
        } else if (role === "manager") {
          mockEmployee.grade_id = "S5";
        } else if (role === "SBUdirector") {
          mockEmployee.grade_id = "S1";
        }
        return mockEmployee;
      } catch (error) {
        console.log("Token verification failed:", error);
      }
    }
    return props.employee;
  }, [props.employee]);

  React.useEffect(() => {
    if (_.isNil(currentEmployee) && props.empId) {
      props.getEmployee({ emp_id: [props.empId] });
    }
  }, [currentEmployee, props.empId]);

  React.useEffect(() => {
    if (!currentEmployee) {
      return;
    }

    if (currentEmployee.grade_id === "S1" && !_.includes(currentEmployee.dept_code, ["ICIM", "MGT"])) {
      setPersonalType("SBUdirector");
    } else if (_.includes(GRADES.director, currentEmployee.grade_id)) {
      setPersonalType("DistrictDirector");
    } else if (_.includes(GRADES.manager, currentEmployee.grade_id)) {
      setPersonalType("Manager")
    } else {
      setPersonalType("Agent");
    }
  }, [currentEmployee]);

  React.useEffect(() => {
    if (!currentEmployee?.emp_id) { return; }

    const empId = currentEmployee.emp_id;  // "S160094A";
    switch (personalType) {
      case "Agent":
        props.getEmployeesMitaclubAgent(empId);
        break;
      case "Manager":
        props.getEmployeesMitaclubManager(empId);
        break;
      case "DistrictDirector":
        props.getEmployeesMitaclubDistrictDirector(empId);
        break;
      case "SBUdirector":
        props.getEmployeesMitaclubSBUDirector(empId);
        break;
    }
  }, [personalType, currentEmployee]);

  return (
    <Layout renderHeader={false}>
      <Header personalType={personalType} />
      {personalType === "Agent" && <Agent employee={currentEmployee} />}
      {personalType === "Manager" && <Manager employee={currentEmployee} />}
      {personalType === "DistrictDirector" && <DistrictDirector employee={currentEmployee} />}
      {personalType === "SBUdirector" && <SBUDirector employee={currentEmployee} />}
      <Footer />
    </Layout>
  );
}

MitaClubPage.propTypes = {
  classes: PropTypes.object.isRequired,
};

const mapStateToProps = state => ({
  empId: _.get(state, "auth.user.login.info.emp_id", ""),
  employee: _.get(state, "employee.employees.0"),
});

const mapDispatchToProps = (dispatch) => ({
  getEmployee: (variable) => dispatch(listEmployees(variable)),
  getEmployeesMitaclubAgent: (empId) => dispatch(getEmployeesMitaclubAgent({ empId })),
  getEmployeesMitaclubManager: (empId) => dispatch(getEmployeesMitaclubManager({ empId })),
  getEmployeesMitaclubDistrictDirector: (empId) => dispatch(getEmployeesMitaclubDistrictDirector({ empId })),
  getEmployeesMitaclubSBUDirector: (empId) => dispatch(getEmployeesMitaclubSBUDirector({ empId })),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(MitaClubPage)));
