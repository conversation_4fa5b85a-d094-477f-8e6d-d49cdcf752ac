import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import Button from "@material-ui/core/Button";
import CloseIcon from '@material-ui/icons/Close';

// We can inject some CSS into the DOM.
const styles = {
  root: {
    background: "rgba(0,0,0,.5)",
    color: "#fff",
    borderRadius: 0,
    width: 24,
    minWidth: 0,
    height: 24,
    padding: 0,
    "&:hover": {
      background: "rgba(0,0,0,.5)",
      color: "#fff",
    }
  },
  icon: {
    width: "100%",
    height: "100%"
  }
};

function DarkBgCross(props) {
  const { classes, className, ...other } = props;

  return (
    <Button className={clsx(classes.root, className)} {...other}>
      <CloseIcon className={classes.icon} />
    </Button>
  );
}

DarkBgCross.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string
};

export default withStyles(styles)(DarkBgCross);
