/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import Layout from "../../../../../components/Layout";
import { FormattedMessage } from "react-intl";

const title = "Proposal";

async function action({ store, params, query }) {
  const { auth } = store.getState();
  if (!auth.user) {
    return { redirect: "/login" };
  } else if (auth.user.authorized == false) {
    return { redirect: "/login" };
  }

  const ProposalList = await require.ensure(
    [],
    require => require("./ProposalList").default,
    "proposal"
  );

  return {
    chunks: ["proposal"],
    title,
    component: (
      <Layout
        header={<FormattedMessage id="proposal.proposal" />}
        hideSearchIcon={true}
      >
        <ProposalList />
      </Layout>
    )
  };
}

export default action;
