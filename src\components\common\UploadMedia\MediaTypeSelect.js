import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Tabs from "@material-ui/core/Tabs";
import Tab from "@material-ui/core/Tab";

import { MuiThemeProvider, createMuiTheme } from "@material-ui/core/styles";
import StandardSvg from "../StandardSvg";
import mediaPhoto from "../../../files/icons/MediaPhoto.svg";
import mediaVideo from "../../../files/icons/MediaVideo.svg";
import mediaVr from "../../../files/icons/MediaVr.svg";
import mediaDocu from "../../../files/icons/MediaDocu.svg";


const styles = theme => ({
  mediaTypeSelect: {

  },
  icon: {
    width: 42,
    height: 32
  },
});

const theme = createMuiTheme({
  overrides: {
    MuiTabs: {
      indicator: {
        height: 0,
      }
    },
    MuiTab: {
      root: {
        fontSize: "0.75em",
        textTransform: "none",
        "&$selected": {
          "filter": "none",
          "opacity": 1
        }
      },
      textColorInherit: {
        color: "#FFF",
        filter: "brightness(0%)",
        opacity: .21,
      },
      wrapper: {
        "&& > *:first-child": {
          marginBottom: 0
        }
      }
    }
  }
});

function MediaTypeSelect(props) {
  const {
    input: { value, onChange }
  } = props;
  const { classes, className, ...other } = props;

  const handleChange = (event, value) => {
    onChange(value);
  };

  return (
    <MuiThemeProvider theme={theme}>
      <div className={classes.mediaTypeSelect}>
        <Tabs
          value={value}
          onChange={handleChange}
          variant="fullWidth"
          aria-label="full width tabs example"
          centered
        >
          <Tab label="Photo" icon={<StandardSvg className={classes.icon} src={mediaPhoto} />} />
          <Tab label="Video" icon={<StandardSvg className={classes.icon} src={mediaVideo} />} />
          <Tab label="VR" icon={<StandardSvg className={classes.icon} src={mediaVr} />} />
          <Tab label="Docu" icon={<StandardSvg className={classes.icon} src={mediaDocu} />} />
        </Tabs>
      </div>
    </MuiThemeProvider>
  );
}

MediaTypeSelect.propTypes = {
  classes: PropTypes.object.isRequired,
  input: PropTypes.object.isRequired,
  className: PropTypes.string,
};

export default withStyles(styles)(MediaTypeSelect);
