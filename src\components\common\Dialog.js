import React from "react";
import PropTypes from "prop-types";
import Dialog from "@material-ui/core/Dialog";
import { MuiThemeProvider, createMuiTheme } from "@material-ui/core/styles";

const theme = createMuiTheme({
  overrides: {
    MuiDialog: {
      paper: {
        color: "#FFF",
        padding: "12px",
        backgroundColor: "#005F5F",
      },
    },
  },
});

function CustomizedDialog(props) {
  const { children, open, handleClose, ...others } = props;

  return (
    <MuiThemeProvider theme={theme}>
      <Dialog onClose={handleClose} open={open} {...others}>
        {children}
      </Dialog>
    </MuiThemeProvider>
  );
}

CustomizedDialog.propTypes = {
  fullWidth: PropTypes.bool,
  children: PropTypes.node,
  open: PropTypes.bool.isRequired,
  handleClose: PropTypes.func.isRequired,
  onBackdropClick: PropTypes.func.isRequired,
};

export default CustomizedDialog;
