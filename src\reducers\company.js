import {
  C<PERSON><PERSON>_COMPANY,
  LIST_COMPANIES,
  LIST_COMPANIES_ERROR,
  LIST_COMPANIES_SUCCESS,
  LIST_MORE_COMPANIES_SUCCESS,
  QUERY_COMPANY_BY_REGNO,
  QUERY_COMPANY_BY_REGNO_ERROR,
  QUERY_COMPANY_BY_REGNO_SUCCESS,
  QUERY_SOURCE_OPTIONS_SUCCESS,
  QUERY_COMPANY_NAME_SUCCESS,
  LIST_COMPANY_SEARCHES_SUCCESS,
  UPDATE_QUERY_VARIABLES,
  UPDATE_SORTER,
  APPLY_COMPANY_SEARCH,
  APPLY_COMPANY_SEARCH_SUCCESS,
  APPLY_COMPANY_SEARCH_ERROR,
  C<PERSON><PERSON>_APPLY_COMPANY_SEARCH,
  UPDATE_CRITERIA,
} from "@/constants/company";

const initialState = {
  criteria: { companyName: [] },
  queryvariables: { limit: 50, offset: 0 },
  sorter: [{ field: "companyNameZh", order: "ASC" }],

  companyNames: [],

  companies: [],
  companiesCount: 0,
  listingCompanies: false,
  listedCompanies: false,
  listCompaniesError: null,

  company: null,
  sourceOptions: [],
  listingCompany: false,
  listedCompany: false,
  listCompanyError: null,

  companySearch: [],
  applyingSearch: false,
  applySearchOk: false,
  applySearchErrors: [],
};

export default function company(state = initialState, action) {
  switch (action.type) {
    case UPDATE_SORTER:
      return {
        ...state,
        sorter: action.payload.sorter,
        queryvariables: { ...state.queryvariables, limit: 50, offset: 0 },
      };
    case UPDATE_QUERY_VARIABLES:
      return {
        ...state,
        queryvariables: {
          ...(action.payload.queryvariables || state.queryvariables),
          ...(action.payload.key
            ? { [action.payload.key]: action.payload.value }
            : {}),
        },
      };
    case UPDATE_CRITERIA:
      return {
        ...state,
        criteria: {
          ...state.criteria,
          [action.payload.key]: action.payload.value,
        },
      };

    case QUERY_COMPANY_NAME_SUCCESS:
      return {
        ...state,
        companyNames: action.payload.names,
      };

    case LIST_COMPANIES:
      return {
        ...state,
        listingCompanies: true,
        ...(!action.payload.fetchMore
          ? { companies: [], companiesCount: 0, listedCompanies: false }
          : {}),
      };
    case LIST_COMPANIES_SUCCESS:
      return {
        ...state,
        listingCompanies: false,
        listedCompanies: true,
        companies: action.payload.companyBook,
        companiesCount: action.payload.companyBookCount,
      };
    case LIST_MORE_COMPANIES_SUCCESS: {
      return {
        ...state,
        listingCompanies: false,
        listedCompanies: true,
        companies: [...state.companies, ...action.payload.companyBook],
      };
    }
    case LIST_COMPANIES_ERROR:
      return {
        ...state,
        listingCompanies: false,
        listCompaniesError: action.payload.error,
      };

    case QUERY_COMPANY_BY_REGNO:
      return {
        ...state,
        company: null,
        listingCompany: true,
        listedCompany: false,
      };

    case QUERY_COMPANY_BY_REGNO_SUCCESS:
      return {
        ...state,
        company: action.payload.company,
        listingCompany: false,
        listedCompany: true,
      };
    case QUERY_SOURCE_OPTIONS_SUCCESS:
      return {
        ...state,
        sourceOptions: action.payload.sourceOptions
      };
    case QUERY_COMPANY_BY_REGNO_ERROR:
      return {
        ...state,
        listingCompany: false,
        listedCompany: true,
        listCompanyError: action.payload.error,
      };
    case LIST_COMPANY_SEARCHES_SUCCESS:
      return {
        ...state,
        companySearch: action.payload.searchDocs,
      };

    case CLEAR_COMPANY:
      return {
        ...state,
        company: null,
        listingCompany: false,
        listedCompany: false,
        listCompanyError: null,
        companySearch: [],
      };

    case APPLY_COMPANY_SEARCH:
      return {
        ...state,
        applyingSearch: true,
        applySearchOk: false,
        applySearchErrors: [],
      };

    case APPLY_COMPANY_SEARCH_SUCCESS:
      return {
        ...state,
        applyingSearch: false,
        applySearchOk: true,
      };

    case APPLY_COMPANY_SEARCH_ERROR:
      return {
        ...state,
        applyingSearch: false,
        applySearchErrors: action.payload.errors,
      };

    case CLEAR_APPLY_COMPANY_SEARCH:
      return {
        ...state,
        applyingSearch: false,
        applySearchOk: false,
        applySearchErrors: [],
      };

    default:
      return state;
  }
}
