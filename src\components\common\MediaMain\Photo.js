import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import squareSvg from "../../../files/icons/transparent-square.svg";
import hourglass from "../../../files/icons/hourglass.svg";
import { injectIntl } from "react-intl";
import MediumProperties from "./MediumProperties";

const styles = {
  image: {
    width: "100%",
    cursor: "pointer",
    backgroundSize: "cover",
    backgroundPosition: "center",
    backgroundRepeat: "no-repeat",
  },
  imageContainer: {
    overflow: "hidden",
    position: "relative",
  },
};

class Photo extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    photo: PropTypes.object,
    handleOpenLightbox: PropTypes.func,
  };

  state = {
    imageLoadError: false
  };

  componentDidMount() {
    this.loadImage();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.photo?.mediumRoot !== this.props.photo?.mediumRoot) {
      this.setState({ imageLoadError: false });
      this.loadImage();
    }
  }

  loadImage = () => {
    const { photo } = this.props;
    if (!photo || !photo.mediumRoot) {
      this.setState({ imageLoadError: true });
      return;
    }

    const imgSrc = `${photo.mediumRoot}${photo.mediumRoot.includes('blob') ? '' : '/s.jpeg?'}`;
    const img = new Image();
    img.onload = () => this.setState({ imageLoadError: false });
    img.onerror = () => this.setState({ imageLoadError: true });
    img.src = imgSrc;
  };

  render() {
    const {
      classes,
      photo,
      handleOpenLightbox,
      intl,
    } = this.props;
    const { imageLoadError } = this.state;

    console.log('photo-------------', photo);

    return (
      <div className={classes.imageContainer}>
        <img
          className={classes.image}
          alt=""
          role="presentation"
          onClick={handleOpenLightbox}
          src={imageLoadError ? hourglass : squareSvg}
          style={!imageLoadError ? {
            backgroundImage: `url(${photo.mediumRoot}${photo.mediumRoot.includes('blob') ? '' : '/s.jpeg?'})`,
          } : null}
        />
        {!imageLoadError && <MediumProperties medium={photo} />}
      </div>
    );
  }
}
export default withStyles(styles)(injectIntl(Photo));
