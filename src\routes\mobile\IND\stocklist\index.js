/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import Layout from "../../../../components/Layout";

const title = "Search Result";

async function action({ store, params, query }) {
  const { auth } = store.getState();
  if (!auth.user) {
    return { redirect: "/login" };
  } else if (auth.user.authorized == false) {
    return { redirect: "/login" };
  }

  const Stocklist = await require.ensure(
    [],
    require => require("./Stocklist").default,
    "stocklist"
  );

  const parsedjson = JSON.parse(query.param);
  const isAdvanced = query.isAdvanced;

  return {
    chunks: ["stocklist"],
    title,
    component: (
      <Layout path="result" header="Search Result" isAdvanced={false}>
        <Stocklist queryvariablesFromUrl={parsedjson} isAdvanced={isAdvanced} />
      </Layout>
    )
  };
}

export default action;
