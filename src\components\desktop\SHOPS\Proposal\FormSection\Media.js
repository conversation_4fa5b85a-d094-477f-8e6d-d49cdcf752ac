import React from "react";
import { connect } from "react-redux";
import { injectIntl } from "react-intl";
import { withStyles } from "@material-ui/core/styles";
import MediaMain from "../../../../common/MediaMain";
import LoadingOverlay from "../../../../LoadingOverlay";
import MediaSection from "../../../../common/MediaMain/MediaSection";
import { staticMapUrl } from "../../../../../helper/generalHelper";

// We can inject some CSS into the DOM.
const styles = {
  notFound: {
    margin: "1em 0",
    textAlign: "center",
    fontWeight: "bold",
  },
  circularLoader: {
    textAlign: "center",
    marginTop: "18vh",
    "& > div": {
      // color: "#AAA"
    },
  },
  sectionWrapper: {
    paddingTop: "1vh",
  },
  title: {
    color: "#EC1F26",
  },
  num: {
    backgroundColor: "#EC1F26",
  },
};


function FieldSection(props) {
  const {
    classes,
    media,
    listedMedia,
    listingMedia,
    buildingMedia,
    listedBuildingMedia,
    listingBuildingMedia,
    stockData,
    intl,
  } = props;

  const hasStockData = Object.keys(media).length > 0;
  const hasBuildingData = Object.keys(buildingMedia).length > 0;

  const hasData = hasStockData || hasBuildingData;
  const listing = listingMedia || listingBuildingMedia;
  const listed = listedMedia || listedBuildingMedia;

  const stockMedia = {
    photo: (media.photo || []),
    video: (media.video || []),
    virtualTour: (media.virtualTour || []),
  };

  const lat = stockData.building && stockData.building.coordinates && stockData.building.coordinates.latitude;
  const lng = stockData.building && stockData.building.coordinates && stockData.building.coordinates.longitude;
  const googleMapMedia = [
    {
      id: "map",
      type: "photo",
      mediumRoot: lat && lng ? staticMapUrl(lat, lng) : "/tempGoogleMap",
      originalFilename: "Google Map",
      approval: "approved",
    }
  ];

  return (
    <div>
      {!listing && listed && hasData && (
        <div>
          <div className={classes.sectionWrapper}>
            <MediaMain
              media={stockMedia}
              mediaType="stock"
              isProposal={true}
              deletable={true}
            />
          </div>

          <div className={classes.sectionWrapper}>
            <MediaMain
              media={buildingMedia}
              mediaType="building"
              isProposal={true}
            />
          </div>

          <div className={classes.sectionWrapper}>
            <MediaSection
              media={googleMapMedia}
              mediaType="map"
              deletable={false}
              isProposal={true}
              proposalFieldName={"googleMapPhoto"}
              hideTypeSelect={true}
            />
          </div>
        </div>
      )}

      {(listing || !listed) && (
        <div className={classes.circularLoader}>
          <LoadingOverlay />
        </div>
      )}

      {!listing && listed && !hasData && (
        <div className={classes.notFound}>{"Stock/Building media not found"}</div>
      )}
    </div>
  );
}

const mapStateToProps = (state) => ({
  media: state.stock.media ? state.stock.media : {},
  listedMedia: state.stock.listedMedia ? state.stock.listedMedia : false,
  listingMedia: state.stock.listingMedia ? state.stock.listingMedia : false,
  buildingMedia: state.building.media ? state.building.media : {},
  listedBuildingMedia: state.building.listedMedia
    ? state.building.listedMedia
    : false,
  listingBuildingMedia: state.building.listingMedia
    ? state.building.listingMedia
    : false,
  stockData: state.stock.detail ? state.stock.detail : {},
});

export default connect(
  mapStateToProps,
)(withStyles(styles)(injectIntl(FieldSection)));
