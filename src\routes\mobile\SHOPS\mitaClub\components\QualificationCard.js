import React from "react";
import {
  Paper,
  Box,
  Typography,
  makeStyles,
} from "@material-ui/core";
import Image89 from "./asset/image89.svg";
import Image62 from "./asset/image62.svg";

const useStyles = makeStyles((theme) => ({
  qualificationCard: {
    "display": "flex",
    "flexDirection": "column",
    "alignItems": "flex-start",
    "padding": "16px 6px 32px 16px",
    "gap": "24px",
    "background": "#FFFFFF",
    "boxShadow": "0px 2px 16px rgba(57, 46, 6, 0.1)",
    "borderRadius": "8px",
    "flex": "none",
    "alignSelf": "stretch",
    "flexGrow": 0,
  },
  qualificationHeader: {
    flexWrap: "wrap",
    rowGap: "4px",
    width: "100%",
    "display": "flex",
    "flexDirection": "row",
    "justifyContent": "space-between",
    "alignItems": "baseline",
    "padding": "0px",
    "lineHeight": "20px",
    "flex": "none",
    "flexGrow": 0,
  },
  qualificationTitle: {
    // "height": "20px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "16px",
    "lineHeight": "20px",
    "letterSpacing": "0.02em",
    "color": "#222222",
    "flex": "none",
    "flexGrow": 0,
  },
  qualificationDeadline: {
    width: "100%",
    // "height": "15px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "12px",
    "lineHeight": "15px",
    "letterSpacing": "0.02em",
    "color": "#727162",
    "flex": "none",
    "flexGrow": 0,
  },
  qualificationLevel: {
    display: "flex",
    alignItems: "flex-start",
    gap: "16px",
    width: "100%",
  },
  badgeIcon: {
    width: 40,
    height: 40,
    borderRadius: "50%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    color: "white",
    fontWeight: "bold",
    flexShrink: 0
  },
  goldBadge: {
  },
  silverBadge: {
    backgroundColor: "#C0C0C0",
  },
  levelInfo: {
    "padding": "0px",
    "gap": "16px",
    "lineHeight": "40px",
    "flex": 1,
  },
  levelTitle: {
    "display": "flex",
    "flexDirection": "column",
    "alignItems": "flex-start",
    "padding": "0px",
    "gap": "24px",
    "flex": "none",
    "flexGrow": 0,
  },
  levelDescription: {
    "height": "15px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "12px",
    "lineHeight": "15px",
    "letterSpacing": "0.02em",
    "color": "#727162",
    "flex": "none",
    "alignSelf": "stretch",
    "flexGrow": 0,
  },
}));
function QualificationCard(props) {
  const classes = useStyles();

  const {
    titleType,
    qualificationLevels,
  } = props;

  {/* 资格卡片 */ }
  return (
    <Paper className={classes.qualificationCard}>
      {/* 卡片头部 */}
      <Box className={classes.qualificationHeader}>
        <Typography className={classes.qualificationTitle}>
          Mita Club {titleType}资格
        </Typography>

        <Typography className={classes.qualificationDeadline}>
          本年度截止日期 31/12/2025
        </Typography>
      </Box>

      {/* 资格级别 */}
      {qualificationLevels.map((level, index) => (
        <Box key={index} className={classes.qualificationLevel}>
          {level.badge && <Box className={`${classes.badgeIcon} ${level.badge === "鑽石" ? classes.goldBadge : classes.silverBadge}`}>
            <img
              alt={level.badge}
              src={level.badge === "鑽石" ? Image89 : Image62}
              style={{ width: "40px", height: "40px" }}
            />
          </Box>}

          <Box className={classes.levelInfo}>
            <Typography className={classes.levelTitle}>
              {level.title}
            </Typography>

            {level.description && <Typography className={classes.levelDescription}>
              {level.description}
            </Typography>}
          </Box>
        </Box>
      ))}
    </Paper>
  );
}

export default QualificationCard;
