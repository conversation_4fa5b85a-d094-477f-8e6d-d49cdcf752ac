import React, { useState } from "react";
import { Grid } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import _ from "lodash";
import moment from "moment";
import { FormattedMessage, injectIntl } from "react-intl";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import ReadOnlyText from "../../common/ReadOnlyText";
import IconChipButton from "../../common/IconChipButton";

import FormButton from "@/components/common/FormButton";
import { takeLandsearchLog } from "@/actions/log";
import SearchConsentDialog from "@/components/common/SearchConsentDialog";

const useStyles = makeStyles({
  wrapper: {
    borderRadius: 4,
    backgroundColor: "rgba(132,132,132,.1)",
    padding: "1vh 2vw",
    marginBottom: 10,
  },
  doc: {
    lineHeight: 1.3,
    fontWeight: 500,
  },
  subtext: {
    fontSize: ".875em",
    color: "#777",
  },
  fileBtn: {
    minWidth: "50px",
    height: "auto",
  },
});

function VendorCard({ vendorInfo, intl }) {
  const classes = useStyles();

  const [consentFile, setConsentFile] = useState(null);
console.log('[ vendorInfo ] >', vendorInfo,_.size(_.get(vendorInfo, 'contact.phones')))
  const displayContactTitle = () => {
    let title = ''

    if(vendorInfo.titleEn || vendorInfo.title){
      title += vendorInfo.titleEn || vendorInfo.title
    }

    if(vendorInfo.nameEn) {
      title += ' ' + vendorInfo.nameEn
    }

    if(vendorInfo.nameZh) {
      title += ' ' + vendorInfo.nameZh
    }

    return title || '---'
  }
  return (
    <div className={classes.wrapper}>
      <ReadOnlyText
        label={intl.formatMessage({ id: "company.detail.contact" })}
        value={displayContactTitle()}
      />
      {
        _.get(vendorInfo, 'contact') && _.size(_.get(vendorInfo, 'contact.phones')) ? (
          <Grid container spacing={1} style={{ margin: "0 -4px 0 0" }}>
            {_.map(_.get(vendorInfo, 'contact.phones'), (phoneItem, index) => {
              return <Grid key={index} item xs={6}>
                <IconChipButton
                  editable={false}
                  title={''}
                  phoneType={_.get(phoneItem, `type`)}
                  phoneNum={_.get(phoneItem, `number`)}
                  transparentbgcolor
                />
              </Grid>
            })}
          </Grid>
        ) : ''
      }
      <ReadOnlyText
        multiline
        label={intl.formatMessage({
          id: "company.detail.remarks",
        })}
        value={_.get(vendorInfo, "remarks") || "---"}
        style={{marginTop: 10}}
      />
    </div>
    // <Grid container className={classes.wrapper} direction="column">
      
    // </Grid>
  );
}

VendorCard.propTypes = {
  vendorInfo: PropTypes.object.isRequired,
  intl: PropTypes.object.isRequired
};

export default connect()(injectIntl(VendorCard));
