import React from "react";
import { injectIntl } from "react-intl";
import Grid from "@material-ui/core/Grid";
import InputWithCheckBoxCustom from "./InputWithCheckBoxCustom";
import InlineTextField from "./InlineTextField";
import InlineTextInputField from "./InlineTextInputField";
import {
  convertCurrency,
  getLangKey,
  numberComma,
  parsePeriod,
} from "../../helper/generalHelper";
import { sbu } from "../../config";

function ProposalTenantField(props) {
  const { fields, changeFieldValue, intl, status = "currentTenants", ...custom } = props;

  const inputToDisplayFee = (v) =>
    v && parseFloat(v) !== 0 ? "$" + convertCurrency(v) : "---";
  const inputToDisplayPeriod = (v) => parsePeriod(v.min, v.max, intl) || "---";
  const inputToDisplayZhEn = (v) => _.get(v, getLangKey(intl, "name")) || "---";

  return fields.map((item, index) => (
    <Grid container spacing={1} key={index}>
      { sbu === "SHOPS" && index === 0 && (
      <Grid item xs={12}>
        {intl.formatMessage({
          id: `proposal.form.${status}`,
        })}
      </Grid>
      )}
      <Grid item xs={12}>
        <InputWithCheckBoxCustom
          name={item}
          label={
            intl.formatMessage({ id: "proposal.form.tenant" }) +
            " " +
            (index + 1)
          }
          renderComponent={InlineTextField}
          checkboxInFront
          aligntoLabel
          checkboxXs={1}
          jsonField="tenant"
          inputToDisplay={inputToDisplayZhEn}
          changeFieldValue={changeFieldValue}
          disabled
          noBottomBorder
        />
      </Grid>

      { sbu === "SHOPS" && (
      <Grid item xs={12}>
        <InputWithCheckBoxCustom
          name={item}
          label={
            intl.formatMessage({ id: "proposal.form.tenantShop" }) +
            " " +
            (index + 1)
          }
          renderComponent={InlineTextField}
          checkboxInFront
          aligntoLabel
          checkboxXs={1}
          jsonField="tenantShop"
          inputToDisplay={inputToDisplayZhEn}
          changeFieldValue={changeFieldValue}
          disabled
          noBottomBorder
        />
      </Grid>
      )}

      <Grid item xs={12}>
        <InputWithCheckBoxCustom
          name={item}
          label={
            intl.formatMessage({ id: "proposal.form.rent" }) + " " + (index + 1)
          }
          renderComponent={InlineTextField}
          checkboxInFront
          aligntoLabel
          checkboxXs={1}
          jsonField="rentalFee"
          changeFieldValue={changeFieldValue}
          inputToDisplay={inputToDisplayFee}
          type="number"
          disabled
          noBottomBorder
        />
      </Grid>

      <Grid item xs={12}>
        <InputWithCheckBoxCustom
          name={item}
          label={
            intl.formatMessage({ id: "proposal.form.tenancyperiod" }) +
            " " +
            (index + 1)
          }
          renderComponent={InlineTextField}
          checkboxInFront
          aligntoLabel
          checkboxXs={1}
          jsonField="period"
          changeFieldValue={changeFieldValue}
          inputToDisplay={inputToDisplayPeriod}
          disabled
          noBottomBorder
        />
      </Grid>

      <Grid item xs={12}>
        <InputWithCheckBoxCustom
          name={item}
          label={
            intl.formatMessage({ id: "proposal.form.tenancy" }) +
            " " +
            (index + 1)
          }
          renderComponent={InlineTextField}
          checkboxInFront
          aligntoLabel
          checkboxXs={1}
          jsonField="tenancy"
          inputToDisplay={inputToDisplayZhEn}
          changeFieldValue={changeFieldValue}
          disabled
          noBottomBorder
        />
      </Grid>

      <Grid item xs={12} style={{marginBottom: "12px"}}>
        <InputWithCheckBoxCustom
          name={item}
          label={
            intl.formatMessage({ id: "proposal.form.tenancyRemarks" }) +
            " " +
            (index + 1)
          }
          renderComponent={InlineTextInputField}
          checkboxInFront
          aligntoLabel
          checkboxXs={1}
          jsonField="tenancyRemarks"
          changeFieldValue={changeFieldValue}
          multiline
          fullWidth
        />
      </Grid>
    </Grid>
  ));
}

export default injectIntl(ProposalTenantField);
