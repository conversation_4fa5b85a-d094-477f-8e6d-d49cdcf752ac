import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import FormButton from "./FormButton";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    boxShadow: "none",
  }
};

function DialogButton(props) {
  const { classes, children, className, ...other } = props;

  return (
    <FormButton className={clsx(classes.root, className)} {...other}>
      {children}
    </FormButton>
  );
}

DialogButton.propTypes = {
  onClick: PropTypes.func,
  children: PropTypes.node,
  classes: PropTypes.object.isRequired,
  className: PropTypes.string
};

export default withStyles(styles)(DialogButton);
