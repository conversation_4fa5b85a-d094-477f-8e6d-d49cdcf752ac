import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import List from "./List";
import LoadingOverlay from "../LoadingOverlay";


const styles = theme => ({
  root: {
    padding: "1vh 2vw",
  },
  notFound: {
    margin: "1em 0",
    textAlign: "center",
    fontWeight: "bold"
  },
});

class MessageCenter extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {
    const { classes, messages, listed, listing } = this.props;

    const hasData = messages.length > 0;

    return (
      <div className={classes.root}>
        {listed && hasData && <List messages={messages} />}

        {(listing || !listed) && <LoadingOverlay />}

        {!listing && listed && !hasData && (
          <div className={classes.notFound}>
            Message not found
          </div>
        )}
      </div>
    );
  }
}

const mapStateToProps = state => ({
  messages: state.messageCenter.messages ? state.messageCenter.messages : [],
  listed: state.messageCenter.listed ? state.messageCenter.listed : false,
  listing: state.messageCenter.listing ? state.messageCenter.listing : false
});

export default connect(mapStateToProps)(withStyles(styles)(MessageCenter));
