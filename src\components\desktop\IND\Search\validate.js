const validate = (values) => {
    const errors = {};
    // console.log(values);

    if (!values.districtId) {
      // console.log("it's null!!!");
      // errors.districtId = '請選輸入地區編號';
    }

    // if (!values.district) {
    //   // console.log("it's null!!!");
    //   errors.district = '請選地區';
    // }
  
    // if (!values.id) {
    //   errors.id = '請填寫ID';
    // } else if (isNaN(Number(values.id))) {
    //   errors.id = 'ID必需是數字';
    // }
  
    return errors;
  };
  
  export default validate;
  