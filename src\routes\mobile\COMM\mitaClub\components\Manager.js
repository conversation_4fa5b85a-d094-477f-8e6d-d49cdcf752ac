import React from "react";
import _ from "lodash";
import { connect } from "react-redux";
import { Box, makeStyles } from "@material-ui/core";
import QualificationCard from "./QualificationCard";
import GoldCard from "./GoldCard";
import TeamDetailsCard from "./TeamDetailsCard";
import { getEmployeesMitaclubAgent, getMitaclubEmpsByTeamCode } from "@/actions/employee";
import { convertCurrency } from "@/helper/generalHelper";

const AGENT_TARGET = [
  2_560_000,
  1_280_000,
];

const qualificationLevels = [
  {
    badge: "鑽石",
    title: "升級至鑽石級別",
    description: "首 5 名工商舖總業績",
  },
  {
    badge: "⽩⾦",
    title: "升級至⽩⾦級別",
    description: "首 10 名年度業績達到750 萬或以上",
  }
];

const membershipTiers = [
  { name: "白金", value: 7_500_000/* "1.28M" */, current: true, color: "#FFB100" },
  // { name: "白金", value: 1_280_000/* "1.28M" */, current: true, color: "#FFB100" },
  // { name: "鑽石", value: 2_560_000/* "2.56M" */, current: false, color: "#9FADC1" },
];
const useStyles = makeStyles((theme) => ({
  "@global": {
    body: {
      margin: 0,
      padding: 0,
      height: "100vh",
      backgroundColor: "#F5F5F5",
    },
    ".goldCard-progressContainer": {
      paddingLeft: 0,
      display: "flex",
      flexDirection: "column",
    },
    ".goldCard-progressContainer .goldCard-tierLabels .goldCard-tierItem": {
      transform: "translate(-21px, 0)",
    },
  },
  root: {
    // paddingBottom: "32px",
  },
  contentStack: {
    display: "flex",
    flexDirection: "column",
    width: "100%",
    gap: theme.spacing(3),
    // marginTop: "-85px",
  },
}));

function CardBox(props) {
  const classes = useStyles();

  const performanceData = React.useMemo(() => {
    return (props.mitaclubInfo || []).reduce((acc, item) => {
      acc.AccumulatedSalesAmount += +item.AccumulatedSalesAmount;
      acc.AccumulatedSalesCase += +item.AccumulatedSalesCase;
      return acc;
    }, {
      AccumulatedSalesAmount: 0,
      AccumulatedSalesCase: 0,
      ExecutionDate: props.mitaclubInfo[0]?.ExecutionDate,
    });
  }, [props.mitaclubInfo]);

  const metricsValue = React.useMemo(() => {
    if (!performanceData) {
      return "$0";
    }
    const { AccumulatedSalesAmount = 0, AccumulatedSalesCase = 0 } = performanceData;
    // return `${convertCurrency(+AccumulatedSalesAmount)} / ${AccumulatedSalesCase}`;
    return `${convertCurrency(+AccumulatedSalesAmount)}`;
  }, [performanceData]);

  return (
    <Box style={{ padding: "0 12px" }}>
      <Box className={classes.contentStack}>
        {/* 业绩卡片 */}
        <GoldCard
          title={"你的年度團隊業績"}
          updateDate={performanceData.ExecutionDate}
          currentPerformance={(+performanceData.AccumulatedSalesAmount || 0)}
          metricsValue={metricsValue}
          membershipTiers={membershipTiers}
          progressMaxColor="#9FADC1"
        />

        {/* 资格卡片 */}
        <QualificationCard
          titleType={"主管"}
          qualificationLevels={qualificationLevels}
        />

        {/* <MyTeamListCard /> */}
        {props.mitaclubAgentMap && Object.keys(props.mitaclubAgentMap).map((key) => {
          const agents = props?.mitaclubAgentMap[key];
          return (<TeamDetailsCard key={key} agents={agents} targetValues={AGENT_TARGET} />)
        })}
      </Box>
    </Box>
  );
}

function Manager(props) {
  const classes = useStyles();
  /* const teamCode = React.useMemo(() => props.employee?.dept_code, [props.employee]);

  React.useEffect(() => {
    if (teamCode) {
      props.getMitaclubEmpsByTeamCode(teamCode);
    }
  }, [props.getMitaclubEmpsByTeamCode, teamCode]); */

  React.useEffect(() => {
    const teamCodes = props.mitaclubInfo?.map(item => item.TeamCode) || [];
    props.getMitaclubEmpsByTeamCode(teamCodes);
  }, [props.mitaclubInfo]);

  return (
    <Box className={classes.root}>
      <CardBox
        mitaclubInfo={props.mitaclubInfo}
        mitaclubAgentMap={props.mitaclubAgentMap}
        mitaclubManagerInfo={props.mitaclubManagerInfo}
      />
    </Box>
  );
}

const mapStateToProps = (state) => ({
  mitaclubInfo: _.get(state, "employee.mitaclubInfo") || [],
  mitaclubManagerInfo: _.get(state, "employee.mitaclubManagerInfo") || [],
  mitaclubAgentMap: _.get(state, "employee.mitaclubAgentMap"),
  // employee: _.get(state, "employee.employees.0"),
});

const mapDispatchToProps = (dispatch) => ({
  getEmployeesMitaclubAgent: (empId) => dispatch(getEmployeesMitaclubAgent({ empId })),
  getMitaclubEmpsByTeamCode: (teamCode) => dispatch(getMitaclubEmpsByTeamCode({ teamCode })),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(Manager);
