import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import FieldVal from "../../../../common/FieldVal";
import { injectIntl } from "react-intl";
import { getLangKey } from "../../../../../helper/generalHelper";

const styles = theme => ({
  root: {
    padding: "1vh 0"
  },
  gridContent: {
    padding: "1vw 2vw"
  }
});

class Remarks extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langKey = getLangKey(intl);

    const oddEvenMapping = {
      "odd": intl.formatMessage({ id: "stock.parity.odd" }),
      "even": intl.formatMessage({ id: "stock.parity.even" }),
      "all": intl.formatMessage({ id: "stock.parity.all" }),
    };

    let remarkMapping = {
      ceilingHeight: intl.formatMessage({
        id: "stock.ceilingheight"
      }),
      entranceWidth: intl.formatMessage({
        id: "stock.entrancewidth"
      }),
      airConditioningType: intl.formatMessage({
        id: "stock.airconditioningtype"
      }),
      unitDepth: intl.formatMessage({
        id: "stock.unitdepth"
      }),
      parity: intl.formatMessage({
        id: "stock.oddeven"
      }),
      backDoor: intl.formatMessage({
        id: "stock.backdoor"
      }),
      toilet: intl.formatMessage({
        id: "stock.washroom"
      }),
      signboard: intl.formatMessage({
        id: "stock.sign"
      }),
    };

    const ceilingHeightFt = detail.ceilingHeight && detail.ceilingHeight.ft ? detail.ceilingHeight.ft + "'" : "";
    const ceilingHeightIn = detail.ceilingHeight && detail.ceilingHeight.in ? detail.ceilingHeight.in + "\"" : "";
    const entranceWidthFt = detail.doorWidth && detail.doorWidth.ft ? detail.doorWidth.ft + "'" : "";
    const entranceWidthIn = detail.doorWidth && detail.doorWidth.in ? detail.doorWidth.in + "\"" : "";
    const unitDepthFt = detail.unitDepth && detail.unitDepth.ft ? detail.unitDepth.ft + "'" : "";
    const unitDepthIn = detail.unitDepth && detail.unitDepth.in ? detail.unitDepth.in + "\"" : "";
    const remarks = {
      ceilingHeight: ceilingHeightFt && ceilingHeightIn ? ceilingHeightFt + " " + ceilingHeightIn : ceilingHeightFt || ceilingHeightIn || "---",
      entranceWidth: entranceWidthFt && entranceWidthIn ? entranceWidthFt + " " + entranceWidthIn : entranceWidthFt || entranceWidthIn || "---",
      airConditioningType: detail.airCond && detail.airCond.type && detail.airCond.type[langKey] ? detail.airCond.type[langKey] : "---",
      unitDepth: unitDepthFt && unitDepthIn ? unitDepthFt + " " + unitDepthIn : unitDepthFt || unitDepthIn || "---",
      parity: detail.parity && oddEvenMapping[detail.parity] ? oddEvenMapping[detail.parity] : "---",
      backDoor: detail.backDoor || "---",
      toilet: detail.toilet || "---",
      signboard: detail.signboard || "---",
    };

    return (
      <div className={classes.root}>
        <DetailBoxSection
          text={intl.formatMessage({
            id: "stock.remarks"
          })}
          expandable={true}
        >
          <Grid container spacing={2} className={classes.gridContent}>
            {Object.keys(remarkMapping).map((v, i) => (
              <Grid item xs={6} key={v}>
                <FieldVal field={remarkMapping[v]}>
                  {remarks[v] || "---"}
                </FieldVal>
              </Grid>
            ))}
          </Grid>
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(Remarks));
