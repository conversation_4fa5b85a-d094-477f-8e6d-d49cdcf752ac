/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import { checkAndParseUrlParam } from "../../../../helper/generalHelper";

const title = "Transaction";

async function action({ store, params, query }) {
  const { auth } = store.getState();
  if (!auth.user) {
    return { redirect: "/login" };
  } else if (auth.user.authorized == false) {
    return { redirect: "/login" };
  }

  const Transaction = await require.ensure(
    [],
    require => require("./Transaction").default,
    "transaction"
  );

  let parsedjson = checkAndParseUrlParam(query.param);
  let selectedData = checkAndParseUrlParam(query.selectedData);
  if (parsedjson === false || selectedData === false)
    return { redirect: "/error" };

  return {
    chunks: ["transaction"],
    title,
    component: (
      <Transaction
        selectedData={selectedData}
        queryvariablesFromUrl={parsedjson}
      />
    )
  };
}

export default action;
