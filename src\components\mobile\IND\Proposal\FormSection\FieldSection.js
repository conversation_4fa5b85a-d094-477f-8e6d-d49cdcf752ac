import React, { useMemo, useState, useRef } from "react";
import { connect } from "react-redux";
import { Field, FieldArray } from "redux-form";
import { injectIntl } from "react-intl";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import _ from "lodash";
import { Grid, IconButton } from "@material-ui/core";
import EmojiPicker from "emoji-picker-react";
import SentimentSatisfiedAltIcon from '@material-ui/icons/SentimentSatisfiedAlt';

import ProposalFormPaper from "../../../../common/ProposalFormPaper";
import MuiSelectArrayOutput from "../../../../common/MuiSelectArrayOutput";
import EditableNormalAndBottomPrice from "../../../../common/EditableNormalAndBottomPrice";
import InlineTextField from "../../../../common/InlineTextField";
import InlineTextInputField from "../../../../common/InlineTextInputField";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import InputWithCheckBox from "../../../../common/InputWithCheckBox";
import InputWithCheckBoxCustom from "../../../../common/InputWithCheckBoxCustom";
import ProposalFeeField from "../../../../common/ProposalFeeField";
import ProposalTenantField from "../../../../common/ProposalTenantField";
import FtInField from "../../../../common/FtInField";
import ShowHiddenCheckBox from "../../../../common/ShowHiddenCheckBox";
import BuildingInfo from "./BuildingInfo";
import { numberComma } from "../../../../../helper/generalHelper";
import {
  createOptions,
  getFloorTypeOptions,
  getProposalName,
  hasIsShowTrue,
} from "../../../../Saleskit/helpers";
import MediaView from "../../../../Media/MediaView";
import InclusiveFeeGroup from "../../../../common/InclusiveFeeGroup";
import StockIdField from "../../../../Saleskit/StockIdField";
import Switch from "../../../../common/Switch";
import PillCheckBox from "../../../../common/PillCheckBox";

// We can inject some CSS into the DOM.
const styles = {
  section: {
    paddingTop: "1vh",
  },
  right: {
    textAlign: "right",
  },
  twoLevelItem: {
    marginBottom: "1vh",
    "& > *:not(:first-child)": {
      color: "rgba(255, 255, 255, 0.75)",
    },
    padding: "0 4px",
    borderRadius: 4,
    color: "#fff",
  },
  rentItem: {
    backgroundColor: "rgba(0, 197, 197, .75)",
  },
  priceItem: {
    backgroundColor: "rgba(232, 0, 0, .75)",
  },
  greyItem: {
    backgroundColor: "rgba(132, 132, 132, .1)",
  },
  rentItemNoValue: {
    backgroundColor: "rgba(140, 190, 190, 0.2)",
  },
  priceItemNoValue: {
    backgroundColor: "rgba(200, 170, 170, 0.2)",
  },
  checkboxNoPadding: {
    padding: 0,
  },
  hidden: {
    visibility: "hidden",
  },
  basicInfo: {
    display: "flex",
    justifyContent: "space-around",
    margin: "1vh 0 0 0",
  },
  remarksFieldMargin: {
    marginBottom: 3,
  },
  autoCalSwitchContainer: {
    display: "flex",
    justifyContent: "flex-end",
    alignItems: "center",
    marginBottom: "1vh",
  },
  autoCalLabel: {
    color: "#777",
    fontSize: "14px",
    marginRight: "4px",
  },
};

function FieldSection({
  classes,
  intl,
  changeFieldValue,
  type,
  formState,
  unitViews,
  decorations,
  possessions,
  currentStates,
  stockId,
  isListProposal,
  mode,
  children,
  mediaData,
  stockDetail,
  initialValues,
  defaultExpand=false
}) {
  const { stock, media } = formState;
  const [autoCal, setAutoCal] = useState("on");
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const customTitleInputRef = useRef(null);

  let priceContainer = classes.priceItem; // TODO: need grey color?
  let leaseContainer = classes.rentItem;

  const hasPrice =
    parseFloat(_.get(stock, "avgPrice[0].value", 0)) !== 0 ||
    parseFloat(_.get(stock, "totalPrice[0].value", 0)) !== 0;
  const hasRent =
    parseFloat(_.get(stock, "avgRent[0].value", 0)) !== 0 ||
    parseFloat(_.get(stock, "totalRent[0].value", 0)) !== 0;

  const disablePriceBox = type === "Lease";
  const disableRentBox = type === "Sale";

  const actualFloor = _.get(stock, "floor[0].value", "---");
  const floorTypeOptions = getFloorTypeOptions(intl, actualFloor);

  const unitViewOptions = useMemo(
    () => createOptions(intl.locale, unitViews),
    [],
  );
  const decorationOptions = useMemo(
    () => createOptions(intl.locale, decorations),
    [],
  );
  const possessionOptions = useMemo(
    () => createOptions(intl.locale, possessions),
    [],
  );
  const currentStateOptions = useMemo(
    () => createOptions(intl.locale, currentStates),
    [],
  );

  const inputToDisplayUnit = (v) => {
    if (v && v.trim().toLowerCase() === "wf")
      return intl.locale === "zh" ? "全層" : v;
    if (intl.locale === "zh")
      return v ? v + intl.formatMessage({ id: "proposal.form.unit" }) : "---";
    else
      return v
        ? intl.formatMessage({ id: "proposal.form.unit" }) + " " + v
        : "---";
  };
  const inputToDisplayArea = (v) =>
    v && parseFloat(v) !== 0 ? numberComma(v, 0) : "---";
  const inputToDisplayAreaEff = (v) =>
    v && parseFloat(v) !== 0 ? `${numberComma(v, 2)}%` : "---";
  const inputToDisplayFee = (v) =>
    v && parseFloat(v) !== 0 ? `$${numberComma(v, 2)}` : "---";
  const inputToDisplayYield = (v) =>
    v && parseFloat(v) !== 0 ? `${numberComma(v, 2)}%` : "---";
  const inputToDisplayTitle = (v) => v || "---";

  const namePrefix = isListProposal ? `stocks.${stockId}.` : "";

  const handleAutoCalChange = (e) => {
    if (autoCal === "on") {
      setAutoCal("off");
    } else if (autoCal === "off") {
      // reset price/rent
      changeFieldValue(`${namePrefix}stock.totalPrice[0]`, {
        ...stock[`totalPrice`][0],
        value: _.get(initialValues, `${namePrefix}stock.totalPrice[0].value`),
      });
      changeFieldValue(`${namePrefix}stock.totalRent[0]`, {
        ...stock[`totalRent`][0],
        value: _.get(initialValues, `${namePrefix}stock.totalRent[0].value`),
      });
      changeFieldValue(`${namePrefix}stock.avgPrice[0]`, {
        ...stock[`avgPrice`][0],
        value: _.get(initialValues, `${namePrefix}stock.avgPrice[0].value`),
      });
      changeFieldValue(`${namePrefix}stock.avgRent[0]`, {
        ...stock[`avgRent`][0],
        value: _.get(initialValues, `${namePrefix}stock.avgRent[0].value`),
      });
      changeFieldValue(`${namePrefix}stock.bottomTotalPrice[0]`, {
        ...stock[`bottomTotalPrice`][0],
        value: _.get(initialValues, `${namePrefix}stock.bottomTotalPrice[0].value`),
      });
      changeFieldValue(`${namePrefix}stock.bottomTotalRent[0]`, {
        ...stock[`bottomTotalRent`][0],
        value: _.get(initialValues, `${namePrefix}stock.bottomTotalRent[0].value`),
      });
      changeFieldValue(`${namePrefix}stock.bottomAvgPrice[0]`, {
        ...stock[`bottomAvgPrice`][0],
        value: _.get(initialValues, `${namePrefix}stock.bottomAvgPrice[0].value`),
      });
      changeFieldValue(`${namePrefix}stock.bottomAvgRent[0]`, {
        ...stock[`bottomAvgRent`][0],
        value: _.get(initialValues, `${namePrefix}stock.bottomAvgRent[0].value`),
      });

      setAutoCal("on");
    }
  };

  const handleChangeIsNego = (field) => (e) => {
    changeFieldValue(`${namePrefix}stock.total${field}[0].isNego`, e.target.checked);
  };

  React.useEffect(() => {
    const includedFeeObj = stock.includedFee[0];
    const { rates, gRent, managementFee, acFee } = stock;
    includedFeeObj.rates = rates[0].paidBy === 'Paid By Landlord';
    includedFeeObj.gRent = gRent[0].paidBy === 'Paid By Landlord';
    includedFeeObj.managementFee = managementFee[0].paidBy === 'Paid By Landlord';
    includedFeeObj.acFee = acFee[0].paidBy === 'Paid By Landlord';
    stock.allInclusive = [rates[0], gRent[0], managementFee[0]].every(({ paidBy }) => paidBy === 'Paid By Landlord');
    if (!stock.allInclusive && [rates[0], gRent[0], managementFee[0]].some(({ paidBy }) => paidBy === 'Paid By Tenant')) {
      stock.allInclusive = false;
    }
  }, [stock.rates[0].paidBy, stock.gRent[0].paidBy, stock.managementFee[0].paidBy, stock.acFee[0].paidBy]);

  React.useEffect(() => {
    // 检查customTitle的value是否有值
    const customTitleValue = _.get(stock, "customTitle.value");
    if (customTitleValue) {
      // 如果有值则设置isShow为true
      changeFieldValue(`${namePrefix}stock.customTitle.isShow`, true);
    }
  }, [_.get(stock, "customTitle.value")]);

  const onEmojiClick = (emojiData) => {
    const { selectionStart, selectionEnd } = customTitleInputRef.current;
    const currentTitle = _.get(stock, "customTitle.value") || "";
    // 在光标位置插入表情
    const newTitle = currentTitle.substring(0, selectionStart) + 
                     emojiData.emoji + 
                     currentTitle.substring(selectionEnd);
    changeFieldValue(`${namePrefix}stock.customTitle.value`, newTitle);
    setShowEmojiPicker(false);
  }

  return (
    <ProposalFormPaper>
      <DetailBoxSection
        text={intl.formatMessage({
          id: "proposal.form.stockinfo",
        })}
        expandable
        isExpanding={true}
      >
        <StockIdField stockId={_.get(stock, "stockId", "")} />
        {mode === "indv" && (
        <>
        <Grid item xs={12}>
          <InputWithCheckBoxCustom
            name={`${namePrefix}stock.termRemarks.nameZh`}
            className={classes.remarksFieldMargin}
            label={intl.formatMessage({
              id: "proposal.form.chiRemarks",
            })}
            checkboxInFront
            aligntoLabel
            checkboxXs={1}
            fakeCheckbox
            renderComponent={InlineTextInputField}
            multiline
            fullWidth
          />
        </Grid>
        <Grid item xs={12}>
          <InputWithCheckBoxCustom
            name={`${namePrefix}stock.termRemarks.nameEn`}
            className={classes.remarksFieldMargin}
            label={intl.formatMessage({
              id: "proposal.form.engRemarks",
            })}
            checkboxInFront
            aligntoLabel
            checkboxXs={1}
            fakeCheckbox
            renderComponent={InlineTextInputField}
            multiline
            fullWidth
          />
        </Grid>
        <Grid item xs={12}>
          <InputWithCheckBoxCustom
            name={`${namePrefix}stock.customTitle`}
            label={
              <div onClick={e => e.preventDefault()} style={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
                <span>{intl.formatMessage({id: "proposal.form.customtitle",})}</span>
                {
                  <IconButton
                    size="small"
                    style={{
                      width: 48,
                      borderRadius: 18,
                      border: '1px solid gray',
                    }}
                    onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  >
                    <SentimentSatisfiedAltIcon style={{ fontSize: 18, color: 'gray' }} />
                  </IconButton>
                }
              </div>
            }
            checkboxInFront
            aligntoLabel
            checkboxXs={1}
            changeFieldValue={changeFieldValue}
            renderComponent={InlineTextField}
            fullWidth
            inputProps={{
              ref: customTitleInputRef,
            }}
          />
          {showEmojiPicker && (
            <div style={{ position: 'absolute', zIndex: 1000 }}>
              <EmojiPicker onEmojiClick={onEmojiClick} searchDisabled={true} skinTonesDisabled={true} previewConfig={{ showPreview: false }} />
            </div>
          )}
        </Grid>
        </>
        )}
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <InputWithCheckBoxCustom
              name={`${namePrefix}stock.floorType`}
              label={intl.formatMessage({
                id: "search.common.floor",
              })}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              fakeCheckbox
              renderComponent={MuiSelectArrayOutput}
              ranges={floorTypeOptions}
              isArrayOutput={false}
              fullWidth
            />
          </Grid>
          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.unit`}
              label={intl.formatMessage({
                id: "search.common.unit",
              })}
              component={InputWithCheckBox}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              inputToDisplay={inputToDisplayUnit}
              checkboxProps={{
                className: classes.checkboxNoPadding,
              }}
              checkboxInFront
              checkboxXs={2}
              // disabled
            />
          </Grid>
        </Grid>
        {/* building info */}
        <Grid container spacing={1}>
          <ShowHiddenCheckBox
            className={classes.hidden}
            input={{
              value: { isShow: true },
            }}
            changeFieldValue={() => {}}
            aligntoLabel
            disabled
          />
          <Grid item xs={11} style={{ margin: "auto" }}>
            <div>{_.get(stock, "customBuilding.value")}</div>

            {/* street info */}
            <div>
              {_.get(stock, "customStreetNo", "")}{" "}
              {_.get(stock, "customStreet", "")}{" "}
              {_.get(stock, "customDistrict", "")}
            </div>
          </Grid>
        </Grid>

        {/* basic info 3 */}
        <Grid container direction="column" className={classes.basicInfo}>
          <Grid item container spacing={1}>
            <Grid item xs={4}>
              <FieldArray
                name={`${namePrefix}stock.areaGross`}
                label={intl.formatMessage({ id: "proposal.form.gross" })}
                component={InputWithCheckBox}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                type="number"
                inputToDisplay={inputToDisplayArea}
                min={0}
                checkboxProps={{
                  className: classes.checkboxNoPadding,
                }}
                checkboxInFront
                checkboxXs={3}
                disabled
                noBottomBorder
              />
            </Grid>
            <Grid item xs={4}>
              <FieldArray
                name={`${namePrefix}stock.areaNet`}
                label={intl.formatMessage({ id: "proposal.form.net" })}
                component={InputWithCheckBox}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                type="number"
                inputToDisplay={inputToDisplayArea}
                min={0}
                checkboxProps={{
                  className: classes.checkboxNoPadding,
                }}
                checkboxInFront
                checkboxXs={3}
                disabled
                noBottomBorder
              />
            </Grid>
            <Grid item xs={4}>
              <FieldArray
                name={`${namePrefix}stock.areaSaleable`}
                label={intl.formatMessage({ id: "proposal.form.saleable" })}
                component={InputWithCheckBox}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                type="number"
                inputToDisplay={inputToDisplayArea}
                min={0}
                checkboxProps={{
                  className: classes.checkboxNoPadding,
                }}
                checkboxInFront
                checkboxXs={3}
                disabled
                noBottomBorder
              />
            </Grid>
          </Grid>
          <Grid item container spacing={1}>
            <Grid item xs={4}>
              <FieldArray
                name={`${namePrefix}stock.areaLettable`}
                label={intl.formatMessage({ id: "proposal.form.lettable" })}
                component={InputWithCheckBox}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                type="number"
                inputToDisplay={inputToDisplayArea}
                min={0}
                checkboxProps={{
                  className: classes.checkboxNoPadding,
                }}
                checkboxInFront
                checkboxXs={3}
                disabled
                noBottomBorder
              />
            </Grid>
            <Grid item xs={4}> 
              <FieldArray
                name={`${namePrefix}stock.areaTerrace`}
                label={intl.formatMessage({ id: "stock.terrace" })}
                component={InputWithCheckBox}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                type="number"
                inputToDisplay={inputToDisplayArea}
                min={0}
                checkboxProps={{
                  className: classes.checkboxNoPadding,
                }}
                checkboxInFront
                checkboxXs={3}
                disabled
                noBottomBorder
              />
            </Grid>
            <Grid item xs={4}> 
              <FieldArray
                name={`${namePrefix}stock.areaRoof`}
                label={intl.formatMessage({ id: "stock.roof" })}
                component={InputWithCheckBox}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                type="number"
                inputToDisplay={inputToDisplayArea}
                min={0}
                checkboxProps={{
                  className: classes.checkboxNoPadding,
                }}
                checkboxInFront
                checkboxXs={3}
                disabled
                noBottomBorder
              />
            </Grid>
          </Grid>
          <Grid item container spacing={1}>
            <Grid item xs={4}>
              <FieldArray
                name={`${namePrefix}stock.areaEfficiency`}
                label={intl.formatMessage({ id: "stock.area.effcy" })}
                component={InputWithCheckBox}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                type="number"
                inputToDisplay={inputToDisplayAreaEff}
                min={0}
                checkboxProps={{
                  className: classes.checkboxNoPadding,
                }}
                checkboxInFront
                checkboxXs={3}
                disabled
                noBottomBorder
              />
            </Grid>
          </Grid>
        </Grid>

        {/* prcie rent box */}
        <div className={classes.right}>
          <div
            className={clsx(
              classes.twoLevelItem,
              leaseContainer,
              (disableRentBox) && classes.rentItemNoValue,
            )}
          >
            <EditableNormalAndBottomPrice
              avgDecimal={2}
              label="Rent"
              fieldNamePrefix={namePrefix}
              changeFieldValue={changeFieldValue}
              formFields={stock}
              disabled={disableRentBox}
              autoCal={autoCal === "on"}
            />
          </div>

          <div style={{ marginBottom: "1vh" }}>
          <Field
            name={`${namePrefix}stock.totalRent[0].isNego`}
            component={PillCheckBox}
            input={{
              value: _.get(stock, "totalRent[0].isNego", false),
              onChange: handleChangeIsNego("Rent"),
            }}
            text={intl.formatMessage({ id: "stock.negotiable" })}
            disabled={disableRentBox}
          />
          </div>

          <div
            className={clsx(
              classes.twoLevelItem,
              priceContainer,
              (disablePriceBox) && classes.priceItemNoValue,
            )}
          >
            <EditableNormalAndBottomPrice
              label="Price"
              fieldNamePrefix={namePrefix}
              changeFieldValue={changeFieldValue}
              formFields={stock}
              disabled={disablePriceBox}
              updateYield
              autoCal={autoCal === "on"}
            />
          </div>

          <div style={{ marginBottom: "1vh" }}>
          <Field
            name={`${namePrefix}stock.totalPrice[0].isNego`}
            component={PillCheckBox}
            input={{
              value: _.get(stock, "totalPrice[0].isNego", false),
              onChange: handleChangeIsNego("Price"),
            }}
            text={intl.formatMessage({ id: "stock.negotiable" })}
            disabled={disablePriceBox}
          />
          </div>

          {/*<div className={classes.autoCalSwitchContainer}>*/}
          {/*  <div className={classes.autoCalLabel}>*/}
          {/*    {intl.formatMessage({ id: "common.autoCalculate" })}*/}
          {/*  </div>*/}
          {/*  <Switch*/}
          {/*    value={autoCal}*/}
          {/*    textL={intl.formatMessage({*/}
          {/*      id: "common.off",*/}
          {/*    })}*/}
          {/*    textR={intl.formatMessage({*/}
          {/*      id: "common.on",*/}
          {/*    })}*/}
          {/*    valleft="off"*/}
          {/*    valright="on"*/}
          {/*    onChange={handleAutoCalChange}*/}
          {/*    onOffSwitch*/}
          {/*  />*/}
          {/*</div>*/}
        </div>

        {/* unit section */}
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.stockType`}
              label={intl.formatMessage({
                id: "stock.stocktype",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              disabled
              noBottomBorder
            />
          </Grid>
          <Grid item xs={6}>
            {isListProposal ? (
              <FieldArray
                name={`${namePrefix}stock.unitView`}
                label={intl.formatMessage({
                  id: "search.form.view",
                })}
                component={InputWithCheckBox}
                checkboxInFront
                aligntoLabel
                checkboxXs={2}
                renderComponent={MuiSelectArrayOutput}
                changeFieldValue={changeFieldValue}
                ranges={unitViewOptions}
                isArrayOutput={false}
              />
            ) : (
              <FieldArray
                name={`${namePrefix}stock.unitView`}
                label={intl.formatMessage({
                  id: "search.form.view",
                })}
                component={InputWithCheckBox}
                checkboxInFront
                aligntoLabel
                checkboxXs={2}
                renderComponent={MuiSelectArrayOutput}
                changeFieldValue={changeFieldValue}
                ranges={unitViewOptions}
                isArrayOutput={false}
                // disabled
              />
            )}
          </Grid>
          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.possession`}
              label={intl.formatMessage({
                id: "search.form.possession",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={MuiSelectArrayOutput}
              changeFieldValue={changeFieldValue}
              ranges={possessionOptions}
              isArrayOutput={false}
            />
          </Grid>
          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.currentState`}
              label={intl.formatMessage({
                id: "search.form.currentState",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={MuiSelectArrayOutput}
              changeFieldValue={changeFieldValue}
              ranges={currentStateOptions}
              isArrayOutput={false}
            />
          </Grid>

          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.decoration`}
              label={intl.formatMessage({
                id: "search.form.decoration",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={MuiSelectArrayOutput}
              changeFieldValue={changeFieldValue}
              ranges={decorationOptions}
              isArrayOutput={false}
            />
          </Grid>
          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.availability`}
              label={intl.formatMessage({
                id: "proposal.form.availabilitydate",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              // disabled={!isListProposal}
            />
          </Grid>

          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.ceilingHeight`}
              label={intl.formatMessage({
                id: "proposal.form.ceilingHeight",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={FtInField}
              changeFieldValue={changeFieldValue}
              disabled
              noBottomBorder
            />
          </Grid>

          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.floorLoading`}
              label={intl.formatMessage({
                id: "stock.floorloading",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              inputToDisplay={inputToDisplayArea}
              disabled
              noBottomBorder
            />
          </Grid>

          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.yield`}
              label={intl.formatMessage({
                id: "stock.yield",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              inputToDisplay={inputToDisplayYield}
              type="number"
              min={0}
              disabled
              noBottomBorder
            />
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <FieldArray
            name={`${namePrefix}stock.managementFee`}
            label={intl.formatMessage({
              id: "stock.mgtfee",
            })}
            component={ProposalFeeField}
            renderComponent={InlineTextField}
            changeFieldValue={changeFieldValue}
            inputToDisplay={inputToDisplayFee}
            type="number"
            min={0}
            included={
              _.get(stock, "allInclusive") ||
              _.get(stock, "includedFee[0].managementFee")
            }
            formState={formState}
            isListProposal={isListProposal}
            stockId={stockId}
          />
        </Grid>

        <Grid item xs={12}>
          <FieldArray
            name={`${namePrefix}stock.gRent`}
            label={intl.formatMessage({
              id: "stock.grent",
            })}
            component={ProposalFeeField}
            renderComponent={InlineTextField}
            changeFieldValue={changeFieldValue}
            inputToDisplay={inputToDisplayFee}
            type="number"
            min={0}
            included={
              _.get(stock, "allInclusive") ||
              _.get(stock, "includedFee[0].gRent")
            }
            formState={formState}
            isListProposal={isListProposal}
            stockId={stockId}
          />
        </Grid>

        <Grid item xs={12}>
          <FieldArray
            name={`${namePrefix}stock.rates`}
            label={intl.formatMessage({
              id: "stock.rates",
            })}
            component={ProposalFeeField}
            renderComponent={InlineTextField}
            changeFieldValue={changeFieldValue}
            inputToDisplay={inputToDisplayFee}
            type="number"
            min={0}
            included={
              _.get(stock, "allInclusive") ||
              _.get(stock, "includedFee[0].rates")
            }
            formState={formState}
            isListProposal={isListProposal}
            stockId={stockId}
          />
        </Grid>

        <Grid item xs={12}>
          <FieldArray
            name={`${namePrefix}stock.acFee`}
            label={intl.formatMessage({
              id: "stock.airconditioningfee",
            })}
            component={ProposalFeeField}
            renderComponent={InlineTextField}
            changeFieldValue={changeFieldValue}
            inputToDisplay={inputToDisplayFee}
            type="number"
            min={0}
            included={
              _.get(stock, "allInclusive") ||
              _.get(stock, "includedFee[0].acFee")
            }
            formState={formState}
            isListProposal={isListProposal}
            stockId={stockId}
          />
        </Grid>

        <Grid item xs={12}>
          <InclusiveFeeGroup
            fieldNamePrefix={namePrefix}
            includedFee={_.get(stock, "includedFee[0]")}
            allInclusive={_.get(stock, "allInclusive")}
            changeFieldValue={changeFieldValue}
          />
        </Grid>

        <Grid item xs={12}>
          <FieldArray
            name={`${namePrefix}stock.currentTenants`}
            component={ProposalTenantField}
            changeFieldValue={(...args) => {
              changeFieldValue(...args);
              if (isListProposal && hasIsShowTrue(args[1])) {
                changeFieldValue(`general.showTenancy[0].isShow`, true);
              }
            }}
          />
        </Grid>

        <Grid item xs={12} style={{ marginTop: 15 }}>
          <InputWithCheckBoxCustom
            name={`${namePrefix}stock.remarks`}
            className={classes.remarksFieldMargin}
            label={intl.formatMessage({
              id: "proposal.form.remarks",
            })}
            checkboxInFront
            aligntoLabel
            checkboxXs={1}
            fakeCheckbox
            renderComponent={InlineTextInputField}
            multiline
            fullWidth
          />
        </Grid>
      </DetailBoxSection>

      <BuildingInfo
        fieldNamePrefix={namePrefix}
        isListProposal={isListProposal}
        changeFieldValue={changeFieldValue}
      />

      {/* <Media stockId={stockId} /> */}
      <MediaView
        fieldNamePrefix={namePrefix}
        stockMedia={_.get(mediaData, "stock")}
        buildingMedia={_.get(mediaData, "building")}
        stockId={stockId}
        isProposal
        coordinates={_.pick(media, ["lat", "lng"])}
        defaultExpand={defaultExpand}
        isListProposal={mode === "list"}
      />

      {children}
    </ProposalFormPaper>
  );
}

const mapStateToProps = (state) => ({
  form: state.form ? state.form : {},
  unitViews: state.stock.unitViews,
  decorations: state.stock.decorations,
  possessions: state.stock.possessions,
  currentStates: state.stock.currentStates,
});

export default connect(mapStateToProps)(
  withStyles(styles)(injectIntl(FieldSection)),
);
