export const LIST_TRANSACTIONS_QUERY = `query ($limit: Int, $offset: Int, $sorter: [transactionSorter], $building: [ID], $district: [ID], $type: [String], $floorMin: Int, $floorMax: Int, $floorLeft: String, $floorRight: String, $areaMin: Int, $areaMax: Int, $priceMinTotal: Int, $priceMaxTotal: Int, $priceMinAvg: Int, $priceMaxAvg: Int, $rentMinTotal: Int, $rentMaxTotal: Int, $rentMinAvg: Int, $rentMaxAvg: Int, $dateMin: String, $dateMax: String, $isDeleted: Boolean, $unicornId: [Int], $dataSource: [String]) {
  transactions(limit: $limit, offset: $offset, sort: $sorter, building: $building, district: $district, type: $type, floorMin: $floorMin, floorMax: $floorMax, floorLeft: $floorLeft, floorRight: $floorRight, areaMin: $areaMin, areaMax: $areaMax, priceMinTotal: $priceMinTotal, priceMaxTotal: $priceMaxTotal, priceMinAvg: $priceMinAvg, priceMaxAvg: $priceMaxAvg, rentMinTotal: $rentMinTotal, rentMaxTotal: $rentMaxTotal, rentMinAvg: $rentMinAvg, rentMaxAvg: $rentMaxAvg, dateMin: $dateMin, dateMax: $dateMax, isDeleted: $isDeleted, unicornId: $unicornId, dataSource: $dataSource) {
    _id
    type
    date
    district {
      en
    }
    building {
      en
      zh
    }
    floor
    unit
    area {
      value
    }
    sell
    ft_sell
    data_source {
      en
      zh
    }
    street {
      en
    }
    agent {
      branch_code
      sales
    }
    last_update {
      datetime
      by {
        name_en
      }
    }
    tenancy {
      ft_rent
    }
  }
}
  `;
