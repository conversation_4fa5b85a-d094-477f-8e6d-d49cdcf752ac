/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

/* eslint-disable max-len */

// if (process.env.BROWSER) {
//   throw new Error(
//     "Do not import `config.js` from inside the client-side code."
//   );
// }

module.exports = {
  mode: process.env.NODE_ENV || "development",

  // Node.js app
  port: process.env.PORT || 3000,

  // https://expressjs.com/en/guide/behind-proxies.html
  trustProxy: process.env.TRUST_PROXY || "loopback",

  deployDomain: process.env.DEPLOY_DOMAIN || "localhost",

  // API Gateway
  api: {
    // API URL to be used in the client-side code
    clientUrl: process.env.API_CLIENT_URL || "",
    // API URL to be used in the server-side code
    serverUrl:
      process.env.API_SERVER_URL ||
      `http://localhost:${process.env.PORT || 3000}`,
    district: process.env.API_DISTRICT,
    street: process.env.API_STREET,
    building: process.env.API_BUILDING,
    stock: process.env.API_STOCK,
    transaction: process.env.API_TRANSACTION,
    employee: process.env.API_EMPLOYEE,
    supplement: process.env.API_SUPPLEMENT,
    search: process.env.API_SEARCH,
    www: process.env.API_WWW,
    contact: process.env.API_CONTACT,
    // media: process.env.API_MEDIA,
    media: "http://ici-media-comm-1:5000/media-c/graphql",
    firstHand: process.env.FIRST_HAND_STOCK_API,
    s3MediaToLocal: process.env.API_S3_MEDIA_TO_LOCAL,
    stockInternal: process.env.API_STOCK_INTERNAL,
    employeeInternal: process.env.API_EMPLOYEE_INTERNAL,
    contactIneternal: process.env.API_CONTACT_INTERNAL,
    proposal: process.env.API_PROPOSAL,
    landsearch: process.env.API_LANDSEARCH,
    landsearchfilepath: process.env.API_LANDSEARCH_FILEPATH,
    landsearchLog: process.env.API_LANDSEARCH_LOG,

    dmo: process.env.API_DMO,
    landSearchDetails: process.env.API_LANDSEARCH_DETAILS,
    getGeneralDailyQuota:
      process.env.API_GET_GENERAL_DAILY_QUOTA || "/getGeneralDailyQuota",
    icimsgeneral: process.env.API_ICIMS_GENERAL || "",
  },
  dmoToken: process.env.API_DMO_TOKEN,

  notificationApi: {
    url: process.env.API_NOTIFICATION_CENTER_URL,
    company: process.env.NOTIFICATION_CENTER_COMPANY || "mr",
    requestFrom: process.env.NOTIFICATION_CENTER_REQUESTFROM || "ICIMS",
  },

  cloudwatchlog: {
    url: process.env.API_CLOUDWATCH_LOG_URL,
    internalurl: process.env.API_CLOUDWATCH_LOG_INTERNAL_URL,
    logGroup: process.env.LOG_GROUP || "testLoggingFromAPI",
  },

  requestTimeout: process.env.REQUEST_TIMEOUT || 5000,
  uploadRequestTimeout: process.env.UPLOAD_REQUEST_TIMEOUT || 30000,
  refresh_token_timeout: process.env.REFRESH_TOKEN_TIMEOUT || 5000,

  // Database
  databaseUrl: process.env.DATABASE_URL || "sqlite:database.sqlite",
  databaseName: process.env.DATABASE_NAME,

  databaseTable: process.env.DATABASETALBE_URL || "",

  sessionDatabaseName: process.env.SESSION_DATABASE_NAME,

  // Web analytics
  analytics: {
    // https://analytics.google.com/
    googleTrackingId: process.env.GOOGLE_TRACKING_ID, // UA-XXXXX-X
    gtagMangerId: process.env.GTAG_MANAGER_ID, // GTM-XXXXXXX
  },

  baseUrl: process.env.BASE_URL || "",
  publicHost: process.env.PUBLIC_HOST || "",
  productionHost: process.env.PRODUCTION_HOST || "",

  // Authentication
  authHost: process.env.AUTH_HOST || "",

  authCentralHost: process.env.AUTH_CENTRAL_HOST || "",

  appNotification: {
    apiUrl: process.env.APP_NOTIFI_BOARDCAST_URL || "",
  },

  resendpinCounter: process.env.RESENDPIN_COUNTER || 15,

  corsOrigin: process.env.CORS_ORIGIN,

  media: {
    collectionName: process.env.MEDIA_COLLECTION_NAME || "media",
    tempDir: process.env.MEDIA_TEMP_DIR || "./public/temp/",
    destDir: process.env.MEDIA_DEST_DIR || "./public/media/",
    acceptableFormat: process.env.ACCEPTABLE_FORMAT || "jpg,png,jpeg,mov,mp4,pdf",
    acceptableFormatForKOL: process.env.ACCEPTABLE_FORMAT_FOR_KOL || "mov,mp4",
    acceptableFormatForKOLCoverPicture: process.env.ACCEPTABLE_FORMAT_FOR_KOL_COVER_PICTURE || "jpg,png,jpeg",
    kolVideoDurationLimitSeconds: +process.env.KOL_VIDEO_DURATION_LIMIT_SECONDS || 600,
  },

  generalDailyQuota: 100,
  generalProposalQuota: process.env.PROPOSAL_QUOTA || 100,
  generalDailyPPStockQuota: process.env.PP_STOCK_QUOTA || 120,

  link: {
    buildingWeb: {
      prefix: "https://www.midlandici.com.hk/ics/property/building/details/",
    },
    stockList: {
      prefix: "https://www.midlandici.com.hk/ics/property/find/?pis_bldg_id=",
    },
    profilePhoto: {
      prefix: "https://file.midlandici.com/mr/img/emp_photo/",
      suffix: "_L.jpg",
    },
    profilePage: {
      prefix: "https://www.midlandici.com.hk/ics/property/agent/",
    },
    proposalPdf: {
      prefix: `http://localhost:8888/sales-kit/pdf`,
      suffix: "?mode=proposal",
      listProposalSuffix: "?mode=listProposal",
    },
    s3File: {
      prefix:
        "https://file.midlandici.com/msearch/",
      reportPrefix: "https://s3.ap-southeast-1.amazonaws.com/file.midlandici.com/msearch/report"
    },
    previewProposal: `http://localhost:8888/sales-kit/preview` || "",
    previewPDFProposal: `http://localhost:8888/sales-kit/preview_pdf` || "",
  },

  loginRestriction: {
    itUsers:
      process.env.IT_USERS ||
      "********,********,********,********,********,********,H202129A,H202218A,********,********",
    bannedUsers: process.env.BANNED_USERS || "",
  },

  mediaBypassCode: process.env.MEDIA_BYPASS_CODE || "",

  auth: {
    url: process.env.OAUTH2_URL || "",
    client_id: process.env.CLIENT_ID || "",
    client_secret: process.env.CLIENT_SECRET || "",
    authenticated_userid: process.env.AUTHENTICATED_USERID || "",
    provision_key: process.env.PROVISION_KEY || "",
    grant_type: process.env.GRANT_TYPE || "",
    refresh_grant_type: process.env.REFRESH_GRANT_TYPE || "",
    scope: process.env.SCOPE || "",
  },

  sbu: process.env.SBU || "COMM",

  locale: process.env.LOCALE || "en",
  enableTranslation: process.env.ENABLE_TRANSLATION || false,

  backendKey: process.env.BACKEND_KEY || "", // key used to call garphql apis from backend (should not be shared with UI)

  recaptchaSiteKey: process.env.RECAPTCHA_SITE_KEY || "",
  recaptchaSecretKey: process.env.RECAPTCHA_SECRET_KEY || "",
  enableRecaptcha: process.env.ENABLE_RECAPTCHA || "true",

  proposalBypassCode: process.env.PROPOSAL_BYPASS_CODE || "",
  enableConsolidLandSearch: process.env.ENABLE_CONSOLID_LANDSEARCH || "false",
  enableDesktopView: process.env.ENABLE_DESKTOPVIEW || "false",

  enableBatchTwo: process.env.ENABLE_BATCH_TWO === "true",
  enableWWWScore: process.env.ENABLE_WWW_SCORE === "true",
  sso: process.env.SSO,
  cas_access_token_host: process.env.CAS_ACCESS_TOKEN_HOST,
  cas_refresh_token_host: process.env.CAS_REFRESH_TOKEN_HOST,
  cas_profile_host: process.env.CAS_PROFILE_HOST,
  cas_logout_host: process.env.CAS_LOGOUT_HOST,
  cas_auth_code_host: process.env.CAS_AUTH_CODE_HOST,
  cas_client_id: process.env.CAS_CLIENT_ID,
  cas_client_secret: process.env.CAS_CLIENT_SECRET,
  GATEWAY: "https://msdc-dev.midlandici.com/api/authorization/permissionsBypassFhc",
  permissions: {
    PERMISSION_VIEW_STOCK_QUOTA: process.env.PERMISSION_VIEW_STOCK_QUOTA,
  },

  cas_client_service: process.env.CAS_CLIENT_SERVICE,

  m1PublicKeyName: process.env.M1_PUBLIC_KEY_NAME,
  wholeBlockTypeId: process.env.WB_TYPE_ID || "5fb4cd977d6989199ce019ea",
  jwtSecret: "3d4257516e05a830004a595ee355e13410402a4dabef4fe7a41b91252a1d48445b9fcd6c5f5ac67a6360de26be9d6fc28331b91b57612357d4d9d1895675742c024e7f0b99f0b4a6e348402b022cee329783a445ab052dd26f89ebe72e2f866b4361275185252124d431b7102fcb4c11423c1444a3d6d0dc8894040e3c69cbcac5540821755e28de06effc9030dfec4d9750492a1756afc790922d09809104ed800c21f6685484dd0425feadd43710cedbb3bbcacd7ce46ae61c5a5d7414bc3729996879c1303edd2104d83faea9d6d19622d9ee3eba0aa43d58ef7d4abb68225f5b17307da930ccee5cf3456a19eb599e86a73c77debed213e07bb69f411da7",

  refreshBeforeTimeoutInterval: 1000 * 60 * 5, // 5 minutes
  // auth: {
  //   jwt: { secret: process.env.JWT_SECRET || "React Starter Kit" },

  //   // https://developers.facebook.com/
  //   facebook: {
  //     id: process.env.FACEBOOK_APP_ID || "186244551745631",
  //     secret:
  //       process.env.FACEBOOK_APP_SECRET || "********************************"
  //   },

  //   // https://cloud.google.com/console/project
  //   google: {
  //     id:
  //       process.env.GOOGLE_CLIENT_ID ||
  //       "251410730550-ahcg0ou5mgfhl8hlui1urru7jn5s12km.apps.googleusercontent.com",
  //     secret: process.env.GOOGLE_CLIENT_SECRET || "Y8yR9yZAhm9jQ8FKAL8QIEcd"
  //   },

  //   // https://apps.twitter.com/
  //   twitter: {
  //     key: process.env.TWITTER_CONSUMER_KEY || "*************************",
  //     secret:
  //       process.env.TWITTER_CONSUMER_SECRET ||
  //       "KTZ6cxoKnEakQCeSpZlaUCJWGAlTEBJj0y2EMkUBujA7zWSvaQ"
  //   }
  // }
};
