pipeline {

    agent any

    stages {
        stage('Git Pull') {
            steps {
                sh '''
                    cd $SOURCE_PATH
                    git pull --rebase
                '''
            }
        }

        stage('Build') {
            steps {
                sh '''
                    cd $SOURCE_PATH
                    cat $SOURCE_PATH/env_file | envsubst > $SOURCE_PATH/.env
                    docker rmi -f $IMAGE_NAME
                    docker build -f $DOCKER_FILE -t $IMAGE_NAME --target prod-build .
                '''
            }
        }

        stage('Deploy') {
            steps {
                script {
                    sh '''
                        cd $SOURCE_PATH
                        cat $SOURCE_PATH/$DEPLOYMENT_FILE | envsubst >> $SOURCE_PATH/tmp.yml
                         aws ecr get-login-password --region ap-southeast-1 | sudo docker login --username AWS --password-stdin 603870317818.dkr.ecr.ap-southeast-1.amazonaws.com
                        sudo docker push $IMAGE_NAME
                        sudo aws eks --region ap-southeast-1 update-kubeconfig --name $EKS_NAME
                    '''
                    try{
                        sh 'sudo kubectl delete -f $SOURCE_PATH/tmp.yml'
                    }catch(exc){}

                    sh '''
                        sudo kubectl apply -f $SOURCE_PATH/tmp.yml
                        cd $SOURCE_PATH
                        rm tmp.yml
                        rm .env
                    '''
                }
            }
        }
    }
}
