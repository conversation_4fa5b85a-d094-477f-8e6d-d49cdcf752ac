/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import { NativeEventSource, EventSourcePolyfill } from "event-source-polyfill";
import hotClient from "webpack-hot-middleware/client";
import launchEditorEndpoint from "react-dev-utils/launchEditorEndpoint";
import formatWebpackMessages from "react-dev-utils/formatWebpackMessages";
import {
  setEditorHandler,
  reportBuildError,
  dismissBuildError,
  startReportingRuntimeErrors,
  stopReportingRuntimeErrors
} from "react-error-overlay";

global.EventSource = NativeEventSource || EventSourcePolyfill;

setEditorHandler(errorLocation => {
  const fileName = encodeURIComponent(errorLocation.fileName);
  const lineNumber = encodeURIComponent(errorLocation.lineNumber || 1);
  fetch(
    // Keep in sync with react-dev-utils/errorOverlayMiddleware
    `${launchEditorEndpoint}?fileName=${fileName}&lineNumber=${lineNumber}`
  );
});

hotClient.useCustomOverlay({
  showProblems(type, errors) {
    const formatted = formatWebpackMessages({
      errors,
      warnings: []
    });

    reportBuildError(formatted.errors[0]);
  },
  clear() {
    dismissBuildError();
  }
});

hotClient.setOptionsAndConnect({
  name: "client",
  reload: true
});

startReportingRuntimeErrors({
  filename: "/assets/client.js"
});

if (module.hot) {
  module.hot.dispose(stopReportingRuntimeErrors);
}
