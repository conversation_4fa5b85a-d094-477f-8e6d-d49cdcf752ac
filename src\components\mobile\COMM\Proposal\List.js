import React, { useEffect } from "react";
import { connect } from "react-redux";
import { getFormValues } from "redux-form";
import PropTypes from "prop-types";
import { injectIntl } from "react-intl";
import _ from "lodash";

import {
  findZhEnFromOptions,
  getDefaultRemarks,
  getFloorTypeInfoFromLangFile,
  getProposalName,
  getStockFloorType,
  getAreaType,
  langKey,
  parseMedia,
  parseTenancyRecords,
  titleMapping,
  twoDec,
  yesNoMapping,
  getOriginalfloorTypeEnValue,
  getNotNullIsShow,
  mergeTenants,
  getStockStatus
} from "../../../Saleskit/helpers";
import langFile from "../../../../lang/COMM/messages";
import ListProposalForm from "../../../Saleskit/Forms/ListProposalForm";
import FieldSection from "./FormSection/FieldSection";
import {
  clearCreateListProposal,
  createListProposal,
} from "../../../../actions/listProposal";
import { sbu } from "../../../../config";
import { getLangKey, checkExpiredDate } from "../../../../helper/generalHelper";

function List({
  detail,
  media,
  buildingMedia,
  stockUnitViews,
  stockDecorations,
  stockPossessions,
  stockCurrentStates,
  reCreatePPStocks,
  createDialogOpen,
  closeDialog,
  mode,
  createListProposal,
  clearCreateListProposal,
  intl,
  formState,
}) {
  useEffect(() => () => clearCreateListProposal(), []);

  const getStockInitialValues = (stockId) => {
    const stock = _.find(detail, (s) => s._id === stockId);
    const stockMediaData = _.find(
      media,
      (m) => m.id === _.get(stock, "unicorn.id").toString(),
    );
    const buildingMediaData = _.find(
      buildingMedia,
      (m) => m.id === _.get(stock, "building.unicorn.id", "").toString(),
    );

    if (_.isEmpty(stock) || _.isNil(stockMediaData)) return {};
    let ppHistoryContent = null;
    if (reCreatePPStocks?.proposals?.length > 0) {
      ppHistoryContent = _.find(reCreatePPStocks.proposals, (p) => p.stockMongoId === stockId);
    } else if (!_.isEmpty(reCreatePPStocks)) {
      ppHistoryContent = reCreatePPStocks;
    }

    let type = null;
    if (_.get(ppHistoryContent, "type")) {
      type = _.get(ppHistoryContent, "type");
    } else if (mode !== "list") {
      type = getStockStatus(stock?.status)
    } else {
      type = "SaleAndLease";
    }

    const possibleFeeType = ["/SqFt", "/Qtr", "/Month", "/SY"];

    const currentTenants = mergeTenants(
      parseTenancyRecords(_.get(stock, "tenancyRecords", []), intl, "Current"),
      ppHistoryContent?.currentTenants || []
    );

    const mainPhoto = []
      .concat(
        _.get(buildingMediaData, "data.photo", []),
        _.get(stockMediaData, "data.photo", []),
      )
      .find((m) => m?.tags?.indexOf("main") >= 0);

    // get the current tenants from stock.tenancyRecords (can be multiple tenants)
    const currTenancyRecords = _.filter(
      _.get(stock, "tenancyRecords", []),
      (rec) => rec.status === "Current" && !rec.deleted,
    );
    // add up to accumulative rental from all tenants
    const currentRent = currTenancyRecords.reduce(
      (prev, curr) => prev + (curr?.rentalFee || 0),
      0,
    );

    const areas = _(_.get(stock, "area.sizes", [])).reduce(
      (areaObj, item) => {
        if (!Object.keys(areaObj).includes(item.type)) return areaObj;
        return { ...areaObj, [item.type]: item.value || 0 };
      },
      { GROSS: 0, NET: 0, SALEABLE: 0, LETTABLE: 0 },
    );

    const getAreaValues = () => {
      const areaGross = _.get(ppHistoryContent, "areaGross.value", _.get(areas, "GROSS")) || 0;
      const areaNet = _.get(ppHistoryContent, "areaNet.value", _.get(areas, "NET")) || 0;
      const areaSaleable = _.get(ppHistoryContent, "areaSaleable.value", _.get(areas, "SALEABLE")) || 0;
      const areaLettable = _.get(ppHistoryContent, "areaLettable.value", _.get(areas, "LETTABLE")) || 0;

      const areaGrossIsShow = _.get(ppHistoryContent, "areaGross.isShow", !!areaGross) || false;
      const areaNetIsShow = _.get(ppHistoryContent, "areaNet.isShow", !!areaNet) || false;
      const areaSaleableIsShow = _.get(ppHistoryContent, "areaSaleable.isShow", !!areaSaleable) || false;
      const areaLettableIsShow = _.get(ppHistoryContent, "areaLettable.isShow", !!areaLettable) || false;

      const areaValues = {
        areaGross: [{ value: areaGross, isShow: areaGrossIsShow }],
        areaNet: [{ value: areaNet, isShow: areaNetIsShow }],
        areaSaleable: [{ value: areaSaleable, isShow: areaSaleableIsShow }],
        areaLettable: [{ value: areaLettable, isShow: areaLettableIsShow }],
      };

      let areaType = "";
      switch (true) {
        case !!areaGross: areaType = "areaGross"; break;
        case !!areaNet: areaType = "areaNet"; break;
        case !!areaSaleable: areaType = "areaSaleable"; break;
        case !!areaLettable: areaType = "areaLettable"; break;
        default: areaType = getAreaType(areas); break;
      }

      return {
        ...areaValues,
        areaType,
      }
    };

    /** 將字符串轉換原有數組結構 */
    const valuesStr2Array = ({
      valueObj = {},
      locale = "zh",
      zhPrefix = "",
      enPrefix = "",
      isShow = false,
      separatorZh = "，",
      separatorEn = ", ",
    }) => {
      const strValueZhArr = (_.get(valueObj, 'nameZh') || "").replace(zhPrefix, "").split(separatorZh);
      const strValueEnArr = (_.get(valueObj, 'nameEn') || "").replace(enPrefix, "").split(separatorEn);

      const maxLenArr = strValueZhArr.length > strValueEnArr.length ? strValueZhArr : strValueEnArr;
      if (!valueObj || !maxLenArr.length) {
        return null;
      }
      const result = [];
      maxLenArr.forEach((_1, index) => {
        const label = _.get(locale === "zh" ? strValueZhArr : strValueEnArr, `${index}`) || "";
        if(!label) { return; }
        result.push({
          label,
          value: {
            nameZh: _.get(strValueZhArr, `${index}`) || "",
            nameEn: _.get(strValueEnArr, `${index}`) || ""
          }
        });
      });
      return [{
        value: result,
        isShow,
      }];
    };

    // const areaType = getAreaType(areas);
    const { areaType, areaGross, areaLettable, areaNet, areaSaleable } = getAreaValues();

    const floor = _.get(stock, "floor") || "";
    const floorType = "Actual Floor";
    // const floorType = !_.isNaN(parseInt(floor, 10))
    //   ? "Actual Floor"
    //   : getStockFloorType(
    //       _.get(stock, "building.floors") || [],
    //       parseInt(floor, 10),
    //     );

    /** AWS-3746 when AC type = Central AC, default check 連中央冷氣 in pp, user can edit afterwards */
    const isMgtFeeOpenACIsShow = ppHistoryContent
      ? (_.get(ppHistoryContent, "isMgtFeeOpenAC") || false)
      : _.get(stock, `isIncludeAirConditioning`)


    const managementFeeUnit = _.get(ppHistoryContent, "managementFee.unit") ||
    (_.indexOf(possibleFeeType, _.get(stock, "managementFee.type")) >
      -1
      ? _.get(stock, "managementFee.type", "")
      : "/SqFt");

    const totalMonthlyMgtFee = managementFeeUnit === "/SqFt" ? parseFloat(_.get(stock, "managementFee", 0)) * parseFloat(_.get(areaGross, "[0].value", 0) || _.get(areaNet, "[0].value", 0) || _.get(areaSaleable, "[0].value", 0) || _.get(areaLettable, "[0].value", 0)) : 0;
    const photos = (ppHistoryContent?.photos || []).filter(p => p?.id);
    const photoIds = photos.map(obj => obj.id);
    return {
      stock: {
        stockId: _.get(stock, "unicorn.id"),
        show: true,
        avgPrice: [
          {
            value: _.get(ppHistoryContent, "avgPrice.value") || _.get(stock, "askingPrice.average", 0),
            // Proposal type is Sale by default
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "avgPrice.isShow"), (type === "Sale" || type === "SaleAndLease")),
          },
        ],
        totalPrice: [
          {
            value: _.get(stock, "askingPrice.total", 0),
            // Proposal type is Sale by default
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "totalPrice.isShow"), (type === "Sale" || type === "SaleAndLease")),
            isNego: getNotNullIsShow(_.get(ppHistoryContent, "totalPrice.isNego"), false),
          },
        ],
        bottomAvgPrice: [
          {
            value: _.get(ppHistoryContent, "bottomAvgPrice.value") || _.get(
              _.find(_.get(stock, "askingPrice.details", []), [
                "type",
                "average_bottom",
              ]),
              "value",
              0,
            ),
            isShow: _.get(ppHistoryContent, "bottomAvgPrice.isShow") || false,
          }
        ],
        bottomTotalPrice: [
          {
            value: _.get(ppHistoryContent, "bottomTotalPrice.value") || _.get(
              _.find(_.get(stock, "askingPrice.details", []), [
                "type",
                "total_bottom",
              ]),
              "value",
              0,
            ),
            isShow: _.get(ppHistoryContent, "bottomTotalPrice.value") || false
          }
        ],
        avgRent: [
          {
            value: _.get(ppHistoryContent, "avgRent.value") || _.get(stock, "askingRent.average", 0),
            // Proposal type is Sale by default
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "avgRent.isShow"), (type === "Lease" || type === "SaleAndLease")),
          },
        ],
        totalRent: [
          {
            value: _.get(ppHistoryContent, "totalRent.value") || _.get(stock, "askingRent.total", 0),
            // Proposal type is Sale by default
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "totalRent.isShow"), (type === "Lease" || type === "SaleAndLease")),
            isNego: _.get(ppHistoryContent, "totalRent.isNego") || false,
          },
        ],
        bottomAvgRent: [
          {
            value: _.get(ppHistoryContent, "bottomAvgRent.value") || _.get(
              _.find(_.get(stock, "askingRent.details", []), [
                "type",
                "average_bottom",
              ]),
              "value",
              0,
            ),
            isShow: _.get(ppHistoryContent, "bottomAvgRent.isShow") || false,
          }
        ],
        bottomTotalRent: [
          {
            value: _.get(ppHistoryContent, "bottomTotalRent.value") || _.get(
              _.find(_.get(stock, "askingRent.details", []), [
                "type",
                "total_bottom",
              ]),
              "value",
              0,
            ),
            isShow: _.get(ppHistoryContent, "bottomTotalRent.isShow") || false,
          }
        ],
        stockType: [
          {
            value: _.get(ppHistoryContent, `stockType.value.${langKey(intl.locale, "name")}`) || _.get(stock, `stockType.${langKey(intl.locale, "name")}`),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "stockType.isShow"), true),
          },
        ],
        floor: [
          {
            value: _.get(ppHistoryContent, `floor.value`) || floor,
            isShow: getNotNullIsShow(_.get(ppHistoryContent, `floor.isShow`), true),
          },
        ],
        unit: [
          {
            value: _.get(ppHistoryContent, `unit.value`) || _.get(stock, "unit", ""),
            isShow: _.get(ppHistoryContent, `unit.isShow`) || true,
          },
        ],
        customBuilding: {
          value: _.get(ppHistoryContent, `customBuilding.value`) || _.get(
            stock,
            `building.${langKey(intl.locale, "name")}`,
            "---",
          ),
          isShow: getNotNullIsShow(_.get(ppHistoryContent, `customBuilding.isShow`), true),
        },
        customStreet: _.get(ppHistoryContent, `customStreet`) || _.get(
          stock,
          `building.street.street.${langKey(intl.locale, "name")}`,
          "",
        ),
        customStreetNo: _.get(ppHistoryContent, `customStreetNo`) || _.get(stock, "building.street.number", ""),
        customDistrict: _.get(ppHistoryContent, `customDistrict`) || _.get(
          stock,
          `building.district.${langKey(intl.locale, "name")}`,
          "---",
        ),
        areaType: areaType,
        areaEfficiency: [
          {
            value: _.get(ppHistoryContent, `areaEfficiency.value`) || _.get(stock, "area.efficiency", 0),
            isShow: _.get(ppHistoryContent, `areaEfficiency.isShow`) || false,
          },
        ],
        areaGross,
        areaNet,
        areaSaleable,
        areaLettable,
        possession: [
          {
            value: _.get(ppHistoryContent?.possession?.value, "nameEn") || _.get(stock, "possession"),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "possession.isShow"), !(type === 'Lease')),
          },
        ],
        currentState: [
          {
            value: _.get(ppHistoryContent, "currentState.value.nameEn") || _.get(stock, `currentState.nameEn`, ""),
            isShow: _.get(ppHistoryContent, "currentState.isShow") || false,
          }
        ],
        managementFee: [
          {
            value: _.get(ppHistoryContent, "managementFee.value") || _.get(stock, "managementFee", "") || 0,
            unit: managementFeeUnit,
            isShow: getNotNullIsShow(_.get(ppHistoryContent, `managementFee.isShow`), true),
            totalMonthlyMgtFee: _.get(ppHistoryContent, "managementFee.totalMonthlyMgtFee", Number.isInteger(totalMonthlyMgtFee) ? totalMonthlyMgtFee : Math.round(totalMonthlyMgtFee + 0.5)),
            totalMonthlyMgtFeeIsShow: getNotNullIsShow(_.get(ppHistoryContent, `managementFee.totalMonthlyMgtFeeIsShow`),
              _.get(stock, "managementFee.totalMonthlyMgtFeeIsShow") || true),
          },
        ],
        gRent: [
          {
            value: _.get(ppHistoryContent, "gRent.value") || _.get(stock, "governmentRent", "") || 0,
            unit: _.get(ppHistoryContent, "gRent.unit") || _.get(stock, "governmentRent.type") || "/Qtr",
            isShow: _.get(ppHistoryContent, "gRent.isShow") || false,
          }
        ],
        rates: [
          {
            value: _.get(ppHistoryContent, "rates.value", _.get(stock, "rates") || 0),
            unit: _.get(ppHistoryContent, "rates.unit") || _.get(stock, "rates.type", "") || "/Qtr",
            isShow: _.get(ppHistoryContent, "rates.isShow") || false,
          }
        ],
        // acFee: [
        //   {
        //     value: _.get(ppHistoryContent, "acFee.value", _.get(stock, "airConditioningFee") || 0),
        //     unit: _.get(ppHistoryContent, "acFee.unit") || _.get(stock, "airConditioningFee.type") || "/Qtr",
        //     isShow: _.get(ppHistoryContent, "acFee.isShow") || false,
        //   }
        // ],
        includedFee: [
          {
            managementFee: _.get(ppHistoryContent, "includedFee.managementFee") || false,
            rates: _.get(ppHistoryContent, "includedFee.rates") || false,
            gRent: _.get(ppHistoryContent, "includedFee.gRent") || false,
            acFee: _.get(ppHistoryContent, "includedFee.acFee") || false,
          },
        ],
        availability: [
          {
            value: _.get(ppHistoryContent, "availability.value") || _.get(stock, "availability", "---"),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "availability.isShow"), !!_.get(stock, "availability")),
          },
        ],
        haveCarPark: [
          {
            value: _.get(ppHistoryContent, "haveCarPark.value.nameEn") || (_.get(stock, "building.haveCarPark") || false
              ? intl.formatMessage({ id: "stock.yes" })
              : intl.formatMessage({ id: "stock.no" })),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "haveCarPark.isShow"), _.get(stock, "building.haveCarPark", false) ? true : false),
          },
        ],
        allInclusive: getNotNullIsShow(_.get(ppHistoryContent, "allInclusive"), _.every(
          [
            _.get(stock, "isIncludeManagementFee", false),
            _.get(stock, "isIncludeRates", false),
            _.get(stock, "isIncludeGovernmentRent", false),
            _.get(stock, "isIncludeAirConditioning", false),
          ],
          Boolean,
        )),
        isMgtFeeOpenAC: [
          {
            value: intl.formatMessage({
              id: "proposal.form.mgtFeeOpenAC",
            }),
            isShow: isMgtFeeOpenACIsShow, // _.get(ppHistoryContent, "isMgtFeeOpenAC") || false,
          },
        ],
        decoration:
          sbu === "COMM"
            ? (_.get(ppHistoryContent, "decoration.value") ? valuesStr2Array({
              valueObj: _.get(ppHistoryContent, "decoration.value"),
              locale: intl.locale,
              zhPrefix: "裝修：",
              enPrefix: "Decoration: ",
              isShow: getNotNullIsShow(_.get(ppHistoryContent, "decoration.isShow"), !!_.get(stock, "decorations")),
            }) : [
                {
                value: _.map(_.get(stock, "decorations"), (v) => {
                  return {
                    label: _.get(v, langKey(intl.locale, "name")),
                    value: findZhEnFromOptions(
                      stockDecorations,
                      _.get(v, "nameEn"),
                    ),
                  }
                }),
                isShow: getNotNullIsShow(_.get(ppHistoryContent, "decoration.isShow"), !!_.get(stock, "decorations")),
                },
              ])
            : [
              {
                value: _.get(ppHistoryContent, "decoration.value.nameEn") || _.get(stock, "decoration", ""),
                isShow: getNotNullIsShow(_.get(ppHistoryContent, "decoration.isShow"), !!_.get(stock, "decoration")),
              },
            ],
        unitView:
          sbu === "COMM"
            ? (_.get(ppHistoryContent, "unitView.value") ? valuesStr2Array({
              valueObj: _.get(ppHistoryContent, "unitView.value"),
              locale: intl.locale,
              zhPrefix: "景觀：",
              enPrefix: "View: ",
              isShow: getNotNullIsShow(_.get(ppHistoryContent, "unitView.isShow"), !!_.get(stock, "unitViews")),
            }) : [
                {
                  value: _.map(_.get(stock, "unitViews"), (v) => ({
                    label: _.get(v, langKey(intl.locale, "name")),
                    value: findZhEnFromOptions(
                      stockUnitViews,
                      _.get(v, "nameEn"),
                    ),
                  })),
                  isShow: !!_.get(stock, "unitViews"),
                },
              ])
            : [
              {
                value: _.get(ppHistoryContent, "unitView.value.nameEn") || _.get(stock, "unitView", ""),
                isShow: getNotNullIsShow(_.get(ppHistoryContent, "unitView.isShow"), !!_.get(stock, "unitView")),
              },
            ],
        remarks: _.get(ppHistoryContent, "remarks") || "",
        yield: [
          {
            value: _.get(ppHistoryContent, "yield.value", _.get(stock, "yield")) ||
              (currentRent && _.get(stock, "askingPrice.total")
                ? twoDec(
                  ((currentRent * 12) /
                    _.get(stock, "askingPrice.total", 1)) *
                  100,
                )
                : 0),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "yield.isShow"), !!(currentRent && _.get(stock, "askingPrice.total"))),
          },
        ],
        videoUrl: [
          {
            value: _.get(ppHistoryContent, `videoUrl.value`) || _.get(stock, `videoUrl`) || "",
            isShow: ppHistoryContent ? _.get(ppHistoryContent, `videoUrl.isShow`, false) : false,
          },
        ],
        usage: [
          {
            value: _.get(ppHistoryContent, `building.usage.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.buildingUsage${intl.locale === "zh" ? "Zh" : ""}`,
                "---",
              ),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "building.usage.isShow"), !!_.get(
              stock,
              `building.buildingUsage${intl.locale === "zh" ? "Zh" : ""}`,
            )),
          },
        ],
        title: [
          {
            value: _.get(ppHistoryContent, `building.title.value.${langKey(intl.locale)}`) ||
              _.get(
                _.get(titleMapping, _.get(stock, "building.title", ""), {}),
                langKey(intl.locale, "name"),
                "---",
              ),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "building.title.isShow"), false),
          },
        ],
        inTakeDate: [
          {
            value: _.get(ppHistoryContent, "building.inTakeDate.value") ||
              _.get(stock, "building.completionDate") || "---",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "building.inTakeDate.isShow"), !!_.get(stock, "building.completionDate")),
          },
        ],
        managementCompany: [
          {
            value: _.get(ppHistoryContent, `building.managementCompany.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.managementCompany.managementCompany.${langKey(
                  intl.locale,
                  "name",
                )}`,
              ) || "---",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "building.managementCompany.isShow"), false),
          },
        ],
        transport: [
          {
            value: _.get(ppHistoryContent, `building.transport.value.${langKey(intl.locale)}`) ||
              _.get(stock, `building.${langKey(intl.locale, "transport")}`) ||
              "---",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "building.transport.isShow"), false),
          },
        ],
        passengerLift: [
          {
            value: _.get(ppHistoryContent, `building.passengerLift.value.${langKey(intl.locale)}`) || _.get(stock, "building.passengerLift") || "",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "building.passengerLift.isShow"), false),
          },
        ],
        cargoLift: [
          {
            value: _.get(ppHistoryContent, `building.cargoLift.value.${langKey(intl.locale)}`) || (_.get(stock, "building.haveCargoLift", false)
              ? intl.formatMessage({ id: "stock.yes" })
              : intl.formatMessage({ id: "stock.no" })),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "building.cargoLift.isShow"), _.get(stock, "building.haveCargoLift", false) ? true : false),
          },
        ],
        airConditioningType: [
          {
            value: _.get(ppHistoryContent, `building.airConditioningType.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.airConditioning.type${intl.locale === "zh" ? "Zh" : ""
                }`,
              ) || "---",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "building.airConditioningType.isShow"), !!_.get(
              stock,
              `building.airConditioning.type${intl.locale === "zh" ? "Zh" : ""
              }`,
            )),
          },
        ],
        airConditioningOpeningTime: [
          {
            value: _.get(ppHistoryContent, `building.airConditioningOpeningTime.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.airConditioning.openingTime`,
              ) || "---",
            isShow: _.get(ppHistoryContent, "building.airConditioningOpeningTime.isShow") || false,
          },
        ],
        airConditioningExtraCharges: [
          {
            value: _.get(ppHistoryContent, `building.airConditioningExtraCharges.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.airConditioning.extraCharges`,
              ) || "---",
            isShow: _.get(ppHistoryContent, "building.airConditioningExtraCharges.isShow") || false,
          },
        ],
        currentTenants,
        floorType: getOriginalfloorTypeEnValue(_.get(ppHistoryContent, `floorType.${langKey('en')}`, floorType)),
        termRemarks: _.get(ppHistoryContent, "termRemarks") || getDefaultRemarks(type),
        customTitle: {
          value: _.get(ppHistoryContent, "customTitle.value") || "",
          isShow: _.get(ppHistoryContent, "customTitle.isShow") || false,
        },
      },
      media: {
        ...(mode === "indv"
          ? {
              mainPhoto: _.get(ppHistoryContent, "mainPhoto.id") || mainPhoto?.id || null,
              main1Photo: _.get(ppHistoryContent, "main1Photo.id") || null,
            }
          : {
              mainPhoto: _.get(ppHistoryContent, "mainPhoto.id") || _.get(ppHistoryContent, "main1Photo.id") || mainPhoto?.id,
            }),
        googleMapPhoto: photoIds.includes('map') ? ['map'] : mode === "indv" ? ['map'] : [],
        selectedMedia: photos.length ? photoIds : mode === "indv" ? ['map'] : [],
        stockMedia: !ppHistoryContent ? [] : _.map(_.get(ppHistoryContent, "photos", []), (p) => _.get(p, "id")) || []
          .concat(media?.photo || [], media?.video || [])
          ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
          ?.map((v) => v.id),
        buildingMedia: !ppHistoryContent ? [] : []
          .concat(buildingMedia?.photo || [], buildingMedia?.video || [])
          ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
          ?.map((v) => v.id),
        lat: _.get(ppHistoryContent, "lat", _.get(stock, "building.coordinates.latitude")),
        lng: _.get(ppHistoryContent, "lng", _.get(stock, "building.coordinates.longitude")),
        govLat: _.get(ppHistoryContent, "govLat", _.get(stock, "building.coordinates.latitude")),
        govLng: _.get(ppHistoryContent, "govLng", _.get(stock, "building.coordinates.longitude")),
        attachmentMultiImgConfig: photos.length ? _.reduce(_.get(ppHistoryContent, "photos", []), (result, photo) => {
          if (!photo?.id || (photo.id === "map" && !_.get(ppHistoryContent, "googleMap.isPP", false))) {
            return result;
          }
          result[photo.id] = photo.multiImg;
          return result;
        }, {}) : mode === "indv" ? { map: 'ONE'} : {},
      },
    };
  };

  const customSort = (a, b) => {
    const districtA = _.get(a, `building.district.nameZh`);
    const districtB = _.get(b, `building.district.nameZh`);
    const buildingA = _.get(a, `building.nameZh`);
    const buildingB = _.get(b, `building.nameZh`);
    let floorA = _.get(a, 'floor');
    let floorB = _.get(b, 'floor');
    let unitA = _.get(a, 'unit');
    let unitB = _.get(b, 'unit');
    if (districtA?.localeCompare(districtB) !== 0) {
      return districtA?.localeCompare(districtB, 'zh-Hant', { numeric: true })
    } else if (buildingA?.localeCompare(buildingB) !== 0) {
      return buildingA?.localeCompare(buildingB, 'zh-Hant', { numeric: true })
    } else if (floorA !== floorB) {
      return floorA?.localeCompare(floorB, undefined, { numeric: true })
    } else {
      return unitA?.localeCompare(unitB, undefined, { numeric: true })
    }
  }

  const initializeFormState = () => {
    const ppHistoryContent = reCreatePPStocks;

    // determine stock type
    let type = null;
    if (_.get(ppHistoryContent, "type")) {
      type = _.get(ppHistoryContent, "type");
    } else if (mode !== "list") {
      type = getStockStatus(_.get(detail, "0.status"))
    } else {
      type = "SaleAndLease";
    }

    const termRemarks = _.get(ppHistoryContent, "termRemarks") || getDefaultRemarks(type);

    const form = {
      stocks: detail.reduce((stockForms, stock) => {
        const stockId = _.get(stock, "_id");
        if (_.isNull(stockId)) return stockForms;

        return {
          ...stockForms,
          [stockId]: getStockInitialValues(stockId),
        };
      }, {}),
      order: detail.sort(customSort).map(stock => stock._id),
    };

    const floorNum = parseInt(_.get(detail, "0.floor"), 10);
    const floorType = "Actual Floor";
    // const floorType = _.isNaN(floorNum)
    //   ? "Actual Floor"
    //   : getStockFloorType(_.get(detail, "0.building.floors") || [], floorNum);

    form.general = {
      type,
      multiImg: _.get(ppHistoryContent, "multiImg", "FOUR") || "FOUR",
      showEmployeePhoto: [
        {
          value: intl.formatMessage({
            id: "proposal.form.showemployeephoto",
          }),
          isShow: getNotNullIsShow(_.get(ppHistoryContent, "showEmployeePhoto"), true),
        },
      ],
      showContact: [
        {
          value: intl.formatMessage({
            id: "proposal.form.showcontact",
          }),
          isShow: getNotNullIsShow(_.get(ppHistoryContent, "showContact"), true),
        },
      ],
      exactFloor: [
        {
          value: intl.formatMessage({
            id: "proposal.form.exactFloor",
          }),
          isShow: getNotNullIsShow(_.get(ppHistoryContent, "exactFloor"), true),
        },
      ],
      showUnit: [
        {
          value: intl.formatMessage({
            id: `proposal.form.showUnit`,
          }),
          isShow: true,
        },
      ],
      ...(mode === "list" ? {
      showMainPhoto: [
        {
          value: intl.formatMessage({
            id: "proposal.form.showMainPhoto",
          }),
          isShow: true,
        },
      ],
      showTenancy: [
        {
          value: intl.formatMessage({
            id: "proposal.form.showTenancy",
          }),
          isShow: false,
        },
      ],
      showPossession: [
        {
          value: intl.formatMessage({
            id: "proposal.form.showPossession",
          }),
          isShow: true,
        },
      ],
      showCurrentState: [
        {
          value: intl.formatMessage({
            id: "proposal.form.currentState.column",
          }),
          isShow: false,
        },
      ],
      } : {}),
      proposalName: _.get(ppHistoryContent, "proposalName") || getProposalName(intl, _.get(detail, "0"), floorType),
      // companyTitle: _.get(ppHistoryContent, "companyTitle") || "midlandici", // List PP 沒有該屬性
      lang: _.get(ppHistoryContent, "lang") || "CHI",
      customTitle: {
        value: _.get(ppHistoryContent, "customTitle.value") || "",
        isShow: _.get(ppHistoryContent, "customTitle.isShow") || false,
      },
      termRemarks,
      mode,
    };
    return form;
  };

  // const parseProposal = (form, stock) => {
  //   const { stock: stockValue, media: mediaValue } = form;

  //   const stockMediaData = _.get(
  //     media.find((m) => m.id === _.get(stock, "unicorn.id").toString()),
  //     "data.photo",
  //     [],
  //   );
  //   const buildingMediaData = _.get(
  //     buildingMedia.find(
  //       (m) => m.id === _.get(stock, "building.unicorn.id", "").toString(),
  //     ),
  //     "data.photo",
  //     [],
  //   );
  //   const mediaData = [].concat(stockMediaData, buildingMediaData);

  //   const parsedForm = {
  //     stockId: _.get(stock, "unicorn.id"),
  //     stockMongoId: _.get(stock, "_id"),
  //     floor: _.get(stockValue, "floor[0]"),
  //     // wf unit display in CHI will be handled on saleskit
  //     // parse back to wf if CHI wf is used
  //     unit: {
  //       ..._.get(stockValue, "unit[0]", {}),
  //       value:
  //         _.get(stockValue, "unit[0].value") === "全層"
  //           ? "WF"
  //           : _.get(stockValue, "unit[0].value"),
  //     },
  //     avgPrice: {
  //       ..._.get(stockValue, "avgPrice[0]"),
  //       value: _.get(stockValue, "avgPrice[0].value", 0),
  //     },
  //     totalPrice: {
  //       ..._.get(stockValue, "totalPrice[0]"),
  //       value: _.get(stockValue, "totalPrice[0].value", 0),
  //     },
  //     avgRent: {
  //       ..._.get(stockValue, "avgRent[0]"),
  //       value: _.get(stockValue, "avgRent[0].value", 0),
  //     },
  //     totalRent: {
  //       ..._.get(stockValue, "totalRent[0]"),
  //       value: _.get(stockValue, "totalRent[0].value", 0),
  //     },

  //     areaEfficiency: _.get(stockValue, "areaEfficiency[0]"),
  //     areaGross: _.get(stockValue, "areaGross[0]"),
  //     areaNet: _.get(stockValue, "areaNet[0]"),
  //     areaSaleable: _.get(stockValue, "areaSaleable[0]"),
  //     areaLettable: _.get(stockValue, "areaLettable[0]"),

  //     possession: {
  //       value: _.defaultTo(
  //         findZhEnFromOptions(
  //           stockPossessions,
  //           _.get(stockValue, "possession[0].value"),
  //         ),
  //         null,
  //       ),
  //       isShow: _.get(stockValue, "possession[0].isShow"),
  //     },
  //     decoration: {
  //       value: _.defaultTo(
  //         findZhEnFromOptions(
  //           stockDecorations,
  //           _.get(stockValue, "decoration[0].value"),
  //         ),
  //         null,
  //       ),
  //       isShow: _.get(stockValue, "decoration[0].isShow"),
  //     },
  //     unitView: {
  //       value: _.defaultTo(
  //         findZhEnFromOptions(
  //           stockUnitViews,
  //           _.get(stockValue, "unitView[0].value"),
  //         ),
  //         null,
  //       ),
  //       isShow: _.get(stockValue, "unitView[0].isShow"),
  //     },
  //     managementFee: _.get(stockValue, "managementFee[0]"),
  //     availability: _.get(stockValue, "availability[0]"),
  //     includedFee: _.get(stockValue, "includedFee[0]"),
  //     allInclusive: _.get(stockValue, "allInclusive"),
  //     remarks: _.get(stockValue, "remarks"),
  //     floorType: getFloorTypeInfoFromLangFile(langFile, stockValue?.floorType),
  //     floorInChinese: _.get(stockValue, "floor[0].value", ""),
  //     districtNameZh: _.get(stock, "building.district.nameZh", ""),
  //     districtNameEn: _.get(stock, "building.district.nameEn", ""),
  //     streetNameZh: _.get(stock, "building.street.street.nameZh", ""),
  //     streetNameEn: _.get(stock, "building.street.street.nameEn", ""),
  //     streetNo: _.get(stock, "building.street.number", ""),
  //     mainPhoto:
  //       _.get(mediaValue, "main1Photo") === "map"
  //         ? null
  //         : parseMedia(mediaData, _.get(mediaValue, "main1Photo")),
  //     photos: _.concat(
  //       [],
  //       _.map(_.get(mediaValue, "stockMedia", []), (m) =>
  //         parseMedia(mediaData, m),
  //       ),
  //       _.map(_.get(mediaValue, "buildingMedia", []), (m) =>
  //         parseMedia(mediaData, m),
  //       ),
  //     ),
  //     includeGGMap: _.get(mediaValue, "googleMapPhoto", []).length > 0,
  //     useGGMapPhoto: _.get(mediaValue, "main1Photo") === "map",
  //     // yield: _.get(stockValue, "yield[0]"),
  //     yield: {
  //       ..._.get(stockValue, "yield[0]", {}),
  //       value: parseFloat(_.get(stockValue, "yield[0].value") || "0"),
  //     },
  //     building: {
  //       nameZh: _.get(stock, "building.nameZh", ""),
  //       nameEn: _.get(stock, "building.nameEn", ""),
  //       districtNameZh: _.get(stock, "building.district.nameZh", ""),
  //       districtNameEn: _.get(stock, "building.district.nameEn", ""),
  //       haveCarPark: {
  //         ..._.get(stockValue, "haveCarPark[0]", {}),
  //         value:
  //           yesNoMapping[
  //             _.get(stock, "building.haveCarPark", false) ? "yes" : "no"
  //           ],
  //       },
  //       airConditioningType: {
  //         ..._.get(stockValue, "airConditioningType[0]", {}),
  //         value: {
  //           nameZh: _.get(stock, "building.airConditioning.typeZh", "---"),
  //           nameEn: _.get(stock, "building.airConditioning.type", "---"),
  //         },
  //       },
  //       usage: {
  //         ..._.get(stockValue, "usage[0]", {}),
  //         value: {
  //           nameZh: _.get(stock, "building.buildingUsageZh") || "",
  //           nameEn: _.get(stock, "building.buildingUsage") || "",
  //         },
  //       },
  //       title: {
  //         ..._.get(stockValue, "title[0]", {}),
  //         value: {
  //           nameZh:
  //             _.get(
  //               _.get(titleMapping, _.get(stock, "building.title", ""), {}),
  //               "nameZh",
  //             ) || "---",
  //           nameEn:
  //             _.get(
  //               _.get(titleMapping, _.get(stock, "building.title", ""), {}),
  //               "nameEn",
  //             ) || "---",
  //         },
  //       },
  //       managementCompany: {
  //         ..._.get(stockValue, "managementCompany[0]", {}),
  //         value: {
  //           nameZh:
  //             _.get(
  //               stock,
  //               "building.managementCompany.managementCompany.nameZh",
  //             ) || "---",
  //           nameEn:
  //             _.get(
  //               stock,
  //               "building.managementCompany.managementCompany.nameEn",
  //             ) || "---",
  //         },
  //       },
  //       transport: {
  //         ..._.get(stockValue, "transport[0]", {}),
  //         value: {
  //           nameZh: _.get(stock, `building.transportZh`) || "---",
  //           nameEn: _.get(stock, `building.transportEn`) || "---",
  //         },
  //       },
  //       passengerLift: {
  //         ..._.get(stockValue, "passengerLift[0]", {}),
  //         value: {
  //           nameZh: _.get(stockValue, "passengerLift[0].value") || "---",
  //           nameEn: _.get(stockValue, "passengerLift[0].value") || "---",
  //         },
  //       },
  //       cargoLift: {
  //         ..._.get(stockValue, "cargoLift[0]", {}),
  //         value:
  //           yesNoMapping[
  //             _.get(stock, "building.haveCargoLift", false) ? "yes" : "no"
  //           ],
  //       },
  //       inTakeDate: _.get(stockValue, "inTakeDate[0]"),
  //       lat: _.get(stock, "building.coordinates.latitude"),
  //       lng: _.get(stock, "building.coordinates.longitude"),
  //     },
  //     isSoleagent:
  //       !_.isNil(_.get(stock, "soleagent.periodStart")) &&
  //       !_.isNil(_.get(stock, "soleagent.periodEnd")) &&
  //       checkExpiredDate(
  //         _.get(stock, "soleagent.periodStart"),
  //         _.get(stock, "soleagent.periodEnd"),
  //       ),
  //     currentTenants: _.map(_.get(stockValue, "currentTenants", []), (v) => ({
  //       tenant: {
  //         ...(_.get(v, "tenant") || {}),
  //         isShow: v.tenantIsShow,
  //       },
  //       rentalFee: {
  //         value: v.rentalFee || 0,
  //         isShow: v.rentalFeeIsShow,
  //       },
  //       period: {
  //         ...(_.get(v, "period") || {}),
  //         isShow: v.periodIsShow,
  //       },
  //       tenancy: {
  //         ...(_.get(v, "tenancy") || {}),
  //         isShow: v.tenancyIsShow,
  //       },
  //     })),
  //   };
  //   return parsedForm;
  // };

  // const handleSubmit = (values) => {
  //   const { stocks: stocksForm, general, order } = values;
  //   const parsedStockForms = order
  //     .map((stockId) =>
  //       _.get(stocksForm, `${stockId}.stock.show`)
  //         ? parseProposal(
  //             stocksForm[stockId],
  //             detail.find((s) => s._id === stockId),
  //           )
  //         : null,
  //     )
  //     .filter((form) => !!form);

  //   const variables = {
  //     ...general,
  //     sbu,
  //     hideEmployeePhoto: _.get(general, "hideEmployeePhoto[0].isShow"),
  //     hideContact: _.get(general, "hideContact[0].isShow"),
  //     hideMainPhoto: _.get(general, "hideMainPhoto[0].isShow"),
  //     hideTenancy: _.get(general, "hideTenancy[0].isShow"),
  //     proposals: parsedStockForms,
  //   };

  //   createListProposal(variables);
  // };

  return (
    <ListProposalForm
      onSubmit={() => createListProposal(mode)}
      initialValues={initializeFormState()}
      FormComponent={FieldSection}
      createDialogOpen={createDialogOpen}
      closeDialog={closeDialog}
      mode={mode}
    />
  );
}

List.propTypes = {
  detail: PropTypes.array.isRequired,
  media: PropTypes.array.isRequired,
  buildingMedia: PropTypes.array.isRequired,
  stockUnitViews: PropTypes.array.isRequired,
  stockDecorations: PropTypes.array.isRequired,
  stockPossessions: PropTypes.array.isRequired,
  createListProposal: PropTypes.func.isRequired,
  clearCreateListProposal: PropTypes.func.isRequired,
};

const mapStateToProps = (state) => ({
  detail: _.get(state, "stock.detail", []),
  media: _.get(state, "stock.media", []),
  buildingMedia: _.get(state, "building.media", []),
  stockUnitViews: _.get(state, "stock.unitViews", []),
  stockDecorations: _.get(state, "stock.decorations", []),
  stockPossessions: _.get(state, "stock.possessions", []),
  stockCurrentStates: _.get(state, "stock.currentStates", []),
  reCreatePPStocks: _.get(state, "proposal.reCreatePPStocks", []),
  formState: getFormValues("proposal")(state),
});
const mapDispatchToProps = (dispatch) => ({
  createListProposal: (mode) => dispatch(createListProposal(mode)),
  clearCreateListProposal: () => dispatch(clearCreateListProposal()),
});

export default connect(mapStateToProps, mapDispatchToProps)(injectIntl(List));
