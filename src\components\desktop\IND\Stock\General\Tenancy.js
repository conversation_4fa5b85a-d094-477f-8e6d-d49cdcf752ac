import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import FieldVal from "../../../../common/FieldVal";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import { convertCurrency, numberComma, getLangKey } from "../../../../../helper/generalHelper";
import TenancyRecordBox from "./TenancyRecordBox";
import { injectIntl } from "react-intl";

const styles = (theme) => ({
  root: {
    padding: "1vh 0",
  },
  gridContent: {
    padding: "1vw 2vw",
  },
  notFound: {
    padding: "1vw 2vw",
    borderRadius: 4,
    backgroundColor: "#F6F6F6",
  },
  contactHeader: {
    paddingTop: 8,
  },
});

class Tenancy extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langKey = getLangKey(intl);
    const titleLangKey = getLangKey(intl, "title");

    const rentFreeMapping = {
      "1": intl.formatMessage({ id: "stock.negotiable" }),
      "2": intl.formatMessage({ id: "stock.yes" }),
      "3": intl.formatMessage({ id: "stock.no" }),
    };

    const feePrefixMapping = {
      "Increment": intl.formatMessage({ id: "stock.tenancy.increment" }),
      "Market Rate": intl.formatMessage({ id: "stock.tenancy.marketrate" }),
      "Fixed": "",
    };

    const feeTypeMapping = {
      "Option": intl.formatMessage({ id: "stock.tenancy.option" }),
      "Fixed": intl.formatMessage({ id: "stock.tenancy.fixed" }),
      "Nego": intl.formatMessage({ id: "stock.tenancy.nego" }),
    };

    const mongoId = detail._id ? detail._id : null;
    const stockId =
      detail.unicorn && Number.isInteger(detail.unicorn.id)
        ? detail.unicorn.id
        : null;

    const tenancyData = detail.tenancy || {};
    const rentFree =
      tenancyData.rentFree && rentFreeMapping[tenancyData.rentFree]
        ? rentFreeMapping[tenancyData.rentFree]
        : "---";
    const freePeriodDay =
      tenancyData.freePeriod && tenancyData.freePeriod.day
        ? tenancyData.freePeriod.day + intl.formatMessage({ id: "stock.days" })
        : "";
    const freePeriodMonth =
      tenancyData.freePeriod && tenancyData.freePeriod.month
        ? tenancyData.freePeriod.month + intl.formatMessage({ id: "stock.months" })
        : "";
    const freePeriod =
      freePeriodDay || freePeriodMonth
        ? freePeriodMonth + " " + freePeriodDay
        : "---";
    const leasePeriodYear =
      tenancyData.leasePeriod && tenancyData.leasePeriod.year
        ? tenancyData.leasePeriod.year + intl.formatMessage({ id: "stock.years" })
        : "";
    const leasePeriodMonth =
      tenancyData.leasePeriod && tenancyData.leasePeriod.month
        ? tenancyData.leasePeriod.month + intl.formatMessage({ id: "stock.months" })
        : "";
    const leasePeriod =
      leasePeriodYear || leasePeriodMonth
        ? leasePeriodYear + " " + leasePeriodMonth
        : "---";
    const depositInMonth =
      tenancyData.deposit && tenancyData.deposit.month
      ? tenancyData.deposit.month + intl.formatMessage({ id: "stock.months" })
      : "---";
    let leaseOptions = tenancyData.leaseOptions || [];
    leaseOptions = leaseOptions.slice(0, 2).map(v => {
      let years = v.years ? v.years + intl.formatMessage({ id: "stock.years" }) : "";
      let months = v.months ? v.months + intl.formatMessage({ id: "stock.months" }) : "";
      return years || months ? years + " " + months : "---";
    });
    const prepay =
      tenancyData.prepay && tenancyData.prepay.month
        ? tenancyData.prepay.month+ intl.formatMessage({ id: "stock.months" })
        : "---";

    const tenancyRecords = detail.tenancyRecords ? detail.tenancyRecords : [];
    let currTenants = [];
    let prevTenants = [];

    for (let i = 0; i < tenancyRecords.length; i++) {
      if (tenancyRecords[i].deleted) continue;
      let tenant = tenancyRecords[i].tenant;
      if (!tenant) continue;

      const status = tenancyRecords[i].status
        ? tenancyRecords[i].status
        : "Former";
      let statustoDisplay;
      if (status === "Current")
        statustoDisplay = intl.formatMessage({
          id: "stock.currenttenancy",
        });
      if (status === "Former")
        statustoDisplay = intl.formatMessage({
          id: "stock.previoustenancy",
        });

      let companyEn = tenant.nameEn;
      let companyZh = tenant.nameZh;
      let companyId = tenant.companyId;
      let floor = tenancyRecords[i].floor || "";
      let unit = tenancyRecords[i].unit || "";
      let displayUnit = (floor + " " + unit).trim() || "---";

      let minDate =
        tenancyRecords[i].expiry && tenancyRecords[i].expiry.minDate
          ? tenancyRecords[i].expiry.minDate
          : "";
      let maxDate =
        tenancyRecords[i].expiry && tenancyRecords[i].expiry.maxDate
          ? tenancyRecords[i].expiry.maxDate
          : "";
      let date =
        minDate && maxDate ? minDate + " - " + maxDate : minDate || maxDate || "---";
      let rent =
        tenancyRecords[i].rent && tenancyRecords[i].rent.total
          ? "$" + numberComma(tenancyRecords[i].rent.total)
          : "---";
      let options = tenancyRecords[i].options || [];
      options = options.slice(0, 3).map(v => {
        let minDate = v.minDate || "";
        let maxDate = v.maxDate || "";
        let feePrefix = v.feePrefix && feePrefixMapping[v.feePrefix] ? " " + feePrefixMapping[v.feePrefix] : "";
        let feeType = v.feeType && feeTypeMapping[v.feeType] ? " (" + feeTypeMapping[v.feeType] + ")" : "";
        let amount = v.rent && v.rent.amount ? "$" + numberComma(v.rent.amount) : "";
        let rate = v.rent && v.rent.rate ? v.rent.rate + "%" : "";
        let rentDisplay = (amount || rate || feePrefix) ? (amount || rate) + feePrefix + feeType : "";
        rentDisplay = rentDisplay.trim() || "---";
        return {
          period: minDate && maxDate ? minDate + " - " + maxDate : minDate || maxDate || "---",
          rent: rentDisplay,
        };
      });
      let updateDate =
        tenancyRecords[i].recordOperation && tenancyRecords[i].recordOperation.lastUpdateDate
          ? tenancyRecords[i].recordOperation.lastUpdateDate
          : "---";

      let contactPersons = tenant.contactPerson;
      if (!tenant.contactPerson || tenant.contactPerson.length === 0)
        contactPersons = [{}];

      for (let j = 0; j < contactPersons.length; j++) {
        let contactName = contactPersons[j][langKey];
        let contactTitle = contactPersons[j][titleLangKey];

        let phones =
          contactPersons[j] && contactPersons[j].contacts
            ? contactPersons[j].contacts
            : [];

        let item = {
          status,
          tenantEn: companyEn,
          tenantZh: companyZh,
          tenantId: companyId,
          unit: displayUnit,
          tenancyPeriod: date,
          rent: rent,
          options,
          updateDate,
          contactName,
          contactTitle,
          phones,
          mongoId,
          stockId,
        };

        if (status === "Current") currTenants.push(item);
        if (status === "Former") prevTenants.push(item);
      }
    }

    if(currTenants.length === 0) currTenants = [{ status: "Current" }];
    if(prevTenants.length === 0) prevTenants = [{ status: "Former" }];

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.tenancy",
          })}
        >
          <DetailBoxSection noStrike={true}>
            <Grid container spacing={2} className={classes.gridContent}>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.rentfree",
                  })}
                >
                  {rentFree}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.freeperiod",
                  })}
                >
                  {freePeriod}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.leaseperiod",
                  })}
                >
                  {leasePeriod}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.deposit",
                  })}
                >
                  {depositInMonth}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.option",
                  }) + " (1)"}
                >
                  {leaseOptions[0] || "---"}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.option",
                  }) + " (2)"}
                >
                  {leaseOptions[1] || "---"}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.prepay",
                  })}
                >
                  {prepay}
                </FieldVal>
              </Grid>
            </Grid>
          </DetailBoxSection>

          <DetailBoxSection noStrike={true}>
            {currTenants.reverse().map((v, i) => (
              <TenancyRecordBox
                {...v}
                key={i}
              />
            ))}
          </DetailBoxSection>

          <DetailBoxSection noStrike={true}>
            {prevTenants.reverse().map((v, i) => (
              <TenancyRecordBox
                {...v}
                key={i}
              />
            ))}
          </DetailBoxSection>
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(Tenancy));
