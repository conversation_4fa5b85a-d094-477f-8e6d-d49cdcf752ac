import React, { useState, useEffect, useRef } from "react";
import { connect } from "react-redux";
import PropTypes from "prop-types";
import { createStyles, withStyles } from "@material-ui/core/styles";
import clsx from "clsx";
import { FormattedMessage, injectIntl } from "react-intl";
import _ from 'lodash'

import CallIcon from "./CallIcon";
import CallIconLong from "./CallIconLong";
import { takeLog, addActivityLog } from "../../actions/log";
import OneBtnDialog from "./OneBtnDialog";
import CustomizedDialog from "./Dialog";

// We can inject some CSS into the DOM.
const styles = createStyles({
  root: {},
  text: {
    minWidth: "30vw",
    fontSize: "1.125em",
    paddingLeft: "2vw",
    boxSizing: "border-box",
    flex: 1,
    textOverflow: "ellipsis",
    overflow: "hidden",
    whiteSpace: "nowrap",
  },
  link: {
    color: "inherit",
    textDecoration: "none",
    display: "flex",
    alignItems: "center",
  },
  numberType: {
    display: "inline",
    backgroundColor: "#8A8A8A",
    color: "#fff",
    padding: 5,
    borderRadius: 6,
  },
  deniedCall: {
    background: "red",
  },
  doNotContact: {
    background: "#FF9F23",
  },
  privacy: {
    background: "#FF3535",
  },
  font18: {
    fontSize: "1.125em",
    textAlign: "center",
  }
});

function phoneNumberFormat(number = "") {
  const regex = /\d+/g;
  const matches = number ? number.match(regex) : [];

  return matches.find(item => item.length === 8) || '';
}

function CallItem(props) {
  const {
    classes,
    type,
    number,
    takeLog,
    addActivityLog,
    stockId,
    mongoId,
    isDeniedCall,
    intl,
    phone,
    ...other
  } = props;
  const [opened, setOpened] = useState(false);
  const [dmoDialogOpen, setDmoDialogOpen] = useState(false);
  const [dncDialogOpen, setDncDialogOpen] = useState(false);
  const [numberIsError, setNumberIsError] = useState(false);

  useEffect(() => {
    if (opened && isDeniedCall) {
      setDmoDialogOpen(true);
    }
  }, [opened, isDeniedCall]);

  const numberTypeMapping = React.useMemo(() => Object.freeze({
    O: intl.formatMessage({ id: "stock.numbertype.office" }),
    D: intl.formatMessage({ id: "stock.numbertype.direct" }),
    R: intl.formatMessage({ id: "stock.numbertype.residential" }),
    M: intl.formatMessage({ id: "stock.numbertype.mobile" }),
    P: intl.formatMessage({ id: "stock.numbertype.pager" }),
    F: intl.formatMessage({ id: "stock.numbertype.fax" }),
    X: intl.formatMessage({ id: "stock.numbertype.x" }),
    "?": intl.formatMessage({ id: "stock.numbertype.unknown" }),
    "-": intl.formatMessage({ id: "stock.numbertype.na" }),
  }), [intl]);

  const numberType = React.useMemo(() => {
    if (phone?.doNotContact) {
      return intl.formatMessage({ id: "stock.numbertype.doNotContact" });
    } else if (phone?.privacy) {
      return intl.formatMessage({ id: "stock.numbertype.privacy" });
    }
    return type && numberTypeMapping[type]
      ? numberTypeMapping[type]
      : intl.formatMessage({ id: "stock.numbertype.na" });
  }, [type, phone]);

  const handleClick = React.useCallback((event) => {
    if(!phoneNumberFormat(number)) {
      event.preventDefault();
      setDmoDialogOpen(true)
      setNumberIsError(true)
      return
    }

    const msg = JSON.stringify({
      action: "phone call",
      number,
      stockId,
      mongoId,
    });
    takeLog(msg);
  }, [takeLog, number, stockId, mongoId]);

  const handleReveal = React.useCallback(() => {
    const msg = JSON.stringify({
      action: "phone reveal",
      number,
      stockId,
      mongoId,
    });
    takeLog(msg);
    addActivityLog("vendor_contact", "read", { ...JSON.parse(msg) });

    setOpened(true);
  }, [takeLog, addActivityLog, number, stockId, mongoId]);

  const iconClassName = React.useMemo(() => {
    return clsx({
      [classes.deniedCall]: isDeniedCall,
      [classes.doNotContact]: phone?.doNotContact,
      [classes.privacy]: phone?.privacy,
    });
  }, [phone, isDeniedCall]);

  React.useEffect(() => {
    if (opened && phone?.doNotContact) {
      setDncDialogOpen(true);
    }
  }, [phone?.doNotContact, opened]);

  const handleDncDialogClose = React.useCallback(() => {
    setDncDialogOpen(false);
  }, []);

  return (
    <div className={classes.root} {...other}>
      {opened ? (
        <a
          className={classes.link}
          href={phoneNumberFormat(number) ? `tel: ${phoneNumberFormat(number)}`: '#'}
          onClick={handleClick}
        >
          <CallIcon className={iconClassName} />
          <div className={classes.text} title={number}>
            {number}{" "}
            {type && (
              <div className={classes.numberType}>{type && ` ${type} `}</div>
            )}
          </div>
        </a>
      ) : (
        <CallIconLong
          className={clsx("ShowContact", iconClassName)}
          onClick={handleReveal}
        >
          {numberType}
        </CallIconLong>
      )}
      <CustomizedDialog
        fullWidth
        open={dmoDialogOpen}
        handleClose={() => {
          setDmoDialogOpen(false)
          _.delay(function() {
            setNumberIsError(false)
          }, 1000)
        }}
      >
        <div className={classes.font18}>
          {
            numberIsError
            ? ( '無效的電話號碼！')
            : ( <FormattedMessage id="stock.dmoMsg" />)
          }
        </div>
      </CustomizedDialog>
      <CustomizedDialog
        fullWidth
        open={dncDialogOpen}
        handleClose={handleDncDialogClose}
      >
        <div
          className={classes.font18}
          dangerouslySetInnerHTML={{
            __html: intl.formatMessage({ id: "stock.vendor.message.deniedCallBothHtml" }),
          }}
        />
      </CustomizedDialog>
    </div>
  );
}

CallItem.propTypes = {
  classes: PropTypes.object.isRequired,
  type: PropTypes.string,
  number: PropTypes.string,
  takeLog: PropTypes.func,
  stockId: PropTypes.number,
  mongoId: PropTypes.string,
  isDeniedCall: PropTypes.bool.isRequired,
  phone: PropTypes.object,
};

const mapDispatchToProps = (dispatch) => {
  return {
    takeLog: (...args) => dispatch(takeLog(...args)),
    addActivityLog: (...args) => dispatch(addActivityLog(...args)),
  };
};

export default connect(
  null,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(CallItem)));
