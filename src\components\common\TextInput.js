import React from "react";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import TextField from "@material-ui/core/TextField";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    width: "100%",
    "& .MuiFormLabel-asterisk": {
      color: "red",
    },
    "& .MuiFormHelperText-root": {
      marginTop: 0,
      color: "rgba(0,0,0,.8)",
    },
  },
};

function TextInput(props) {
  const {
    classes,
    className,
    label,
    placeholder,
    input,
    meta: { touched, invalid, error },
    showPlaceholder = true,
    ...custom
  } = props;

  return (
    <TextField
      className={clsx(classes.root, className)}
      label={label}
      placeholder={showPlaceholder ? placeholder || label : ""}
      error={touched && (invalid || error)}
      helperText={touched && error}
      InputLabelProps={{
        shrink: true,
      }}
      onChange={input.onChange}
      {...input}
      {...custom}
    />
  );
}

export default withStyles(styles)(TextInput);
