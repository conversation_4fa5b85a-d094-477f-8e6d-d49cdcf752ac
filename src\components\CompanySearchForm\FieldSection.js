import React from "react";
import { Grid } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import { Field, getFormValues } from "redux-form";
import _ from "lodash";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { injectIntl } from "react-intl";

import AutoCompleteSelect from "../common/AutoCompleteSelect";
import { queryCompanyName, updateCriteria } from "@/actions/company";

const useStyles = makeStyles({
  wrapper: {
    padding: "4px 8px",
  },
});

function FieldSection({
  companyNames,
  queryCompanyName,
  formValues,
  criteria,
  changeCriteria,
  intl,
}) {
  const classes = useStyles();

  // useEffect(() => {
  //   queryCompanyName();
  // }, []);

  console.log(
    "selected",
    _.filter(companyNames, (name) =>
      (_.get(formValues, "companyName") || []).includes(name.nameEn),
    ),
  );
  return (
    <Grid container direction="column" className={classes.wrapper}>
      <Grid item>
        <Field
          name="companyName"
          label={intl.formatMessage({ id: "company.search.companyName" })}
          component={AutoCompleteSelect}
          apiaction={queryCompanyName}
          optionsdata={companyNames}
          selectedData={_.get(criteria, "companyName")}
          setSelectedData={changeCriteria}
        />
      </Grid>
    </Grid>
  );
}

FieldSection.propTypes = {
  companyNames: PropTypes.array.isRequired,
  queryCompanyName: PropTypes.func.isRequired,
  criteria: PropTypes.object.isRequired,
  changeCriteria: PropTypes.func.isRequired,
  formValues: PropTypes.object.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapDispatchToProps = (dispatch) => ({
  queryCompanyName: (name) => dispatch(queryCompanyName(name)),
  changeCriteria: (key, value) => dispatch(updateCriteria(key, value)),
});

const mapStateToProps = (state) => ({
  companyNames: state.company.companyNames,

  criteria: state.company.criteria,
  formValues: getFormValues("companySearch")(state),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(FieldSection));
