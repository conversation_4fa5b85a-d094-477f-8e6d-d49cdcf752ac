/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import withStyles from "isomorphic-style-loader/lib/withStyles";
import BuildingDetail from "../../../../components/desktop/COMM/Building";
import s from "./Building.css";
import {
  listBuildingDetail,
  clearBuildingDetail
} from "../../../../actions/building";

class Building extends React.Component {
  static propTypes = {
    listBuildingDetail: PropTypes.func.isRequired,
    clearBuildingDetail: PropTypes.func.isRequired
  };

  constructor(props) {
    super(props);
  }

  componentDidMount() {
    const variables = {
      _id: this.props.buildingId.id
    };
    this.props.listBuildingDetail(variables);
  }

  componentWillUnmount() {
    this.props.clearBuildingDetail();
  }

  render() {
    return (
      <div className={s.root}>
        <div className={s.div}>
          <div>
            <BuildingDetail headerRef={this.props.headerRef} />
          </div>
        </div>
      </div>
    );
  }
}

const mapDispatchToProps = dispatch => {
  return {
    listBuildingDetail: (...args) => dispatch(listBuildingDetail(...args)),
    clearBuildingDetail: (...args) => dispatch(clearBuildingDetail(...args))
  };
};

export default connect(
  null,
  mapDispatchToProps
)(withStyles(s)(Building));
