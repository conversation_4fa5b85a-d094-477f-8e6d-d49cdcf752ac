import { combineReducers } from "redux";
import { reducer as formReducer } from "redux-form";

export default combineReducers({
  form: formReducer,
  header: require("./header").default,
  auth: require("./auth").default,
  building: require("./building").default,
  stock: require("./stock").default,
  district: require("./district").default,
  stocklist: require("./stocklist").default,
  street: require("./street").default,
  medium: require("./medium").default,
  transaction: require("./transaction").default,
  messageCenter: require("./messageCenter").default,
  employee: require("./employee").default,
  proposal: require("./proposal").default,
  listProposal: require("./listProposal").default,
  landsearch: require("./landsearch").default,
  company: require("./company").default,
  kolVideo: require("./kolVideo").default,
  listTxReport: require("./listTxReport").default,
  wwwStock: require("./wwwStock").default,
});
