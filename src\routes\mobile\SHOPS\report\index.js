/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import Layout from "../../../../components/Layout";
import { FormattedMessage } from "react-intl";
import ReportList from '@/components/ReportList';

const title = "Report";

async function action({ store, params, query }) {
  const { auth } = store.getState();
  if (!auth.user) {
    return { redirect: "/login" };
  } else if (auth.user.authorized == false) {
    return { redirect: "/login" };
  }

  return {
    chunks: ["report"],
    title,
    component: (
      <Layout
        header={<FormattedMessage id="report.report" />}
        hideSearchIcon={true}
      >
        <ReportList />
      </Layout>
    )
  };
}

export default action;
