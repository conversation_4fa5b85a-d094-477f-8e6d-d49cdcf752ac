import { sbu } from "../../config";

export default function createGetQuery() {
  // import query files from the corresponding SBU folder
  const promises = [
    import("./" + sbu + "/building"),
    import("./" + sbu + "/district"),
    import("./" + sbu + "/employee"),
    import("./" + sbu + "/messageCenter"),
    import("./" + sbu + "/stock"),
    import("./" + sbu + "/stocklist"),
    import("./" + sbu + "/street"),
    import("./" + sbu + "/transaction"),
    import("./" + sbu + "/landsearch"),
    import("./" + sbu + "/kolvideo"),
    import("./" + sbu + "/www"),
    import("./" + sbu + "/wwwStock"),
  ];

  const promise = Promise.all(promises);

  return async function getQuery(key) {
    let query = null;
    const modules = await promise;
    modules.forEach((v) => {
      if (v[key]) query = v[key];
    });
    return query;
  };
}
