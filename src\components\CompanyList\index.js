import React, { useMemo } from "react";
import PropTypes from "prop-types";
import { Box, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import InfiniteScroll from "react-infinite-scroll-component";
import { FormattedMessage } from "react-intl";
import { connect } from "react-redux";

import { sbu } from "@/config";
import COMMBg from "../../files/bg/comm_watermark_result_list.png";
import INDBg from "../../files/bg/ind_watermark_result_list.png";
import SHOPSBg from "../../files/bg/shop_watermark_result_list.png";
import { numberComma } from "@/helper/generalHelper";
import LoadingOverlay from "../LoadingOverlay";
import CompanyCard from "./CompanyCard";
import { updateQueryVariables } from "@/actions/company";

let bgImage;
switch (sbu) {
  case "COMM":
    bgImage = COMMBg;
    break;
  case "IND":
    bgImage = INDBg;
    break;
  case "SHOPS":
    bgImage = SHOPSBg;
    break;
  default:
    bgImage = null;
}

const useStyles = makeStyles({
  bg: {
    width: "100%",
    position: "fixed",
    top: "25%",
    zIndex: -1,
  },
  count: {
    fontSize: "0.95rem",
  },
});

function CompanyList({
  companies,
  companyCount,
  listingCompanies,
  listedCompanies,
  updateOffset,
}) {
  const classes = useStyles();

  return (
    <Box padding="3px 8px" marginTop="40px" overflow="auto">
      {bgImage && <img src={bgImage} className={classes.bg} alt="background" />}

      <div className={classes.count}>
        <FormattedMessage
          id="company.list.count"
          values={{ count: numberComma(companyCount) || "---" }}
        />
      </div>

      {listedCompanies && (
        <InfiniteScroll
          dataLength={companies.length}
          next={() => updateOffset(companies.length)}
          hasMore={companies.length < companyCount}
          endMessage={
            <Typography style={{ textAlign: "center", fontWeight: 500 }}>
              <FormattedMessage id="search.noresult" />
            </Typography>
          }
        >
          {companies.map((company) => (
            <CompanyCard key={company._id} company={company} />
          ))}
        </InfiniteScroll>
      )}

      {listingCompanies && <LoadingOverlay />}
    </Box>
  );
}

CompanyList.propTypes = {
  companies: PropTypes.array.isRequired,
  companyCount: PropTypes.number.isRequired,
  listingCompanies: PropTypes.bool.isRequired,
  listedCompanies: PropTypes.bool.isRequired,
  updateOffset: PropTypes.func.isRequired,
};

const mapDispatchToProps = (dispatch) => ({
  updateOffset: (offset) => dispatch(updateQueryVariables("offset", offset)),
});

const mapStateToProps = (state) => ({
  companies: state.company.companies,
  companyCount: state.company.companiesCount,
  listingCompanies: state.company.listingCompanies,
  listedCompanies: state.company.listedCompanies,
  listCompaniesError: state.company.listCompanyError,
});

export default connect(mapStateToProps, mapDispatchToProps)(CompanyList);
