export const LIST_TEAMS_KOL_VIDEO_QUERY = `
query($approval: String, $employees: [String]) {
  media(type: "kol_video", approval: $approval, employees: $employees) {
    id
    stockId
    filename
    originalFilename
    type
    status
    employeeId
    employee {
      cName
      eName
    }
    availableStartTime
    availableEndTime
    tags
    processing
    approval
    lastModified
    createdDate
    operator {
      id
      branchId
      eName
      email
    }
    ... on KolVideo {
     mediumRoot
     thumbnail
     description
     characteristicZh
     characteristicEn
     buildingName
     address
     propertyRefId
     vimeoId
    }
  }
}
`;

export const BATCH_REMOVE_KOL_VIDEO = `
mutation($ids: [ID], $bypassCode: String!) {
  batchRemoveMediumBypass(
    ids: $ids
    bypassCode: $bypassCode
  )
}
`;

export const BATCH_APPROVED_KOL_VIDEO = `
mutation($ids: [ID], $bypassCode: String!) {
  batchApprovedMediumBypass(
    ids: $ids
    bypassCode: $bypassCode
  )
}
`;