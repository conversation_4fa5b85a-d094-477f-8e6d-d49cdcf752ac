import React from "react";
import PropTypes from "prop-types";
import _ from "lodash";
import { MuiThemeProvider, createMuiTheme } from "@material-ui/core/styles";
import Tabs from "@material-ui/core/Tabs";
import SortingTab from "./SortingTab";

const theme = createMuiTheme({
  overrides: {
    MuiTabs: {
      root: {
        minHeight: 40,
      },
      indicator: {
        height: 0,
      },
    },
    MuiTab: {
      root: {
        minHeight: 40,
        lineHeight: 1.2,
        fontSize: "0.875em",
        textTransform: "none",
        backgroundColor: "rgba(0, 0, 0, .6)",
      },
      labelIcon: {
        minHeight: 40,
        paddingTop: 6,
      },
      textColorInherit: {
        color: "#FFF",
        opacity: 1,
      },
      wrapper: {
        whiteSpace: "nowrap",
        flexDirection: "row",
        "&& > *:first-child": {
          marginBottom: 0,
        },
      },
    },
  },
});

function a11yProps(index) {
  return {
    id: `full-width-tab-${index}`,
    "aria-controls": `full-width-tabpanel-${index}`,
  };
}

class SortingBar extends React.Component {
  static propTypes = {
    className: PropTypes.string,
    options: PropTypes.array.isRequired,
    queryvariables: PropTypes.object,
    updateQuery: PropTypes.func,
    fetchData: PropTypes.func,
  };

  handleChange = (event, value) => {
    // we use the click event of the tab, so there is no need to handle onChange
  };

  handleClick = (fieldName) => () => {
    let { queryvariables, fetchData, updateQuery } = this.props;
    let clone = JSON.parse(JSON.stringify(queryvariables || {}));
    let sorter = clone && clone.sorter;
    clone.offset = 0;
    let newSorter = [];
    if (typeof fieldName === "object") {
      if (_.isEqual(sorter, fieldName.asc)) newSorter = fieldName.desc;
      else newSorter = fieldName.asc;
    } else {
      if (false) { //可以指定是否支持多排序
        newSorter = _.clone(sorter);
        if (!newSorter) {
          newSorter = [];
        }
        const existingIndex = _.findIndex(newSorter, (element) => element.field === fieldName);
        if (existingIndex !== -1) {
          if (newSorter[existingIndex].order.toUpperCase() === "ASC") {
            newSorter[existingIndex].order = "DESC";
          } else {
            newSorter.splice(existingIndex, 1);
          }
        } else {
          newSorter.unshift({ field: fieldName, order: "ASC" });
        }
      } else {
        if (
          sorter &&
          sorter.length === 1 &&
          sorter[0].field === fieldName &&
          sorter[0].order.toUpperCase() === "ASC"
        ) {
          newSorter.push({ field: fieldName, order: "DESC" });
        } else {
          newSorter.push({ field: fieldName, order: "ASC" });
        }
      }
    }
    clone.sorter = newSorter;
    if (fetchData) fetchData(clone);
    if (updateQuery) updateQuery(clone);
  };

  render() {
    const { options, queryvariables, className } = this.props;
    let sorter = queryvariables && queryvariables.sorter;

    return (
      <div className={className}>
        <MuiThemeProvider theme={theme}>
          <Tabs
            value={0}
            onChange={this.handleChange}
            variant="fullWidth"
            centered
          >
            {options.map((v, i) => (
              <SortingTab
                label={v.label}
                key={i}
                onClick={this.handleClick(v.value)}
                field={v.value}
                order={
                  typeof v.value === "object"
                    ? _.isEqual(sorter, v.value.asc)
                      ? "ASC"
                      : _.isEqual(sorter, v.value.desc)
                      ? "DESC"
                      : null
                    : _.find(sorter, (element) =>element.field === v.value)
                    ? _.find(sorter, (element) =>element.field === v.value).order.toUpperCase()
                    : null
                }
                {...a11yProps(i)}
              />
            ))}
          </Tabs>
        </MuiThemeProvider>
      </div>
    );
  }
}

export default SortingBar;
