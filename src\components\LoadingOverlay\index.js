import React from "react";
import { withStyles } from "@material-ui/styles";
import CircularProgress from "@material-ui/core/CircularProgress";

const styles = theme => ({
  loading: {
    width: "100%",
    height: "100%",
    top: 0,
    left: 0,
    position: "fixed",
    display: "block",
    opacity: 0.9,
    backgroundColor: "#fff",
    zIndex: 3000,
    textAlign: "center"
  },

  loadingicon: {
    margin: "36vh auto",
    zIndex: 4000,
    opacity: 1
  }
});

function LoadingOverlay(props) {
  const { classes } = props;
  return (
    <div className={classes.loading}>
      <CircularProgress size={80} className={classes.loadingicon} />
    </div>
  );
}

export default withStyles(styles)(LoadingOverlay);
