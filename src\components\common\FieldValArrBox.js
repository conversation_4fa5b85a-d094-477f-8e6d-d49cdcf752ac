import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import FieldVal from "./FieldVal";
import Grid from "@material-ui/core/Grid";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    padding: "1vh 2vw",
    borderRadius: "4px",
    backgroundColor: "rgba(132, 132, 132, .1)",
    "&:not(:last-child)": {
      marginBottom: "1vh"
    }
  },
};

function FieldValArrBox(props) {
  const {
    classes,
    className,
    spacing = 1,
    items,
    ...others
  } = props;

  return (
    <div className={`${classes.root} ${className}`} {...others}>
      <Grid container spacing={spacing}>
        {items.map((v, i) => (
          <Grid item xs={v.xs || 12} key={i} className={v.className}>
            <FieldVal field={v.field}>{v.val}</FieldVal>
            {v.extra}
          </Grid>
        ))}
      </Grid>
    </div>
  );
}

FieldValArrBox.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  spacing: PropTypes.number,
  items: PropTypes.arrayOf(PropTypes.object).isRequired,
};

export default withStyles(styles)(FieldValArrBox);
