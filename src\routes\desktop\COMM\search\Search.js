/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { submit, getFormValues } from "redux-form";
import withStyles from "isomorphic-style-loader/lib/withStyles";
import _ from "lodash";
import querystring from "querystring";
import { FormattedMessage, injectIntl } from "react-intl";
import TextField from "@material-ui/core/TextField";
import s from "./Search.css";
import { listUnitViews } from "../../../../actions/building";
import {
  listDecorations,
  getHistoryField,
} from "../../../../actions/stocklist";
import { listDistricts } from "../../../../actions/district";
import { listmyFavorite } from "../../../../actions/stock";
import {
  saveSearch,
  clearSaveSearch,
  listSearch,
} from "../../../../actions/stocklist";
import SearchForm from "../../../../components/desktop/COMM/Search/SearchForm";
import StockList from "../../../../components/desktop/COMM/StockList";
import Layout from "../../../../components/Layout";
import { getSearchQueryString, goToSearchResult } from "../../../../helper/generalHelper";
import SubmitDialog from "../../../../components/common/SubmitDialog";
import SelectField from "../../../../components/common/SelectField";

class Search extends React.Component {
  static propTypes = {
    listDistricts: PropTypes.func.isRequired,
    listDecorations: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      offset: 0,
      selectedData: this.props.selectedData || {},
      expanded: false,
      isFirstFetch: true,
      appIsMounted: false,
      init: null,
      saveDialogOpen: false,
      searchCriteriaName: "",
      loadDialogOpen: false,
      loadedSearch: "",
    };
  }

  submit = (values) => {
    this.setState({ expanded: false });
    values.limit = 50;
    values.offset = 0;
    values = _.pickBy(values, (v) => v !== "");

    goToSearchResult(values, this.state.selectedData, true);
  };

  setSelectedData = (field, selectedData) => {
    this.setState({
      selectedData: {
        ...this.state.selectedData,
        [field]: selectedData ? selectedData : null,
      },
    });
  };

  clearSelectedData = () => {
    this.setState({ selectedData: {} });
  };

  componentDidMount() {
    requestAnimationFrame(() => {
      this.setState({ appIsMounted: true });
    });
    this.props.getHistoryField();
    this.props.listmyFavorite();
    if (!(this.props.districts && this.props.districts.length > 0)) {
      // districts is null
      this.props.listDistricts();
    }
    if (!(this.props.decoration && this.props.decoration.length > 0)) {
      // decoration is null
      this.props.listDecorations();
    }
    if (!(this.props.unitview && this.props.unitview.length > 0)) {
      // unitview is null
      this.props.listUnitViews();
    }
    if (!(this.props.searches && this.props.searches.length > 0)) {
      // searches is null
      this.props.listSearch();
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.selectedData !== this.props.selectedData) {
      this.setState({
        selectedData: this.props.selectedData,
        isFirstFetch: true,
      });
    }
  }

  toggleContent = () => {
    this.setState({ expanded: !this.state.expanded, isFirstFetch: false });

    // go to back last query when the form is collapsed
    if (this.state.expanded == true) {
      this.setState({
        selectedData: this.props.selectedData,
      });
      this.state.init(this.props.queryvariablesFromUrl);

      this.setState({ searchCriteriaName: "", loadedSearch: "" });
    }
    // window.scrollTo(0, 0);
  };

  onFormInit = (init) => {
    !this.state.init && this.setState({ init });
  };

  handleOpenSaveDialog = () => {
    this.setState({ saveDialogOpen: true });
  };

  handleCloseSaveDialog = () => {
    this.setState({ saveDialogOpen: false });
    this.props.clearSaveSearch();
  };

  handleNameChange = (e) => {
    this.setState({ searchCriteriaName: e.target.value });
  };

  submitSaveDialog = async () => {
    const query = getSearchQueryString(this.props.formFields, this.state.selectedData);
    // console.log(querystring.parse(query));

    // await saveSearch to finish and the reducer will update searches list automatically
    await this.props.saveSearch({
      name: this.state.searchCriteriaName,
      query,
    });

    // searches list has already been updated here. we can find the upserted/modified item
    const searchItem = this.props.searches.filter(v => v.name === this.state.searchCriteriaName)[0];
    this.setState({ loadedSearch: searchItem._id, searchCriteriaName: "" });
  };

  handleOpenLoadDialog = () => {
    this.setState({ loadDialogOpen: true });
  };

  handleCloseLoadDialog = () => {
    this.setState({ loadDialogOpen: false });
  };

  handleLoadChange = (v) => {
    this.setState({ loadedSearch: v.target.value });
  };

  submitLoadDialog = () => {
    const searchItem = this.props.searches.filter(v => v._id === this.state.loadedSearch)[0];

    const query = querystring.parse(searchItem.query);
    const queryvariables = JSON.parse(query.param);
    const selectedData = JSON.parse(query.selectedData);

    this.setState({ selectedData: selectedData });
    this.state.init(queryvariables);

    this.setState({ searchCriteriaName: searchItem.name });
    this.handleCloseLoadDialog();
  };

  render() {
    const {
      queryvariablesFromUrl,
      isAdvanced,
      selectedData: selectedDataFromUrl,
      savingSearch,
      savedSearch,
      saveSearchError,
      searches,
      upsertedSearch,
      intl,
    } = this.props;
    const {
      selectedData,
      saveDialogOpen,
      searchCriteriaName,
      loadDialogOpen,
      loadedSearch,
    } = this.state;

    const loadedSearchOptions = searches.map(v => {
      return {
        value: v._id,
        label: v.name,
      }
    });

    return (
      <div>
        {this.state.appIsMounted && (
          <Layout
            path="search"
            header={<FormattedMessage id="home.search" />}
            isAdvanced={true}
            toggleContent={this.toggleContent}
            isExpanded={this.state.expanded}
            isSticky={true}
          >
            <div className={this.state.expanded ? s.root : s.expandedroot}>
              {/*<div className={s.debug}>{JSON.stringify(selectedData)}</div>*/}
              <SearchForm
                onSubmit={this.submit}
                selectedData={selectedData}
                setSelectedData={this.setSelectedData}
                clearSelectedData={this.clearSelectedData}
                initialValues={queryvariablesFromUrl}
                expanded={this.state.expanded}
                fromChildToParentCallback={this.onFormInit}
                handleSaveClick={this.handleOpenSaveDialog}
                handleLoadClick={this.handleOpenLoadDialog}
              />
            </div>

            {/* {!this.state.expanded && (
              <StockList
                queryvariablesFromUrl={queryvariablesFromUrl}
                selectedDataFromUrl={selectedDataFromUrl}
                isAdvanced={isAdvanced}
                expanded={this.state.expanded}
                isFirstFetch={this.state.isFirstFetch}
              />
            )} */}

            <SubmitDialog
              dialogOpen={saveDialogOpen}
              handleCloseDialog={this.handleCloseSaveDialog}
              submitting={savingSearch}
              submitted={savedSearch}
              error={saveSearchError}
              submit={this.submitSaveDialog}
              submitBtnText={intl.formatMessage({ id: "search.button.save" })}
              succMsg={
                upsertedSearch
                  ? intl.formatMessage({ id: "search.save.savesuccess" })
                  : intl.formatMessage({ id: "search.save.modifysuccess" })
              }
            >
              <TextField
                label={intl.formatMessage({ id: "search.save.name" })}
                value={searchCriteriaName}
                onChange={this.handleNameChange}
                variant="outlined"
                fullWidth
              />
            </SubmitDialog>

            <SubmitDialog
              dialogOpen={loadDialogOpen}
              handleCloseDialog={this.handleCloseLoadDialog}
              submitting={false}
              submitted={false}
              error={""}
              submit={this.submitLoadDialog}
              submitBtnText={intl.formatMessage({ id: "search.button.load" })}
            >
              <SelectField
                label={intl.formatMessage({ id: "search.save.name" })}
                ranges={loadedSearchOptions}
                input={{
                  value: loadedSearch,
                  onChange: this.handleLoadChange,
                }}
                meta={{}}
                variant="outlined"
                fullWidth
              />
            </SubmitDialog>
          </Layout>
        )}
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  queryvariables: state.stocklist.queryvariables
    ? state.stocklist.queryvariables
    : {},
  districts: state.district.districts ? state.district.districts : [],
  decoration: state.stocklist.decoration ? state.stocklist.decoration : [],
  unitview: state.building.unitview ? state.building.unitview : [],
  savingSearch: state.stocklist.savingSearch ? state.stocklist.savingSearch : false,
  savedSearch: state.stocklist.savedSearch ? state.stocklist.savedSearch : false,
  saveSearchError: state.stocklist.saveSearchError ? state.stocklist.saveSearchError : null,
  searches: state.stocklist.searches ? state.stocklist.searches : [],
  upsertedSearch: state.stocklist.upsertedSearch ? state.stocklist.upsertedSearch : false,
  formFields: getFormValues('searchForm')(state),
});

const mapDispatchToProps = (dispatch) => {
  return {
    dispatchSubmitForm: () => dispatch(submit("searchForm")),
    listDistricts: () => dispatch(listDistricts()),
    listDecorations: () => dispatch(listDecorations()),
    listUnitViews: () => dispatch(listUnitViews()),
    getHistoryField: () => dispatch(getHistoryField()),
    listmyFavorite: () => dispatch(listmyFavorite()),
    saveSearch: (...args) => dispatch(saveSearch(...args)),
    clearSaveSearch: () => dispatch(clearSaveSearch()),
    listSearch: (...args) => dispatch(listSearch(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(s)(injectIntl(Search)));
