import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import EXIF from 'exif-js';


// We can inject some CSS into the DOM.
const styles = {
  hide: {
    display: "none"
  }
};

// ref: https://www.npmjs.com/package/exif2css
const exifOrientationCss = [
  {},
  { transform: "rotateY(180deg)" },
  { transform: "rotate(180deg)" },
  { transform: "rotate(180deg) rotateY(180deg)" },
  { transform: "rotate(270deg) rotateY(180deg)", transformOrigin: "top left" },
  { transform: "translateY(-100%) rotate(90deg)", transformOrigin: "bottom left" },
  { transform: "translateY(-100%) translateX(-100%) rotate(90deg) rotateY(180deg)", transformOrigin: "bottom right" },
  { transform: "translateX(-100%) rotate(270deg)", transformOrigin: "top right" },
];

function ExifOrientationFix(props) {
  const { classes, children, src, ...other } = props;
  const [ css, setCss ] = React.useState({});

  const showMeta = (e) => {
    let imageElement = e.target;
    // ref: https://github.com/exif-js/exif-js/issues/143
    imageElement.exifdata = null;

    // Fix for an issue affecting exif-js: see https://github.com/exif-js/exif-js/issues/95
    const windowImage = window.Image;
    window.Image = null;

    try {
      // ref: https://github.com/exif-js/exif-js
      if (!EXIF.getData(imageElement, function() {
        let orientation = EXIF.getTag(this, 'Orientation');
        // console.log("EXIF: " + orientation);
        // console.log(exifOrientationCss[orientation - 1]);
        if (!isNaN(parseInt(orientation)) && orientation >= 1 && orientation <= 8)
          setCss(exifOrientationCss[orientation - 1]);
        else
          setCss({});
      })) {
        console.log("NO EXIF!!!!!");
      }
    } catch (e) {
      console.log("EXIF ERROR:");
      console.log(e);
    }

    // Re-establish the reference
    window.Image = windowImage;
  };

  return (
    <>
      {children(css)}
      <img alt="" className={classes.hide} src={src} onLoad={showMeta} />
    </>
  );
}

ExifOrientationFix.propTypes = {
  children: PropTypes.func,
  classes: PropTypes.object.isRequired,
  src: PropTypes.string.isRequired,
};

export default withStyles(styles)(ExifOrientationFix);
