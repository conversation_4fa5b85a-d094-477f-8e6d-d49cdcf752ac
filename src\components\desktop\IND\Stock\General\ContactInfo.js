import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import ContactInfoBox from "./ContactInfoBox";
import { injectIntl } from "react-intl";
import { getLangKey } from "../../../../../helper/generalHelper";

const styles = theme => ({
  root: {
    padding: "1vh 0"
  },
  notFound: {}
});

class ContactInfo extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, hand, intl } = this.props;
    const langKey = getLangKey(intl);
    const langKeyTitle = getLangKey(intl, "title");
    const langKeyCompany = getLangKey(intl, "company");

    const mongoId = detail._id ? detail._id : null;
    const stockId =
      detail.unicorn && Number.isInteger(detail.unicorn.id)
        ? detail.unicorn.id
        : null;

    const people = detail.contactPerson || [];
    let contactInfo = [];
    for (let i = 0; i < people.length; i++) {
      if (!people[i]) continue;
      if (people[i].hands !== hand) continue;
      let companyEn = people[i].companyEn;
      let companyZh = people[i].companyZh;
      let companyId = people[i].companyId;

      let remarks = people[i] && people[i].remarks ? people[i].remarks : "---";
      let contactName = people[i][langKey];
      let contactTitle = people[i][langKeyTitle];
      let phones = people[i] && people[i].contacts ? people[i].contacts : [];

      contactInfo.push({
        companyEn,
        companyZh,
        companyId,
        remarks,
        contactName,
        contactTitle,
        phones,
        mongoId,
        stockId,
      });
    }
    if (contactInfo.length === 0) contactInfo = [{}];

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.contact"
          })}
        >
          {contactInfo.map((v, i) => (
            <ContactInfoBox
              {...v}
              key={i}
            />
          ))}
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(ContactInfo));
