import React, { useEffect, useMemo } from "react";
import { Field, formValueSelector, reduxForm } from "redux-form";
import _ from "lodash";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/core/styles";
import { connect } from "react-redux";
import { Grid } from "@material-ui/core";
import { injectIntl } from "react-intl";

import ApplicantDetail from "./FormSection/ApplicantDetail";
import UnderlineSingleSelect from "../common/UnderlineSingleSelect";
import { LANDSEARCH_TYPES } from "../../constants/landsearch";
import LandSearch from "./FormSection/LandSearch";
import OccupationPermit from "./FormSection/OccupationPermit";
import Memorial from "./FormSection/Memorial";
import GovernmentLease from "./FormSection/GovernmentLease";
import Condition from "./FormSection/Condition";
import { applySearchDetail } from "@/actions/landsearch";
import { getFullAddress } from "../Saleskit/helpers";
import { sbu } from "@/config";
import { addActivityLog } from "@/actions/log";

const useStyles = makeStyles({
  form: {
    // apply to all inputs in the form
    "& .MuiFormLabel-root": {
      color: "rgba(0, 0, 0, 0.9)",
      fontSize: 17,
    },
    "& .MuiInputBase-input": {
      padding: "5px 0px !important",
    },
    "& .MuiInput-underline:after": {
      borderBottom: "2px solid #000000",
    },
  },
  section: {
    margin: "5px 0",
  },
});

const parseForm = (form, stock, employee, userId, intl) => {
  const { searchType } = form;
  const clonedForm = { ...form, stockId: _.get(stock, "_id") };

  if (searchType === "O1") {
    clonedForm.memorialNumber = `${clonedForm.memKey.replaceAll('---', '')}${clonedForm.memNo}`;
    clonedForm.memorialType = `${clonedForm.memKey.replaceAll('---', '')}`;
    clonedForm.memorialId = `${clonedForm.memNo}`;

    _.unset(clonedForm, "memKey");
    _.unset(clonedForm, "memNo");
  }

  if (searchType === "CS") {
    clonedForm.conditionId = `${clonedForm.conditionKey}${clonedForm.conditionValue}`;
    clonedForm.memorialType = `${clonedForm.conditionKey}`;
    clonedForm.memorialId = `${clonedForm.conditionValue}`;

    _.unset(clonedForm, "conditionKey");
    _.unset(clonedForm, "conditionValue");
  }

  return {
    ...clonedForm,
    team: _.get(employee, "dept_code"),
    dept_code: _.get(employee, "dept_code"),
    applicantId: _.get(employee, "emp_id"),
    employeeId: _.get(employee, "emp_id"),
    user_id: userId,
    ...(searchType !== "O1" && searchType !== "O2"
      ? { address: getFullAddress({ ...intl, locale: "en" }, stock) }
      : {}),
  };
};

const validate = (values) => {
  const errors = {};
  const { searchType } = values;

  const mandatoryField = { categoryMsgId: "common.inputs.cannotBeEmpty" };

  if (!values.contactPhone)
    errors.contactPhone = {
      ...mandatoryField,
      fieldMsgId: "stock.applySearch.contactPhone",
    };

  if (searchType === "LD" || searchType === "OP") {
    if (sbu === "SHOPS") {
      if (!values.shopNumber)
        errors.shopNumber = {
          ...mandatoryField,
          fieldMsgId: "stock.applySearch.shopno",
        };
    } else if (!values.unit) {
      errors.unit = {
        ...mandatoryField,
        fieldMsgId: "stock.applySearch.unit",
      };
    }

    if (!values.floor)
      errors.floor = {
        ...mandatoryField,
        fieldMsgId: "stock.applySearch.floor",
      };
  }

  if (searchType === "CS") {
    if (!values.conditionKey || !values.conditionValue)
      errors.conditionKey = {
        ...mandatoryField,
        fieldMsgId: "stock.applySearch.conditionalNo",
      };
  }

  if (searchType === "O1") {
    // if (!values.memKey || !values.memNo)
    if (!values.memNo)
      errors.memKey = {
        ...mandatoryField,
        fieldMsgId: "stock.applySearch.memorialNo",
      };
  }

  if (searchType === "O2") {
    if (!values.ddType)
      errors.ddType = {
        ...mandatoryField,
        fieldMsgId: "stock.applySearch.ddType",
      };
    if (!values.ddNumber)
      errors.ddNumber = {
        ...mandatoryField,
        fieldMsgId: "stock.applySearch.ddNumber",
      };
  }

  return errors;
};

let ApplySearchForm = ({
  employee,
  stock,
  initialize,
  searchType,
  initialized,
  dirty,
  contactPhone,
  intl,
}) => {
  const classes = useStyles();

  useEffect(() => {
    const address = getFullAddress({ ...intl, locale: "en" }, stock);
    if (!initialized) {
      initialize({
        contactPhone: _.get(employee, "phone") || "",
        searchType: "LD",

        address,
        landSearch: "Current",
        item: "Unit",
        [sbu === "SHOPS" ? "shopNumber" : "unit"]: _.get(stock, "unit"),
        floor: _.get(stock, "floor"),
      });
    } else if (dirty) {
      initialize({
        contactPhone: contactPhone || _.get(employee, "phone") || "",
        searchType,

        ...(searchType === "LD"
          ? {
              landSearch: "Current",
              item: "Unit",
              floor: _.get(stock, "floor"),
              [sbu === "SHOPS" ? "shopNumber" : "unit"]: _.get(stock, "unit"),
            }
          : {}),

        ...(searchType === "OP"
          ? {
              occupationPermitType: "Rating and Valuation Department",
              floor: _.get(stock, "floor"),
              [sbu === "SHOPS" ? "shopNumber" : "unit"]: _.get(stock, "unit"),
            }
          : {}),

        ...(searchType === "O1"
          ? {
              memKey: "---",
              memNo: "",
              filmType: "Full Set Form",
            }
          : {}),

        ...(searchType === "O2"
          ? {
              item: "Government Lease",
              ddType: "",
              ddNumber: "",
              filmType: "Full Set Form",
            }
          : {}),

        ...(searchType === "CS"
          ? {
              conditionKey: "",
              conditionValue: "",
              filmType: "Full Set Form",
            }
          : {}),
      });
    }
  }, [searchType]);

  const searchTypes = useMemo(
    () =>
      LANDSEARCH_TYPES.map((type) => ({
        value: type.value,
        label: type[intl.locale],
      })),
    [intl.locale],
  );

  return (
    initialized && (
      <form className={classes.form}>
        <Grid container direction="column">
          <ApplicantDetail />

          <Grid item xs={12} className={classes.section}>
            <Field
              name="searchType"
              label={intl.formatMessage({ id: "stock.applySearch.searchType" })}
              component={UnderlineSingleSelect}
              type="select-multiple"
              options={searchTypes}
            />
          </Grid>

          {searchType === "LD" && <LandSearch />}
          {searchType === "OP" && <OccupationPermit />}
          {searchType === "O1" && <Memorial />}
          {searchType === "O2" && <GovernmentLease />}
          {searchType === "CS" && <Condition />}
        </Grid>
      </form>
    )
  );
};

ApplySearchForm = reduxForm({
  form: "applySearch",
  enableReinitialize: true,
  keepDirtyOnReinitialize: true,
  validate,

  onSubmit: (value, dispatch, props) => {
    const formData = parseForm(value, props.stock, props.employee, props.userId, props.intl);

    dispatch(addActivityLog("property.stock.search.land.applySearch", "create", formData));
    dispatch(
      applySearchDetail(
        formData,
      ),
    );
  },
})(ApplySearchForm);

ApplySearchForm.propTypes = {
  employee: PropTypes.object.isRequired,
  stock: PropTypes.object.isRequired,

  // from redux-form
  initialize: PropTypes.func,
  initialized: PropTypes.bool,
  dirty: PropTypes.bool,

  // from form state
  searchType: PropTypes.string,
  contactPhone: PropTypes.string,
  intl: PropTypes.object.isRequired,
};

const formSelector = formValueSelector("applySearch");

const mapStateToProps = (state) => ({
  employee: _.get(state, "employee.employees.0"),
  userId: _.get(state, "auth.user.login.info.user_id"),
  stock: _.get(state, "stock.detail.0"),

  searchType: formSelector(state, "searchType"),
  contactPhone: formSelector(state, "contactPhone"),
});

export default connect(mapStateToProps)(injectIntl(ApplySearchForm));
