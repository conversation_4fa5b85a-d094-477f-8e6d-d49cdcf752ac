import React from "react";
import _ from "lodash";
import { Chip, Avatar, TextField } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import { injectIntl } from "react-intl";
import { Phone } from "@material-ui/icons";
import clsx from 'clsx'

const useStyles = makeStyles(theme => ({
  title: {
    marginTop: "-10px",
    color: "#555555",
    fontSize: "13px",
    fontWeight: "400",
  },
  common: {
    fontSize: "17px",
    height: "25px",
    minWidth: "144px",
    borderRadius: "14px",
    "& span": { pointerEvents: "none" },
    "&.Mui-disabled": {
      opacity: "1",
    },
    "& > .MuiChip-label": {
      display: "block",
      flex: "1",
      textAlign: "center",
    },
  },
  notSelected: {
    backgroundColor: "rgba(85, 243, 112)",
    color: "#FFFFFF",
    "& .MuiChip-icon": {
      color: "#FFFFFF",
    },
    "& > .MuiChip-label": {
      padding: "0 18px 0 0",
    },
  },
  selected: {
    backgroundColor: "rgba(85, 243, 112)",
    flexFlow: "row-reverse",
    "& > *": {
      margin: theme.spacing(0.1),
    },
    "& > .MuiChip-label": {
      padding: "0 0 0 18px",
    },
    "& >.MuiChip-avatarSmall": {
      margin: "0px",
      color: "rgba(0, 0, 0, 0.87)",
      backgroundColor: "transparent",
      fontSize: "17px"
    },
  },
  doNotContactNotSelected: {
    backgroundColor: "#FFF7DA",
    color: "#EC8600",
    "& .MuiChip-icon": {
      color: "#EC8600"
    }
  },
  doNotContactSelected: {
    backgroundColor: "#FFF7DA",
  },
  privacyNotSelected: {
    backgroundColor: "#FFECEC",
    color: "#F90000",
    "& .MuiChip-icon": {
      color: "#F90000"
    }
  },
  privacySelected: {
    backgroundColor: "#FFECEC"
  },
  input: {
    "& label": {
      width: "100%",
      fontSize: "13px",
      color: "#555555!important",
      fontWeight: "400",
      transform: "translate(0, 1.5px) scale(1)",
    },

    "& div >.MuiInputBase-input": {
      color: "#000000",
    },

    "& div >.MuiInputBase-input::placeholder": {
      opacity: 1,
    },

    "& > div": {
      height: "25px",
      minHeight: "24px",
      marginTop: "0px",
      borderBottomColor: "#000000",
      borderBottomWidth: "1px",
      borderBottomStyle: "solid",
      backgroundColor: "#ffffff",
    },

    "& > div > div": {
      padding: "0px",
      margin: "0px",
      backgroundColor: "#ffffff",
    },

    "& > div > div > span": {
      width: "0px",
    },

    "& > div > div > div": {
      padding: "0px",
      paddingLeft: "5px",
      fontSize: "15px",
      marginLeft: "0px",
      backgroundColor: "#ffffff",
      color: "#000000",
    },

    // Mandatory field marker
    "& span.MuiFormLabel-asterisk.MuiInputLabel-asterisk": {
      color: "red",
    },

    "& .MuiInput-underline": {
      borderBottomColor: "#000000",
      borderBottomWidth: "1px",
      // marginTop: "16px",
      // marginBottom: "10px",
    },

    "& .MuiInput-underline.Mui-disabled:before": {
      borderBottomColor: "#000000",
      borderBottomWidth: "0px",
      borderBottomStyle: "solid",
    },

    "& .MuiInput-underline:before": {
      borderBottomColor: "#000000",
      borderBottomWidth: "0px",
      top: "0",
      height: "24px",
      borderBottomStyle: "solid",
    },

    "& .MuiInput-underline:hover:not(.Mui-disabled):before": {
      borderBottomColor: "#000000",
      borderBottomWidth: "0px",
    },

    "& .MuiFormLabel-root.Mui-focused": {
      fontColor: "#000000",
    },

    "& .MuiInput-underline:after": {
      borderBottomColor: "#000000",
      borderBottomWidth: "0px",
      borderBottomStyle: "solid",
      top: "0",
      height: "24px",
    },

    width: "100%",
    "& .MuiInputBase-input": {
      fontSize: "15px",
      background: "#FFFFFF",
      // paddingLeft: "5px",
      paddingRight: "0px",
      paddingTop: "0px",
      paddingBottom: "0px",
      marginBottom: "0px",
    },
  },
  transparentbgcolor: {
    "& > div": {
      backgroundColor: "transparent",
    },

    "& > div > .MuiInputBase-input": {
      backgroundColor: "transparent",
    }
  }
  // icon:{
  //   // height:"18px",
  //   // width:"18px",
  //   backgroundPosition: "100px 5px",
  //   background: "url('http://') no-repeat",
  //   backgroundSize: "18px 18px"
  //   // color:"#FFFFFF",
  //   // position:"fixed",
  //   // margin:"3px 9px",
  //   // "& > svg":{
  //   //   height:"18px",
  //   //   width:"18px"
  //   // }
  // }
}));

const typeToSymbol = type => (type ? type.charAt(0) : "");

/*
privacy         boolean         control the style and the logic of the button
                true

phoneNum        string          define the phone number of the chip button
                "23382338"

phoneType       string          define the phone type of the chip button
                "Office"

onClick         func            callback fired when the component is clicked
                (event) => {
                  // handle the logic after clicked
                }

editable        boolean         define if value inside chip button can be edit
                true | false
*/

export default React.memo(
  injectIntl(props => {
    // define the props value here
    const {
      disabled,
      privacy,
      allowViewPrivacy,
      phoneNum,
      phoneType,
      onClick,
      onBlur,
      title,
      intl,
      editable,
      revealed,
      doNotContact,
      innerRef,
      transparentbgcolor
    } = props;
    const classes = useStyles();
    const [selected, setSelected] = React.useState(false);
    const [phoneNumInput, setPhoneNumInput] = React.useState("");

    let phoneLabel;

    if (privacy) {
      phoneLabel = intl.formatMessage({
        id: "company.detail.phoneLabel.privacy",
      });
    } else if (doNotContact && intl.locale === "en") {
      phoneLabel = intl.formatMessage({
        id: "company.detail.phoneLabel.doNotContact",
      });
    } else if (doNotContact && intl.locale === "zh") {
      phoneLabel = `${phoneType} ${intl.formatMessage({
        id: "company.detail.phoneLabel.doNotContact",
      })}`;
    } else {
      phoneLabel = phoneType;
    }

    const handleClick = event => {
      if (!revealed && !selected && (!privacy || allowViewPrivacy)) {
        setSelected(!selected);
        if (onClick) onClick(phoneNum);
      }
    };

    const handleBlur = event => {
      console.log(event.target.value);
      onBlur(event.target.value);
    };

    const handleChange = event => {
      setPhoneNumInput(event.target.value);
    };

    React.useEffect(() => {
      setPhoneNumInput(phoneNum);
    }, []);

    React.useEffect(() => {
      if (revealed === true) setSelected(true);
      if (revealed === false) setSelected(false);
    }, [revealed]);

    // UI here
    return (
      <React.Fragment>
        {!_.isEmpty(title) && (
          <div className={classes.title}>
            {intl.formatMessage({ id: title })}
          </div>
        )}
        {!editable ? (
          <Chip
            icon={selected ? null : <Phone />}
            size="small"
            className={`
              ${classes.common}
              ${selected ? classes.selected : classes.notSelected}
              ${privacy && (selected ? classes.privacySelected : classes.privacyNotSelected)}
              ${doNotContact && (selected ? classes.doNotContactSelected : classes.doNotContactNotSelected)}
              ShowContact
            `}
            disabled={disabled || selected}
            label={selected ? phoneNum : phoneLabel}
            value={phoneNum}
            clickable={false}
            onClick={handleClick}
            avatar={
              selected ? <Avatar>{typeToSymbol(phoneType)}</Avatar> : null
            }
            ref={innerRef}
          />
        ) : (
          <TextField
            className={clsx(classes.input, transparentbgcolor && classes.transparentbgcolor)}
            value={phoneNumInput}
            InputLabelProps={{
              shrink: true,
            }}
            // InputProps={InputProps}
            onChange={handleChange}
            onBlur={handleBlur}
            type="text"
            // {...other}
          />
        )}
      </React.Fragment>
    );
  }),
);
