import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import { convertNewlineToBr } from "../../helper/generalHelper";

const styles = theme => ({
  root: {
    borderRadius: 4,
    padding: "1vw 2vw",
    backgroundColor: "#FFF",
  },
  updateRow: {
    fontSize: ".875em",
    color: "#777",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between"
  },
  content: {
    fontSize: "1.125em",
  }
});

class MessageCard extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
    className: PropTypes.string,
    title: PropTypes.node,
  };

  render() {
    const { classes, detail, className, title, listing } = this.props;

    const createDateTime = detail.recordOperation && detail.recordOperation.createDateTime ? detail.recordOperation.createDateTime : "";
    const createBy = detail.recordOperation && detail.recordOperation.createBy && detail.recordOperation.createBy.name_en ? detail.recordOperation.createBy.name_en : "";
    const subject = detail.subject || "";
    const message = detail.message ? convertNewlineToBr(detail.message) : "";

    return (
      <div className={`${classes.root} ${className}`}>
        {title}
        {listing && <span>loading...</span>}
        <div className={classes.updateRow}>
          <div>{createDateTime}</div>
          <div>{createBy}</div>
        </div>
        <div className={classes.content}>
          {subject}<br />
          {message}
        </div>
      </div>
    );
  }
}

export default withStyles(styles)(MessageCard);
