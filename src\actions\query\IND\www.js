import { sbu } from "../../../config";
export const WWW_DETAILS_QUERY = `
    query($stock:ID) {
      details(stock: $stock) {
        _id
        stock
        categories{
            wwwId
            addressOnWeb
            isPriceNego
            isRentNego
            price
            rent
            unitPrice
            bottomPrice
            bottomRent
            bottomUnitPrice
            bottomUnitRent
            isInclusive
            unitRent
            features {
              featureEng
              featureChi
              updateBy {
                emp_id
                name_en
                dept {
                  dept_id
                  dept_code
                  name_zh
                  name_en
                  phone
                  fax
                  status
                  email
                  type
                  ics
                  head_id
                  dist_head_id
                  addr_zh
                  addr_en
                  addr_full_zh
                  addr_full_en
                  comp_licence_no
                }
                auth {
                  team
                  role
                }
              }
              updateDate
            }
            updateDate
            updateBy {
              emp_id
              name_en
              dept {
                dept_id
                dept_code
                name_zh
                name_en
                phone
                fax
                status
                email
                type
                ics
                head_id
                dist_head_id
                addr_zh
                addr_en
                addr_full_zh
                addr_full_en
                comp_licence_no
              }
              auth {
                team
                role
              }
            }
            category
            isEffective
            startDate
            endDate
            stockRemarks
            decoration
            unitView
            ${sbu === "COMM" ? `decorations
            unitViews`: ""}
            business
            isCarPark
            isEaa
        }
      }
    }
    `;

export const WWW_USED_COUNT_QUERY = `
query($emp_id: String){
  getUsedCount(emp_id: $emp_id){
    usedQuota
    markWWWList {
      wwwId
      stockId
    }
  }
}
`;