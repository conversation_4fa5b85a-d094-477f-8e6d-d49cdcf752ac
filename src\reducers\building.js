import {
  LIST_BUILDINGS_START,
  LIST_BUILDINGS_SUCCESS,
  LIST_BUILDINGS_ERROR,
  C<PERSON>AR_BUILDINGS,
  LIST_BUILDING_DETAIL_START,
  LIST_BUILDING_DETAIL_SUCCESS,
  LIST_BUILDING_DETAIL_ERROR,
  LIST_BUILDING_MEDIA_START,
  LIST_BUILDING_MEDIA_SUCCESS,
  LIST_BUILDING_MEDIA_ERROR,
  <PERSON><PERSON><PERSON>_BUILDING_DETAIL,
  LIST_UNITVIEW_START,
  LIST_UNITVIEW_SUCCESS,
  LIST_UNITVIEW_ERROR,
  LIST_FLOOR_ORDERING_START,
  LIST_FLOOR_ORDERING_SUCCESS,
  LIST_FLOOR_ORDERING_ERROR,
  UPDATE_BUILDING_MEDIA_BY_INDEX,
  UPDATE_BUILDING_MEDIA_LIST,
} from "../constants/building";

const initialState = {
  buildings: [],
  listed: false,
  listedDetail: false,
  valid: false,
};

export default function building(state = initialState, action) {
  switch (action.type) {
    case LIST_BUILDINGS_START:
      return {
        ...state,
        // listed: false,
        // listing: true,
        // buildings: []
      };
    case LIST_BUILDINGS_SUCCESS:
      return {
        ...state,
        listed: true,
        listing: false,
        buildings: action.payload.data.data.buildingSourceSearch,
      };
    case LIST_BUILDINGS_ERROR:
      return {
        ...state,
        listed: false,
        listing: false,
        error: action.payload.error,
      };
    case LIST_UNITVIEW_START:
      return {
        ...state,
        listing: true,
        listed: false,
        unitview: null,
      };
    case LIST_UNITVIEW_SUCCESS:
      return {
        ...state,
        listing: false,
        listed: true,
        unitview: action.payload.data.data.unitViews,
      };
    case LIST_UNITVIEW_ERROR:
    case LIST_FLOOR_ORDERING_ERROR:
      return {
        ...state,
        listing: false,
        listed: false,
        error: action.payload.error,
      };
    case LIST_FLOOR_ORDERING_START:
      return {
        ...state,
        listing: true,
        listed: false,
        floorOrdering: null,
      };
    case LIST_FLOOR_ORDERING_SUCCESS:
      return {
        ...state,
        listing: false,
        listed: true,
        floorOrdering: action.payload.data.data.listFloorOrdering,
      };
    case CLEAR_BUILDINGS:
      return {
        ...state,
        listed: false,
        listing: false,
        buildings: null,
      };
    case LIST_BUILDING_DETAIL_START:
      return {
        ...state,
        listedDetail: false,
        listingDetail: true,
        buildingDetail: null,
      };
    case LIST_BUILDING_DETAIL_SUCCESS:
      return {
        ...state,
        listedDetail: true,
        listingDetail: false,
        detail: action.payload.data.data.source
          ? action.payload.data.data.source
          : null,
      };
    case LIST_BUILDING_DETAIL_ERROR:
      return {
        ...state,
        listedDetail: false,
        listingDetail: false,
        errorDetail: action.payload.error,
      };
    case LIST_BUILDING_MEDIA_START:
      return {
        ...state,
        listedMedia: false,
        listingMedia: true,
        media: [],
      };
    case LIST_BUILDING_MEDIA_SUCCESS:
      return {
        ...state,
        listedMedia: true,
        listingMedia: false,
        media: action.payload.data,
      };
    case LIST_BUILDING_MEDIA_ERROR:
      return {
        ...state,
        listedMedia: false,
        listingMedia: false,
        errorMedia: action.payload.error,
      };
    case UPDATE_BUILDING_MEDIA_BY_INDEX:
      const { index, mediaIndex, mediaType, replace = false, media = {} } = action.payload;
      if (index === -1 || mediaIndex === -1) {
        return state;
      }
      if (state.media?.[index]?.data?.[mediaType]?.[mediaIndex]) {
        const newMedia = JSON.parse(JSON.stringify(state.media));
        const newMedias = newMedia[index].data[mediaType] || [];

        newMedias[mediaIndex] = replace ? media : { ...newMedias[mediaIndex], ...media };
        return {
          ...state,
          media: newMedia,
        };
      }
      return state;
    case CLEAR_BUILDING_DETAIL:
      return {
        ...state,
        listedDetail: false,
        listingDetail: false,
        buildingDetail: null,
        errorDetail: null,
        listedMedia: false,
        listingMedia: false,
        media: [],
        errorMedia: null,
      };
    case UPDATE_BUILDING_MEDIA_LIST:
      if (state.media && state.media.length > 0) {
        const { mediaType, mediaList } = action.payload;
        const newMedia = JSON.parse(JSON.stringify(state.media));
        
        if (newMedia[0] && newMedia[0].data) {
          newMedia[0].data = {
            ...newMedia[0].data,
            [mediaType]: mediaList
          };
          
          return {
            ...state,
            media: newMedia,
            listedMedia: true
          };
        }
      }
      return state;
    default:
      return state;
  }
}
