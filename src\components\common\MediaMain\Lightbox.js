import React from "react";
import PropTypes from "prop-types";
import { createStyles, withStyles } from "@material-ui/styles";
import DeleteOutlineIcon from "@material-ui/icons/DeleteOutline";
import MediaHandleResultDialog from "./MediaHandleResultDialog";
import { updateMedium } from "@/actions/medium";
import ReactLightbox from "react-image-lightbox";
import { injectIntl, useIntl } from "react-intl";
import DialogButton from "../DialogButton";
import { connect } from "react-redux";
import ReactDOM from "react-dom";
import { sbu } from "@/config";
import { dateFormatter, getDisplayStockId } from "@/helper/generalHelper";
import { getPhotoContentOptions } from "../UploadMedia/StepOne";

/**
 * @typedef {Object} Props
 * @property {() => void} [onClick]
 * @property {boolean} isOpen
 * @property {string=} [obsClassName=".ril-image-current"] - 用于观察的元素的类名
 * @property {(media: any) => React.ReactNode} [renderCustomButton] - 自定义按钮渲染函数，接收当前 media 作为参数
 * @property {any} [media] - 当前显示的媒体对象
 */

/** 
 * @type {React.NamedExoticComponent<Props>}
 * - 使用React.memo优化按钮组件
 */
export const LightboxButtonInjector = React.memo(({
  isOpen,
  onClick,
  obsClassName = ".ril-image-current",
  renderCustomButton,
  media,
}) => {
  const intl = useIntl();
  const [position, setPosition] = React.useState({ top: 0 });
  const currentImageRef = React.useRef(null);
  const rafId = React.useRef(null);
  const resizeObserverRef = React.useRef(null);

  const updatePosition = React.useCallback(() => {
    if (!currentImageRef.current) return;

    rafId.current = requestAnimationFrame(() => {
      const rect = currentImageRef.current.getBoundingClientRect();
      setPosition({
        top: rect.top + rect.height + 20,
      });
    });
  }, []);

  const escapedObsClassName = React.useMemo(() => {
    // MUI 生成的 className 会包含圆括号, 需要转义
    return obsClassName.replace(/([()])/g, '\\$1');
  }, [obsClassName]);

  const queryTargetElement = (className = "") => {
    if (!className) return null;
    try {
      return document.querySelector(className);
    } catch (e) {
      // 错误恢复：尝试转义特殊字符后重试
      const safeSelector = CSS.escape(className);
      return document.querySelector(safeSelector);
    }
  };

  // 统一元素处理函数
  const handleElement = (element = null) => {
    if (!element || element === currentImageRef.current) return;

    resizeObserverRef.current?.disconnect();
    currentImageRef.current = element;
    resizeObserverRef.current = new ResizeObserver(updatePosition);
    resizeObserverRef.current.observe(element);
    updatePosition();
  };

  React.useEffect(() => {
    if (!isOpen || !escapedObsClassName) return;

    // 初始化检查
    handleElement(queryTargetElement(escapedObsClassName));

    const observer = new MutationObserver((mutations) => {
      handleElement(queryTargetElement(escapedObsClassName));
    });

    observer.observe(document.body, {
      subtree: true,
      childList: true,
      attributes: true,
      attributeFilter: ['class'],
    });

    return () => {
      observer.disconnect();
      cancelAnimationFrame(rafId.current);
      resizeObserverRef.current?.disconnect();
    };
  }, [isOpen, updatePosition, escapedObsClassName]);

  const handleClick = React.useCallback((e) => {
    e.stopPropagation();
    onClick?.();
  }, [onClick]);

  /** @type {any} */
  const containerEle = React.useMemo(() => (
    <div
      style={{
        position: 'fixed',
        top: `${position.top}px`,
        transform: 'translateX(calc(50vw - 50%))',
        zIndex: 1001, // 确保高于 Lightbox 的 1000
      }}
    >
      {renderCustomButton ? (
        renderCustomButton(media)
      ) : (
        <DialogButton onClick={handleClick}>
          {intl.formatMessage({ id: "media.update.shareToPublic" })}
        </DialogButton>
      )}
    </div>
  ), [position, handleClick, intl, renderCustomButton, media]);

  return isOpen && position.top !== 0
    ? ReactDOM.createPortal(containerEle, document.body)
    : null;
});

const styles = createStyles({
  deleteSvg: {
    padding: "1px 6px",
    cursor: "pointer",
  },
  caption: {
    wordBreak: "break-word",
  },
});

/**
 * 记录每个图片节点的原始 transform 和 maxHeight
 * @type {WeakMap<HTMLImageElement, {transform: string, maxHeight: string}>}
 */
const originalImgStyleMap = new WeakMap();

function Lightbox(props) {
  const {
    classes,
    media,
    mediaId,
    deletable,
    handleOpenDeleteMediaDialog,
    handleCloseLightbox,
    setMediaId,
    intl,
    isApproveMediaPage,
  } = props;

  const photos = React.useMemo(() => media.filter(v => v.type === "photo"), [media]);
  const photoIndex = React.useMemo(() => photos.map(v => v.id).indexOf(mediaId), [photos, mediaId]);
  const isPersonal = React.useMemo(() => photos[photoIndex]?.approval !== "approved", [photos, photoIndex]);

  // const [lightboxButtonIsOpen, setLightboxButtonIsOpen] = React.useState(isPersonal);
  const lightboxButtonIsOpen = React.useMemo(() => {
    return (photos[photoIndex]?.approval === "pending" || photos[photoIndex]?.approval === "rejected") && !props.isListProposal && !props.isProposal;
  }, [props.isListProposal, props.isProposal, photos, photoIndex]);

  React.useEffect(() => {
    // 修正当前 pending 图片
    const fixLightboxImageTransform = () => {
      /** @type {HTMLImageElement | null} */
      const img = document.querySelector('.ril-image-current');
      if (img) {
        // naturalWidth/Height 可能为 0，等图片加载完成再修正
        if (img.naturalWidth === 0 || img.naturalHeight === 0) {
          img.onload = () => fixLightboxImageTransform();
          return;
        }
        // 记录原始样式（只记录一次）
        if (!originalImgStyleMap.has(img)) {
          originalImgStyleMap.set(img, {
            transform: img.style.transform,
            maxHeight: img.style.maxHeight,
          });
        }
        if (lightboxButtonIsOpen || isApproveMediaPage) {
          // 判断图片比例，竖图才修正
          if (img.naturalHeight > img.naturalWidth) {
            img.style.maxHeight = '60vh';
            if (img.style.transform) {
              img.style.transform = img.style.transform.replace(
                /translate3d\([^)]+\)/,
                'translate3d(0, 0, 0)'
              );
            }
          } else {
            // 宽图恢复原始样式
            const original = originalImgStyleMap.get(img);
            if (original) {
              img.style.transform = original.transform;
              img.style.maxHeight = original.maxHeight;
            }
          }
        } else {
          // 恢复原始样式
          const original = originalImgStyleMap.get(img);
          if (original) {
            img.style.transform = original.transform;
            img.style.maxHeight = original.maxHeight;
          }
        }
      }
    };

    // 先修正一次
    fixLightboxImageTransform();

    // 监听图片切换和 DOM 变化
    const observer = new MutationObserver(() => {
      // 先恢复所有已记录图片的原始样式
      document.querySelectorAll('.ril-image-current').forEach((/** @type {HTMLImageElement} */img) => {
        const original = originalImgStyleMap.get(img);
        if (original) {
          if (img.style.transform !== original.transform) {
            img.style.transform = original.transform;
          }
          if (img.style.maxHeight !== original.maxHeight) {
            img.style.maxHeight = original.maxHeight;
          }
        }
      });
      // 再修正当前 pending 图片
      fixLightboxImageTransform();
    });

    observer.observe(document.body, {
      subtree: true,
      childList: true,
      attributes: true,
      attributeFilter: ['class'],
    });

    return () => {
      observer.disconnect();
      // 离开时恢复所有图片的原始样式
      document.querySelectorAll('.ril-image-current').forEach((/** @type {HTMLImageElement} */img) => {
        const original = originalImgStyleMap.get(img);
        if (original) {
          img.style.transform = original.transform;
          img.style.maxHeight = original.maxHeight;
          originalImgStyleMap.delete(img);
        }
      });
    };
  }, [lightboxButtonIsOpen, isApproveMediaPage]);

  const handleShareToPublic = React.useCallback(() => {
    if (!photos[photoIndex]?.id) return;

    props.updateMedium({
      id: photos[photoIndex].id,
      approval: "waiting",
    });
  }, [photos, photoIndex, props.updateMedium]);

  const handleLightboxClose = React.useCallback(() => {
    // setLightboxButtonIsOpen(false);
    handleCloseLightbox();
  }, [handleCloseLightbox]);

  const getPhotoUrl = React.useCallback((photo) => {
    const suffix = photo.mediumRoot.includes('blob') ? '' : '/l.jpeg?';
    return `${photo.mediumRoot}${suffix}`;
  }, []);

  const onMovePrevRequest = React.useCallback(() => {
    setMediaId(photos[(photoIndex + photos.length - 1) % photos.length].id);
  }, [photos, photoIndex]);
  const onMoveNextRequest = React.useCallback(() => {
    setMediaId(photos[(photoIndex + 1) % photos.length].id);
  }, [photos, photoIndex]);

  const photoContent = React.useMemo(() => {
    if (!photos[photoIndex]?.photoContent) { return ""; }
    const options = getPhotoContentOptions(intl) || [];

    const option = options.find(v => v.value === photos[photoIndex].photoContent);
    return option.label || "";
  }, [photos, photoIndex, intl]);

  return (
    <>
      {!photos[photoIndex] ? null : <ReactLightbox
        mainSrc={getPhotoUrl(photos[photoIndex])}
        nextSrc={getPhotoUrl(photos[(photoIndex + 1) % photos.length])}
        prevSrc={getPhotoUrl(photos[(photoIndex + photos.length - 1) % photos.length])}
        imageCaption={
          isApproveMediaPage ? <>
            {(photos[photoIndex].buildingName && photos[photoIndex].propertyRefId) ? (
              <div className={classes.caption}>
                {"樓盤: "}
                {photos[photoIndex].buildingName} ({photos[photoIndex].propertyRefId})
              </div>
            ) : ((photos[photoIndex].stockAddress?.[intl.locale] && photos[photoIndex].stockId) ?
              <div className={classes.caption}>
                {"樓盤: "}
                {photos[photoIndex].stockAddress[intl.locale]} ({getDisplayStockId(photos[photoIndex].stockId)})
              </div> : null
            )}
            {(photos[photoIndex].createdDate && photos[photoIndex].employee) && (
              <div className={classes.caption}>
                {"擁有人: "}
                {dateFormatter(photos[photoIndex].createdDate || "")} {photos[photoIndex].employee?.branchId} {intl.locale === 'zh' ? photos[photoIndex].employee?.cName : photos[photoIndex].employee?.eName}
              </div>
            )}
            <div className={classes.caption}>
              {"多媒體類型: Photo"}
            </div>
            {(photoContent) && (
              <div className={classes.caption}>
                {"内容: "}
                {photoContent}
              </div>
            )}
            {(photos[photoIndex].originalFilename) && (
              <div className={classes.caption}>
                {"名稱: "}
                {photos[photoIndex].originalFilename}
              </div>
            )}
          </> : <div className={classes.caption}>
            {`${intl.formatMessage({ id: "stock.photo.name" })}: ${photos[photoIndex].originalFilename}`}
            {photos[photoIndex].photoContent && (
              <>
                <br />
                {`${intl.formatMessage({ id: "stock.photo.content" })}: ${photos[photoIndex].photoContent}`}
              </>
            )}
          </div>
        }
        onCloseRequest={handleLightboxClose}
        toolbarButtons={
          deletable && isPersonal
            ? [
              <DeleteOutlineIcon
                key="delete"
                className={classes.deleteSvg}
                onClick={() => handleOpenDeleteMediaDialog(photos[photoIndex].id)}
              />,
            ]
            : []
        }
        onMovePrevRequest={onMovePrevRequest}
        onMoveNextRequest={onMoveNextRequest}
      />}

      {lightboxButtonIsOpen && <>
        <LightboxButtonInjector
          isOpen={photos[photoIndex]?.approval === "pending" || photos[photoIndex]?.approval === "rejected"}
          onClick={handleShareToPublic}
        />

        <MediaHandleResultDialog
          media={photos[photoIndex]}
          mediaType={"photo"}
        />
      </>}

      {props.isApproveMediaPage && <>
        <LightboxButtonInjector
          isOpen={photos[photoIndex]?.approval === "waiting"}
          // onClick={handleShareToPublic}
          renderCustomButton={props.renderCustomButton}
          media={photos[photoIndex]}
        />
      </>}
    </>
  );
}

Lightbox.propTypes = {
  classes: PropTypes.object.isRequired,
  media: PropTypes.arrayOf(PropTypes.object),
  mediaType: PropTypes.oneOf(['stock', 'building']),
  isProposal: PropTypes.bool,
  isListProposal: PropTypes.bool,
  id: PropTypes.string,
  deletable: PropTypes.bool,
  handleOpenDeleteMediaDialog: PropTypes.func,
  handleCloseLightbox: PropTypes.func.isRequired,
  setMediaId: PropTypes.func.isRequired,
  updateMedium: PropTypes.func,
  allProgressingMedia: PropTypes.array,
  renderCustomButton: PropTypes.func,
  isApproveMediaPage: PropTypes.bool,
};

Lightbox.defaultProps = {
  media: [],
  isProposal: false,
  isListProposal: false,
  handleOpenDeleteMediaDialog: () => void 0,
  handleCloseLightbox: () => void 0,
  setMediaId: () => void 0,
  allProgressingMedia: [],
  /** @type {Function | null}  */
  renderCustomButton: null,
};

const mapStateToProps = state => ({
  // ref: https://stackoverflow.com/questions/48819138/how-do-you-get-syncerrors-out-of-state-using-redux-form-selectors
  allProgressingMedia: state.medium.progressingMedia || [],
  buildingMedia: state.building.media || {},
  stockMedia: state.stock.media || {},
  isApproveMediaPage: state.medium.isApproveMediaPage,
});

const mapDispatchToProps = (dispatch) => ({
  updateMedium: (...args) => dispatch(updateMedium(...args)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(Lightbox)));
