import React, { useEffect, useState, memo } from "react";
import _ from "lodash";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/core/styles";
import { connect } from "react-redux";
import { injectIntl } from "react-intl";

import DetailBoxSection from "../../../../common/DetailBoxSection";
import { getDeniedCalls } from "../../../../../actions/stock";
import ContactInfoBox from "../../../../common/ContactInfoBox";

const useStyles = makeStyles({
  root: {
    padding: "1vh 0",
  },
  notFound: {},
});

function ContactInfo({ detail, getDeniedCalls, intl }) {
  const classes = useStyles();

  const [contactInfo, setContactInfo] = useState([]);
  const [deniedCalls, setDeniedCalls] = useState(null);

  const mongoId = _.get(detail, "_id");
  const stockId = Number.isInteger(_.get(detail, "unicorn.id") ?? "")
    ? _.get(detail, "unicorn.id")
    : null;

  const findByHandsId = (array, hands) => {
    return _.filter(array, item => item.hands === hands);
  };

  useEffect(() => {
    const vendors = findByHandsId(_.get(detail, "vendors"), detail.currentHands) || [];
    if (!_.isEmpty(vendors)) {
      setContactInfo(vendors);
    }
  }, [detail]);

  useEffect(() => {
    if (!_.isEmpty(contactInfo) && _.isNull(deniedCalls)) {
      getDeniedCalls(_.map(contactInfo, "phones")).then((res) =>
        setDeniedCalls(res),
      );
    }
  }, [contactInfo]);

  return (
    <div className={classes.root}>
      <DetailBoxSection
        expandable
        text={intl.formatMessage({
          id: "stock.contact",
        })}
      >
        {contactInfo.map((v, i) => (
          <ContactInfoBox contact={v} mongoId={mongoId} stockId={stockId} key={i} deniedCalls={deniedCalls} />
        ))}
      </DetailBoxSection>
    </div>
  );
}

ContactInfo.propTypes = {
  detail: PropTypes.object,
  getDeniedCalls: PropTypes.func.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapDispatchToProps = (dispatch) => ({
  getDeniedCalls: (phoneNos) => dispatch(getDeniedCalls(phoneNos)),
});

export default connect(null, mapDispatchToProps)(memo(injectIntl(ContactInfo)));
