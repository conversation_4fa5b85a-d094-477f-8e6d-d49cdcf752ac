import Cookies from "universal-cookie";

import {
  LIST_EMPLOYEES_START,
  LIST_EMPLOYEES_SUCCESS,
  LIST_EMPLOYEES_ERROR,
  LIST_VALID_EMPLOYEES_START,
  LIST_VALID_EMPLOYEES_SUCCESS,
  LIST_VALID_EMPLOYEES_ERROR,
  <PERSON><PERSON><PERSON>_EMPLOYEES,
  SET_PERMISSION,
  EMPLOYEES_MITACLUB_START,
  EMPLOYEES_MITACLUB_SUCCESS,
  EMPLOYEES_MITACLUB_ERROR,
  EMPLOYEES_MITACLUB_MANAGER_START,
  EMPLOYEES_MITACLUB_MANAGER_SUCCESS,
  EMPLOYEES_MITACLUB_MANAGER_ERROR,

  MITACLUB_MANAGER_AGENT_LIST_START,
  MITACLUB_MANAGER_AGENT_LIST_SUCCESS,
  MITACLUB_MANAGER_AGENT_LIST_ERROR,
  SET_MITACLUB_AGENT_DISTRICT_DIRECTOR_LIST_START,
  SET_MITACLUB_AGENT_DISTRICT_DIRECTOR_LIST,
  SET_MITACLUB_AGENT_DISTRICT_DIRECTOR_LIST_ERROR,

  SET_MITACLUB_AGENT_SBU_DIRECTOR_START,
  SET_MITACLUB_AGENT_SBU_DIRECTOR,
  SET_MITACLUB_AGENT_SBU_DIRECTOR_ERROR,

  SET_MITACLUB_AGENT_MANAGER_LIST_START,
  SET_MITACLUB_AGENT_MANAGER_LIST,
  SET_MITACLUB_AGENT_MANAGER_LIST_ERROR,

  SET_MITACLUB_AGENTS_WITH_TEAM_START,
  SET_MITACLUB_AGENTS_WITH_TEAM,
  SET_MITACLUB_AGENTS_WITH_TEAM_ERROR,
} from "../constants/employee";

import { PERMISSIONS } from "../constants/auth";
import _ from "lodash";
import {
  cas_profile_host,
  cas_client_id,
  generalDailyQuota,
  // enableInternalService,
} from "../config";

import { logout } from "./auth";

export function checkCASTokenValid(casAccessToken) {
  return async (dispatch, getState, { fetch, universalRequest }) => {
    const checkToken = await fetch(
      `${cas_profile_host}?access_token=${casAccessToken}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      },
    );

    if (checkToken.status == 401) {
      dispatch(logout());
    }
  }
}

export function getEmployeePermission() {
  return async (dispatch, getState, { fetch }) => {
    const cookies = new Cookies(); // Initialize universal-cookie

    const casAccessToken = cookies.get("casAccessToken");
    const casRefreshToken = cookies.get("casRefreshToken");
    const casAccessTokenExpiresIn = cookies.get("casAccessTokenExpiresIn");

    if (casAccessToken && localStorage.getItem("casAccessToken")) {
      localStorage.setItem("casAccessToken", casAccessToken);
      localStorage.setItem("casRefreshToken", casRefreshToken);
      localStorage.setItem("casAccessTokenExpiresIn", casAccessTokenExpiresIn);
    }

    try {
      if (casAccessToken) {
        await dispatch(checkCASTokenValid(casAccessToken));
      }

      fetch("/getUserPermissions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          accessToken: casAccessToken,
        }),
      })
        .then((res) => res.json())
        .then((permissionData) => {
          if (
            _.isEmpty(_.get(permissionData, "error")) &&
            _.get(permissionData, "result.code") == 200
          ) {
            const permissions = [];
            _.forEach(
              _.values(
                _.get(
                  permissionData,
                  `result.data.permissions[${cas_client_id}]`,
                ),
              ),
              (category) => {
                _.forEach(category, (value, key) => {
                  if (value) permissions.push({ [key]: value });
                });
              },
            );
            const permissionSet = new Set(
              _.map(permissions, (p) => _.keys(p)[0]),
            );
            const permissionList = _.fromPairs(
              _.map(PERMISSIONS, (p) => [
                p,
                permissionSet.has(p) &&
                _.get(
                  _.find(permissions, (per) => _.keys(per)[0] === p),
                  p,
                  false,
                ),
              ]),
            );
            dispatch({
              type: SET_PERMISSION,
              payload: permissionList,
            });
          }
        });
    } catch (error) {
      console.error("Fail to getUserPermission: ", error);
      // throw new Error(error);
      if (casAccessToken) {
        await dispatch(checkCASTokenValid(casAccessToken));
      }
    }
  };
}

export function listEmployees(variables) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_EMPLOYEES_START,
      checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        }, //getState().auth.user.casAccessToken  }
      };

      const query = await getQuery("LIST_EMPLOYEES_QUERY");

      const data = await universalRequest("/employee/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_EMPLOYEES_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_EMPLOYEES_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };
}

export function listValidEmployees(variables) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_VALID_EMPLOYEES_START,
      checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        }, //getState().auth.user.casAccessToken  }
      };

      const query = await getQuery("LIST_VALID_EMPLOYEES_QUERY");

      const data = await universalRequest("/employee/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_VALID_EMPLOYEES_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_VALID_EMPLOYEES_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };
}

export function clearEmployees() {
  return async (dispatch) => {
    dispatch({
      type: CLEAR_EMPLOYEES,
    });
  };
}

const personalTypeMap = Object.freeze({
  agent: {
    query: "EMPLOYEES_MITACLUB_AGENT_QUERY",
  },
  manager: {
    query: "EMPLOYEES_MITACLUB_MANAGER_QUERY",
  },
  district_manager: {
    query: "EMPLOYEES_MITACLUB_DISTRICT_DIRECTOR_QUERY",
  },
  sbu_district_manager: {
    query: "EMPLOYEES_MITACLUB_AGENT_DISTRICT_MANAGER",
  },
});
export function getEmployeesMitaclubByPersonalType(
  personalType = "agent",
  variables = {},
  typePrefix = "",
) {
  return async (
    dispatch,
    getState,
    { getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: EMPLOYEES_MITACLUB_START,
      personalType: `${typePrefix}${personalType}`,
      // checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
      };

      const query = await getQuery(personalTypeMap[`${typePrefix}${personalType}`]['query']);
      const data = await universalRequest("/employee/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: EMPLOYEES_MITACLUB_SUCCESS,
        payload: {
          personalType: `mitaclub_${personalType}`,
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: EMPLOYEES_MITACLUB_ERROR,
        payload: {
          error,
        },
      });
    }
  };
}

export function getEmployeesMitaclubAgent(variables) {
  return getEmployeesMitaclubByPersonalType("agent", variables);
}

export function getEmployeesMitaclubManager(variables) {
  return getEmployeesMitaclubByPersonalType("manager", variables);
}

export function getEmployeesMitaclubDistrictDirector(variables) {
  return getEmployeesMitaclubByPersonalType("district_manager", variables);
}

export function getEmployeesMitaclubSBUDirector(variables) {
  return getEmployeesMitaclubByPersonalType("district_manager", variables, "sbu_");
}

export function getEmployeesMitaclubManagerInfo(variables = {}) {
  return async (
    dispatch,
    getState,
    { getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: EMPLOYEES_MITACLUB_MANAGER_START,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
      };

      const query = await getQuery(personalTypeMap['manager']['query']);
      const data = await universalRequest("/employee/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: EMPLOYEES_MITACLUB_MANAGER_SUCCESS,
        payload: {
          personalType: `mitaclub_manager`,
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: EMPLOYEES_MITACLUB_MANAGER_ERROR,
        payload: {
          error,
        },
      });
    }
  };
}

export function getMitaclubEmpsByTeamCode(variables) {
  return async (
    dispatch,
    getState,
    { getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: MITACLUB_MANAGER_AGENT_LIST_START,
      // checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
      };

      const query = await getQuery("EMPLOYEES_MITACLUB_AGENT_QUERY");
      const data = await universalRequest("/employee/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables,
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: MITACLUB_MANAGER_AGENT_LIST_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: MITACLUB_MANAGER_AGENT_LIST_ERROR,
        payload: {
          error,
        },
      });
    }
  };
}

export function queryMitaClubDistrictDirectorList(variables) {
  return async (
    dispatch,
    getState,
    { getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: SET_MITACLUB_AGENT_DISTRICT_DIRECTOR_LIST_START,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
      };

      const query = await getQuery("EMPLOYEES_MITACLUB_AGENT_DISTRICT_MANAGER");
      const data = await universalRequest("/employee/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables,
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: SET_MITACLUB_AGENT_DISTRICT_DIRECTOR_LIST,
        payload: {
          data: data.data,
        },
      });
    } catch (error) {
      dispatch({
        type: SET_MITACLUB_AGENT_DISTRICT_DIRECTOR_LIST_ERROR,
        payload: {
          error,
        },
      });
    }
  };
}

/** -------------------- PC Actions Start -------------------- */
export function queryMitaClubDistrictManager(variables = {}) {
  return async (
    dispatch,
    getState,
    { getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: SET_MITACLUB_AGENT_SBU_DIRECTOR_START,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
      };

      const query = await getQuery(personalTypeMap.sbu_district_manager.query);
      const data = await universalRequest("/employee/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables,
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: SET_MITACLUB_AGENT_SBU_DIRECTOR,
        payload: {
          data: data.data,
        },
      });
    } catch (error) {
      dispatch({
        type: SET_MITACLUB_AGENT_SBU_DIRECTOR_ERROR,
        payload: {
          error,
        },
      });
    }
  };
}

export function queryMitaClubManagerList(variables = {}) {
  return async (
    dispatch,
    getState,
    { getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: SET_MITACLUB_AGENT_MANAGER_LIST_START,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
      };

      const query = await getQuery("EMPLOYEES_MITACLUB_AGENT_MANAGER_LIST_QUERY");
      const data = await universalRequest("/employee/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables,
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: SET_MITACLUB_AGENT_MANAGER_LIST,
        payload: {
          data: data.data,
        },
      });
    } catch (error) {
      dispatch({
        type: SET_MITACLUB_AGENT_MANAGER_LIST_ERROR,
        payload: {
          error,
        },
      });
    }
  };
}

export function queryMitaClubAgentsWithTeam(variables = {}) {
  return async (
    dispatch,
    getState,
    { getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: SET_MITACLUB_AGENTS_WITH_TEAM_START,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
      };

      const query = await getQuery("EMPLOYEES_MITACLUB_AGENT_QUERY");
      const data = await universalRequest("/employee/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables,
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: SET_MITACLUB_AGENTS_WITH_TEAM,
        payload: {
          data: data.data,
        },
      });
    } catch (error) {
      dispatch({
        type: SET_MITACLUB_AGENTS_WITH_TEAM_ERROR,
        payload: {
          error,
        },
      });
    }
  };
}

/** -------------------- PC Actions End -------------------- */
