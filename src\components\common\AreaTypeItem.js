import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import { injectIntl } from "react-intl";
import { numberComma } from "../../helper/generalHelper";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    width: "35vw",
    display: "flex",
    justifyContent: "flex-end",
  },
  type: {
    textAlign: "right",
  },
  value: {
    width: "23vw",
    textAlign: "right",
    marginRight: "auto",
    textOverflow: "ellipsis",
    overflow: "hidden",
    whiteSpace: "nowrap",
  },
  verified: {
    width: "5vw",
    display: "flex",
    justifyContent: "flex-end",
    alignItems: "center",
  },
  verifiedBox: {
    fontSize: ".7em",
    textAlign: "center",
    backgroundColor: "#8A8A8A",
    color: "#fff",
    padding: "0 3px",
    borderRadius: 6,
  },
};

function AreaTypeItem(props) {
  const {
    classes,
    className,
    typeData,
    intl,
    ...other
  } = props;

  const typeName = typeData && typeData.type ? typeData.type : "";
  const typeValue = typeData
    ? (typeData.value && !isNaN(parseInt(typeData.value)) ? numberComma(typeData.value) : "---")
    : "";
  const typeVerified = !!(typeData && typeData.verified);

  return (
    <div className={classes.root} {...other}>
      <div className={classes.value}>{typeValue}</div>
      <div className={classes.type}>{typeName}</div>
      <div className={classes.verified}>
        {typeVerified && <div className={classes.verifiedBox}>
          {intl.formatMessage({ id: "stock.area.verified" })}
        </div>}
      </div>
    </div>
  );
}

AreaTypeItem.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  typeData: PropTypes.object,
};

export default withStyles(styles)(injectIntl(AreaTypeItem));
