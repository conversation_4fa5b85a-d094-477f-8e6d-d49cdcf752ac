import React from "react";
import { connect } from "react-redux";
import TextField from "@material-ui/core/TextField";
import { makeStyles } from "@material-ui/core/styles";
// import AsyncSelect from "react-select/async";
import Select from "react-select";
import Paper from "@material-ui/core/Paper";
import { listBuildings } from "../../actions/building";
import { listStreets } from "../../actions/street";
import StandardSvg from "./StandardSvg";
import district from "../../files/icons/district.svg";
import building from "../../files/icons/building.svg";
import street from "../../files/icons/street.svg";

const useStyles = makeStyles(theme => ({
  root: {
    // marginLeft: theme.spacing(1),
    // marginRight: theme.spacing(1)
    // width: '100%',
  },
  input: {
    display: "flex",
    // padding: "2px 0 2px 14px",
    height: "auto",
    cursor: "pointer"
  },
  valueContainer: {
    display: "flex",
    flexWrap: "wrap",
    flex: 1,
    alignItems: "center",
    overflow: "hidden",
    color: "#fff"
  },
  MultiValueContainer: {
    backgroundColor: "#2B92D4",
    borderRadius: "2px",
    display: "flex",
    margin: "2px",
    minWidth: 0,
    color: "#fff"
  },
  MultiValueLabel: {
    borderRadius: "2px",
    color: "#fff",
    fontSize: "85%",
    overflow: "hidden",
    padding: "3px",
    paddingLeft: "6px"
    // text-overflow: ellipsis;
    // white-space: nowrap;
    // box-sizing: border-box;
  },
  MultiValueRemove: {
    alignItems: "center",
    borderRadius: "2px",
    display: "flex",
    paddingLeft: "4px",
    paddingRight: "4px"
  },
  TypeIcon: {
    maxWidth: "75%",
    filter: "invert(100%)"
  },
  MenuTypeIcon: {
    maxWidth: "75%"
  },
  paper: {
    position: "absolute",
    marginTop: theme.spacing(1),
    left: 0,
    right: 0,
    zIndex: 10
  },
  option: {
    padding: "8px 12px",
    display: "flex",
    backgroundColor: "#fff",
    cursor: "default",
    "&:active": {
      backgroundColor: "#33CCCC"
    }
  },
  IndicatorsContainer: {
    display: "no"
  }
}));

function Control(props) {
  const {
    children,
    innerProps,
    innerRef,
    selectProps: { classes, TextFieldProps }
  } = props;

  return (
    <TextField
      fullWidth
      InputProps={{
        inputComponent,
        inputProps: {
          className: classes.input,
          ref: innerRef,
          children,
          ...innerProps
        }
      }}
      {...TextFieldProps}
    />
  );
}

function Menu(props) {
  return (
    <Paper
      square
      className={props.selectProps.classes.paper}
      {...props.innerProps}
    >
      {props.children}
    </Paper>
  );
}

function ValueContainer(props) {
  return (
    <div className={props.selectProps.classes.valueContainer}>
      {props.children}
    </div>
  );
}

function inputComponent({ inputRef, ...props }) {
  return <div ref={inputRef} {...props} />;
}

function MultiValueContainer(props) {
  const typeIcon = TypeIcon(props.data.type);
  return (
    <div className={props.selectProps.classes.MultiValueContainer}>
      <StandardSvg
        src={typeIcon}
        imgClass={props.selectProps.classes.TypeIcon}
      />
      {props.children}
    </div>
  );
}

function MultiValueLabel(props) {
  return (
    <div className={props.selectProps.classes.MultiValueLabel}>
      {props.children}
    </div>
  );
}

// function MultiValueRemove(props) {
//   return (
//     <div
//       className={props.selectProps.classes.MultiValueRemove}
//       {...props.innerProps}
//     >
//       {props.children}
//     </div>
//   );
// }

function Option(props) {
  const typeIcon = TypeIcon(props.data.type);
  return (
    <div className={props.selectProps.classes.option} {...props.innerProps}>
      <StandardSvg
        src={typeIcon}
        imgClass={props.selectProps.classes.MenuTypeIcon}
      />
      {props.children}
    </div>
  );
}

function IndicatorsContainer(props) {
  return <div className={props.selectProps.classes.IndicatorsContainer}></div>;
}

function TypeIcon(type) {
  let typeIcon = null;
  switch (type) {
    case "district":
      typeIcon = district;
      break;
    case "building":
      typeIcon = building;
      break;
    case "street":
      typeIcon = street;
      break;
    default:
      typeIcon = street;
      break;
  }
  return typeIcon;
}

const components = {
  Control,
  Menu,
  ValueContainer,
  MultiValueContainer,
  MultiValueLabel,
  // MultiValueRemove,
  Option,
  IndicatorsContainer
};

function KeywordsAutoComplete(props) {
  const {
    input,
    districts,
    buildings,
    streets,
    listBuildings,
    listStreets
  } = props;

  const { building, street, district, selectedData, setSelectedData } = props;

  const buildingonChange = building.input.onChange;
  const streetonChange = street.input.onChange;
  const districtonChange = district.input.onChange;
  const [single, setSingle] = React.useState(selectedData || []);

  React.useEffect(() => {
    setSingle(selectedData);
  }, [selectedData]);

  const classes = useStyles();

  let options = [];
  if (districts && districts.length > 0) {
    const filtereddistricts = districts.map(item => ({
      value: item["_id"],
      label: item["nameEn"],
      type: "district"
    }));
    options = options.concat(filtereddistricts);
  }

  if (buildings && buildings.length > 0) {
    const filteredbuildings = buildings.map(item => ({
      value: item["_id"],
      label: item["nameEn"],
      type: "building"
    }));
    options = options.concat(filteredbuildings);
  }

  if (streets && streets.length > 0) {
    const filteredstreets = streets.map(item => ({
      value: item["_id"],
      label: item["nameEn"],
      type: "street"
    }));
    options = options.concat(filteredstreets);
  }

  function handleInputChange(newValue) {
    const graphqlvariable = {
      name: newValue.trim(),
      limit: 5,
      offset: 0,
      sorter: [{ field: "nameEn", order: "ASC" }]
    };
    listStreets(graphqlvariable);
    listBuildings(graphqlvariable);
    return newValue;
  }

  const buildingarr = [];
  const streetarr = [];
  const districtarr = [];
  function handleClick(newValue) {
    setSingle(newValue);
    setSelectedData(newValue);
    if (newValue && newValue.length > 0) {
      newValue.map(item => {
        switch (item.type) {
          case "building":
            buildingarr.push(item.value);
            break;
          case "street":
            streetarr.push(item.value);
            break;
          case "district":
            districtarr.push(item.value);
            break;
          default:
            break;
        }
      });
      buildingonChange(buildingarr);
      streetonChange(streetarr);
      districtonChange(districtarr);
    } else {
      clearSelectedOption();
    }
  }

  function clearSelectedOption() {
    buildingonChange("");
    streetonChange("");
    districtonChange("");
  }

  const selectStyles = {
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected ? "#33CCCC" : "#fff",
      ":active": {
        backgroundColor: state.isSelected ? "#33CCCC" : "#33CCCC"
      }
    })
  };

  const noOptionsMessage = inputValue => {
    if (!inputValue) {
      return "It's null";
    }
    return "No record is found.";
  };

  return (
    <div className={classes.root}>
      <Select
        isMulti
        // loadOptions={loadOptions}
        options={options}
        defaultOptions
        onChange={handleClick}
        onInputChange={handleInputChange}
        classes={classes}
        styles={selectStyles}
        noOptionsMessage={noOptionsMessage}
        TextFieldProps={{
          label: props.label,
          InputLabelProps: {
            htmlFor: "fieldname",
            shrink: true
          },
          margin: "dense",
          variant: "outlined"
        }}
        instanceId="keywordsselect"
        components={components}
        value={single ? single : null}
        isClearable={true}
      />
    </div>
  );
}

const mapStateToProps = state => ({
  buildings: state.building.buildings || [],
  districts: state.district.districts ? state.district.districts : [],
  streets: state.street.streets ? state.street.streets : []
});

const mapDispatchToProps = dispatch => {
  return {
    listBuildings: graphqlvariable => {
      dispatch(listBuildings(graphqlvariable));
    },
    listStreets: graphqlvariable => dispatch(listStreets(graphqlvariable))
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(KeywordsAutoComplete);
