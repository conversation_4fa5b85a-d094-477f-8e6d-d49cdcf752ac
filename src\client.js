/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import "whatwg-fetch";
import React from "react";
import ReactDOM from "react-dom";
import { ThemeProvider } from "@material-ui/styles";
import { Provider } from "react-redux";
import deepForceUpdate from "react-deep-force-update";
import queryString from "query-string";
import { createPath } from "history";
import MobileDetect from "mobile-detect";
import App from "./components/App";
import createFetch from "./core/createFetch";
import configureStore from "./store/configureStore";
import history from "./core/history";
import { updateMeta } from "./core/DOMUtils";
import router from "./core/router";
import ConnectedIntlProvider from "./ConnectedIntlProvider";

import theme from "./core/theme";
import lightboxStyle from "react-image-lightbox/style.css";
import smoothscroll from "smoothscroll-polyfill";
import { enableDesktopView } from "./config";

// Universal HTTP client
const clientfetch = createFetch(fetch, {
  baseUrl: window.App.apiUrl,
  // access_token: window.App.state.auth.user
  //   ? window.App.state.auth.user.oauth
  //   : ""
});

const md = new MobileDetect(window.navigator.userAgent);
// Global (context) variables that can be easily accessed from any React component
// https://facebook.github.io/react/docs/context.html
const context = {
  // Enables critical path CSS rendering
  // https://github.com/kriasoft/isomorphic-style-loader
  insertCss: (...styles) => {
    // eslint-disable-next-line no-underscore-dangle
    lightboxStyle._insertCss();
    const removeCss = styles.map((x) => x._insertCss());
    return () => {
      removeCss.forEach((f) => f());
    };
  },
  // Universal HTTP client
  fetch: clientfetch,
  // Initialize a new Redux store
  // http://redux.js.org/docs/basics/UsageWithReact.html
  store: configureStore(window.App.state, { fetch: clientfetch, history }),
  storeSubscription: null,
  browserDetect: md.mobile() != null ? "mobile" : "desktop",
  windowHeight: window.innerHeight,
};

const container = document.getElementById("app");
let currentLocation = history.location;
let appInstance;

const scrollPositionsHistory = {};

// Re-render the app when window.location changes
async function onLocationChange(location, action) {
  // Remember the latest scroll position for the previous location
  scrollPositionsHistory[currentLocation.key] = {
    scrollX: window.pageXOffset,
    scrollY: window.pageYOffset,
  };
  // Delete stored scroll position for next page if any
  if (action === "PUSH") {
    delete scrollPositionsHistory[location.key];
  }
  currentLocation = location;

  const isInitialRender = !action;
  try {
    context.pathname = location.pathname;
    context.query = queryString.parse(location.search);

    // Traverses the list of routes in the order they are defined until
    // it finds the first route that matches provided URL path string
    // and whose action method returns anything other than `undefined`.

    let route = await router("mobile").resolve(context);
    if (enableDesktopView == "true") {
      if (md.mobile() != null) {
        // do nothing already loaded mobile router
      } else {
        // load desktop router
        route = await router("desktop").resolve(context);
      }
    }

    // Prevent multiple page renders during the routing process
    if (currentLocation.key !== location.key) {
      return;
    }

    if (route.redirect) {
      history.replace(route.redirect);
      return;
    }

    const jssStyles = document.querySelector("#jss-server-side");
    if (jssStyles) {
      jssStyles.parentNode.removeChild(jssStyles);
    }

    const locale = window.App.locale;

    const renderReactApp = isInitialRender ? ReactDOM.hydrate : ReactDOM.render;
    appInstance = renderReactApp(
      <Provider store={context.store}>
        <ConnectedIntlProvider locale={locale}>
          <App context={context}>
            <ThemeProvider theme={theme}>{route.component}</ThemeProvider>
          </App>
        </ConnectedIntlProvider>
      </Provider>,
      container,
      () => {
        if (isInitialRender) {
          // Switch off the native scroll restoration behavior and handle it manually
          // https://developers.google.com/web/updates/2015/09/history-api-scroll-restoration
          if (window.history && "scrollRestoration" in window.history) {
            window.history.scrollRestoration = "manual";
          }

          const elem = document.getElementById("css");
          if (elem) elem.parentNode.removeChild(elem);
          return;
        }

        document.title = route.title;

        updateMeta("description", route.description);
        // Update necessary tags in <head> at runtime here, ie:
        // updateMeta('keywords', route.keywords);
        // updateCustomMeta('og:url', route.canonicalUrl);
        // updateCustomMeta('og:image', route.imageUrl);
        // updateLink('canonical', route.canonicalUrl);
        // etc.

        let scrollX = 0;
        let scrollY = 0;
        const pos = scrollPositionsHistory[location.key];
        if (pos) {
          scrollX = pos.scrollX;
          scrollY = pos.scrollY;
        } else {
          const targetHash = location.hash.substr(1);
          if (targetHash) {
            const target = document.getElementById(targetHash);
            if (target) {
              scrollY = window.pageYOffset + target.getBoundingClientRect().top;
            }
          }
        }

        // Restore the scroll position if it was saved into the state
        // or scroll to the given #hash anchor
        // or scroll to top of the page
        window.scrollTo(scrollX, scrollY);

        // Google Analytics tracking. Don't send 'pageview' event after
        // the initial rendering, as it was already sent
        if (window.ga) {
          window.ga("send", "pageview", createPath(location));
        }
      },
    );
  } catch (error) {
    if (__DEV__) {
      throw error;
    }

    console.error(error);

    // Do a full page reload if error occurs during client-side navigation
    if (!isInitialRender && currentLocation.key === location.key) {
      console.error("RSK will reload your page after error");
      window.location.reload();
    }
  }
}

// Handle client-side navigation by using HTML5 History API
// For more information visit https://github.com/mjackson/history#readme
history.listen(onLocationChange);
onLocationChange(currentLocation);
// kick off the polyfill!
smoothscroll.polyfill();

// Enable Hot Module Replacement (HMR)
if (module.hot) {
  module.hot.accept("./core/router", () => {
    if (appInstance && appInstance.updater.isMounted(appInstance)) {
      // Force-update the whole tree, including components that refuse to update
      deepForceUpdate(appInstance);
    }

    onLocationChange(currentLocation);
  });
}
