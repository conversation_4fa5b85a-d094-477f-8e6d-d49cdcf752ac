import {
  LIST_TX_REPORTS_START,
  LIST_TX_REPORTS_SUCCESS,
  LIST_TX_REPORTS_NULL_SUCCESS,
  LIST_TX_REPORTS_ERROR,
  CLEAR_TX_REPORTS
} from "../constants/listTxReport";

const initialState = {
  listed: false,
  listing: false,
  listTxReports: [],
};

export default function listTxReport(state = initialState, action) {
  switch (action.type) {
    case LIST_TX_REPORTS_START:
      return {
        ...state,
        listed: false,
        listing: true,
        listTxReports: [],
      };
    case LIST_TX_REPORTS_SUCCESS:
      return {
        ...state,
        listed: true,
        listing: false,
        listTxReports: action.payload.data.largeAmountTxReports || []
      };
    case LIST_TX_REPORTS_NULL_SUCCESS:
      return {
        ...state,
        listing: false,
        listed: true,
        listTxReports: action.payload.data.largeAmountTxReports || []
      };
    case LIST_TX_REPORTS_ERROR:
      return {
        ...state,
        listed: false,
        listing: false,
        error: action.payload.error
      };
    case CLEAR_TX_REPORTS:
      return {
        ...state,
        listed: false,
        listing: false,
        listTxReports: [],
        queryvariables: null,
        error: null
      };
    default:
      return state;
  }
}
