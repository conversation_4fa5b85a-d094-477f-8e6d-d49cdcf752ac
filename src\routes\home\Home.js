/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import _ from "lodash";
import Grid from "@material-ui/core/Grid";
import { FormattedMessage, injectIntl } from "react-intl";
import Cookies from "universal-cookie";
import HelpIcon from "@material-ui/icons/Help";
import VideocamIcon from '@material-ui/icons/Videocam';
import PermMediaIcon from '@material-ui/icons/PermMedia';
import RssFeedIcon from "@material-ui/icons/RssFeed";
import FavoriteIcon from "@material-ui/icons/Favorite";
import PictureAsPdfIcon from "@material-ui/icons/PictureAsPdf";
import ApartmentIcon from "@material-ui/icons/Apartment";
import HomeWork from "@material-ui/icons/HomeWork";
import Work from "@material-ui/icons/Work";
import AssignmentIcon from '@material-ui/icons/Assignment';

import ProfilePhoto from "../../components/common/ProfilePhoto";
import StandardSvg from "../../components/common/StandardSvg";
import Switch from "../../components/common/Switch";
import buildingSvg from "../../files/icons/building.svg";
import transactionSvg from "../../files/icons/transaction.svg";
import personSvg from "../../files/icons/person.svg";
import IndexButton from "../../components/common/IndexButton";
import Link from "../../components/Link";
import {
  link,
  enableTranslation,
  sbu,
  permissions,
  generalDailyQuota,
  api,
} from "../../config";
import { getUnlockCount } from "../../actions/stocklist";
import { listMarkStock, listmyFavorite, clearStock } from "../../actions/stock";
import {
  listEmployees,
  clearEmployees,
  getEmployeePermission,
} from "../../actions/employee";
import {
  getDefaultSearchResultURL,
  getDefaultTxResultURL,
  getSearchResultURL,
  getTransactionSearchResultURL,
  getCookieDomain,
} from "../../helper/generalHelper";
import { getJweToken, setTokensAndPermission } from "../../actions/auth";
import { PERMISSIONS } from "@/constants/auth";
import { gtagHandler, addActivityLog } from "../../actions/log";
import {enableWWWScore, deployDomain} from "@/config";

const { VIEW_KOL, APPROVE_MEDIA } = PERMISSIONS;
// import fetch from "node-fetch";

const styles = (theme) => ({
  root: {},
  link: {
    color: "inherit",
    textDecoration: "none",
  },
  container: {
    // margin: "0 auto",
    padding: "0 5vw",
    backgroundColor: theme.base.backgroundColor,
    minHeight: "calc(100vh - 52px)",
    // position: "fixed",
    overflowY: "hidden",
  },
  MainBtnsContainer: {
    margin: "2vh 0",
  },
  indexBtn: {
    margin: "0.1vh auto",
  },
  btnIcon: {
    color: "#000000",
    minWidth: 100,
    height: 50,
    filter: "invert(100%)",
  },
  btnIconMui: {
    width: 50,
    height: 50,
  },
  profilePhoto: {
    margin: "0 auto",
    height: 50,
    width: 50,
  },
  quotaText: {
    height: 50,
    lineHeight: "50px",
    fontSize: "1.8em",
    "& span": {
      fontSize: "2em",
    },
  },
  logOutRow: {
    textAlign: "center",
    padding: "2vh 0",
  },
  LangContainer: {
    textAlign: "center",
    marginBottom: "2vh",
  },
  myFavoriteCount: {
    width: "20px",
    height: "20px",
    color: "#FFF",
    fontSize: "1.5em",
    textAlign: "center",
    lineHeight: "20px",
    borderRadius: "100%",
    backgroundColor: "#EC1F26",
    padding: "1.5vw",
    position: "absolute",
    bottom: "40px",
    right: "40px",
  },
});

class Home extends React.Component {
  static propTypes = {
    // news: PropTypes.arrayOf(
    //   PropTypes.shape({
    //     title: PropTypes.string.isRequired,
    //     link: PropTypes.string.isRequired,
    //     content: PropTypes.string,
    //   }),
    // ).isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      profilePhotoUrl: personSvg,
    };
  }

  componentDidMount() {
    const { userInfo, employee } = this.props;

    const cookies = new Cookies();
    let homepageCounter = cookies.get("homepageCounter");
    homepageCounter++;
    cookies.set("homepageCounter", homepageCounter, {
      domain: getCookieDomain(deployDomain),
    });

    if (userInfo && userInfo.emp_id) {
      this.setState({
        profilePhotoUrl: `${link.profilePhoto.prefix}${userInfo.emp_id}${link.profilePhoto.suffix}`,
      });

      // fetch user data from employee graphql only if it has not been fetched
      if (!employee || !employee.firstname_en)
        this.props.listEmployees({ emp_id: [userInfo.emp_id] });

      this.props.getEmployeePermission();
    }

    this.props.getUnlockCount();
    this.props.listmyFavorite();
    this.props.listMarkStocks();

    // clear stock detail to prevent the scrolling action when go to search page from home
    this.props.clearStockDetail();

    this.props.getJweToken();

    if (homepageCounter === 1) {
      this.props.gtagHandler("Login");
    }
  }

  componentDidUpdate() {
    const { employee, headerRef } = this.props;

    if (employee && employee.firstname_en && headerRef.current)
      headerRef.current.changeHeader({ title: `Hi ${employee.firstname_en}!`, subTitle: "" });
  }

  componentWillUnmount() {
    const { headerRef } = this.props;
    if (headerRef.current)
      headerRef.current.changeHeader({ title: "", subTitle: "" });
  }

  photoHandleError = () => {
    this.setState({ profilePhotoUrl: personSvg });
  };

  onChangeLang = (locale) => {
    const cookies = new Cookies();
    cookies.set("locale", locale);
    window.location.reload();
  };

  handleAddActivityLog = (id) => {
    this.props.addActivityLog(`header.${id}`, "read");
  }

  render() {
    const {
      classes,
      userInfo,
      max,
      count,
      queryvariables,
      txqueryvariables,
      selectedData,
      txselectedData,
      favoriteStockIds,
      jweToken,
      intl,
      employee,
      permissions,
      setTokensAndPermission,
    } = this.props;

    const profileLink =
      userInfo && userInfo.email
        ? `${link.profilePage.prefix}${userInfo.email.split("@")[0]}`
        : "";

    const buttons = [
      {
        id: "home.profileLink",
        name: <FormattedMessage id="home.profileLink" />,
        ...(enableWWWScore ? { link: "/my-www-stock" } : { aLink: profileLink }),
        icon: (
          <ProfilePhoto
            className={classes.profilePhoto}
            src={this.state.profilePhotoUrl}
            onError={this.photoHandleError}
          />
        ),
      },
      {
        id: "home.quotaText",
        name: <FormattedMessage id="home.quotaText" />,
        link: "/unlocked",
        icon: (
          <div className={classes.quotaText}>
            <span>{max - count}</span>/{max}
          </div>
        ),
      },
      {
        id: "home.stock",
        name: <FormattedMessage id="home.stock" />,
        link: !_.isEmpty(queryvariables)
          ? getSearchResultURL(queryvariables, selectedData)
          : getDefaultSearchResultURL(),
        icon: <StandardSvg className={classes.btnIcon} src={buildingSvg} />,
      },
      {
        id: "home.myfavorite",
        name: <FormattedMessage id="home.myfavorite" />,
        link: "/myFavorite",
        icon: (
          <div>
            <FavoriteIcon className={classes.btnIconMui} />
            <span className={classes.myFavoriteCount}>
              {favoriteStockIds.length}
            </span>
          </div>
        ),
      },
      {
        id: "home.transaction",
        name: <FormattedMessage id="home.transaction" />,
        link: getDefaultTxResultURL(),
        // link: txqueryvariables ? getTransactionSearchResultURL(txqueryvariables, txselectedData) : getDefaultTxResultURL(),
        icon: <StandardSvg className={classes.btnIcon} src={transactionSvg} />,
      },
      {
        id: "home.company",
        name: <FormattedMessage id="home.company" />,
        link: "/companySearch",
        icon: <Work className={classes.btnIcon} />,
      },
      {
        id: "home.publicmessage",
        name: <FormattedMessage id="home.publicmessage" />,
        link: "/messageCenter",
        icon: <RssFeedIcon className={classes.btnIconMui} />,
      },
      {
        id: "proposal.proposal",
        name: <FormattedMessage id="proposal.proposal" />,
        link: "/proposal/list",
        icon: <PictureAsPdfIcon className={classes.btnIconMui} />,
      },
      {
        id: "home.report",
        name: <FormattedMessage id="home.report" />,
        link: "/report",
        icon: <AssignmentIcon className={classes.btnIconMui} />,
      },
      {
        id: "home.firstHandStock",
        name: <FormattedMessage id="home.firstHandStock" />,
        icon: <HomeWork className={classes.btnIconMui} />,
        onClick: function () {
          var requestBody = {
            casAccessToken: localStorage.getItem("casAccessToken"),
            casRefreshToken: localStorage.getItem("casRefreshToken"),
            source: "m1tablet",
            kid: "SalesKit",
          };

          fetch("/castoken-refresh", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              refresh_token: localStorage.getItem("casRefreshToken"),
            }),
          })
            .then((refreshTokenResponse) => refreshTokenResponse.json())
            .then((refreshTokenData) => {
              if (refreshTokenData.valid) {

                const payload = {
                  ...refreshTokenData,
                  config: { bypassPermission: true }
                };
                setTokensAndPermission(payload);

                fetch("/jweToken", {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify(requestBody),
                })
                  .then((encryptTokenResponse) => encryptTokenResponse.json())
                  .then((encryptTokenData) => {
                    let a = document.createElement("a");
                    document.body.appendChild(a);
                    a.style = "display: none";
                    a.href = api.firstHand + encryptTokenData.jweToken;
                    a.target = "_blank";
                    a.click();
                    document.body.removeChild(a);
                  });
              }
            })
            .catch((e) => {
              console.log("error refreshing cas token", e);
            });
        },
      },
      // {
      //   name: <FormattedMessage id="home.shelf" />,
      //   icon: <ApartmentIcon className={classes.btnIconMui} />,
      //   onClick: async () => {
      //     await this.props.getJweToken();
      //     let a = document.createElement("a");
      //     document.body.appendChild(a);
      //     a.style = "display: none";
      //     a.href = `https://mcf-dev.outsystemsenterprise.com/dsMRMSMobile/joseLogin?token=${jweToken}`;
      //     a.target = '_blank';
      //     a.click();
      //     document.body.removeChild(a);
      //   }
      // },
      // ...(permissions[VIEW_KOL] ? [{
      //   id: "home.kol.video",
      //   name: <FormattedMessage id="home.kol.video" />,
      //   link: "/kolVideo",
      //   icon: <VideocamIcon className={classes.btnIconMui} />,
      // }] : []),
      ...(
        permissions[APPROVE_MEDIA] &&
        (
          employee?.auth?.role?.includes("DISTRICT MANAGER") ||
          employee?.auth?.role?.includes("SALES MANAGER") ||
          employee?.auth?.role?.includes("MARKETING") ||
          employee?.auth?.role?.includes("IT")
        ) ? [{
        id: "home.approve.media",
        name: <FormattedMessage id="home.approve.media" />,
        link: "/approveMedia",
        icon: <PermMediaIcon className={classes.btnIconMui} />,
      }] : []),
      {
        id: "home.help",
        name: <FormattedMessage id="home.help" />,
        link: "/help",
        icon: <HelpIcon className={classes.btnIconMui} />,
      },

      // {
      //   name: "Sales Kit",
      //   link: "/search",
      //   icon: <StandardSvg className={classes.btnIcon} src={salesKitSvg} />,
      //   disabled: true
      // },
      // {
      //   name: "Media",
      //   link: "",
      //   icon: <StandardSvg className={classes.btnIcon} src={mediaSvg} />,
      //   disabled: true
      // }
    ];

    return (
      <div className={classes.root}>
        <div className={classes.container}>
          {/*<Link className={classes.link} to={"/messageCenter"}>*/}
          {/*  <PublicMsg />*/}
          {/*</Link>*/}

          <div className={classes.MainBtnsContainer}>
            <Grid container spacing={1}>
              {buttons.map(
                (v, i) =>
                  !v.hide && (
                    <Grid item xs={6} key={i}>
                      {v.link && !v.disabled ? (
                        <Link className={classes.link} to={v.link} onClick={() => this.handleAddActivityLog(v.id)}>
                          <IndexButton
                            className={classes.indexBtn}
                            icon={v.icon}
                            disabled={v.disabled}
                          >
                            {v.name}
                          </IndexButton>
                        </Link>
                      ) : v.aLink && !v.disabled ? (
                        <a
                          className={classes.link}
                          href={v.aLink}
                          target="_blank"
                          onClick={() => this.handleAddActivityLog(v.id)}
                        >
                          <IndexButton
                            className={classes.indexBtn}
                            icon={v.icon}
                            disabled={v.disabled}
                          >
                            {v.name}
                          </IndexButton>
                        </a>
                      ) : (
                        <IndexButton
                          className={classes.indexBtn}
                          icon={v.icon}
                          disabled={v.disabled}
                          onClick={() => {
                            this.handleAddActivityLog(v.id);
                            v.onClick();
                          }}
                        >
                          {v.name}
                        </IndexButton>
                      )}
                    </Grid>
                  ),
              )}
            </Grid>
          </div>

          <div className={classes.LangContainer}>
            {enableTranslation === "true" && (
              <Switch
                value={intl.locale}
                textL="ENG"
                textR="中"
                valleft="en"
                valright="zh"
                onChange={() => {
                  intl.locale === "en" && this.onChangeLang("zh");
                  intl.locale === "zh" && this.onChangeLang("en");
                }}
              />
            )}

            {/* <button
               onClick={() => {
                 this.onChangeLang("en");
               }}
             >
               en
             </button>
             <button
               onClick={() => {
                 this.onChangeLang("zh");
               }}
             >
               zh
             </button> */}
          </div>

          {/* <div className={classes.logOutRow}>
             <a className={classes.link} href="/logout">
               <DialogButton>Log Out</DialogButton>
             </a>
           </div> */}
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    userInfo:
      state.auth &&
      state.auth.user &&
      state.auth.user.login &&
      state.auth.user.login.info
        ? state.auth.user.login.info
        : {},
    count: state.stocklist.count ? state.stocklist.count : 0,
    max: !_.isEmpty(state.employee.permissions)
      ? !_.isNil(
          state.employee.permissions[permissions.PERMISSION_VIEW_STOCK_QUOTA],
        )
        ? state.employee.permissions[permissions.PERMISSION_VIEW_STOCK_QUOTA]
        : 0
      : generalDailyQuota, //state.stocklist.max ? state.stocklist.max : 30,
    employee:
      state.employee.employees && state.employee.employees[0]
        ? state.employee.employees[0]
        : {},
    permissions: state.employee.permissions ? state.employee.permissions : {},
    queryvariables: state.stocklist.queryvariables
      ? state.stocklist.queryvariables
      : null,
    txqueryvariables: state.transaction.queryvariables
      ? state.transaction.queryvariables
      : null,
    selectedData: state.stocklist.selectedData
      ? state.stocklist.selectedData
      : {},
    txselectedData: state.transaction.selectedData
      ? state.transaction.selectedData
      : {},
    favoriteStockIds: state.stock.favoriteStockIds
      ? state.stock.favoriteStockIds
      : [],
    jweToken: state.auth.jweToken ? state.auth.jweToken : null,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    getUnlockCount: () => dispatch(getUnlockCount()),
    listmyFavorite: () => dispatch(listmyFavorite()),
    listMarkStocks: () => dispatch(listMarkStock()),
    listEmployees: (...args) => dispatch(listEmployees(...args)),
    clearEmployees: () => dispatch(clearEmployees()),
    clearStockDetail: () => dispatch(clearStock()),
    getJweToken: () => dispatch(getJweToken()),
    getEmployeePermission: () => dispatch(getEmployeePermission()),
    setTokensAndPermission: (...args) => dispatch(setTokensAndPermission(...args)),
    addActivityLog: (...args) => dispatch(addActivityLog(...args)),
    gtagHandler: (...args) => dispatch(gtagHandler(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(Home)));
