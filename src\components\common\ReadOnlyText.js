import React from "react";
import { TextField } from "@material-ui/core";
import { withStyles } from "@material-ui/core/styles";
import clsx from "clsx";

export default withStyles({
  root: {
    width: "100%",
    "& .MuiInputBase-root": {
      color: "#000000",
    },
    "& .MuiInput-underline:before": {
      border: "none !important",
    },
    "& .MuiFormLabel-root": {
      color: "rgba(0, 0, 0, 0.54)",
    },
  },
})(({ classes, className, ...props }) => (
  <TextField className={clsx(classes.root, className)} disabled {...props} />
));
