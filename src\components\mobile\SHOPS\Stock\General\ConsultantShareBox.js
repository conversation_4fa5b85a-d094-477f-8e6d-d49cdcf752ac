import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import FieldValArrBox from "../../../../common/FieldValArrBox";
import { injectIntl } from "react-intl";

// We can inject some CSS into the DOM.
const styles = {
  root: {

  },
};

function ConsultantShareBox(props) {
  const {
    classes,
    employee = "---",
    dateTime = "---",
    remarks = "---",
    intl,
    ...others
  } = props;

  const items = [
    {
      field: intl.formatMessage({ id: "stock.handledby" }),
      val: employee,
    },
    {
      field: intl.formatMessage({ id: "stock.date" }),
      val: dateTime,
    },
    {
      field: intl.formatMessage({ id: "stock.remarks" }),
      val: remarks,
    },
  ];

  return (
    <FieldValArrBox
      className={classes.root}
      items={items}
      {...others}
    />
  );
}

ConsultantShareBox.propTypes = {
  classes: PropTypes.object.isRequired,
  employee: PropTypes.string,
  dateTime: PropTypes.string,
  remarks: PropTypes.string,
};

export default withStyles(styles)(injectIntl(ConsultantShareBox));
