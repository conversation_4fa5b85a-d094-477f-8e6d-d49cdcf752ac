FROM node:16.13.1-alpine as module-install

WORKDIR /app

COPY package.json /app/package.json
COPY yarn.lock /app/yarn.lock

# Fix an issue - Timeout error on yarn install
RUN yarn config set network-timeout 300000

RUN yarn install

###### Stop running steps after this stage in development
###### Specify --target=serve when building the image
FROM node:16.13.1-alpine as serve

COPY --from=module-install --chown=node:node /app/node_modules/ /home/<USER>/node_modules

WORKDIR /home/<USER>
COPY . .

RUN chmod +x entrypoint-eks.sh

###### Run only in production. Specify --target=prod-build in build
FROM node:16.13.1-alpine as prod-build

RUN apk add findutils

WORKDIR /home/<USER>
COPY --from=serve --chown=node:node /home/<USER>/ /home/<USER>

RUN eval "$(egrep "^[^#;]" .env | xargs -d'\n' -n1 | sed 's/^/export /')" && yarn run build --release
