import { api, cloudwatchlog, requestTimeout } from "../config";
import <PERSON>bort<PERSON><PERSON>roller from "abort-controller";
import createGetQuery from "../actions/query";
import moment from "moment";
import Cookies from "universal-cookie";

function createGraphqlRequest(fetch) {
  return function graphqlRequest(url, query, variables, options) {
    let body = {
      query,
    };
    if (variables) body.variables = variables;

    return createUniversalRequest(fetch)(
      url,
      {
        method: "POST",
        headers: {
          ...(options && options.headers),
        },
        body: JSON.stringify(body),
      },
      options,
    );
  };
}

function createUniversalRequest(fetch) {
  return async function universalRequest(url, reqConfig, options) {
    const timeoutMS =
      options && options.timeoutMS
        ? parseInt(options.timeoutMS)
        : parseInt(requestTimeout);

    //ref: https://www.npmjs.com/package/node-fetch#request-cancellation-with-abortsignal
    const controller = new AbortController();
    const timeout = setTimeout(() => {
      controller.abort();
    }, timeoutMS);

    try {
      // console.log(url, reqConfig, controller.signal);
      const resp = await fetch(url, {
        ...reqConfig,
        signal: controller.signal,
      });
      resp.status == 401 && window.location.replace("/logout");

      if (resp.status !== 200) throw new Error(resp.statusText);
      return resp.json();
    } catch (e) {
      if (e.name === "AbortError") {
        throw new Error("Request timeout.");
      }
      console.log(e);
      throw e;
    } finally {
      clearTimeout(timeout);
    }
  };
}

function createTakeLog(fetch) {
  return async function takeLog(stream, msg, fetch, isInternal, ...token) {
    // check if it is take log for internal
    // const url = isInternal ? "/addLog" : cloudwatchlog.url;
    const url = "/addLog";
    const cookies = new Cookies();
    let logMsg = JSON.parse(msg);
    logMsg["user-agent"] = cookies.get("user-agent");

    // one-time header when login is successful
    const LoginHeaders = new Headers({
      "Content-Type": "application/json",
      Authorization: token[0],
    });

    // try {
    //   await fetch(url, {
    //     method: "POST",
    //     headers: LoginHeaders,
    //     body: JSON.stringify({
    //       logGroup: cloudwatchlog.logGroup,
    //       logStream: `${stream}-${moment().format("YYYY-MM-DD")}`,
    //       msg: JSON.stringify(logMsg),
    //       isInternal
    //     }),
    //   });
    // } catch (e) {
    //   console.log(e);
    // }

    return createUniversalRequest(fetch)(
      url,
      {
        method: "POST",
        headers: LoginHeaders,
        body: JSON.stringify({
          logGroup: cloudwatchlog.logGroup,
          logStream: `${stream}-${moment().format("YYYY-MM-DD")}`,
          msg: JSON.stringify(logMsg),
          isInternal
        }),
      },
    );
  };
}

function createDelay() {
  return async function delay(t, val) {
    return new Promise(function (resolve) {
      setTimeout(function () {
        resolve(val);
      }, t);
    });
  };
}

export default function createHelpers({ fetch, history }) {
  const getQuery = createGetQuery();

  return {
    fetch,
    history,
    graphqlRequest: createGraphqlRequest(fetch),
    universalRequest: createUniversalRequest(fetch),
    api,
    getQuery,
    takeLog: createTakeLog(fetch),
    delay: createDelay(),
  };
}
