import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import { getLang<PERSON>ey, numberComma } from "../../../../../helper/generalHelper";
import FieldVal from "../../../../common/FieldVal";
import { injectIntl } from "react-intl";

const styles = (theme) => ({
  lmrAlign: {
    padding: "1vw 2vw",
    "& > :nth-child(3n+2)": {
      textAlign: "center",
    },
    "& > :nth-child(3n)": {
      textAlign: "right",
    },
  },
});

class Structure extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langkey = getLangKey(intl);

    const lobby =
      detail.lobby && detail.lobby[langkey] ? detail.lobby[langkey] : "---";
    const wall =
      detail.wall && detail.wall[langkey] ? detail.wall[langkey] : "---";
    const ceiling = detail.ceiling || "---";

    // count each type of lifts
    const lifts = detail.lifts ? detail.lifts : [];
    let cargoLift = "---";
    let passengerLift = "---";
    let carLift = "---";
    let otherLifts = "---";
    lifts.map((v, i) => {
      switch (v.type) {
        case "Cargo":
          cargoLift = v.quantity;
          break;
        case "Passenger":
          passengerLift = v.quantity;
          break;
        case "Car":
          carLift = v.quantity;
          break;
        case "Others":
          otherLifts = v.quantity;
          break;
        default:
          break;
      }
    });

    const entrances = detail.entrances || [];
    let container = [];
    for (let i = 0; i < entrances.length; i++) {
      let containerData =
        entrances[i]["container"] && entrances[i]["container"][langkey] !== null
          ? entrances[i]["container"][langkey]
          : "---";

      const haveloadingBay =
        entrances[i]["haveLoadingBay"] && containerData !== "---"
          ? entrances[i]["haveLoadingBay"]
          : false;
      if (haveloadingBay) {
        containerData +=
          " " +
          intl.formatMessage({
            id: "building.loadingbay",
          });
      }
      container.push(containerData);
    }

    const totalFloor = detail.totalFloor || "---";
    const efficiency = detail.efficiency ? detail.efficiency + "%" : "---";

    const haveCarParkBool = detail.carParkInfo
      ? detail.carParkInfo.haveCarPark
      : null;
    const haveCarPark =
      haveCarParkBool === true
        ? "Yes"
        : haveCarParkBool === false
        ? "No"
        : "---";

    let structureMapping = {
      [intl.formatMessage({
        id: "building.lobby",
      })]: { value: lobby, xs: 4 },
      [intl.formatMessage({
        id: "building.wall",
      })]: { value: wall, xs: 4 },
      [intl.formatMessage({
        id: "building.totalfloor",
      })]: { value: totalFloor, xs: 4 },
      [intl.formatMessage({
        id: "building.container",
      }) + " 1"]: { value: container[0] || "---", xs: 4 },
      [intl.formatMessage({
        id: "building.container",
      }) + " 2"]: { value: container[1] || "---", xs: 4 },
      [intl.formatMessage({
        id: "building.passengerlift",
      })]: { value: passengerLift, xs: 4 },
      [intl.formatMessage({
        id: "building.cargolift",
      })]: { value: cargoLift, xs: 4 },
      [intl.formatMessage({
        id: "building.carlift",
      })]: { value: carLift, xs: 4 },
      [intl.formatMessage({
        id: "building.otherlift",
      })]: { value: otherLifts, xs: 4 },
      [intl.formatMessage({
        id: "building.efficiency",
      })]: { value: efficiency, xs: 4 },
      [intl.formatMessage({
        id: "building.carpark",
      })]: { value: haveCarPark, xs: 4 },
    };

    return (
      <DetailBoxSection
        text={intl.formatMessage({
          id: "building.structure",
        })}
        expandable={true}
        isExpanding={true}
      >
        <Grid container spacing={2} className={classes.lmrAlign}>
          {Object.keys(structureMapping).map((v, i) => (
            <Grid item xs={structureMapping[v].xs} key={v}>
              <FieldVal field={v}>{structureMapping[v].value}</FieldVal>
            </Grid>
          ))}
        </Grid>
      </DetailBoxSection>
    );
  }
}

export default withStyles(styles)(injectIntl(Structure));
