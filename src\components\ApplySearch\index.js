import React, { useEffect, useState } from "react";
import { makeStyles } from "@material-ui/core/styles";
import { connect } from "react-redux";
import { FormattedMessage, injectIntl } from "react-intl";
import _ from "lodash";
import { Box, DialogContent } from "@material-ui/core";
import { formValueSelector, getFormSyncErrors, submit } from "redux-form";
import PropTypes from "prop-types";

import DetailTabPanelFrame from "../common/DetailTabPanelFrame";
import ApplySearchForm from "./form";
import { listEmployees } from "@/actions/employee";
import { clearApplySearch, queryDDType } from "@/actions/landsearch";
import { takeLandsearchLog } from "@/actions/log";
import InputErrorDialog from "../common/InputErrorDialog";
import CustomizedDialog from "../common/Dialog";
import ErrorsDialog from "../common/ErrorsDialog";
import SearchConsentDialog from "../common/SearchConsentDialog";

const useStyles = makeStyles({
  container: {
    maxHeight: "calc(100% - 60px - 2vw)",
    overflow: "auto",
  },
  error: {
    color: "#ff3333",
  },
});

function ApplySearch({
  employee,
  empId,
  stockId,
  getEmployee,
  getDDType,
  takeLog,
  submitForm,
  clearApplySearch,
  errors,
  listingEmployee,
  listingDDTypes,

  applying,
  applyOk,
  applyError,
  searchType,
  intl,
}) {
  const classes = useStyles();
  const [showConsent, setShowConsent] = useState(false);
  const [showInputError, setShowInputError] = useState(false);

  useEffect(() => {
    if (_.isNil(employee)) {
      getEmployee({ emp_id: [empId] });
    }
    getDDType();
  }, []);

  const sendLog = (
    callback,
    requireHkId = false,
    log_seq = null,
    status = null,
  ) => takeLog(stockId, requireHkId, log_seq, status).then(callback);

  const renderConsentContent = (logRes) => (
    <>
      <p>就此次查冊申請，本人謹此確認：</p>
      <p>
        (i) 同意僱主公司披露本人的姓名[
        {_.get(logRes, "emp_eng_name")}]及身分證明文件編號[
        {_.get(logRes, "hkid")}
        ]予公司以提供予土地註冊處作查冊申請；
      </p>
      <p>
        (ii) 無意及不會違反《個人資料(私隱)條例》(第 486 章) (《私隱條例》)
        的情況下使用透過查冊獲取的個人資料(“該等資料”)；
      </p>
      <p>
        (iii)
        無意及不會將該等資料用於與備存及供公眾人士查閱相關紀錄的宗旨無關之目的；及
      </p>
      <p>
        (iv)
        明白本人的個人資料可能會在《私隱條例》許可下被披露或轉交予執法機關。
      </p>
    </>
  );

  const hasData = !_.isNil(employee);
  return (
    <DetailTabPanelFrame
      wrapperProps={{ className: classes.container }}
      showBg={false}
      hasData={hasData}
      listing={listingEmployee || listingDDTypes}
      listed={!listingEmployee && !listingDDTypes}
      bottomButtons={[
        {
          label: <FormattedMessage id="stock.applySearch.confirm" />,
          onClick: () => {
            if (!_.isEmpty(errors)) {
              setShowInputError(true);
            } else if (searchType === "OP") {
              // dont need consent when applying for OP search
              submitForm();
            } else {
              setShowConsent(true);
            }
          },
        },
      ]}
    >
      <Box padding="0px 5px">
        <ApplySearchForm />
      </Box>
      <SearchConsentDialog
        open={showConsent}
        handleClose={() => setShowConsent(false)}
        renderChildren={renderConsentContent}
        okMsg="確認及繼續申請"
        cancelMsg="取消申請"
        okCallback={submitForm}
        sendLog={sendLog}
        isApplyConsent
      />
      <InputErrorDialog
        open={showInputError}
        handleClose={() => setShowInputError(false)}
        errors={_.groupBy(Object.values(errors), "categoryMsgId")}
      />
      <CustomizedDialog
        open={!applying && applyOk}
        handleClose={() => clearApplySearch(true)}
      >
        <DialogContent style={{ padding: "10px 20px" }}>
          {applyOk && <FormattedMessage id="stock.applySearch.submitSuccess" />}
        </DialogContent>
      </CustomizedDialog>
      <ErrorsDialog
        errors={_.map(
          Array.isArray(applyError) ? applyError : [applyError],
          (e) => ({
            message: e.message,
            btn: intl.formatMessage({ id: "common.cancel" }),
            btnHandleClick: () => clearApplySearch(false),
          }),
        )}
      />
    </DetailTabPanelFrame>
  );
}

ApplySearch.defaultProps = {
  employee: null,
  searchType: "LD",
};

ApplySearch.propTypes = {
  employee: PropTypes.object,
  empId: PropTypes.string.isRequired,
  stockId: PropTypes.number.isRequired,
  getEmployee: PropTypes.func.isRequired,
  getDDType: PropTypes.func.isRequired,
  takeLog: PropTypes.func.isRequired,
  submitForm: PropTypes.func.isRequired,
  clearApplySearch: PropTypes.func.isRequired,

  listingEmployee: PropTypes.bool.isRequired,
  listingDDTypes: PropTypes.bool.isRequired,

  applying: PropTypes.bool.isRequired,
  applyOk: PropTypes.bool.isRequired,
  applyError: PropTypes.oneOfType([PropTypes.array, PropTypes.object])
    .isRequired,

  searchType: PropTypes.string,
  errors: PropTypes.object.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapDispatchToProps = (dispatch) => ({
  getEmployee: (variable) => dispatch(listEmployees(variable)),
  getDDType: () => dispatch(queryDDType()),
  takeLog: (stockId, requireHkid, logSeq, status) =>
    dispatch(
      takeLandsearchLog({
        stockId,
        refNo: null,
        docPath: null,
        location: "APPLY LANDSEARCH",
        status,
        logSeq,
        requireHkid,
      }),
    ),
  submitForm: () => dispatch(submit("applySearch")),
  clearApplySearch: (resetForm) => dispatch(clearApplySearch(resetForm)),
});

const formSelector = formValueSelector("applySearch");

const mapStateToProps = (state) => ({
  employee: _.get(state, "employee.employees.0"),
  empId: _.get(state, "auth.user.login.info.emp_id"),
  stockId: _.get(state, "stock.detail.0.unicorn.id"),

  listingEmployee: _.get(state, "employee.listing"),
  listingDDTypes: _.get(state, "landsearch.listingDDTypes"),

  applying: _.get(state, "landsearch.applying"),
  applyOk: _.get(state, "landsearch.applyOk"),
  applyError: _.get(state, "landsearch.applyError"),

  searchType: formSelector(state, "searchType"),

  errors: getFormSyncErrors("applySearch")(state),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(ApplySearch));
