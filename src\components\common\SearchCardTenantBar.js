import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import { injectIntl } from "react-intl";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    marginTop: ".5vh",
    display: "flex",
    alignItems: "center",
  },
  name: {
    flex: "1 1 auto",
  },
  date: {
    flex: "0 0 auto",
  },
};

function SearchCardTenantBar(props) {
  const {
    classes,
    className,
    children,
    date,
    intl,
    ...other
  } = props;

  const hasTenant = children && children !== "---";
  const hasDate = date && date !== "---";

  return (
    (hasTenant || hasDate) && <div className={`${classes.root} ${className}`} {...other}>
      <div className={classes.name}>{children}</div>
      <div className={classes.date}>{intl.formatMessage({ id: "search.card.expireat" })} {date}</div>
    </div>
  );
}

SearchCardTenantBar.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  children: PropTypes.node,
  date: PropTypes.string,
};

export default withStyles(styles)(injectIntl(SearchCardTenantBar));
