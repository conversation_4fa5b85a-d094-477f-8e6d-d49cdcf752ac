import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import moment from "moment";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import UnlockDialogItem from "../../../common/UnlockDialogItem";
import DetailBoxTitle from "../../../common/DetailBoxTitle";
import DetailBoxSection from "../../../common/DetailBoxSection";
import VaryingVal from "../../../common/VaryingVal";
import GridSection from "../../../common/GridSection";
import { unlockStock } from "../../../../actions/stocklist";
import PropertyTagBar from "../../../common/PropertyTagBar";
import FavoriteButton from "../../../common/FavoriteButton";
import SearchCardTenantBar from "../../../common/SearchCardTenantBar";
import {
  numberComma,
  convertCurrency,
  paresFloorUnit,
} from "../../../../helper/generalHelper";
import history from "../../../../core/history";
import clsx from "clsx";
import { injectIntl } from "react-intl";
import { enableConsolidLandSearch, permissions, generalDailyQuota } from "../../../../config";
import _ from "lodash";

const styles = (theme) => ({
  unlockedroot: {
    padding: "1vw 2vw",
    backgroundColor: "rgba(255, 255, 200, .6)",
    borderTop: "0px solid #fff",
    boxShadow: "none",
  },
  root: {
    padding: "1vw 2vw",
    backgroundColor: "rgba(255, 255, 255, .6)",
    borderTop: "0px solid #fff",
    boxShadow: "none",
  },
  priceRow: {
    display: "flex",
    justifyContent: "space-between",
    textAlign: "right",
    padding: "1vh 0 0.5vh 0",
  },
  commonItem: {
    display: "flex",
    fontSize: "1.5em",
    padding: "0 4px",
    borderRadius: 4,
    color: "#fff",
  },
  rentItem: {
    backgroundColor: "rgba(0, 197, 197, .75)",
  },
  priceItem: {
    backgroundColor: "rgba(232, 0, 0, .75)",
  },
  greyItem: {
    backgroundColor: "rgba(132, 132, 132, .1)",
  },
  rentItemNoValue: {
    backgroundColor: "rgba(140, 190, 190, 0.2)",
  },
  priceItemNoValue: {
    backgroundColor: "rgba(200, 170, 170, 0.2)",
  },
  fieldRow: {
    display: "flex",
    justifyContent: "space-between",
    marginTop: "-1vh",
  },
  fieldItem: {
    fontSize: "1.175em",
  },
  districtCode: {
    fontSize: "0.8em",
    fontWeight: 700,
    alignSelf: "center",
  },
  status: {
    fontWeight: 700,
  },
  link: {
    textDecoration: "none",
    color: "#000",
  },
  DetailBoxSectionContent: {
    padding: 0,
  },
  totalItem: {
    display: "block",
    width: "100%",
    fontSize: "0.75em",
  },
  tagContainer: {
    padding: "0",
  },
  myfavoriteIcon: {
    padding: "0 1vw",
  },
});

class SearchResultCard extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    result: PropTypes.object,
    count: PropTypes.number,
    max: PropTypes.number,
    unlockStock: PropTypes.func.isRequired,
  };
  constructor() {
    super();
    this.state = {
      dialogOpen: false,
    };
  }

  handleClickOpenDialog = () => {
    this.setState({ dialogOpen: true });
  };

  handleCloseDialog = () => {
    this.setState({ dialogOpen: false });
  };

  goStockDetail = () => {
    history.push("/stock/" + this.props.result._id);
  };

  render() {
    const {
      classes,
      result,
      unlockStock,
      count,
      max,
      isUnlocked,
      unlockfinished,
      favoriteStockIds,
      intl,
    } = this.props;
    const statusLangKey = intl.locale === "zh" ? "statusZh" : "status";
    const currentTenantLangKey =
      intl.locale === "zh" ? "tenancyCurrentTenantZh" : "tenancyCurrentTenant";

    const id = result._id ? result._id : "---";
    const unicornid =
      result.unicorn && result.unicorn.stock ? result.unicorn.stock : "---";
    const building = result.building ? result.building : null;
    const buildingnameEn =
      building && building.nameEn ? building.nameEn : "---";
    const buildingnameZh =
      building && building.nameZh ? building.nameZh : "---";
    const districtabbr =
      result.district && result.district.abbr ? result.district.abbr : "---";
    const districtZh =
      result.district && result.district.nameZh
        ? result.district.nameZh
        : "---";
    const buildingRowEn = buildingnameEn + ", " + districtabbr;
    const buildingRowZh = buildingnameZh + ", " + districtZh;
    const titleUp = intl.locale === "zh" ? buildingRowZh : buildingRowEn;
    const titleDown = intl.locale === "zh" ? buildingRowEn : buildingRowZh;

    const status =
      result.status && result.status[statusLangKey]
        ? result.status[statusLangKey]
        : "---";
    let statusDisplay = result[statusLangKey] ? result[statusLangKey] : "---";

    let size = result.area ? numberComma(result.area) : null;
    size = size
      ? intl.locale == "zh"
        ? (size = intl.formatMessage({ id: "search.card.gross" }) + " " + size)
        : (size = size + " " + intl.formatMessage({ id: "search.card.gross" }))
      : "---";

    const currentTenant = result[currentTenantLangKey]
      ? result[currentTenantLangKey]
      : "---";
    const tenancyExpireDate = result.tenancyExpireDate
      ? moment(result.tenancyExpireDate, "x").format("YYYY-MM-DD")
      : "---";

    let askingRent = result.askingRent ? result.askingRent.average : 0;
    let askingTotalRent = result.askingRent ? result.askingRent.total + " " : 0;
    const askingRentType =
      result.askingRent && result.askingRent.trend
        ? result.askingRent.trend
        : null;
    let askingPrice = result.askingPrice ? result.askingPrice.average + " " : 0;
    let askingTotalPrice = result.askingPrice
      ? result.askingPrice.total + " "
      : 0;
    const askingPriceType =
      result.askingPrice && result.askingPrice.trend
        ? result.askingPrice.trend
        : null;

    const floor =
      result.floor && result.floor.input ? result.floor.input : "---";
    const unit = result.unit ? result.unit : "---";

    let priceContainer, leaseContainer;
    switch (status) {
      case "S": //出售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        askingRent = 0;
        askingTotalRent = 0;
        break;
      case "L": //出租
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "SL": //連約售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        askingRent = 0;
        askingTotalRent = 0;
        break;
      case "S+L": //租及售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Pending": //封盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "TEL": //電話盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "SE": //查冊盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Don't Call": //拒致電
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Sold": //已售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Leased": //已租
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Fail": //錯誤盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "History": //舊資料
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Cancel": //錯盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      default:
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
    }

    const tagTerrace = !!result.haveTerrace;
    const tagRoof = !!result.haveRoof;
    const tagCockloft = !!result.haveCockloft;
    const tagsOverride = {
      [intl.formatMessage({ id: "stock.tag.terrace" })]: tagTerrace,
      [intl.formatMessage({ id: "stock.tag.roof" })]: tagRoof,
      [intl.formatMessage({ id: "stock.tag.cockloft" })]: tagCockloft,
    };

    return (
      <div>
        <div
          onClick={isUnlocked ? this.goStockDetail : this.handleClickOpenDialog}
          className={classes.link}
        >
          <GridSection
            className={isUnlocked ? classes.unlockedroot : classes.root}
          >
            <DetailBoxTitle
              text={titleUp}
              subtitle={<span>{paresFloorUnit(floor, unit, intl)}</span>}
              rightIcon={
                <span className={classes.status}>
                  {statusDisplay}
                  <FavoriteButton
                    favoriteStockIds={favoriteStockIds}
                    mongoid={id}
                    stockid={unicornid}
                    className={classes.myfavoriteIcon}
                  />
                </span>
              }
            >
              <DetailBoxTitle
                subtitle={titleDown}
                rightIcon={<span className={classes.fieldItem}>{size}</span>}
              />

              {enableConsolidLandSearch == "true" && (
                <SearchCardTenantBar date={tenancyExpireDate}>
                  {currentTenant}
                </SearchCardTenantBar>
              )}

              <DetailBoxSection
                contentClass={classes.DetailBoxSectionContent}
                noStrike={true}
              >
                <Grid container spacing={1} className={classes.priceRow}>
                  <Grid item xs={6}>
                    <VaryingVal
                      className={clsx(
                        classes.commonItem,
                        priceContainer,
                        askingPrice <= 0 && classes.priceItemNoValue,
                      )}
                      type={askingPriceType}
                    >
                      <span>
                        {askingPrice > 0
                          ? `@${numberComma(askingPrice)}`
                          : "---"}
                        {askingPrice != 0 && askingTotalPrice != 0 ? (
                          <span className={classes.totalItem}>
                            ${convertCurrency(askingTotalPrice)}
                          </span>
                        ) : (
                          <span className={classes.totalItem}>---</span>
                        )}
                      </span>
                    </VaryingVal>
                  </Grid>

                  <Grid item xs={6}>
                    <VaryingVal
                      className={clsx(
                        classes.commonItem,
                        leaseContainer,
                        askingRent <= 0 && classes.rentItemNoValue,
                      )}
                      type={askingRentType}
                      // label={"Rent"}
                    >
                      <span>
                        {askingRent > 0 ? `@${numberComma(askingRent)}` : "---"}
                        {askingRent != 0 && askingTotalRent != 0 ? (
                          <span className={classes.totalItem}>
                            ${convertCurrency(askingTotalRent)}
                          </span>
                        ) : (
                          <span className={classes.totalItem}>---</span>
                        )}
                      </span>
                    </VaryingVal>
                  </Grid>
                </Grid>

                {/* <div className={classes.priceRow}>
                  <div>
                    {askingPrice > 0 ? (
                      <VaryingVal
                        className={clsx(classes.commonItem, classes.priceItem)}
                        type={askingPriceType}
                        // label={"Price"}
                      >
                        <div>
                          <span>@{numberComma(askingPrice)}</span>
                          {askingTotalPrice != 0 && (
                            <span className={classes.totalItem}>
                              ${convertCurrency(askingTotalPrice)}
                            </span>
                          )}
                        </div>
                      </VaryingVal>
                    ) : (
                      <div></div>
                    )}
                  </div>

                  <div>
                    {askingRent > 0 ? (
                      <VaryingVal
                        className={clsx(classes.commonItem, classes.rentItem)}
                        type={askingRentType}
                        // label={"Rent"}
                      >
                        <div>
                          <span>@{numberComma(askingRent)}</span>
                          {askingTotalRent != 0 && (
                            <span className={classes.totalItem}>
                              ${convertCurrency(askingTotalRent)}
                            </span>
                          )}
                        </div>
                      </VaryingVal>
                    ) : (
                      <div></div>
                    )}
                  </div>
                </div> */}
                <div className={classes.tagContainer}>
                  <PropertyTagBar detail={result} tagsOverride={tagsOverride} />
                </div>
              </DetailBoxSection>
            </DetailBoxTitle>
          </GridSection>
        </div>

        <UnlockDialogItem
          dialogOpen={this.state.dialogOpen}
          handleCloseDialog={this.handleCloseDialog}
          // unlockStock={unlockStock}
          stockid={id}
          stockunicornid={unicornid}
          // count={count}
          // max={max}
          // readytoRedirect={unlockfinished}
        />
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  count: state.stocklist.count ? state.stocklist.count : 0,
  max: !_.isEmpty(state.employee.permissions)? 
  (!_.isNil(state.employee.permissions[permissions.PERMISSION_VIEW_STOCK_QUOTA])? 
    state.employee.permissions[permissions.PERMISSION_VIEW_STOCK_QUOTA] : 0)
 : generalDailyQuota,
  unlockfinished: state.stocklist.unlockfinished
    ? state.stocklist.unlockfinished
    : false,
  favoriteStockIds: state.stock.favoriteStockIds
    ? state.stock.favoriteStockIds
    : [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    unlockStock: (id) => dispatch(unlockStock(id)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(SearchResultCard)));
