import React, { useState } from "react";
import { connect } from "react-redux";
import { Field, FieldArray, change } from "redux-form";
import { withStyles } from "@material-ui/core/styles";
import OutlinedInput from "@material-ui/core/OutlinedInput";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import TextInput from "../../../../common/TextInput";
import TextInputCombined from "../../../../common/TextInputCombined";
import ArrayPillCheckBox from "../../../../common/ArrayPillCheckBox";
import PillCheckBox from "../../../../common/PillCheckBox";
import PillButton from "../../../../common/PillButton";
import TextArrayInput from "../../../../common/TextArrayInput";
import SelectFieldArrayOutput from "../../../../common/SelectFieldArrayOutput";
import ReactSelectCreatable from "../../../../common/ReactSelectCreatable";
import ChipsCheckBox from "../../../../common/ChipsCheckBox";
import AutoCompleteSelect from "../../../../common/AutoCompleteSelect";
import Search from "../../../../common/Search";
import Switch from "../../../../common/Switch";
import Grid from "@material-ui/core/Grid";
import Collapse from "@material-ui/core/Collapse";
import DatePicker from "../../../../common/DatePicker";
import { minValue, maxValue, number } from "../../../../../core/formValidators";
import InputAdornment from "@material-ui/core/InputAdornment";
import { injectIntl, FormattedMessage } from "react-intl";
import { listBuildings } from "../../../../../actions/building";
import { listStreets } from "../../../../../actions/street";
import { listCompanies } from "../../../../../actions/stocklist";
import { getLangKey } from "../../../../../helper/generalHelper";
import { enableConsolidLandSearch } from "../../../../../config";

// We can inject some CSS into the DOM.
const styles = {
  expendbutton: {
    textTransform: "none",
    display: "flex",
    marginLeft: "auto",
    color: "#33CCCC",
  },
  divider: {
    textAlign: "center",
    margin: "auto 0px",
  },
  Headertitle: {
    color: "rgba(0, 0, 0, 0.54)",
    fontSize: "1em",
    fontWeight: "400",
  },
  DetailBoxSectionContent: {
    paddingLeft: 0,
    paddingRight: 0,
    paddingBottom: "3vh",
  },
  sticky: {
    position: "-webkit-sticky" /* Safari */,
    position: "sticky",
    top: "52px",
  },
  sectionTitleBtnContainer: {
    "& > button": {
      marginLeft: "1vw",
      fontSize: 12,
    },
  },
  fieldsdivider: {
    marginTop: 8,
    marginBottom: 10,
    borderRadius: 0,
    "& input": {
      textAlign: "center",
      transform: "scale(1.6)",
    },
    "& .MuiOutlinedInput-notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23)",
      borderLeft: 0,
      borderRight: 0,
    },
    "&:hover .MuiOutlinedInput-notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23)",
    },
    "& $focused $notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23) !important",
    },
  },
  fieldMargin: {
    marginTop: 8,
    marginBottom: 10,
  },
  paddingSection: {
    paddingBottom: "3vh",
  },
};

const boolranges = [
  {
    value: "",
    label: "All",
  },
  {
    value: true,
    label: "True",
  },
  {
    value: false,
    label: "False",
  },
];

function getstatusOptions(intl) {
  const statusOptions = [
    {
      value: "Sale",
      label: intl.formatMessage({
        id: "search.status.sale",
      }),
    },
    {
      value: "Lease",
      label: intl.formatMessage({
        id: "search.status.lease",
      }),
    },
    {
      value: "Tenanted",
      label: intl.formatMessage({
        id: "search.status.saleslease",
      }),
    },
    {
      value: "Sale+Lease",
      label: intl.formatMessage({
        id: "search.status.salesandlease",
      }),
    },
    {
      value: "Pending",
      label: intl.formatMessage({
        id: "search.status.pending",
      }),
    },
    {
      value: "TEL",
      label: intl.formatMessage({
        id: "search.status.tel",
      }),
    },
    {
      value: "Search",
      label: intl.formatMessage({
        id: "search.status.se",
      }),
    },
    {
      value: "Don't Call",
      label: intl.formatMessage({
        id: "search.status.dontcall",
      }),
    },
    {
      value: "Sold",
      label: intl.formatMessage({
        id: "search.status.sold",
      }),
    },
    {
      value: "Leased",
      label: intl.formatMessage({
        id: "search.status.leased",
      }),
    },
    {
      value: "History",
      label: intl.formatMessage({
        id: "search.status.history",
      }),
    },
    {
      value: "Cancel",
      label: intl.formatMessage({
        id: "search.status.cancel",
      }),
    },
  ];
  return statusOptions;
}

function getpropertyconditionOptions(intl) {
  const miscOptions = [
    {
      value: "isSoleAgent",
      label: intl.formatMessage({
        id: "stock.tag.sole",
      }),
    },
    {
      value: "isWithKey",
      label: intl.formatMessage({
        id: "search.form.withkey",
      }),
    },
    {
      value: "isCarPark",
      label: intl.formatMessage({
        id: "stock.tag.carpark",
      }),
    },
    {
      value: "isMortgagee",
      label: intl.formatMessage({
        id: "stock.tag.mortgagee",
      }),
    },
    {
      value: "havePropertyAdvertisements",
      label: intl.formatMessage({
        id: "search.form.advsconsent",
      }),
    },
    {
      value: "confirmorStatus",
      label: intl.formatMessage({
        id: "search.form.confirmor",
      }),
    },
    {
      value: "isSaleEquity",
      label: intl.formatMessage({
        id: "search.form.saleequity",
      }),
    },
    {
      value: "withPassCodes",
      label: intl.formatMessage({
        id: "search.form.passcode",
      }),
    },
    {
      value: "isParentStock",
      label: intl.formatMessage({
        id: "search.form.parentstock",
      }),
    },
    {
      value: "isChildStock",
      label: intl.formatMessage({
        id: "search.form.childstock",
      }),
    },
    {
      value: "havePhoto",
      label: intl.formatMessage({
        id: "stock.photo",
      }),
    },
    {
      value: "haveVideo",
      label: intl.formatMessage({
        id: "stock.video",
      }),
    },
  ];
  return miscOptions;
}

const unitM = {
  endAdornment: (
    <InputAdornment position="end">
      <span>M</span>
    </InputAdornment>
  ),
};

const unitK = {
  endAdornment: (
    <InputAdornment position="end">
      <span>K</span>
    </InputAdornment>
  ),
};

const unitFormatter = (value, unit) => {
  if (!value || value == null) return "";

  let unitValue;
  if (unit == "K") {
    unitValue = 1000;
  } else if (unit == "M") {
    unitValue = 1000000;
  } else {
    unitValue = 1;
  }
  return value / unitValue;
};

const formatMaxLength = (value, length) => {
  if (!value) {
    return value;
  }

  if (value.length <= length) {
    return `${value.slice(0, length)}`;
  }
  return `${value.slice(0, length)}`;
};

const formatUnitandMaxLength = (value, unit) => {
  if (!value) {
    return value;
  }

  value = value.replace(/[^0-9.]/g, "");

  let unitValue;
  if (unit == "K") unitValue = 1000;
  if (unit == "M") unitValue = 1000000;

  let number = value * unitValue;
  const roundnumber = number.toFixed();
  // return the value after rounding
  return `${roundnumber.toString()}`;
};

function createMinvalue(min) {
  return minValue(min, <FormattedMessage id="search.form.invalidinput" />);
}
function createMaxvalue(max) {
  return maxValue(max, <FormattedMessage id="search.form.invalidinput" />);
}
const numberValidate = number(
  <FormattedMessage id="search.form.invalidinput" />
);
const minvaluezero = createMinvalue(0);
const maxvalue1 = createMaxvalue(200);
const maxvalue2 = createMaxvalue(999);
const maxvalue3 = createMaxvalue(9999);
const maxvalue4 = createMaxvalue(99999);
const maxvalue5 = createMaxvalue(9999999);
const maxvalue6 = createMaxvalue(99999999);
const maxvalue7 = createMaxvalue(999999999);
const maxvalue8 = createMaxvalue(9999999999);

function FieldSection(props) {
  const {
    classes,
    buildings,
    fieldhistory,
    streets,
    districts,
    listBuildings,
    listStreets,
    decoration,
    unitview,
    selectedData,
    setSelectedData,
    expanded,
    intl,
    changeForm,
    initialValues,
    companies,
    listCompanies,
  } = props;

  const grades = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "A",
      label: "A",
    },
  ];

  const usages = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "Commercial",
      label: intl.formatMessage({
        id: "common.commercial",
      }),
    },
    {
      value: "Godown",
      label: intl.formatMessage({
        id: "search.form.godown",
      }),
    },
    {
      value: "Hotel",
      label: intl.formatMessage({
        id: "search.form.hotel",
      }),
    },
    {
      value: "I/O",
      label: intl.formatMessage({
        id: "search.form.io",
      }),
    },
    {
      value: "Industrial",
      label: intl.formatMessage({
        id: "common.industrial",
      }),
    },
    {
      value: "Shop",
      label: intl.formatMessage({
        id: "common.shop",
      }),
    },
    {
      value: "Site",
      label: intl.formatMessage({
        id: "search.form.site",
      }),
    },
  ];

  const ownerTypes = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "Single Owner",
      label: intl.formatMessage({
        id: "search.form.singleowner",
      }),
    },
    {
      value: "Investor",
      label: intl.formatMessage({
        id: "search.form.investor",
      }),
    },
    {
      value: "Strata Title",
      label: intl.formatMessage({
        id: "search.form.stratatitle",
      }),
    },
    {
      value: "Cooperation",
      label: intl.formatMessage({
        id: "search.form.cooperation",
      }),
    },
    {
      value: "Mortgagee",
      label: intl.formatMessage({
        id: "search.form.mortgagee",
      }),
    },
    {
      value: "Developer",
      label: intl.formatMessage({
        id: "search.form.developer",
      }),
    },
    {
      value: "HandOver",
      label: intl.formatMessage({
        id: "search.form.handover",
      }),
    },
  ];

  const stocktypes = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "Industrial",
      label: intl.formatMessage({
        id: "common.industrial",
      }),
    },
    {
      value: "G/F Workshop",
      label: intl.formatMessage({
        id: "search.form.gfloorworkshop",
      }),
    },
    {
      value: "Godown",
      label: intl.formatMessage({
        id: "search.form.godown",
      }),
    },
    {
      value: "Office",
      label: intl.formatMessage({
        id: "common.office",
      }),
    },
    {
      value: "Carpark",
      label: intl.formatMessage({
        id: "search.form.carpark",
      }),
    },
    {
      value: "Shop",
      label: intl.formatMessage({
        id: "common.shop",
      }),
    },
    {
      value: "I/O",
      label: intl.formatMessage({
        id: "search.form.io",
      }),
    },
    {
      value: "Commercial",
      label: intl.formatMessage({
        id: "common.commercial",
      }),
    },
    {
      value: "Hotel",
      label: intl.formatMessage({
        id: "search.form.hotel",
      }),
    },
    {
      value: "Site",
      label: intl.formatMessage({
        id: "search.form.site",
      }),
    },
    {
      value: "F&B",
      label: intl.formatMessage({
        id: "search.form.fandb",
      }),
    },
    {
      value: "Canteen",
      label: intl.formatMessage({
        id: "search.form.canteen",
      }),
    },
    {
      value: "AncillaryOffice",
      label: intl.formatMessage({
        id: "search.form.ancillaryoffice",
      }),
    },
    {
      value: "House",
      label: intl.formatMessage({
        id: "search.form.house",
      }),
    },
  ];

  const possession = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "Vacant",
      label: intl.formatMessage({
        id: "search.form.vacant",
      }),
    },
    {
      value: "Immediate Vacant",
      label: intl.formatMessage({
        id: "search.form.immediatevacant",
      }),
    },
    {
      value: "Nego. Vacant",
      label: intl.formatMessage({
        id: "search.form.negovacant",
      }),
    },
    {
      value: "Tenant",
      label: intl.formatMessage({
        id: "search.form.tenant",
      }),
    },
    {
      value: "Construction",
      label: intl.formatMessage({
        id: "search.form.construction",
      }),
    },
    {
      value: "Incomplete",
      label: intl.formatMessage({
        id: "search.form.incomplete",
      }),
    },
    {
      value: "Confirmor",
      label: intl.formatMessage({
        id: "search.form.confirmor",
      }),
    },
    {
      value: "Sale & Leaseback",
      label: intl.formatMessage({
        id: "search.form.saleandleaseback",
      }),
    },
    {
      value: "Occupied",
      label: intl.formatMessage({
        id: "search.form.occupied",
      }),
    },
  ];

  const createMaxValue = (max) => {
    console.log("creating max value...");
    const maxvalue = maxValue(max);
    return maxvalue;
  };

  let priceType, rentType;

  if (initialValues.priceMinTotal || initialValues.priceMaxTotal)
    priceType = "Total";
  if (initialValues.priceMinAvg || initialValues.priceMaxAvg) priceType = "Avg";
  if (initialValues.rentMinTotal || initialValues.rentMaxTotal)
    rentType = "Avg";
  if (initialValues.rentMinAvg || initialValues.rentMaxAvg) rentType = "Avg";
  const [TogglePriceValue, setTogglePriceValue] = useState(
    priceType ? priceType : "Total"
  );
  const [ToggleRentValue, setToggleRentValue] = useState(
    rentType ? rentType : "Total"
  );

  const langkey = getLangKey(intl);
  const decorationselection = (decoration || []).map((item) => ({
    value: item["_id"],
    label: item[langkey],
  }));
  decorationselection.unshift({
    value: "",
    label: intl.formatMessage({
      id: "search.form.all",
    }),
  });

  const viewselection = (unitview || []).map((item) => ({
    value: item["_id"],
    label: item[langkey],
  }));
  viewselection.unshift({
    value: "",
    label: intl.formatMessage({
      id: "search.form.all",
    }),
  });

  const deselectAllStatus = () => {
    changeForm("status", []);
  };

  const onChangePriceTotalAvg = () => {
    if (TogglePriceValue === "Total") {
      changeForm("priceMinTotal", "");
      changeForm("priceMaxTotal", "");
      setTogglePriceValue("Avg");
    } else if (TogglePriceValue === "Avg") {
      changeForm("priceMinAvg", "");
      changeForm("priceMaxAvg", "");
      setTogglePriceValue("Total");
    }
  };

  const onChangeRentTotalAvg = () => {
    if (ToggleRentValue === "Total") {
      changeForm("rentMinTotal", "");
      changeForm("rentMaxTotal", "");
      setToggleRentValue("Avg");
    } else if (ToggleRentValue === "Avg") {
      changeForm("rentMinAvg", "");
      changeForm("rentMaxAvg", "");
      setToggleRentValue("Total");
    }
  };

  const selectAllStatus = () => {
    const statusValue = getstatusOptions(intl).map((v) => v.value);
    changeForm("status", statusValue);
  };

  const deselectAllMisc = () => {
    const miscOptions = getpropertyconditionOptions(intl).map((v) => v.value);
    for (let key in miscOptions) {
      changeForm(miscOptions[key], false);
    }
  };

  const selectAllMisc = () => {
    const miscOptions = getpropertyconditionOptions(intl).map((v) => v.value);
    for (let key in miscOptions) {
      changeForm(miscOptions[key], true);
    }
  };

  const dateonFocus = (e) => {
    e.target.placeholder = "";
    e.target.type = "date";
  };

  const dateonBlur = (e) => (minormax) => {
    e.target.placeholder = intl.formatMessage({
      id: `search.form.common.${minormax}`,
    });
  };

  return (
    <div>
      {!expanded ? (
        <div className={classes.sticky}>
          <Field
            name="building"
            margin="normal"
            label={intl.formatMessage({
              id: "search.form.building",
            })}
            fullWidth
            component={AutoCompleteSelect}
            optionsdata={buildings}
            history={fieldhistory}
            apiaction={listBuildings}
            selectedData={selectedData && selectedData.building}
            setSelectedData={setSelectedData}
          />
        </div>
      ) : (
          <Field
            name="building"
            margin="normal"
            label={intl.formatMessage({
              id: "search.form.building",
            })}
            fullWidth
            component={AutoCompleteSelect}
            optionsdata={buildings}
            apiaction={listBuildings}
            selectedData={selectedData && selectedData.building}
            setSelectedData={setSelectedData}
            customInputProps={{
              className: classes.fieldMargin,
            }}
          />
        )}

      {/* <Field
          name="building"
          type="text"
          margin="normal"
          label="Building"
          component={TextInput}
          fullWidth
          variant="outlined"
        /> */}

      <Collapse in={expanded}>
        <div className={classes.paddingSection}>
          <Field
            name="district"
            margin="normal"
            label={intl.formatMessage({
              id: "search.form.district",
            })}
            fullWidth
            component={Search}
            searchItems={districts}
            selectedData={selectedData && selectedData.district}
            setSelectedData={setSelectedData}
            customInputProps={{
              className: classes.fieldMargin,
            }}
          />

          <DetailBoxSection
            text={intl.formatMessage({
              id: "search.header.status",
            })}
            titleClass={classes.Headertitle}
            contentClass={classes.DetailBoxSectionContent}
            customRight={
              <div className={classes.sectionTitleBtnContainer}>
                <PillButton onClick={selectAllStatus}>
                  {intl.formatMessage({
                    id: "search.form.all",
                  })}
                </PillButton>
                <PillButton onClick={deselectAllStatus}>
                  {intl.formatMessage({
                    id: "search.form.none",
                  })}
                </PillButton>
              </div>
            }
          >
            <FieldArray
              name="status"
              component={ArrayPillCheckBox}
              options={getstatusOptions(intl)}
            />
          </DetailBoxSection>

          {/* last update date */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="lastUpdateDateMin"
                  type="date"
                  inputProps={{
                    placeholder: intl.formatMessage({
                      id: "search.form.common.min",
                    }),
                    // onFocus: dateonFocus,
                    // onBlur: () => dateonBlur("min"),
                  }}
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.lastupdatedate",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="lastUpdateDateMax"
                  type="date"
                  inputProps={{
                    placeholder: intl.formatMessage({
                      id: "search.form.common.min",
                    }),
                    // onFocus: dateonFocus,
                    // onBlur: () => dateonBlur("min"),
                  }}
                  margin="normal"
                  className={classes.fieldMargin}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>

          <Field
            name="unicornId"
            type="number"
            margin="normal"
            className={classes.fieldMargin}
            label={intl.formatMessage({
              id: "search.header.stockid",
            })}
            component={TextArrayInput}
            fullWidth
            validate={[numberValidate, minvaluezero, maxvalue5]}
            variant="outlined"
          />

          {/* area */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="areaMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.area",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="areaMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue8]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>

          {/* price */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"priceMin" + TogglePriceValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  label={intl.formatMessage({
                    id: "search.common.price",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  validate={
                    TogglePriceValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue8]
                      : [minvaluezero]
                  }
                  InputProps={TogglePriceValue === "Total" ? unitM : null}
                  format={(value) => {
                    let formattedvalue;
                    if (TogglePriceValue === "Total") {
                      formattedvalue = unitFormatter(value, "M");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (TogglePriceValue === "Total") {
                      return formatUnitandMaxLength(value, "M");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"priceMax" + TogglePriceValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  validate={
                    TogglePriceValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue8]
                      : [minvaluezero, maxvalue8]
                  }
                  InputProps={TogglePriceValue == "Total" && unitM}
                  format={(value) => {
                    let formattedvalue;
                    if (TogglePriceValue === "Total") {
                      formattedvalue = unitFormatter(value, "M");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (TogglePriceValue === "Total") {
                      return formatUnitandMaxLength(value, "M");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                  rightcomp={
                    <Switch
                      value={TogglePriceValue}
                      textL={intl.formatMessage({
                        id: "search.common.pricetotal",
                      })}
                      textR={intl.formatMessage({
                        id: "search.common.priceavg",
                      })}
                      valleft="Total"
                      valright="Avg"
                      onChange={onChangePriceTotalAvg}
                    />
                  }
                />
              </div>
            </Grid>
          </Grid>

          {/* rent */}
          <Grid container className={classes.paddingSection}>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"rentMin" + ToggleRentValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  label={intl.formatMessage({
                    id: "search.common.rent",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  validate={
                    ToggleRentValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue6]
                      : [minvaluezero]
                  }
                  InputProps={ToggleRentValue === "Total" && unitK}
                  format={(value) => {
                    let formattedvalue;
                    if (ToggleRentValue === "Total") {
                      formattedvalue = unitFormatter(value, "K");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (ToggleRentValue === "Total") {
                      return formatUnitandMaxLength(value, "K");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"rentMax" + ToggleRentValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  validate={
                    ToggleRentValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue6]
                      : [minvaluezero, maxvalue8]
                  }
                  InputProps={ToggleRentValue === "Total" && unitK}
                  format={(value) => {
                    let formattedvalue;
                    if (ToggleRentValue === "Total") {
                      formattedvalue = unitFormatter(value, "K");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (ToggleRentValue === "Total") {
                      return formatUnitandMaxLength(value, "K");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                  rightcomp={
                    <Switch
                      value={ToggleRentValue}
                      textL={intl.formatMessage({
                        id: "search.common.renttotal",
                      })}
                      textR={intl.formatMessage({
                        id: "search.common.rentavg",
                      })}
                      valleft="Total"
                      valright="Avg"
                      onChange={onChangeRentTotalAvg}
                    />
                  }
                />
              </div>
            </Grid>
          </Grid>

          {/* street */}
          <Grid container >
            <Grid item xs={12}>
              <Field
                name="street"
                margin="normal"
                label={intl.formatMessage({
                  id: "search.form.street",
                })}
                fullWidth
                component={AutoCompleteSelect}
                optionsdata={streets}
                apiaction={listStreets}
                selectedData={selectedData && selectedData.street}
                setSelectedData={setSelectedData}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
              />
            </Grid>

            <Grid item xs={5}>
              <div>
                <Field
                  name="streetNoMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.form.streetnumber",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 4)}
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="streetNoMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue3]}
                  parse={(value) => formatMaxLength(value, 4)}
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>

          {/* floor number */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="floorMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.form.floor",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue3]}
                  parse={(value) => formatMaxLength(value, 4)}
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="floorMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue3]}
                  parse={(value) => formatMaxLength(value, 4)}
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>

          {/* building grade */}
          <Grid container>
            <Grid item xs={5}>
              <Field
                name="buildingGrade"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.grade",
                })}
                ranges={grades}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
              />
            </Grid>
            <Grid item xs={2} className={classes.divider}>
              {""}
            </Grid>
            <Grid item xs={5}>
            </Grid>

            <Grid item xs={12}>
              <Field
                name="buildingUsage"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.usage",
                })}
                ranges={usages}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
              />
            </Grid>
          </Grid>

          {/* age */}
          <Grid container className={classes.paddingSection}>
            <Grid item xs={5}>
              <div>
                <Field
                  name="ageMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.age",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 3)}
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="ageMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue2]}
                  parse={(value) => formatMaxLength(value, 3)}
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>



          <DetailBoxSection
            text={intl.formatMessage({
              id: "search.header.miscellaneous",
            })}
            titleClass={classes.Headertitle}
            contentClass={classes.DetailBoxSectionContent}
            customRight={
              <div className={classes.sectionTitleBtnContainer}>
                <PillButton onClick={selectAllMisc}>
                  {intl.formatMessage({
                    id: "search.form.all",
                  })}
                </PillButton>
                <PillButton onClick={deselectAllMisc}>
                  {intl.formatMessage({
                    id: "search.form.none",
                  })}
                </PillButton>
              </div>
            }
          >
            <div>
              <Grid container spacing={1}>
                {getpropertyconditionOptions(intl).map((v, i) => {
                  return (
                    <Grid key={i} item xs={4}>
                      <div>
                        <Field
                          name={v.value}
                          text={v.label}
                          component={PillCheckBox}
                        />
                      </div>
                    </Grid>
                  );
                })}
              </Grid>
            </div>
          </DetailBoxSection>

          <Grid container>

            {/* create date */}
            <Grid container>
              <Grid item xs={5}>
                <div>
                  <Field
                    name="createDateMin"
                    type="date"
                    inputProps={{
                      placeholder: intl.formatMessage({
                        id: "search.form.common.min",
                      }),
                      // onFocus: dateonFocus,
                      // onBlur: () => dateonBlur("min"),
                    }}
                    margin="normal"
                    className={classes.fieldMargin}
                    label={intl.formatMessage({
                      id: "search.common.createdate",
                    })}
                    component={TextInputCombined}
                    fullWidth
                    variant="outlined"
                    compPosition={"left"}
                  />
                </div>
              </Grid>
              <Grid item xs={2}>
                <OutlinedInput
                  disabled
                  readOnly={true}
                  defaultValue="|"
                  variant="outlined"
                  className={classes.fieldsdivider}
                />
              </Grid>
              <Grid item xs={5}>
                <div>
                  <Field
                    name="createDateMax"
                    type="date"
                    inputProps={{
                      placeholder: intl.formatMessage({
                        id: "search.form.common.min",
                      }),
                      // onFocus: dateonFocus,
                      // onBlur: () => dateonBlur("min"),
                    }}
                    margin="normal"
                    className={classes.fieldMargin}
                    component={TextInputCombined}
                    fullWidth
                    variant="outlined"
                    compPosition={"right"}
                  />
                </div>
              </Grid>
            </Grid>

            {/* tenancy expiry date */}
            <Grid container>
              <Grid item xs={5}>
                <div>
                  <Field
                    name="tenancyExpireDateMin"
                    type="date"
                    inputProps={{
                      placeholder: intl.formatMessage({
                        id: "search.form.common.min",
                      }),
                      // onFocus: dateonFocus,
                      // onBlur: () => dateonBlur("min"),
                    }}
                    margin="normal"
                    className={classes.fieldMargin}
                    label={intl.formatMessage({
                      id: "search.common.tenancy",
                    })}
                    component={TextInputCombined}
                    fullWidth
                    variant="outlined"
                    compPosition={"left"}
                  />
                </div>
              </Grid>
              <Grid item xs={2}>
                <OutlinedInput
                  disabled
                  readOnly={true}
                  defaultValue="|"
                  variant="outlined"
                  className={classes.fieldsdivider}
                />
              </Grid>
              <Grid item xs={5}>
                <div>
                  <Field
                    name="tenancyExpireDateMax"
                    type="date"
                    inputProps={{
                      placeholder: intl.formatMessage({
                        id: "search.form.common.min",
                      }),
                      // onFocus: dateonFocus,
                      // onBlur: () => dateonBlur("min"),
                    }}
                    margin="normal"
                    className={classes.fieldMargin}
                    component={TextInputCombined}
                    fullWidth
                    variant="outlined"
                    compPosition={"right"}
                  />
                </div>
              </Grid>
            </Grid>

            <Grid item xs={12}>
              <Field
                name="stockType"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.type",
                })}
                ranges={stocktypes}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <Field
                name="ownerType"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.ownertype",
                })}
                ranges={ownerTypes}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <Field
                name="possession"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.possession",
                })}
                ranges={possession}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <Field
                name="decoration"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.decoration",
                })}
                ranges={decorationselection}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <Field
                name="unitView"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.view",
                })}
                ranges={viewselection}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
              />
            </Grid>
          </Grid>
        </div>

        {enableConsolidLandSearch == "true" && <DetailBoxSection
          text={intl.formatMessage({
            id: "search.header.consolidate",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
        >
          <Grid container>
            <Grid item xs={6}>
              <Field
                name="isContact"
                label={intl.formatMessage(
                  { id: "search.form.owner" },
                )}
                component={ChipsCheckBox}
              />
            </Grid>
            <Grid item xs={6}>
              <Field
                name="isCurrent"
                label={intl.formatMessage(
                  { id: "stock.tenant" },
                  { status: intl.formatMessage({ id: "stock.currenttenancy" }) },
                )}
                component={ChipsCheckBox}
              />
            </Grid>
            <Grid item xs={6}>
              <Field
                name="isFormer"
                label={intl.formatMessage(
                  { id: "stock.tenant" },
                  { status: intl.formatMessage({ id: "stock.previoustenancy" }) },
                )}
                component={ChipsCheckBox}
              />
            </Grid>

            <Grid item xs={12}>
              <Field
                name="person"
                margin="normal"
                label={intl.formatMessage({
                  id: "stock.contact",
                })}
                fullWidth
                component={ReactSelectCreatable}
                optionsdata={[]}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                selectedData={selectedData && selectedData.person}
                setSelectedData={setSelectedData}
              />
            </Grid>
            <Grid item xs={12}>
              <Field
                name="company"
                margin="normal"
                label={intl.formatMessage({
                  id: "search.form.company",
                })}
                fullWidth
                component={AutoCompleteSelect}
                optionsdata={companies}
                apiaction={listCompanies}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                selectedData={selectedData && selectedData.company}
                setSelectedData={setSelectedData}
                showZhEnLabel={true}
              />
            </Grid>
            {/*<Grid item xs={12}>*/}
            {/*  <Field*/}
            {/*    name="phone"*/}
            {/*    margin="normal"*/}
            {/*    label={intl.formatMessage({*/}
            {/*      id: "building.phone",*/}
            {/*    })}*/}
            {/*    fullWidth*/}
            {/*    component={ReactSelectCreatable}*/}
            {/*    optionsdata={[]}*/}
            {/*    customInputProps={{*/}
            {/*      className: classes.fieldMargin,*/}
            {/*    }}*/}
            {/*    selectedData={selectedData && selectedData.phone}*/}
            {/*    setSelectedData={setSelectedData}*/}
            {/*  />*/}
            {/*</Grid>*/}
            <Grid item xs={12}>
              <Field
                name="email"
                margin="normal"
                label={intl.formatMessage({
                  id: "search.form.email",
                })}
                fullWidth
                component={ReactSelectCreatable}
                optionsdata={[]}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                selectedData={selectedData && selectedData.email}
                setSelectedData={setSelectedData}
              />
            </Grid>
          </Grid>
        </DetailBoxSection>}

        {props.children}
      </Collapse>
    </div>
  );
}

const mapStateToProps = (state) => ({
  buildings: state.building.buildings || [],
  fieldhistory: state.stocklist.fieldhistory || [],
  queryvariables: state.stocklist.queryvariables
    ? state.stocklist.queryvariables
    : {},
  listing: state.building.listing ? state.building.listing : false,
  listed: state.building.listed ? state.building.listed : false,
  streets: state.street.streets ? state.street.streets : [],
  districts: state.district.districts ? state.district.districts : [],
  decoration: state.stocklist.decoration ? state.stocklist.decoration : [],
  unitview: state.building.unitview ? state.building.unitview : [],
  companies: state.stocklist.companies ? state.stocklist.companies : [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    listBuildings: (graphqlvariable) => {
      dispatch(listBuildings(graphqlvariable));
    },
    listStreets: (graphqlvariable) => dispatch(listStreets(graphqlvariable)),
    listCompanies: (graphqlvariable) => dispatch(listCompanies(graphqlvariable)),
    changeForm: (field, val) => dispatch(change("searchForm", field, val)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(FieldSection)));
