import React, { useState, useEffect } from "react";
import { connect } from "react-redux";
import _ from "lodash";
import PropTypes from "prop-types";
import withStyles from "isomorphic-style-loader/lib/withStyles";
import { Box } from "@material-ui/core";
// external-global styles must be imported in your JS.
import normalizeCss from "normalize.css";
import s from "./Layout.css";
import Header from "../Header";
import RedirectIfNoToken from "./RedirectIfNoToken";
// import RefreshTimer from "./RefreshTimer";
import PermissionController from "./PermissionController";

function Layout({ headerRef, children, renderHeader = true, ...props }, { windowHeight }) {
  return (
    <React.Fragment>
      {renderHeader ?
        <Box height={windowHeight}>
          <div id={"layout-header-container"}>
            <Header ref={headerRef} {...props} />
            {children}
          </div>
        </Box> :
        <React.Fragment>
          {children}
        </React.Fragment>
      }
      <RedirectIfNoToken/>
      {/* commented out because the session and CAS token both expire in 2 hours, so refreshing is unnecessary as they can expire together */}
      {/* <RefreshTimer/> */ }
      <PermissionController/>
    </React.Fragment>
  );
}

Layout.contextTypes = {
  windowHeight: PropTypes.number,
};

Layout.propTypes = {
  children: PropTypes.node.isRequired,
};

export default withStyles(normalizeCss, s)(Layout);
