export const LIST_STOCKLIST_QUERY = `query ($unicornId: [String], $status: [String], $sbu: [String], $stockIds: [ID], $sorter: [stockSorter], $includeId: [ID], $buildingSourcesId: [ID], $district: [ID], $streets: [ID], $streetsNoMin: Int, $streetsNoMax: Int, $street: [ID], $streetNoMin: Int, $streetNoMax: Int, $areaMin: Int, $areaMax: Int, $areaForShopMin: Int, $areaForShopMax: Int, $priceMinAvg: Float, $priceMaxAvg: Float, $rentMinAvg: Float, $rentMaxAvg: Float, $priceMinTotal: Float, $priceMaxTotal: Float, $rentMinTotal: Float, $rentMaxTotal: Float, $isWithKey: Boolean, $isMortgagee: Boolean, $isConfirmorSales: Boolean, $possession: [String], $limit: Int, $offset: Int, $floorMin: Int, $floorMax: Int, $floorLeft: String, $floorRight: String, $isSoleAgent: Boolean, $createDateMin: String, $createDateMax: String, $lastUpdateDateMin: String, $lastUpdateDateMax: String, $tenancyExpireDateMin: String, $tenancyExpireDateMax: String, $havePropertyAdvertisements: Boolean, $confirmorStatus: Boolean, $isSaleEquity: Boolean, $isHandOver: Boolean, $wasSoleAgentForSale: Boolean, $wasSoleAgentForLease: Boolean, $havePropertyAdvertisementsForSale: Boolean, $havePropertyAdvertisementsForLease: Boolean, $havePropertyAdvertisementsExpire: Boolean, $isWithoutKey: Boolean,  $haveVR: Boolean, $haveStockVideo: Boolean, $haveBuildingVideo: Boolean, $haveLandSearchDoc: Boolean, $isNew: Boolean, $isWWW: Boolean, $withPassCodes: Boolean, $stockType: [String], $ownerType: [String], $parity: [String], $isSoleAgentForLease: Boolean, $isSoleAgentForSale: Boolean, $isMarketableForLease: Boolean, $isMarketableForSale: Boolean, $isMarketableForSaleOrLease: Boolean, $isShoppingMallStock: Boolean, $isSingleSideStock: Boolean, $isFrontAndRearPortion: Boolean, $haveSurveyorProposal: Boolean, $pdfPP: Boolean, $isPrivacy: Boolean, $yieldMin: Float, $yieldMax: Float, $boardStatus: [String], $newsStatus: [String], $haveCockloft: Boolean, $haveLegalCockloft: Boolean, $haveCockloftOrLegalCockloft: Boolean, $priceChangeDateMin: String, $priceChangeDateMax: String, $isVendor: Boolean, $isCurrent: Boolean, $isPrevious: Boolean, $isFormer: Boolean, $isAdvance: Boolean, $contactsCompany: [ID], $contactsPerson: [String], $contactsPhone: [String], $contactsEmail: [String], $isInWater: Boolean, $isOutWater: Boolean, $haveTownGas: Boolean) {
  stocksCount(unicornId: $unicornId, status: $status, sbu: $sbu, _id: $stockIds, includeId: $includeId, buildingSourcesId: $buildingSourcesId, district: $district, streets: $streets, streetsNoMin: $streetsNoMin, streetsNoMax: $streetsNoMax, street: $street, streetNoMin: $streetNoMin, streetNoMax: $streetNoMax, areaMin: $areaMin, areaMax: $areaMax, areaForShopMin: $areaForShopMin, areaForShopMax: $areaForShopMax, priceMinAvg: $priceMinAvg, priceMaxAvg: $priceMaxAvg, rentMinAvg: $rentMinAvg, rentMaxAvg: $rentMaxAvg, priceMinTotal: $priceMinTotal, priceMaxTotal: $priceMaxTotal, rentMinTotal: $rentMinTotal, rentMaxTotal: $rentMaxTotal, isWithKey: $isWithKey, isMortgagee: $isMortgagee, isConfirmorSales: $isConfirmorSales, possession: $possession, floorMin: $floorMin, floorMax: $floorMax, floorLeft: $floorLeft, floorRight: $floorRight, isSoleAgent: $isSoleAgent, createDateMin: $createDateMin, createDateMax: $createDateMax, lastUpdateDateMin: $lastUpdateDateMin, lastUpdateDateMax: $lastUpdateDateMax, tenancyExpireDateMin: $tenancyExpireDateMin, tenancyExpireDateMax: $tenancyExpireDateMax, havePropertyAdvertisements: $havePropertyAdvertisements, confirmorStatus: $confirmorStatus, isSaleEquity: $isSaleEquity, isHandOver: $isHandOver, wasSoleAgentForSale: $wasSoleAgentForSale, wasSoleAgentForLease: $wasSoleAgentForLease, havePropertyAdvertisementsForSale: $havePropertyAdvertisementsForSale, havePropertyAdvertisementsForLease: $havePropertyAdvertisementsForLease, havePropertyAdvertisementsExpire: $havePropertyAdvertisementsExpire, isWithoutKey: $isWithoutKey,  haveVR: $haveVR, haveStockVideo: $haveStockVideo, haveBuildingVideo: $haveBuildingVideo, haveLandSearchDoc: $haveLandSearchDoc, isNew: $isNew,  isWWW: $isWWW, withPassCodes: $withPassCodes, stockType: $stockType, ownerType: $ownerType, parity: $parity, isSoleAgentForLease: $isSoleAgentForLease, isSoleAgentForSale: $isSoleAgentForSale, isMarketableForLease: $isMarketableForLease, isMarketableForSale: $isMarketableForSale, isMarketableForSaleOrLease: $isMarketableForSaleOrLease, isShoppingMallStock: $isShoppingMallStock, isSingleSideStock: $isSingleSideStock, isFrontAndRearPortion: $isFrontAndRearPortion, haveSurveyorProposal: $haveSurveyorProposal, pdfPP: $pdfPP, isPrivacy: $isPrivacy, yieldMin: $yieldMin, yieldMax: $yieldMax, boardStatus: $boardStatus, newsStatus: $newsStatus, haveCockloft: $haveCockloft, haveLegalCockloft: $haveLegalCockloft, haveCockloftOrLegalCockloft: $haveCockloftOrLegalCockloft, priceChangeDateMin: $priceChangeDateMin, priceChangeDateMax: $priceChangeDateMax, isCurrVendor: $isVendor, isCurrent: $isCurrent, isPrevious: $isPrevious, isFormer: $isFormer, isAdvance: $isAdvance, contactsCompany: $contactsCompany, contactsPerson: $contactsPerson, contactsPhone: $contactsPhone, contactsEmail: $contactsEmail, isInWater: $isInWater, isOutWater: $isOutWater, haveTownGas: $haveTownGas)
  stocks(unicornId: $unicornId, status: $status, sbu: $sbu, _id: $stockIds, sort: $sorter, includeId: $includeId, buildingSourcesId: $buildingSourcesId, district: $district, streets: $streets, streetsNoMin: $streetsNoMin, streetsNoMax: $streetsNoMax, street: $street, streetNoMin: $streetNoMin, streetNoMax: $streetNoMax, areaMin: $areaMin, areaMax: $areaMax, areaForShopMin: $areaForShopMin, areaForShopMax: $areaForShopMax, priceMinAvg: $priceMinAvg, priceMaxAvg: $priceMaxAvg, rentMinAvg: $rentMinAvg, rentMaxAvg: $rentMaxAvg, priceMinTotal: $priceMinTotal, priceMaxTotal: $priceMaxTotal, rentMinTotal: $rentMinTotal, rentMaxTotal: $rentMaxTotal, isWithKey: $isWithKey, isMortgagee: $isMortgagee, isConfirmorSales: $isConfirmorSales, possession: $possession, limit: $limit, offset: $offset, floorMin: $floorMin, floorMax: $floorMax, floorLeft: $floorLeft, floorRight: $floorRight, isSoleAgent: $isSoleAgent, createDateMin: $createDateMin, createDateMax: $createDateMax, lastUpdateDateMin: $lastUpdateDateMin, lastUpdateDateMax: $lastUpdateDateMax, tenancyExpireDateMin: $tenancyExpireDateMin, tenancyExpireDateMax: $tenancyExpireDateMax, havePropertyAdvertisements: $havePropertyAdvertisements, confirmorStatus: $confirmorStatus, isSaleEquity: $isSaleEquity, isHandOver: $isHandOver, wasSoleAgentForSale: $wasSoleAgentForSale, wasSoleAgentForLease: $wasSoleAgentForLease, havePropertyAdvertisementsForSale: $havePropertyAdvertisementsForSale, havePropertyAdvertisementsForLease: $havePropertyAdvertisementsForLease, havePropertyAdvertisementsExpire: $havePropertyAdvertisementsExpire, isWithoutKey: $isWithoutKey, haveVR: $haveVR, haveStockVideo: $haveStockVideo, haveBuildingVideo: $haveBuildingVideo, haveLandSearchDoc: $haveLandSearchDoc, isNew: $isNew, isWWW: $isWWW, withPassCodes: $withPassCodes, stockType: $stockType, ownerType: $ownerType, parity: $parity, isSoleAgentForLease: $isSoleAgentForLease, isSoleAgentForSale: $isSoleAgentForSale, isMarketableForLease: $isMarketableForLease, isMarketableForSale: $isMarketableForSale, isMarketableForSaleOrLease: $isMarketableForSaleOrLease, isShoppingMallStock: $isShoppingMallStock, isSingleSideStock: $isSingleSideStock, isFrontAndRearPortion: $isFrontAndRearPortion, haveSurveyorProposal: $haveSurveyorProposal, pdfPP: $pdfPP, isPrivacy: $isPrivacy, yieldMin: $yieldMin, yieldMax: $yieldMax, boardStatus: $boardStatus, newsStatus: $newsStatus, haveCockloft: $haveCockloft, haveLegalCockloft: $haveLegalCockloft, haveCockloftOrLegalCockloft: $haveCockloftOrLegalCockloft, priceChangeDateMin: $priceChangeDateMin, priceChangeDateMax: $priceChangeDateMax, isCurrVendor: $isVendor, isCurrent: $isCurrent, isPrevious: $isPrevious, isFormer: $isFormer, isAdvance: $isAdvance, contactsCompany: $contactsCompany, contactsPerson: $contactsPerson, contactsPhone: $contactsPhone, contactsEmail: $contactsEmail, isInWater: $isInWater, isOutWater: $isOutWater, haveTownGas: $haveTownGas) {
    unit
    streets {
      _id
      nameEn
      nameZh
      number
      numberInNum
    }
    matchedStreets {
      nameEn
      nameZh
      number
    }
    district {
      abbr
      nameZh
    }
    askingRent {
      average
      total
      trend
    }
    askingPrice {
      average
      total
      trend
    }
    area
    areaForShop
    _id
    sbu
    status
    statusZh
    floor {
      input
      inputInChinese
    }
    building {
      nameEn
      nameZh
    }
    mortgagee
    isSoleAgentForLease
    isSoleAgentForSale
    isShoppingMallStock
    isSingleSideStock
    isFrontAndRearPortion
    isWithKey
    isHandOver
    isWWW
    haveVideo
    boardStatus
    isNew
    havePropertyAdvertisements
    tenancyCurrentTenant
    tenancyCurrentTenantZh
    tenancyExpireDate
    unicorn {
      stock
    }
    stockTypeId
    isSaleEquity
    isConfirmorSales
    haveSurveyorProposal
    pdfPP
    isMarketableForSale
    isMarketableForLease
    wasSoleAgentForSale
    wasSoleAgentForLease
    havePropertyAdvertisementsForSale
    havePropertyAdvertisementsForLease
    havePropertyAdvertisementsExpire
    isWithoutKey
    haveVR
    haveStockVideo
    haveBuildingVideo
    haveLandSearchDoc
    wwwScore {
      eaaOwner
      soleagent
      stockInformation
      newStock
      video
      kolVideo
      photo
    }
    wwwScoreTotal
    isOnline
    wwwAgentInfos {
      wwwBy
      wwwByName
      wwwByDept
      wwwDate
      wwwDesc
      scoreTotal
      score {
        eaaOwner
        soleAgent
        photo
        video
        kolVideo
      }
      eaaOwner
      soleAgent
      video
      videoId
      kolVideo
      kolVideoId
      photo
      photoIds
    }
    wwwChannelFull {
      eaaOwner
      soleagent
      video
      kolVideo
      photo
    }
  }
}
`;

export const LIST_POSSESSIONS_QUERY = `
{
  possessions(sort: [{ field: _id, order: ASC }]) {
    _id
    nameEn
    nameZh
  }
}
`;

export const LIST_STOCKTYPES_QUERY = `
{
  stockTypes(sort: [{ field: _id, order: ASC }]) {
    _id
    nameEn
    nameZh
  }
}
`;
