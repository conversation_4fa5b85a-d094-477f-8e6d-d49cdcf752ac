/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import path from "path";
import express from "express";
import cors from "cors";
import session from "express-session";
import bodyParser from "body-parser";
import cookieParser from "cookie-parser";
import connectMongo from "connect-mongo";
// import expressJwt, { UnauthorizedError as Jwt401Error } from "express-jwt";
// import { graphql } from "graphql";
// import expressGraphQL from "express-graphql";
// import jwt from "jsonwebtoken";
import nodeFetch from "node-fetch";
import fetch from "node-fetch";
import React from "react";
import ReactDOM from "react-dom/server";
import { ServerStyleSheets, ThemeProvider } from "@material-ui/core/styles";
import { Provider } from "react-redux";
import Pretty<PERSON>rror from "pretty-error";
import Speakeasy from "speakeasy";
import helmet from "helmet";
import MobileDetect from "mobile-detect";
import moment from "moment";
import multer from "multer";
import UUID from "uuid/v1";

import App from "./components/App";
import Html from "./components/Html";
import { ErrorPageWithoutStyle } from "./routes/error/ErrorPage";
import errorPageStyle from "./routes/error/ErrorPage.css";
import createFetch from "./core/createFetch";
import passport from "./core/passport";
import router from "./core/router";
import encodeFormData from "./core/encodeFormData";
import mongodb from "./data/mongodb";
import ConnectedIntlProvider from "./ConnectedIntlProvider";
// import models from "./data/models";
// import schema from "./data/schema";
// import assets from './asset-manifest.json'; // eslint-disable-line import/no-unresolved
import chunks from "./chunk-manifest.json"; // eslint-disable-line import/no-unresolved
import configureStore from "./store/configureStore";
import config, { m1PublicKeyName } from "./config";
import theme from "./core/theme";
import {
  unlockStock,
  getUnlockCount,
  checkUnlockStock,
  clearUnlockRecord,
  unlockListStock,
} from "./core/unlockStock";
import { saveMedia, updateMedia, removeMedia } from "./core/handleMedia";
import { sendSMS, sendappNotification } from "./core/sendPin";
import { getDeniedCalls, listStockDetail } from "./core/api/stock";
import {
  createProposal,
  createPreview,
  listProposal,
  removeProposal,
  getProposalCount,
} from "./core/api/proposal";
import {
  listLandSearch,
  saveLandSearch,
  getLandSearchDoc,
  takeLandSearchLog,
  listCompanySearch,
  saveCompanySearch,
  getCompanySearchDoc,
  applyLandSearch,
  listLandSearchPdf,
} from "./core/api/landsearch";
// import { ppStockCount } from "./core/api/saleskit";
import { addtoMyFavorite, listMyFavorite } from "./core/myfavorite";
import { saveSearch, listSearch } from "./core/saveSearch";
import {
  listCompanies,
  createCompany,
  listCompany,
  querySourceOptions,
  applyCompanySearch,
} from "./core/api/company";
import createGetQuery from "./core/api/query";
import { createListProposal } from "./core/api/listProposal";
import { getReCreatePP } from "./core/api/proposal";
import jose from "./core/jose";
import {
  addMarkStock,
  listMarkStock,
  removeMarkStock,
} from "./core/markedStock";
import {
  addActivityLog,
} from "./core/activityLog";
import {
  addLog,
  districtGql,
  streetGql,
  contactGql,
  stockGql,
  employeeGql,
  searchGql,
  wwwGql,
  buildingGql,
  transactionGql,
  mediaGql,
  supplementGql,
  landsearchGql
} from "./core/api/internal";
import {
  getPinImg,
} from "./core/api/asset";
import {
  video
} from "./core/api/s3Tools";
import { getCookieDomain } from "./helper/generalHelper";

process.on("unhandledRejection", (reason, p) => {
  console.error("Unhandled Rejection at:", p, "reason:", reason);
  // send entire app down. Process manager will restart it
  process.exit(1);
});

//
// Tell any CSS tooling (such as Material UI) to use all vendor prefixes if the
// user agent is not known.
// -----------------------------------------------------------------------------
global.navigator = global.navigator || {};
global.navigator.userAgent = global.navigator.userAgent || "all";

const app = express();
app.use(cors());
//
// If you are using proxy from external machine, you can set TRUST_PROXY env
// Default is to trust proxy headers only from loopback interface.
// -----------------------------------------------------------------------------
app.set("trust proxy", config.trustProxy);

//
// Register Node.js middleware
// -----------------------------------------------------------------------------
app.use(express.static(path.resolve(__dirname, "public")));
app.use(cookieParser());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json({ limit: "50mb" }));

// session expries in 1 day
app.use(
  session({
    secret: "somesecret",
    cookie: {
      expires: false,
      maxAge: 2 * 60 * 60 * 1000,
      domain: getCookieDomain(config.deployDomain),
    },
    saveUninitialized: false,
    store: new (connectMongo(session))({
      url: `${config.databaseUrl}/${config.sessionDatabaseName}`,
      stringify: false,
    }),
  }),
);

app.use(passport.initialize());
app.use(passport.session());
app.use(helmet());

const isMobile = (req) => {
  const md = new MobileDetect(req.headers["user-agent"]);
  return md.mobile() != null;
};

// app.get("/", (req, res, next) => {
//   const md = new MobileDetect(req.headers["user-agent"]);
//   if (md.mobile() != null) {
//     res.redirect("/m");
//   } else {
//     res.redirect("/d");
//   }
// });

app.use("/login", (req, res, next) => {
  if (isMobile(req)) {
    res.cookie("user-agent", req.headers["user-agent"]);
    res.cookie("locale", req.cookies.locale || config.locale);
    res.cookie("searchHistory", []);
    next();
  } else {
    // add log when user-agent is detected as non-mobile
    const msg = JSON.stringify({
      action: "detectmobile",
      status: "failed",
      message: JSON.stringify({
        useragent: req.headers["user-agent"],
      }),
    });
    fetch(config.cloudwatchlog.internalurl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        logGroup: config.cloudwatchlog.logGroup,
        logStream: `detect-mobile-${moment().format("YYYY-MM-DD")}`,
        msg,
      }),
    }).catch((e) => console.error(e));

    // redirect to error page
    const url = `/error?message=${encodeURIComponent(
      "Only mobile device can access",
    )}&reason=${encodeURIComponent(req.headers["user-agent"])}`;
    res.redirect(url);
  }
});

//
// Authentication
// -----------------------------------------------------------------------------
// app.use(
//   expressJwt({
//     secret: config.auth.jwt.secret,
//     credentialsRequired: false,
//     getToken: req => req.cookies.id_token
//   })
// );
// Error handler for express-jwt
// app.use((err, req, res, next) => {
//   // eslint-disable-line no-unused-vars
//   if (err instanceof Jwt401Error) {
//     console.error("[express-jwt-error]", req.cookies.id_token);
//     // `clearCookie`, otherwise user can't use web-app until cookie expires
//     res.clearCookie("id_token");
//   }
//   next(err);
// });

// OAuth2 authentication
// app.get("/auth", passport.authenticate("oauth2"));

// app.get(
//   "/auth/callback",
//   passport.authenticate("oauth2", {
//     session: true,
//     failureRedirect: `${config.publicHost}/auth`
//   }),
//   (req, res) => {
//     const redirectUri = `${config.publicHost}${config.baseUrl}`;
//     res.redirect(redirectUri);
//   }
// );

// passportjs authentication
app.get("/login", (req, res, next) => {
  if (req.query.code) {
    fetch(config.cas_access_token_host, {
      method: "POST",
      body: `grant_type=authorization_code&client_id=${config.cas_client_id}&client_secret=${config.cas_client_secret}&code=${req.query.code}&redirect_uri=${config.publicHost}/login`,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Accept: "application/json",
      },
    })
      .then(async (response) => {
        try {
          const result = await response.json();
          res.cookie("casAccessToken", result.access_token, {
            domain: getCookieDomain(config.deployDomain)
          });
          res.cookie("casRefreshToken", result.refresh_token, {
            domain: getCookieDomain(config.deployDomain)
          });
          res.cookie("casAccessTokenExpiresIn", result.expires_in, {
            domain: getCookieDomain(config.deployDomain)
          });
          next();
        } catch (e) {
          console.error("Fail to parse response from CAS");
          res.redirect("/login");
        }
      })
      .catch((e) => {
        console.error(e);
        res.redirect("/login");
      });
  } else if (!req.user) {
    res.redirect(
      `${config.cas_auth_code_host}&client_id=${config.cas_client_id}&redirect_uri=${config.publicHost}/login`,
    );
    console.error("No User Session");
  } else if (!req.user.authorized) {
    next();
  } else {
    res.redirect("/#success");
  }
});

app.post("/login", (req, res, next) =>
  passport.authenticate("local", (err, user, info) => {
      console.log(user);

    if (err) {
        console.log("err");
        console.log(err);
      return next(err);
    }
    if (!user) {
        console.log("!user");
        console.log(info);
      res.status(200).send({ error: info });
      next();
    } else {
      req.logIn(user, (err) => {
        console.log("logIn err");
        console.log(info);
        if (err) {

          res.status(200).send({ error: info });
          next();
        } else {
        console.log("logIn no err");
        console.log(user);

          res.status(200).send({ user });
        }
      });
    }
  })(req, res, next),
);

app.get("/logout", (req, res) => {
  const session = req.session;

  req.logout();
  req.session.destroy();

  if (session && session.id) {
    mongodb.sessionDb
      .collection("sessions")
      .deleteOne({ _id: session.id }, (error, r) => {
        if (error) {
          console.error(error);
        } else {
          console.log("Deleted session");
        }
      });
  }

  res.clearCookie("homepageCounter", {
    domain: getCookieDomain(config.deployDomain),
  });
  res.clearCookie("casAccessToken", {
    domain: getCookieDomain(config.deployDomain),
  });
  res.clearCookie("casRefreshToken", {
    domain: getCookieDomain(config.deployDomain),
  });
  res.clearCookie("casAccessTokenExpiresIn", {
    domain: getCookieDomain(config.deployDomain),
  });
  res.redirect(`${config.cas_logout_host}?service=${config.publicHost}`);
});

app.post("/totp-secret", (req, res, next) => {
  const secret = Speakeasy.generateSecret({ length: 20 });
  res.send({ secret: secret.base32 });
});

app.post("/totp-generate", (req, res, next) => {
  const token = Speakeasy.totp({
    secret: req.body.secret,
    encoding: "base32",
    digits: 4,
    step: 30,
    window: 30,
    // step: 900
  });

  console.log("🚀 ~ file: server.js:342 ~ app.post ~ token:", token)
  const { emp_id } = req.body;

  const employee_gql_query = `
   query ($emp_id: [String]) {
     employees(emp_id: $emp_id) {
       phone
       email
     }
   }
   `;

  const employee_gql_variables = {
    emp_id,
  };

  let phone = null;
  // get phone number from empid
  fetch(config.api.employeeInternal, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer rVEk4fDSvbVGCL93ANHto8LR0KQIIP6v"
    },
    body: JSON.stringify({
      query: employee_gql_query,
      variables: employee_gql_variables,
    }),
  })
    .then((data) => data.json())
    .then((result) => {
      console.log("🚀 ~ file: server.js:343 ~ .then ~ result:", result)
      phone = result.data.employees[0].phone;
      // send pin through sms
      phone = `+852${phone.replace(/\s/g, "")}`;
      console.log("🚀 ~ file: server.js:376 ~ .then ~ phone:", phone)
      sendSMS(token, phone);
    })
    .catch((e) => {
      console.log("🚀 ~ file: server.js:350 ~ app.post ~ e:", e)
      console.error(e);
    });

  // send pin through app notification
  sendappNotification(token, emp_id);

  res.status(200).send({
    token,
    secret: req.body.secret,
    // remaining: 30 - Math.floor((new Date().getTime() / 1000.0) % 30)
  });
});

app.post("/totp-verify", (req, res, next) => {
  // retrieve secret from database
  const query = {
    "user.login.info.emp_id": req.body.emp_id,
  };

  const col = mongodb.sessionDb.collection("user");
  if (req.user !== undefined) {
    col
      .find(query)
      .sort({ _id: -1 })
      .limit(1)
      .toArray((err, docs) => {
        const valid = Speakeasy.totp.verify({
          secret: docs[0].secret,
          encoding: "base32",
          token: req.body.token,
          digits: 4,
          step: 30,
          window: 30,
          // step: 900
        });

        if (!valid) {
          req.user.authorized = false;
          res.send({
            valid,
            error: "PIN is invalid.",
          });
        } else {
          const { casAccessToken, casRefreshToken, casAccessTokenExpiresIn } = req.cookies;
          req.user.authorized = true;
          // find session by emp_id if already in db
          const sessionsquery = {
            "session.passport.user.login.info.emp_id": req.body.emp_id,
          };
          mongodb.sessionDb
            .collection("sessions")
            .find(sessionsquery)
            .sort({ expires: 1 })
            // .limit(1)
            .toArray((err, result) => {
              if (!err) {
                result.forEach((item, index) => {
                  if (result[index]._id != req.session.id) {
                    mongodb.sessionDb
                      .collection("sessions")
                      .deleteOne({ _id: result[index]._id }, (error, r) => {
                        if (error) {
                          console.error(error);
                        } else {
                          console.log("Deleted previous session");
                        }
                      });
                  }
                });
              }
            });

          // get ouath token
          const formData = {
            client_id: config.auth.client_id,
            client_secret: config.auth.client_secret,
            provision_key: config.auth.provision_key,
            authenticated_userid: config.auth.authenticated_userid,
            grant_type: config.auth.grant_type,
            scope: config.auth.scope,
          };

          const encodedformData = encodeFormData(formData);

          fetch(config.auth.url, {
            method: "POST",
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
            body: encodedformData,
          })
            .then((data) => data.json())
            .then((result) => {
              const { access_token, refresh_token, expires_in } = result;
              req.user.oauth = access_token;
              req.user.datetime = new Date().getTime();

              // save the refresh token for current user
              col.update(
                query,
                {
                  $set: {
                    refresh: refresh_token,
                    lastupdate: new Date(),
                    "user.casAccessToken": casAccessToken,
                    "user.casRefreshToken": casRefreshToken,
                    "user.casAccessTokenExpiresIn": casAccessTokenExpiresIn,
                  },
                },
                (err, r) => {
                  if (err) {
                    console.error(err);
                  } else {
                    console.log("added refresh token to user db");
                    // return oauth token for logging when login is successful
                    res.send({
                      valid,
                      token: access_token,
                      casAccessToken,
                      casRefreshToken,
                      casAccessTokenExpiresIn,
                    });
                  }
                },
              );
            });
        }
      });
  } else {
    res.send({
      valid: false,
      error: "Login is invalid",
    });
  }
});

app.post("/castoken-refresh", (req, res, next) => {
  fetch(
    `${config.cas_refresh_token_host}&client_id=${config.cas_client_id}&client_secret=${config.cas_client_secret}&refresh_token=${req.body.refresh_token}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    },
  ).then((result) => {
    if (result.status !== 400) {
      result.json().then((token) => {
        const col = mongodb.sessionDb.collection("user");
        const query = {
          "user.login.info.emp_id": req.user.login.info.emp_id,
        };
        col.update(
          query,
          {
            $set: {
              "user.casAccessToken": token.access_token,
              "user.casRefreshToken": token.refresh_token,
              "user.casAccessTokenExpiresIn": token.expires_in,
              lastupdate: new Date(),
            },
          },
          (err, r) => {
            if (err) {
              console.error(err);
            } else {
              res.send({
                valid: true,
                access_token: token.access_token,
                refresh_token: token.refresh_token,
                expires_in: token.expires_in,
              });
            }
          },
        );
      });
    } else {
      res.send({
        valid: false,
      });
    }
  });
});

app.post("/getUserPermissions", (req, res, next) => {
  const url = `${config.GATEWAY}`;
  console.log('----------------accessToken',req.body.accessToken);
  console.log("🚀 ~ file: server.js:535 ~ app.post ~ url:", url)
  const header = {
    headers: {
      appId: `${config.cas_client_id}`,
      accessToken: req.body.accessToken,
    },
  };

  fetch(url, header)
    .then((data) => data.json())
    .then((result) => {
      console.log("🚀 ~ file: server.js:546 ~ .then ~ result:", result)
      res.send({
        result,
      });
    })
    .catch((error) => {
      console.log("🚀 ~ file: server.js:552 ~ app.post ~ error:", error)
      res.send({
        error,
      });
    });
});

app.post("/totp-refresh", (req, res, next) => {
  // retrieve refresh token from database
  if (req.user.login.info.emp_id) {
    const query = {
      "user.login.info.emp_id": req.user.login.info.emp_id,
    };
    const col = mongodb.sessionDb.collection("user");
    col
      .find(query)
      .sort({ _id: -1 })
      .limit(1)
      .toArray((err, docs) => {
        if (!err) {
          const formData = {
            client_id: config.auth.client_id,
            client_secret: config.auth.client_secret,
            refresh_token: docs[0].refresh,
            grant_type: config.auth.refresh_grant_type,
          };

          const encodedformData = encodeFormData(formData);

          fetch(config.auth.url, {
            method: "POST",
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
            body: encodedformData,
          })
            .then((data) => data.json())
            .then((result) => {
              let valid = false;
              if (result.error) {
                valid = false;
                res.send({
                  valid,
                });
              } else {
                valid = true;
                const { access_token, refresh_token } = result;
                req.user.oauth = access_token;
                req.user.datetime = new Date().getTime();

                col.update(
                  query,
                  { $set: { refresh: refresh_token } },
                  (err, r) => {
                    if (err) {
                      console.error(err);
                    } else {
                      console.log("refresh token is updated");
                      // return oauth token for logging when login is successful
                      res.send({
                        valid,
                        token: access_token,
                      });
                    }
                  },
                );
              }
            });
        }
      });
  }
});

app.post("/checklogin", (req, res, next) => {
  mongodb.sessionDb
    .collection("sessions")
    .find({ _id: req.session.id })
    .limit(1)
    .toArray((err, result) => {
      let UserSessionExist = false;
      if (err) {
        console.error(err);
      } else if (result === undefined || result.length == 0) {
        UserSessionExist = false;
      } else {
        UserSessionExist = true;
      }
      UserSessionExist = true
      res.send({ UserSessionExist });
    });
});

app.post("/checkReCaptcha", (req, res, next) => {
  if (!req.body.response) {
    res.status(300).send("Missing params");
    return;
  }

  const formData = req.body;
  formData.secret = config.recaptchaSecretKey;
  const encodedformData = encodeFormData(formData);

  fetch("https://www.google.com/recaptcha/api/siteverify", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: encodedformData,
  })
    .then((resp) => {
      return resp.json();
    })
    .then((data) => {
      res.send(data);
    })
    .catch((error) => {
      console.error(error);
    });
});

app.post("/addLog", (req, res, next) => {
  addLog(req, res, next);
});

app.post("/jweToken", (req, res, next) => {
  let content = "";
  let publicKeyPath = "";
  if (req.body.source === "m1tablet") {
    publicKeyPath = `/home/<USER>/keys/${m1PublicKeyName}`;
    content = {
      accessToken: {
        access_token: req.body.casAccessToken,
        refresh_token: req.body.casRefreshToken,
        expires_in: 7200,
        token_type: "bearer",
      },
      source: "MSearch",
      exp: Math.floor(Date.now() / 1000) + 5 * 60,
      jid: UUID(),
    };
  } else if (req.body.source === "msearch") {
    publicKeyPath = "/home/<USER>/keys/os-uat.publickey.cer";
    content = {
      jid: UUID(),
      source: req.body.source,
      sso_token: {
        access_token: req.body.casAccessToken,
        token_type: "bearer",
        expires_in: 7200,
        refresh_token: req.body.casRefreshToken,
      },
      exp: Math.floor(Date.now() / 1000) + 5 * 60, // current time + 5 min
    };
  }

  res.json({
    jweToken: jose.encryptSource(
      req.body.kid,
      publicKeyPath,
      JSON.stringify(content),
    ),
  });
});

// Unlock stock detail
// -----------------------------------------------------------------------------
app.get("/stock/:stockid", (req, res, next) => {
  checkUnlockStock(req, res, next);
});

app.post("/getUnlockCount", (req, res, next) => {
  getUnlockCount(req, res, next);
});

app.post("/unlockStock", (req, res, next) => {
  unlockStock(req, res, next);
});

app.post("/unlockListStock", (req, res, next) => {
  unlockListStock(req, res, next);
});

// Media Upload
// -----------------------------------------------------------------------------
// const storage = multer.diskStorage({
//   destination: function (req, file, cb) {
//     cb(null, path.join(__dirname, config.media.tempDir))
//   }
// });
const getQuery = createGetQuery();

const storage = multer.memoryStorage();
const upload = multer({ storage });

// const upload = multer({
//   dest: path.join(__dirname, config.media.tempDir)
// });
const fileFields = [
  { name: 'medium', maxCount: 1 },
  { name: 'thumbnail', maxCount: 1 },
];
app.post("/createMedium", upload.fields(fileFields), (req, res, next) => {
  saveMedia(req, res, next, getQuery);
});

app.post("/updateMedium", (req, res, next) => {
  updateMedia(req, res, next, getQuery);
});

app.post("/removeMedium", (req, res, next) => {
  removeMedia(req, res, next, getQuery);
});

// Decrypt stock detail
// -----------------------------------------------------------------------------
app.post("/listStockDetail", (req, res, next) => {
  listStockDetail(req, res, next, getQuery);
});

// Proposal service
// -----------------------------------------------------------------------------
app.post("/createProposal", (req, res, next) => {
  createProposal(req, res, next, getQuery);
});

app.post("/createListProposal", (req, res, next) => {
  createListProposal(req, res, next, getQuery);
});

app.post("/createPreview", (req, res, next) => {
  createPreview(req, res, next, getQuery);
});

app.post("/listProposal", (req, res, next) => {
  listProposal(req, res, next, getQuery);
});

app.post("/removeProposal", (req, res, next) => {
  removeProposal(req, res, next, getQuery);
});

app.post("/getProposalCount", (req, res, next) => {
  getProposalCount(req, res, next, getQuery);
});

app.post("/getReCreatePP", (req, res, next) => {
  getReCreatePP(req, res, next, getQuery);
});

// My Favorite service
// -----------------------------------------------------------------------------
app.post("/addMyFavorite", (req, res, next) => {
  addtoMyFavorite(req, res, next);
});

app.post("/listMyFavorite", (req, res, next) => {
  listMyFavorite(req, res, next);
});

// Marked Stock service
// -----------------------------------------------------------------------------
app.post("/addMarkStock", (req, res, next) => {
  addMarkStock(req, res, next);
});

app.post("/listMarkStock", (req, res, next) => {
  listMarkStock(req, res, next);
});

app.post("/removeMarkStock", (req, res, next) => {
  removeMarkStock(req, res, next);
});

// Saleskit
// -----------------------------------------------------------------------------
// app.post("/ppStockCount", (req, res, next) => {
//   ppStockCount(req, res, next);
// })

// Save search
// -----------------------------------------------------------------------------
app.post("/saveSearch", (req, res, next) => {
  saveSearch(req, res, next);
});

app.post("/listSearch", (req, res, next) => {
  listSearch(req, res, next);
});

// Land search service
// -----------------------------------------------------------------------------
app.post("/listLandSearch", (req, res, next) => {
  listLandSearch(req, res, next);
});

app.post("/listLandSearchPdf", (req, res, next) => {
  listLandSearchPdf(req, res, next);
});

app.post("/saveLandSearch", (req, res, next) => {
  saveLandSearch(req, res, next);
});

app.get("/getLandSearchDoc/:id/:name?", (req, res, next) => {
  getLandSearchDoc(req, res, next);
});

app.post("/takeLandsearchLog", (req, res, next) => {
  takeLandSearchLog(req, res, next);
});

app.post("/applyLandSearch", (req, res, next) => {
  applyLandSearch(req, res, next, getQuery);
});

// Contacts
// -----------------------------------------------------------------------------
app.post("/getDeniedCalls", (req, res, next) => {
  getDeniedCalls(req, res, next);
});

// Company search service
// -----------------------------------------------------------------------------
app.post("/listCompanySearch", (req, res, next) => {
  listCompanySearch(req, res, next, getQuery);
});

app.post("/saveCompanySearch", (req, res, next) => {
  saveCompanySearch(req, res, next);
});

app.get("/getCompanySearchDoc/:companyCode/:fileName", (req, res, next) => {
  getCompanySearchDoc(req, res, next);
});

// Company service
// -----------------------------------------------------------------------------
app.post("/listCompanies", (req, res, next) => {
  listCompanies(req, res, next, getQuery);
});

app.post("/createCompany", (req, res, next) => {
  createCompany(req, res, next, getQuery);
});

app.post("/listCompany", (req, res, next) => {
  listCompany(req, res, next, getQuery);
});

app.post("/querySourceOptions", (req, res, next) => {
  querySourceOptions(req, res, next, getQuery);
});

app.post("/applyCompanySearch", (req, res, next) => {
  applyCompanySearch(req, res, next, getQuery);
});

// Log
// -----------------------------------------------------------------------------
app.post("/addActivityLog", (req, res, next) => {
  addActivityLog(req, res, next);
});

// Internal service
// -----------------------------------------------------------------------------
app.post("/district/graphql", (req, res, next) => {
  districtGql(req, res, next);
});

app.post("/street/graphql", (req, res, next) => {
  streetGql(req, res, next);
});

app.post("/contact/graphql", (req, res, next) => {
  contactGql(req, res, next);
});

app.post("/stock/graphql", (req, res, next) => {
  stockGql(req, res, next);
});

app.post("/employee/graphql", (req, res, next) => {
  employeeGql(req, res, next);
});

app.post("/search/graphql", (req, res, next) => {
  searchGql(req, res, next);
});

app.post("/www/graphql", (req, res, next) => {
  wwwGql(req, res, next);
});

app.post("/building/graphql", (req, res, next) => {
  buildingGql(req, res, next);
});

app.post("/transaction/graphql", (req, res, next) => {
  transactionGql(req, res, next);
});

app.post("/media/graphql", (req, res, next) => {
  mediaGql(req, res, next);
});

app.post("/supplement/graphql", (req, res, next) => {
  supplementGql(req, res, next);
});

app.post("/landsearch/graphql", (req, res, next) => {
  landsearchGql(req, res, next);
});

// asset
app.get("/asset/getPinImg", (req, res, next) => {
  getPinImg(req, res, next);
});

app.get("/asset/video", (req, res, next) => {
  video(req, res, next);
});
//
// Register API middleware
// -----------------------------------------------------------------------------
// app.use(
//   "/graphql",
//   expressGraphQL(req => ({
//     schema,
//     graphiql: __DEV__,
//     rootValue: { request: req },
//     pretty: __DEV__
//   }))
// );

//
// Register server-side rendering middleware
// -----------------------------------------------------------------------------
app.get("*", async (req, res, next) => {
  try {
    const css = new Set();
    const fromMobile = isMobile(req);

    // Enables critical path CSS rendering
    // https://github.com/kriasoft/isomorphic-style-loader
    const insertCss = (...styles) => {
      // eslint-disable-next-line no-underscore-dangle
      styles.forEach((style) => css.add(style._getCss()));
    };

    // Universal HTTP client
    const fetch = createFetch(nodeFetch, {
      baseUrl: config.api.serverUrl,
      cookie: req.headers.cookie,
      // access_token: req.user ? req.user.oauth : null
      // schema,
      // graphql
    });
    const initialState = {
      auth: { user: req.user },
      stocklist: {
        max: parseInt(config.generalDailyQuota, 10),
        fieldhistory: [],
      },
      transaction: {},
    };

    const store = configureStore(initialState, {
      fetch,
      // I should not use `history` on server.. but how I do redirection? follow universal-router
    });

    // Global (context) variables that can be easily accessed from any React component
    // https://facebook.github.io/react/docs/context.html
    const context = {
      insertCss,
      fetch,
      // The twins below are wild, be careful!
      pathname: req.path,
      query: req.query,
      // You can access redux through react-redux connect
      store,
      storeSubscription: null,
      browserDetect: fromMobile ? "mobile" : "desktop",
    };

    let route = await router("mobile").resolve(context);
    if (config.enableDesktopView == "true") {
      if (fromMobile) {
        // do nothing already loaded mobile router
      } else {
        // load desktop router
        route = await router("desktop").resolve(context);
      }
    }

    if (route.redirect) {
      res.redirect(route.status || 302, route.redirect);
      return;
    }

    const locale = req.cookies.locale || config.locale;

    const sheets = new ServerStyleSheets();
    const data = { ...route };
    data.children = ReactDOM.renderToString(
      sheets.collect(
        <Provider store={store}>
          <ConnectedIntlProvider locale={req.cookies.locale}>
            <App context={context}>
              <ThemeProvider theme={theme}>{route.component}</ThemeProvider>
            </App>
          </ConnectedIntlProvider>
        </Provider>,
      ),
    );
    data.styles = [{ id: "css", cssText: [...css].join("") }];

    const scripts = new Set();
    const addChunk = (chunk) => {
      if (chunks[chunk]) {
        chunks[chunk].forEach((asset) => scripts.add(asset));
      } else if (__DEV__) {
        throw new Error(`Chunk with name '${chunk}' cannot be found`);
      }
    };
    addChunk("client");
    if (route.chunk) addChunk(route.chunk);
    if (route.chunks) route.chunks.forEach(addChunk);

    data.scripts = Array.from(scripts);
    data.app = {
      apiUrl: config.api.clientUrl,
      state: context.store.getState(),
      locale,
    };

    const muicss = sheets.toString();

    const html = ReactDOM.renderToStaticMarkup(<Html {...data} css={muicss} />);
    res.status(route.status || 200);
    res.send(`<!doctype html>${html}`);
  } catch (err) {
    next(err);
  }
});

//
// Error handling
// -----------------------------------------------------------------------------
const pe = new PrettyError();
pe.skipNodeFiles();
pe.skipPackage("express");

// eslint-disable-next-line no-unused-vars
app.use((err, req, res, next) => {
  console.error(pe.render(err));
  const html = ReactDOM.renderToStaticMarkup(
    <Html
      title="Internal Server Error"
      description={err.message}
      styles={[{ id: "css", cssText: errorPageStyle._getCss() }]} // eslint-disable-line no-underscore-dangle
    >
      {ReactDOM.renderToString(<ErrorPageWithoutStyle error={err} />)}
    </Html>,
  );
  res.status(err.status || 500);
  res.send(`<!doctype html>${html}`);
});

//
// Launch the server
// -----------------------------------------------------------------------------
// const promise = models.sync().catch(err => console.error(err.stack));
if (config.mode === "production") {
} else if (config.mode === "development") {
  app.hot = module.hot;
  module.hot.accept("./core/router");
}

mongodb
  .connect()
  .catch((err) => console.error(err))
  .then(() => {
    clearUnlockRecord();
    if (config.mode === "production") {
      app.listen(config.port, () => {
        console.log(
          `The server is running at http://localhost:${config.port}/`,
        );
      });
    }
  });

export default app;
