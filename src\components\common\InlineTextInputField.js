import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import TextField from '@material-ui/core/TextField';

// We can inject some CSS into the DOM.
const styles = {
    root: {

    },
    inputBase: {
        fontSize: "1em",
    },
    input: {
        minHeight: "1.375em",
        padding: "2px 0",
    },
    hidden: {
        display: "none",
    },
    display: {
        width: "100%",
        overflow: "hidden",
        textOverflow: "ellipsis",
        whiteSpace: "nowrap",
    },
    error: {
        borderBottom: "1px solid #f44336",
    },
    errorText: {
        color: "#f44336",
        fontSize: "0.75em",
        marginTop: "3px",
        lineHeight: "1.66",
        letterSpacing: "0.03333em",
    },
    smlabel: {
        fontSize: 18,
        lineHeight: 1.5
    },
    inputbox: {
        padding: "8px 0"
    }
};

function InlineTextInputField(props) {
    const {
        classes,
        className,
        placeholder,
        input,
        label,
        meta: { touched, invalid, error },
        customInputProps = {},
        inputToDisplay = (v) => v,
        jsonField = "value",
        extraHandleChange,
        aligntoLabel,
        ...custom
    } = props;

    const handleBlur = (e) => {
        e.preventDefault();
        input.onBlur();
    };

    const handleOnChange = (e) => {
        if (extraHandleChange) extraHandleChange(e.target.value);
        if (typeof input.value === "object")
            input.onChange({ ...input.value, [jsonField]: e.target.value });
        else
            input.onChange(e.target.value);
    };

    let value = typeof input.value === "object" ? input.value[jsonField] : input.value;
    if (typeof value === "undefined") value = "";

    return (
        <div className={clsx(classes.root, className)}>
            <TextField
                id="standard-number"
                label={label}
                error={touched && invalid}
                helperText={touched && error}
                InputLabelProps={{
                    shrink: true,
                    className: classes.smlabel,
                    // style: { fontSize: 18 }
                }}
                InputProps={{
                    className: classes.inputbox
                }}
                {...input}
                value={value || null}
                onChange={handleOnChange}
                onBlur={handleBlur}
                {...custom}
            />
        </div>
    );
}

InlineTextInputField.propTypes = {
    classes: PropTypes.object.isRequired,
    className: PropTypes.string,
    placeholder: PropTypes.string,
    input: PropTypes.object.isRequired,
    meta: PropTypes.object,
    customInputProps: PropTypes.object,
    inputToDisplay: PropTypes.func,
    jsonField: PropTypes.string,
};

export default withStyles(styles)(InlineTextInputField);
