import React, { useRef, useState, useCallback } from "react";
import PropTypes from "prop-types";
import { injectIntl } from "react-intl";
import { withStyles } from "@material-ui/core/styles";

const styles = {
  input: {
    display: "none"
  },
  error: {
    color: "#F44336",
    fontSize: "0.875em",
  },
  fileName: {
    marginTop: 5,
    fontSize: "0.875em",
  }
};

const FieldFileInput = (props) => {
  const {
    input: { value, onChange, onBlur },
    classes,
    label,
    className,
    accept,
    multiple,
    fileName,
    meta: { touched, invalid, error },
    maxFiles,
    intl,
  } = props;
  
  const fileInput = useRef(null);
  const [firstFileName, setFirstFileName] = useState(null);
  const [filesError, setFilesError] = useState(null);

  const handleFileChange = useCallback((e) => {
    const files = e.target.files;
    
    // 检查文件数量限制
    if (maxFiles && files.length > maxFiles) {
      onChange([]);
      setFilesError(`${intl.formatMessage({ id: "media.maxFiles" }, { maxFiles })}`);
      return;
    }
    
    if (files?.[0]?.name) {
      setFirstFileName(files[0].name);
    }
    
    onChange(files);
  }, [maxFiles, onChange, intl]);

  const handleLabelClick = useCallback((e) => {
    if (fileInput.current) {
      fileInput.current.click();
      onBlur(); // update touched
    }
  }, [onBlur]);

  // 显示文件名或者已上传文件数量
  const renderFileInfo = () => {
    if (filesError) {
      return <div className={classes.error}>{filesError}</div>;
    }
    
    if (touched && error) {
      return <div className={classes.error}>{error}</div>;
    }
    
    if (fileName) {
      return <div className={classes.fileName}>{fileName}</div>;
    }
    
    if (firstFileName) {
      return <div className={classes.fileName}>
        {multiple && value && value.length > 1 
          ? `${firstFileName} (共 ${value.length} 个文件)` 
          : firstFileName}
      </div>;
    }
    
    return null;
  };

  return(
    <div className={className}>
      <div onClick={handleLabelClick} style={{ cursor: 'pointer' }}>
        {label}
      </div>
      <input
        type="file"
        className={classes.input}
        ref={fileInput}
        accept={accept}
        multiple={multiple || false}
        onChange={handleFileChange}
      />
      {renderFileInfo()}
    </div>
  );
};

FieldFileInput.propTypes = {
  classes: PropTypes.object.isRequired,
  input: PropTypes.object.isRequired,
  meta: PropTypes.object.isRequired,
  label: PropTypes.node,
  className: PropTypes.string,
  accept: PropTypes.string,
  multiple: PropTypes.bool,
  fileName: PropTypes.string,
  maxFiles: PropTypes.number, // 新增最大文件数量限制属性
  intl: PropTypes.object.isRequired, // 添加 intl 属性验证
};

export default React.memo(withStyles(styles)(injectIntl(FieldFileInput)));
