import React from "react";
import { reduxForm } from "redux-form";
import { withStyles } from "@material-ui/core/styles";
import FormButton from "../../../common/FormButton";
import FieldSection from "./FormSection/FieldSection";
import clsx from "clsx";
import { FormattedMessage } from "react-intl";

const styles = (theme) => ({
  root: {
    // backgroundColor: "#FFF",
    // paddingBottom: "7vh"
  },
  searchbutton: {
    margin: "1vw",
    textTransform: "none",
    fontSize: "1.125em",
    height: "42px",
  },
  clearbutton: {
    background: "#6be6a1",
  },
  buttoncontainer: {
    display: "flex",
    justifyContent: "space-evenly",
    alignItems: "center",
    zIndex: "999",
    position: "fixed",
    left: "50vw",
    bottom: "2vh",
    transform: "translateX(-50%)",
  },
});

const validate = (values) => {
  const errors = {};
  // if (values.dateMin && values.dateMax) {
  //   if (values.dateMin > values.dateMax) {
  //     errors.dateMin = errors.dateMax = "Input format is invalid";
  //   }
  // }
  return errors;
};

let ProposalForm = (props) => {
  const {
    classes,
    handleSubmit,
    error,
    submitting,
    reset,
    pristine,
    isSticky,
    initialize,
    initialValues,
    changeFieldValue,
    formFields,
    handleOpenSubmitDialog,
    handleProposalNameChange,
  } = props;

  const handleResetClick = () => {
    initialize(initialValues);
  };

  return (
    <form onSubmit={handleSubmit} className={classes.root}>
      <FieldSection
        initialValues={initialValues}
        changeFieldValue={changeFieldValue}
        formFields={formFields}
        initialize={initialize}
        handleProposalNameChange={handleProposalNameChange}
      >
        {/*<div className={classes.buttoncontainer}>*/}
        {/*  <FormButton*/}
        {/*    disabled={submitting}*/}
        {/*    onClick={handleResetClick}*/}
        {/*    className={clsx(classes.searchbutton, classes.clearbutton)}*/}
        {/*  >*/}
        {/*    <FormattedMessage id="proposal.form.reset" />*/}
        {/*  </FormButton>*/}
        {/*  <FormButton className={classes.searchbutton} onClick={handleOpenSubmitDialog}>*/}
        {/*    <FormattedMessage id="proposal.form.save" />*/}
        {/*  </FormButton>*/}
        {/*</div>*/}
      </FieldSection>
    </form>
  );
};

ProposalForm = reduxForm({
  form: "proposalForm",
  enableReinitialize: true,
  validate,
})(ProposalForm);

export default withStyles(styles)(ProposalForm);
