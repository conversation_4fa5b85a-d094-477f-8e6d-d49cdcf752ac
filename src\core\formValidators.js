// import validator from 'validator';
import moment from "moment";

const isEmpty = (value) =>
  value === undefined || value === null || value === "";

export const required = (intl, fieldNameOrId) => (value) => {
  if (isEmpty(value)) {

    if (!fieldNameOrId) {
      return "Must be filled";
    }

    const fieldName = intl.formatMessage({ id: fieldNameOrId })
    return intl.formatMessage({ id: 'common.field.required' }, { field: fieldName });
  }
  return undefined;
}

export const arrayRequired = (arr) =>
  !arr || arr.length === 0 ? "Must be filled" : undefined;

export const minLength = (min) => (value) =>
  !isEmpty(value) && value.length < min
    ? `Must be at least ${min} characters`
    : undefined;

export const maxLength = (max) => (value) =>
  !isEmpty(value) && value.length > max
    ? `Must be no more than ${max} characters`
    : undefined;

export const date = (format) => (value) =>
  !moment(value, format).isValid() ? "Must be a valid date" : undefined;

export const atLeastOneFile = (value) =>
  !value || Array.from(value).length === 0
    ? "At least one photo must be chosen."
    : undefined;

export const imageFile = (value) => {
  const imageFileExtensions = ["jpg", "jpeg", "png"];

  if (!value) return null;

  let notImage = Array.from(value).filter((v) => {
    if (!v.name) return true;
    let bits = v.name.split(".");
    if (bits.length < 2) return true;
    let extension = bits[bits.length - 1];
    if (imageFileExtensions.indexOf(extension.toLowerCase()) < 0) return true;
  });

  return notImage.length > 0
    ? "Only some photo formats can be selected. (.jpg, .jpeg, .png)"
    : undefined;
};

export const videoFile = (value) => {
  const videoFileExtensions = ["mp4", "mov" ]

  if (!value) return null;

  let notVideo = Array.from(value).filter((v) => {
    if (!v.name) return true;
    let bits = v.name.split(".");
    if (bits.length < 2) return true;
    let extension = bits[bits.length - 1];
    if (videoFileExtensions.indexOf(extension.toLowerCase()) < 0) return true;
  });

  return notVideo.length > 0 ? "Only some video formats can be selected. (.mp4, .mov)" : undefined;
};

export const pdfFile = (value) => {
  const pdfFileExtensions = ["pdf"];

  if (!value) return null;

  let notPdf = Array.from(value).filter((v) => {
    if (!v.name) return true;
    let bits = v.name.split(".");
    if (bits.length < 2) return true;
    let extension = bits[bits.length - 1];
    if (pdfFileExtensions.indexOf(extension.toLowerCase()) < 0) return true;
  });

  return notPdf.length > 0 ? "Only some pdf formats can be selected. (.pdf)" : undefined;
};

export const minValue = (min, msg) => (value) =>
  value && value < min ? msg : undefined;

export const maxValue = (max, msg) => (value) =>
  value && value > max ? msg : undefined;

export const number = (msg) => (value) =>
  value && isNaN(Number(value)) ? msg : undefined;
