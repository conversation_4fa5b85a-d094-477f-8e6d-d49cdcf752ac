/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React, { useEffect } from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import _ from "lodash";

import StockDetail from "../../../../components/mobile/IND/Stock";
import {
  listStockDetail,
  clearStock,
  listmyFavorite,
  listMarkStock,
  listStockMedia,
  queryMarkWWWList,
} from "../../../../actions/stock";
import {
  clearBuildingDetail,
  listBuildingMedia,
} from "../../../../actions/building";
import { addActivityLog } from "../../../../actions/log";
function Stock({
  empId,
  detail,
  listStockDetail,
  listmyFavorite,
  listMarkStocks,
  listStockMedia,
  listBuildingMedia,
  queryMarkWWWList,
  clearStock,
  clearBuildingDetail,
  stockid,
  headerRef,
  defaultTab,
  hash,
  mode,
  isListProposal,
  addActivityLog
}) {
  useEffect(() => {
    const variables = {
      _id: stockid.map((id) => id.toString()),
    };
    listStockDetail(variables, hash, isListProposal);
    listmyFavorite();
    listMarkStocks();

    return () => {
      clearStock();
      clearBuildingDetail();
    };
  }, [stockid]);

  React.useEffect(() => {
    const stockIds = stockid.filter(v => v).map(v => v.toString());
    if (stockIds.length) queryMarkWWWList(stockIds);
  }, [stockid, queryMarkWWWList]);

  useEffect(() => {
    if (!_.isEmpty(detail)) {
      const ids = { stockId: [], buildingId: [] };
      detail.forEach((stockElem) => {
        if (stockElem) {
          const unicornId = _.get(stockElem, "unicorn.id");
          const buildingUnicornId = _.get(stockElem, "building.unicorn.id");
          if (unicornId && Number.isInteger(unicornId)) {
            ids.stockId.push(unicornId.toString());
          }
          if (buildingUnicornId && Number.isInteger(buildingUnicornId)) {
            ids.buildingId.push(buildingUnicornId.toString());
          }
          addActivityLog("property.stock.general", "read", { ...stockElem });
        }
      });

      // fetch stock and building media
      listStockMedia({
        sid: ids.stockId,
        empId,
      });
      listBuildingMedia({
        sid: ids.buildingId,
        empId,
      });
    }
  }, [detail]);

  useEffect(() => {
    if (headerRef.current)
      headerRef.current.changeHeader({ title: "", subTitle: "" });
  }, []);

  return (
    <StockDetail
      headerRef={headerRef}
      defaultTab={defaultTab}
      multipleStocks={stockid.length > 1}
      mode={mode}
    />
  );
}

Stock.propTypes = {
  detail: PropTypes.array.isRequired,
  empId: PropTypes.string.isRequired,
  listStockDetail: PropTypes.func.isRequired,
  clearStock: PropTypes.func.isRequired,
  listmyFavorite: PropTypes.func.isRequired,
  listMarkStocks: PropTypes.func.isRequired,
  listStockMedia: PropTypes.func.isRequired,
  listBuildingMedia: PropTypes.func.isRequired,
  stockid: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  defaultTab: PropTypes.number,
  hash: PropTypes.string,
  mode: PropTypes.string,
  isListProposal: PropTypes.string,
  clearBuildingDetail: PropTypes.func.isRequired,
};

const mapStateToProps = (state) => ({
  empId: _.get(state, "auth.user.login.info.emp_id") || "",
  detail: _.get(state, "stock.detail") || [],
  markedWWW: _.get(state, "stock.markedWWW", []),
});

const mapDispatchToProps = (dispatch) => {
  return {
    listStockDetail: (graphqlvariable, hash, isListProposal) =>
      dispatch(listStockDetail(graphqlvariable, hash, isListProposal)),
    listmyFavorite: () => dispatch(listmyFavorite()),
    listMarkStocks: () => dispatch(listMarkStock()),
    listStockMedia: (...args) => dispatch(listStockMedia(...args)),
    listBuildingMedia: (...args) => dispatch(listBuildingMedia(...args)),
    clearStock: () => dispatch(clearStock()),
    clearBuildingDetail: () => dispatch(clearBuildingDetail()),
    addActivityLog: (...args) => dispatch(addActivityLog(...args)),
    queryMarkWWWList: (stockId = "") => dispatch(queryMarkWWWList({ stockId })),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(Stock);
