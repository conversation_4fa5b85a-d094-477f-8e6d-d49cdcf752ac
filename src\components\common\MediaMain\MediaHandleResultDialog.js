import React from "react";
import { useIntl } from "react-intl";
import Dialog from "../Dialog";
import DialogFrame from "../DialogFrame";
import { connect } from "react-redux";
import { clearProgressingMediumById, updateMedium } from "@/actions/medium";
import { updateBuildingMediaByIndex } from "@/actions/building";
import { updateStockMediaByIndex } from "@/actions/stock";
import PropTypes from "prop-types";

function MediaHandleResultDialog(props) {
  const {
    media,
  } = props;
  const intl = useIntl();

  const currentProgressingMedia = React.useMemo(() => {
    return props.allProgressingMedia.find(v => media?.id === v.id);
  }, [media, props.allProgressingMedia]);

  // 检查媒体是否正在删除中
  const isBeingDeleted = React.useMemo(() => {
    return currentProgressingMedia?.operation === "delete";
  }, [currentProgressingMedia]);

  const handleProgressingMediaDialogClose = React.useCallback((media) => {
    const { buildingMedia = {}, stockMedia = {} } = props;

    // 在本地更新 media state, 不要重新请求, 节省资源
    const findMediaIndexById = (mediaArr, mediaId) => {
      const result = { index: -1, mediaIndex: -1, media: {}, mediaType: props.mediaType };
      for (let i = 0; i < mediaArr.length; ++i) {
        const mediaItem = mediaArr[i];
        result.mediaIndex = mediaItem?.data?.[props.mediaType]?.findIndex(m => m.id === mediaId) ?? -1;
        if (result.mediaIndex !== -1) {
          result.index = i;
          result.media = { approval: media.approval };
          break;
        }
      }
      return result;
    }
    const stockFindResult = findMediaIndexById(stockMedia, media.id);
    if (stockFindResult.index === -1) {
      const buildFindResult = findMediaIndexById(buildingMedia, media.id);
      if (buildFindResult.index !== -1 && buildFindResult.mediaIndex !== -1) {
        props.updateBuildingMediaByIndex(buildFindResult);
      }
    } else if (stockFindResult.mediaIndex !== -1) {
      props.updateStockMediaByIndex(stockFindResult);
    }
    props.clearProgressingMediumById(media.id);
  }, [
    props.mediaType,
    props.stockMedia,
    props.buildingMedia,
    props.updateBuildingMediaByIndex,
    props.updateStockMediaByIndex,
    props.clearProgressingMediumById,
  ]);

  const handleMainClick = React.useCallback(() => {
    return handleProgressingMediaDialogClose(currentProgressingMedia);
  }, [currentProgressingMedia, handleProgressingMediaDialogClose]);

  return (
    <Dialog
      open={currentProgressingMedia?.state && 
            currentProgressingMedia?.state !== "updating" && 
            !isBeingDeleted}
      handleClose={handleMainClick}
      fullWidth={true}
    >
      <DialogFrame
        // buttonMain={intl.formatMessage({ id: "common.ok" })}
        // handleMain={handleMainClick}
      >
        {currentProgressingMedia?.state === "error" && intl.formatMessage({ id: "media.message.update.failed" })}
        {currentProgressingMedia?.state === "done" && intl.formatMessage({ id: "media.message.shareToPublic.ok" })}
      </DialogFrame>
    </Dialog>
  );
}

MediaHandleResultDialog.propTypes = {
  media: PropTypes.object,
  /** video | kolVideo | panorama | photo | document | virtualTour */
  mediaType: PropTypes.string,

  updateMedium: PropTypes.func,
  clearProgressingMediumById: PropTypes.func,
  updateStockMediaByIndex: PropTypes.func,
  updateBuildingMediaByIndex: PropTypes.func,
  allProgressingMedia: PropTypes.array,

  buildingMedia: PropTypes.object,
  stockMedia: PropTypes.object,
};

const mapStateToProps = state => ({
  allProgressingMedia: state.medium.progressingMedia || [],
  buildingMedia: state.building.media || {},
  stockMedia: state.stock.media || {},
});

const mapDispatchToProps = (dispatch) => ({
  updateMedium: (...args) => dispatch(updateMedium(...args)),
  clearProgressingMediumById: (mediaId) => dispatch(clearProgressingMediumById(mediaId)),
  updateStockMediaByIndex: (...args) => dispatch(updateStockMediaByIndex(...args)),
  updateBuildingMediaByIndex: (...args) => dispatch(updateBuildingMediaByIndex(...args)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(MediaHandleResultDialog);
