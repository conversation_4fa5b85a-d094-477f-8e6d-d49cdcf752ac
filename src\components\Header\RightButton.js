import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import Button from "@material-ui/core/Button";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    minWidth: 0,
    color: "#FFF",
    padding: 6,
    "& svg": {
      width: "1.5em",
      height: "1.5em",
    }
  }
};

function RightButton(props) {
  const { classes, children, className, ...other } = props;

  return (
    <Button className={clsx(classes.root, className)} {...other}>
      {children}
    </Button>
  );
}

RightButton.propTypes = {
  children: PropTypes.node,
  classes: PropTypes.object.isRequired,
  className: PropTypes.string
};

export default withStyles(styles)(RightButton);
