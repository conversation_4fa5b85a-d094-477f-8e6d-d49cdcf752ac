import { createStyles } from "@material-ui/core";
import "../variables.css";

const style = createStyles((theme) => ({
  root: {
    background: theme.base.backgroundColor,
    color: "#fff",
  },
  container: {
    minHeight: 48,
    margin: "0 auto",
    padding: "2px 0",
    maxWidth: "var(--max-content-width)",
    display: "flex",
    alignItems: "center",
  },
  brand: {
    color: "color(var(--brand-color) lightness(+10%))",
    textDecoration: "none",
    fontSize: "1.75em",
  },
  brandTxt: {
    fontSize: "1.25em",
    textAlign: "center",
    margin: "0 5px",
    flex: 1,
    textOverflow: "ellipsis",
    overflow: "hidden",
    whiteSpace: "nowrap",
  },
  welcomebrandTxt: {
    fontSize: "1.25em",
    textAlign: "left",
    textOverflow: "ellipsis",
    overflow: "hidden",
    whiteSpace: "nowrap",
    position: "absolute",
    padding: "0 5vw",
  },
  logout: {
    position: "absolute",
    right: "5vw",
  },
  banner: {
    textAlign: "center",
  },
  bannerTitle: {
    margin: 0,
    padding: 10,
    fontWeight: "normal",
    fontSize: "4em",
    lineHeight: "1em",
  },
  bannerDsec: {
    padding: 0,
    color: "#000",
    fontSize: "1.25em",
    margin: 0,
  },
  link: {
    color: "inherit",
    textDecoration: "none",
  },
  hidden: {
    visibility: "hidden",
    /* display: none; */
  },
  sticky: {
    position: "-webkit-sticky" /* Safari */,
    position: "sticky",
    top: 0,
    zIndex: 999,
  },
  logoutbtn: {
    backgroundColor: theme.base.secondbaseColor,
  },
  titleContainer: {
    flex: 1,
    overflow: "hidden",
  },
  subTitle: {
    fontSize: ".8em",
    textAlign: "center",
    margin: "0 5px",
    textOverflow: "ellipsis",
    overflow: "hidden",
    whiteSpace: "nowrap",
  },
}));

export { style };
