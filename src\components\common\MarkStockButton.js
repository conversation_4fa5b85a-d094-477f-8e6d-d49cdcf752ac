import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import InsertDriveFileIcon from "@material-ui/icons/InsertDriveFile";
import NoteAddIcon from "@material-ui/icons/NoteAdd";
import { toggleMarkStock } from "../../actions/stock";
import { connect } from "react-redux";

const styles = {
  root: {
    display: "inline-block",
  },
  icon: {
    color: "#E3E3E3",
  },
  checkedIcon: {
    color: "#13CE66",
  },
};

function MarkStockButton({
  classes,
  checked,
  stockId,
  toggleMarkStock,
}) {
  const handleClick = (e) => {
    e.stopPropagation();
    toggleMarkStock(stockId);
  };

  return (
    <div className={classes.root}>
      {checked ? (
        <InsertDriveFileIcon
          className={classes.checkedIcon}
          onClick={handleClick}
        />
      ) : (
        <NoteAddIcon className={classes.icon} onClick={handleClick} />
      )}
    </div>
  );
}

MarkStockButton.propTypes = {
  classes: PropTypes.object.isRequired,
  checked: PropTypes.bool.isRequired,
  stockId: PropTypes.string.isRequired,
  toggleMarkStock: PropTypes.func.isRequired,
};

const mapDispatchToProps = (dispatch) => ({
  toggleMarkStock: (id) => dispatch(toggleMarkStock(id)),
});

export default connect(
  null,
  mapDispatchToProps,
)(withStyles(styles)(MarkStockButton));
