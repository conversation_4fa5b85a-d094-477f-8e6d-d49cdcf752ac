import React, { useEffect, useRef, useState } from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import _ from "lodash";
import { FormattedMessage, injectIntl } from "react-intl";
import { makeStyles } from "@material-ui/core/styles";
import { Tab, Typography } from "@material-ui/core";

import Layout from "@/components/Layout/Layout";
import { clearCompany, queryCompany } from "@/actions/company";
import LoadingOverlay from "@/components/LoadingOverlay";
import StyledTabs from "@/components/common/StyledTabs";
import General from "@/components/Company/General";
import ApplyCompanySearch from "@/components/Company/ApplyCompanySearch";

const useStyles = makeStyles({
  tabs: {},
});

function TabPanel(props) {
  const { children, value, index, ...other } = props;
  const [loaded, setLoaded] = React.useState(false);

  if (!loaded && value === index) setLoaded(true);

  return (
    <Typography
      component="div"
      role="tabpanel"
      hidden={value !== index}
      id={`full-width-tabpanel-${index}`}
      aria-labelledby={`full-width-tab-${index}`}
      {...other}
    >
      {loaded ? <>{children}</> : null}
    </Typography>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.any.isRequired,
  value: PropTypes.any.isRequired,
};

const a11yProps = (index) => ({
  id: `full-width-tab-${index}`,
  "aria-controls": `full-width-tabpanel-${index}`,
});

function CompanyPage({
  id,
  company,
  getCompany,
  clearCompany,
  gettingCompany,
  intl,
}) {
  const classes = useStyles();

  const [tab, setTab] = useState(0);
  const headerRef = useRef(null);

  useEffect(() => {
    if (id) {
      getCompany(id);
    }
    return () => {
      clearCompany();
    };
  }, [id]);

  useEffect(() => {
    if (headerRef && !_.isEmpty(company)) {
      const companyName =
        _.get(company, `companyName${_.startCase(intl.locale)}`) ||
        _.get(
          company,
          intl.locale === "zh" ? "companyNameEn" : "companyNameZh",
        );
      if (headerRef.current){
        headerRef.current.changeTitle(companyName);
        headerRef.current.changeSubTitle(
          _.get(company, "companyRegistrationNumber") || "",
        );
      }
    }
  }, [company, headerRef.current]);

  return (
    <Layout headerRef={headerRef} backToListStep={1} isSticky>
      <StyledTabs tab={tab} handleTabChange={setTab} position="sticky">
        <Tab
          labelKey="company.tab.generalInfo"
          label={<FormattedMessage id="company.tab.generalInfo" />}
          {...a11yProps(0)}
        />
        <Tab
          labelKey="company.tab.applySearch"
          label={<FormattedMessage id="company.tab.applySearch" />}
          {...a11yProps(1)}
        />
      </StyledTabs>

      <TabPanel value={tab} index={0}>
        <General />
      </TabPanel>
      <TabPanel value={tab} index={1}>
        <ApplyCompanySearch />
      </TabPanel>

      {gettingCompany && <LoadingOverlay />}
    </Layout>
  );
}

CompanyPage.defaultProps = {
  company: null,
};

CompanyPage.propTypes = {
  id: PropTypes.string.isRequired,
  getCompany: PropTypes.func.isRequired,
  clearCompany: PropTypes.func.isRequired,
  gettingCompany: PropTypes.bool.isRequired,
  company: PropTypes.object,
  intl: PropTypes.object.isRequired,
};

const mapDispatchToProps = (dispatch) => ({
  getCompany: (id) => dispatch(queryCompany(id)),
  clearCompany: () => dispatch(clearCompany()),
});

const mapStateToProps = (state) => ({
  company: state.company.company,
  gettingCompany: state.company.listingCompany,
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(CompanyPage));
