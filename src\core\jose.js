const { JWE, <PERSON><PERSON><PERSON> } = require('jose');
const fs = require('fs');
let commonParams = { use: 'enc' };

module.exports = {
  /**
   * JOSE加密字符串
   * @param {string} payload 待加密的内容
   * @time 2020-03-10 12:21:30
   * @returns
   */
  encryptSource: function encryptSource(kid, publicKeyPath, payload) {
    const pubKey = JWK.asKey(fs.readFileSync(publicKeyPath), { kid: kid, ...commonParams });
    const dataJwsInJwe = JWE.encrypt(payload, pubKey, { kid: pubKey.kid });
    return dataJwsInJwe;
  },
  /**
   * JOSE解密字符串
   * @param {string}} payload 待解密的内容
   * @time 2020-03-10 12:21:30
   * @returns
   */
  decryptSource: function decryptSource(payload) {
    const privateKey = JWK.asKey(fs.readFileSync("/home/<USER>/os-uat.privatekey.pem"), { kid: 'OHS', ...commonParams });
    const decrypted = JWE.decrypt(payload, privateKey);
    return decrypted.toString();
  }
}
