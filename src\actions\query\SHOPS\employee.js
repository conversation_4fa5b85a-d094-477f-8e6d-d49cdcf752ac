export const LIST_EMPLOYEES_QUERY = `
  query ($emp_id: [String]) {
    employees(emp_id: $emp_id) {
      emp_id
      firstname_en
      nickname
      surname_en
      dept_code
      phone
      email
      is_manager
      grade_id
      belongingTeams
      _id
      name_en
      name_zh
      licence
      sex
      auth {
        team
        role
      }
    }
  }
  `;

export const LIST_VALID_EMPLOYEES_QUERY = `
  query ($name: String, $limit: Int, $offset: Int, $hasTeam: Boolean, $sbu: String, $valid_emp: Boolean, $isNew: Boolean) {
    employees(
      name: $name,
      limit: $limit,
      offset: $offset,
      hasTeam: $hasTeam,
      sort: [{field: name_en, order: asc}],
      sbu: $sbu,
      valid_emp: $valid_emp,
      isNew: $isNew,
    ) {
      _id
      emp_id
      name_en
      name_zh
      licence
      phone
      email
      sex
      dept_code
      valid_emp
    }
  }
  `;

export const LIST_TEAMS_EMPLOYEES_QUERY = `
  query ($team: [String], $grade_id: [String], $emp_type: [String]) {
    employees(valid_emp: true, team: $team, grade_id: $grade_id, emp_type: $emp_type) {
      emp_id
      dept_code
      nickname
    }
  }
  `;

export const EMPLOYEES_MITACLUB_AGENT_QUERY = `
query ($empId: String, $teamCode: [String], $dept_id: String, $clubYear: String, $clubMonth: String) {
  mitaclub_agent(empId: $empId, teamCode: $teamCode, dept_id: $dept_id, clubYear: $clubYear, clubMonth: $clubMonth) {
    _id
    ClubYear
    ClubMonth
    EmpId
    EnglishName
    DeptId
    TeamCode
    AccumulatedSalesAmount
    AccumulatedSalesCase
    ExecutionDate
  }
}
`;

export const EMPLOYEES_MITACLUB_MANAGER_QUERY = `
query ($empId: String, $teamCode: [String], $clubYear: String, $clubMonth: String) {
  mitaclub_manager(empId: $empId, teamCode: $teamCode, clubYear: $clubYear, clubMonth: $clubMonth) {
    _id
    ClubYear
    ClubMonth
    EmpId
    EnglishName
    DeptId
    TeamCode
    AccumulatedSalesAmount
    AccumulatedSalesCase
    ExecutionDate
  }
}
`;

export const EMPLOYEES_MITACLUB_DISTRICT_DIRECTOR_QUERY = `
query ($empId: String, $teamCode: [String],$clubYear: String, $clubMonth: String) {
  mitaclub_district_manager(empId: $empId, teamCode: $teamCode, clubYear: $clubYear, clubMonth: $clubMonth) {
    _id
    ClubYear
    ClubMonth
    EmpId
    EnglishName
    DeptId
    TeamCode
    AccumulatedSalesAmount
    AccumulatedSalesCase
    ExecutionDate
  }
}
`;

export const EMPLOYEES_MITACLUB_AGENT_DISTRICT_MANAGER = `
query(
  $empId: String,
  $teamCode: [String]
) {
  mitaclub_district_manager(empId: $empId, teamCode: $teamCode) {
    EmpId
    EnglishName
    TeamCode
    AccumulatedSalesAmount
    AccumulatedSalesCase
    ExecutionDate
    ClubYear
    ClubMonth
  }
}
`;

export const EMPLOYEES_MITACLUB_AGENT_MANAGER_LIST_QUERY = `
query(
  $empId: String,
  $teamCode: [String]
) {
  mitaclub_manager(empId: $empId, teamCode: $teamCode) {
    EmpId
    EnglishName
    TeamCode
    AccumulatedSalesAmount
    AccumulatedSalesCase
    ExecutionDate
    ClubYear
    ClubMonth
  }
}`;
