export const CREATE_PROPOSAL_QUERY = `
  mutation($proposal: CreateProposalArguments!, $bypassCode: String!) {
    createProposal(proposal: $proposal, bypassCode: $bypassCode)
  }
`;

export const LIST_PROPOSALS_QUERY = `
  query($emp_id: String, $sbu: String, $limit: Int, $offset: Int, $bypassCode: String!) {
    proposalsCount(emp_id: $emp_id, sbu: $sbu, bypassCode: $bypassCode)
    proposals(emp_id: $emp_id, sbu: $sbu, limit: $limit, offset: $offset, bypassCode: $bypassCode) {
      id
      urlHash
      stockId
      stockMongoId
      createdDate
      building {
        nameEn
        nameZh
      }
      floorType {
        nameEn
        nameZh
      }
      floor {
        value
        isShow
      }
      unit {
        value
        isShow
      }
      proposalName
      useNameAsPdf
      type
      salesman {
        nameZh
        nameEn
        nickname
      }
    }
    listProposalsCount(emp_id: $emp_id, sbu: $sbu, bypassCode: $bypassCode)
    listProposals(emp_id: $emp_id, sbu: $sbu, bypassCode: $bypassCode) {
      id
      urlHash
      createdDate
      proposalName
      useNameAsPdf
      type
      mode
      salesman {
        nameZh
        nameEn
        nickname
      }
      proposals {
        stockId
        stockMongoId
      }
    }
  }
`;

export const GET_PROPOSAL_COUNT = `
  query($emp_id: String, $sbu: String, $limit: Int, $offset: Int, $bypassCode: String!) {
    proposals(emp_id: $emp_id, sbu: $sbu, limit: $limit, offset: $offset, bypassCode: $bypassCode) {
      urlHash
    }
  }
`;

export const REMOVE_PROPOSAL_QUERY = `
mutation($hash: String!, $salesmanId: String!, $bypassCode: String!) {
  removeProposal(hash: $hash, salesmanId: $salesmanId, bypassCode: $bypassCode)
}
`;

export const CREATE_PREVIEW = `
  mutation (
    $type: PreviewTypeEnum!
    $proposal: CreateProposalArguments
    $listProposal: CreateListProposalArguments
    $empId: String!
    $bypassCode: String!
  ) {
    createPreview(
      type: $type
      proposal: $proposal
      listProposal: $listProposal
      empId: $empId
      bypassCode: $bypassCode
    )
  }`;
