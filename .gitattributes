# Automatically normalize line endings for all text-based files
# http://git-scm.com/docs/gitattributes#_end_of_line_conversion
* text=auto

# For the following file types, normalize line endings to LF on
# checkin and prevent conversion to CRLF when they are checked out
# (this is required in order to prevent newline related issues like,
# for example, after the build script is run)
.*      text eol=lf
*.html  text eol=lf
*.css   text eol=lf
*.less  text eol=lf
*.styl  text eol=lf
*.scss  text eol=lf
*.sass  text eol=lf
*.sss   text eol=lf
*.js    text eol=lf
*.jsx   text eol=lf
*.json  text eol=lf
*.md    text eol=lf
*.mjs   text eol=lf
*.sh    text eol=lf
*.svg   text eol=lf
*.txt   text eol=lf
*.xml   text eol=lf
