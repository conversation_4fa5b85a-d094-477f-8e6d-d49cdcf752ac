/* eslint-disable import/prefer-default-export */
import {
  LIST_BUILDINGS_START,
  LIST_BUILDINGS_SUCCESS,
  LIST_BUILDINGS_ERROR,
  CLEAR_BUILDINGS,
  LIST_BUILDING_DETAIL_START,
  LIST_BUILDING_DETAIL_SUCCESS,
  LIST_BUILDING_DETAIL_ERROR,
  LIST_BUILDING_MEDIA_START,
  LIST_BUILDING_MEDIA_SUCCESS,
  LIST_BUILDING_MEDIA_ERROR,
  <PERSON><PERSON><PERSON>_BUILDING_DETAIL,
  LIST_UNITVIEW_START,
  LIST_UNITVIEW_SUCCESS,
  LIST_UNITVIEW_ERROR,
  LIST_FLOOR_ORDERING_START,
  LIST_FLOOR_ORDERING_SUCCESS,
  LIST_FLOOR_ORDERING_ERROR,
  UPDATE_BUILDING_MEDIA_BY_INDEX,
  UPDATE_BUILDING_MEDIA_LIST,
} from "../constants/building";
import { addActivityLog } from "./log";

export function listBuildings(variables) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    try {
      dispatch({
        type: LIST_BUILDINGS_START,
        checkrefreshToken: true,
      });

      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem('casAccessToken'),//getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("LIST_SIMPLE_BUILDINGS_SOURCE_QUERY");

      const data = await universalRequest("/building/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: { ...variables, first: variables.limit },
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_BUILDINGS_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_BUILDINGS_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };
}

export function listUnitViews() {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_UNITVIEW_START,
      checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem('casAccessToken'),//getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("LIST_UNIT_VIEWS_QUERY");

      const data = await universalRequest("/stock/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: {}
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_UNITVIEW_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_UNITVIEW_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };
}

export function listFloorOrdering() {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_FLOOR_ORDERING_START,
      checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem('casAccessToken'),//getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("LIST_FLOOR_ORDERING_QUERY");

      const data = await universalRequest("/stock/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: {}
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_FLOOR_ORDERING_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_FLOOR_ORDERING_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };
}

export function clearBuildings() {
  return async (dispatch) => {
    dispatch({
      type: CLEAR_BUILDINGS,
    });
  };
}

export function listBuildingDetail(variables) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_BUILDING_DETAIL_START,
      checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          // "content-type": "application/json; charset=utf-8",
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem('casAccessToken')//getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("LIST_BUILDING_DETAIL_QUERY");

      const data = await universalRequest("/building/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: variables,
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_BUILDING_DETAIL_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_BUILDING_DETAIL_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };
}

export function listBuildingMedia(variables) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_BUILDING_MEDIA_START,
      checkrefreshToken: true,
    });

    dispatch(addActivityLog("media.search", "read", { ...variables, mediaType: "building" }));

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem('casAccessToken'),//getState().auth.user.casAccessToken,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      };

      const query = await getQuery("LIST_BUILDING_MEDIA_QUERY");

      const { sid, empId } = variables;
      const promises = sid.map(id =>
        universalRequest("/media/graphql", {
          method: "POST",
          body: JSON.stringify({
            query,
            variables: { sid: id, empId },
          }),
          ...options
        }).then(res => {
          if (!res.errors) {
            return { id, data: res.data.building };
          }
          return null;
        }).catch(() => null)
      );
      const results = await Promise.all(promises);
      const data = results.filter(Boolean);

      dispatch({
        type: LIST_BUILDING_MEDIA_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_BUILDING_MEDIA_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };
}

export function updateBuildingMediaByIndex({ index = -1, mediaIndex = -1, mediaType = "phone", media }) {
  return async (
    dispatch,
  ) => {
    dispatch({
      type: UPDATE_BUILDING_MEDIA_BY_INDEX,
      payload: {
        index,
        mediaIndex,
        mediaType,
        media,
      },
    });
  };
}

export function updateBuildingMediaList(mediaType, mediaList) {
  return {
    type: UPDATE_BUILDING_MEDIA_LIST,
    payload: {
      mediaType,
      mediaList
    }
  };
}

export function clearBuildingDetail() {
  return async (dispatch) => {
    dispatch({
      type: CLEAR_BUILDING_DETAIL,
    });
  };
}
