import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import FieldVal from "../../../../common/FieldVal";
import { numberComma, convertNewlineToBr } from "../../../../../helper/generalHelper";
import { injectIntl } from "react-intl";
import DetailBoxSection from "../../../../common/DetailBoxSection";

const styles = theme => ({
  root: {
    padding: "1vh 0"
  },
  lmrAlign: {
    paddingLeft: "2vw"
  }
});

class ExtraCharge extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;

    const parseDisplayField = (number, type, paidBy, extra = "") => {
      const paidByMapping = {
        "Paid By Tenant": intl.formatMessage({ id: "stock.paidbytenant" }),
        "Paid By Landlord": intl.formatMessage({ id: "stock.paidbylandlord" }),
      };
      paidBy = paidByMapping[paidBy] || "";

      const typeMapping = {
        "/SqFt": "/" + intl.formatMessage({ id: "common.sqft" }),
        "/Qtr": "/" + intl.formatMessage({ id: "common.qtr" }),
        "/Month": "/" + intl.formatMessage({ id: "common.month" }),
        "/SY": "/" + intl.formatMessage({ id: "common.sy" }),
      };
      type = typeMapping[type] || "";

      return number
        ? number + type + " " + paidBy + extra
        : paidBy + extra || "---";
    };

    const airConditioningFee =
      detail.airConditioningFee && detail.airConditioningFee.number
        ? "$" + detail.airConditioningFee.number
        : "";
    const airConditioningFeeType =
      detail.airConditioningFee && detail.airConditioningFee.type
        ? detail.airConditioningFee.type
        : "";
    const airConditioningFeePaidBy =
      detail.airConditioningFee && detail.airConditioningFee.paidBy && detail.airConditioningFee.paidBy
        ? detail.airConditioningFee.paidBy
        : "";
    const isIncludeAirConditioning = detail.isIncludeAirConditioning
      ? intl.formatMessage({ id: "stock.includedinmgtfee" })
      : "";
    const managementFee =
      detail.managementFee && detail.managementFee.number
        ? "$" + detail.managementFee.number
        : "";
    const managementFeeType =
      detail.managementFee && detail.managementFee.type
        ? detail.managementFee.type
        : "";
    const managementFeePaidBy =
      detail.managementFee && detail.managementFee.paidBy && detail.managementFee.paidBy
        ? detail.managementFee.paidBy
        : "";
    const isIncludeManagementFee = detail.isIncludeManagementFee
      ? " Inclusive"
      : "";
    const rates =
      detail.rates && detail.rates.number
        ? "$" + numberComma(detail.rates.number)
        : "";
    const ratesType =
      detail.rates && detail.rates.type
        ? detail.rates.type
        : "";
    const ratesPaidBy =
      detail.rates && detail.rates.paidBy && detail.rates.paidBy
        ? detail.rates.paidBy
        : "";
    const governmentRent =
      detail.governmentRent && detail.governmentRent.number
        ? "$" + numberComma(detail.governmentRent.number)
        : "";
    const governmentRentType =
      detail.governmentRent && detail.governmentRent.type
        ? detail.governmentRent.type
        : "";
    const governmentRentPaidBy =
      detail.governmentRent && detail.governmentRent.paidBy && detail.governmentRent.paidBy
        ? detail.governmentRent.paidBy
        : "";

    const airConditioningFeeHeader = intl.formatMessage({
      id: "stock.airconditioningfee"
    });
    const mgtfeeHeader = intl.formatMessage({
      id: "stock.mgtfee"
    });
    const ratesHeader = intl.formatMessage({
      id: "stock.rates"
    });
    const grentHeader = intl.formatMessage({
      id: "stock.grent"
    });

    let generalMapping = {
      [airConditioningFeeHeader]: {
        value: isIncludeAirConditioning || parseDisplayField(airConditioningFee, airConditioningFeeType, airConditioningFeePaidBy),
        xs: 6
      },
      [mgtfeeHeader]: {
        value: parseDisplayField(managementFee, managementFeeType, managementFeePaidBy, isIncludeManagementFee),
        xs: 6
      },
      [ratesHeader]: { value: parseDisplayField(rates, ratesType, ratesPaidBy), xs: 6 },
      [grentHeader]: { value: parseDisplayField(governmentRent, governmentRentType, governmentRentPaidBy), xs: 6 },
    };

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.extracharge",
          })}
        >
          <Grid container spacing={2} className={classes.lmrAlign}>
            {Object.keys(generalMapping).map((v, i) => (
              <Grid item xs={generalMapping[v].xs} key={v}>
                <FieldVal field={v}>
                  {generalMapping[v].value}
                </FieldVal>
              </Grid>
            ))}
          </Grid>
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(ExtraCharge));
