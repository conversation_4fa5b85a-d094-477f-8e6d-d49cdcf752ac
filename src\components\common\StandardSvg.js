import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    width: 24,
    height: 24,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  svg: {
    maxWidth: "100%",
    maxHeight: "100%",
  },
};

function StandardSvg(props) {
  const { classes, src, className, imgClass, ...other } = props;

  return (
      <div className={`${classes.root} ${className}`}>
        <img className={`${classes.svg} ${imgClass}`} src={src} />
      </div>
  );
}

StandardSvg.propTypes = {
  classes: PropTypes.object.isRequired,
  src: PropTypes.string,
  className: PropTypes.string,
  imgClass: PropTypes.string,
};

export default withStyles(styles)(StandardSvg);
