export const LIST_STOCK_DETAIL_QUERY = `
query ($_id: [ID], $backendKey: String!) {
  stocks(_id: $_id, backendKey: $backendKey) {
    _id
    commission
    yield
    saleEquity
    unicorn {
      id
      landSearch
      landSearchDetail {
        search_ref_no
        address
        flat
        floor
      }
    }
    street {
      street {
        nameEn
        nameZh
      }
      number
    }
    askingRent {
      total
      average
      trend
      details {
        value
        type
      }
    }
    askingPrice {
      total
      average
      trend
      details {
        value
        type
      }
    }
    unit
    floor
    status {
      nameEn
      nameZh
    }
    source {
      nameEn
      nameZh
    }
    possession {
      nameEn
      nameZh
    }
    currentState {
      nameZh
      nameEn
    }
    currentStateId
    availability
    managementFee {
      number
      type
      paidBy
    }
    isIncludeAirConditioning
    isIncludeManagementFee
    keyNumber
    rates {
      number
      type
      paidBy
    }
    governmentRent {
      number
      type
      paidBy
    }
    inspection {
      type {
        nameEn
        nameZh
      }
    }
    area {
      size
      efficiency
      sizes {
        value
        type
      }
      terrace
      roof
      cockloft
    }
    unitView {
      nameEn
      nameZh
    }
    decoration {
      nameEn
      nameZh
    }
    building {
      _id
      nameEn
      nameZh
      unicorn {
        id
      }
      street {
        number
        street {
          nameEn
          nameZh
        }
      }
      floors {
        type
        name
        managementFee
      }
      district {
        nameEn
        nameZh
      }
      coordinates {
        latitude
        longitude
      }
      completionDate
      developers {
        nameZh
        nameEn
      }
      managementCompany {
        name
      }
      management {
        fee
        isIncludeAirCondCharge
      }
      lifts {
        type
        quantity
      }
      entrances {
        container {
          nameZh
          nameEn
        }
        haveLoadingBay
      }
      airConditioning {
        type {
          nameZh
          nameEn
        }
        openingTime {
          nameEn
          nameZh
        }
        extraCharges {
          nameEn
          nameZh
        }
      }
      transportZh
      transportEn
      title
      buildingUsage {
        nameZh
        nameEn
      }
    }
    facilities {
      conferenceRoom
      meetingRoom
      utilityRoom
      workstation
      desk
      chair
      carpark
      haveCabinets
      haveReceptionArea
      generalRoom
      haveComputerRoom
      havePantry
      havePrivateToilets
      carparkNumber
    }
    remarks {
      internal
    }
    recordOperation {
      createDate
      lastUpdateDate
      createBy {
        emp_id
        name_en
      }
      lastUpdateBy {
        emp_id
        name_en
      }
    }
    propertyAdvertisements {
      saleData {
        minTotal
        minAverage
        maxTotal
        maxAverage
        minDate
        maxDate
        consultant {
          name_en
          dept_code
        }
      }
      rentData {
        minTotal
        minAverage
        maxTotal
        maxAverage
        minDate
        maxDate
        consultant {
          name_en
          dept_code
        }
      }
    }
    tenancy {
      rentFree
      rentFreeZh
      freePeriod {
        days
        months
      }
      leasePeriod {
        months
        years
      }
      leaseOptions {
        months
        years
        feePrefix
        feeType
      }
      depositInMonth
      prepaidInMonth
    }
    tenancyRecords {
      status
      deleted
      recordOperation {
        lastUpdateDate
      }
      floor
      unit
      area
      rentalFee
      extends {
        minDate
        maxDate
        feePrefix
        feeType
        rent {
          amount
          rate
        }
      }
      tenants {
        isTenant
        contactsPerson {
          _id
          nameEn
          nameZh
          titleEn
          titleZh
          companies {
            _id
            nameEn
            nameZh
          }
          contact {
            phones {
              type
              number
              doNotContact
            }
          }
        }
        contactsPersons {
          _id
          nameEn
          nameZh
          title
          remarks
          isTenant
          contact {
            phones {
              type
              number
              doNotContact
            }
          }
        }
        contactsCompanies {
          _id
          nameEn
          nameZh
          remarks
          contact {
            phones {
              type
              number
              doNotContact
            }
          }
        }
      }
      expiry {
        minDate
        maxDate
      }
    }
    mortgagee
    soleagent {
      periodStart
      periodEnd
      assignDate
      agents {
        emp_id
      }
    }
    updateHistory {
      handsId
      datetime
      employee {
        name_en
        dept_code
      }
      status {
        nameEn
        nameZh
      }
      askingPrice {
        average
      }
      askingRent {
        average
      }
      possession {
        nameEn
        nameZh
      }
      availability
      deleted
      description
    }
    stockType {
      nameEn
      nameZh
    }
    ownerType
    airConditioningFee {
      number
      type
      paidBy
    }
    currentHands
    vendors {
      hands
      handsId
      contactsPerson {
        _id
        nameEn
        nameZh
        titleEn
        titleZh
        remarks
        companies {
          _id
          nameEn
          nameZh
        }
        contact {
          phones {
            type
            number
            doNotContact
            privacy
          }
        }
      }
      contactsPersons {
        _id
        nameEn
        nameZh
        title
        remarks
        contact {
          phones {
            type
            number
            doNotContact
            privacy
          }
        }
      }
      contactsCompanies {
        _id
        nameEn
        nameZh
        remarks
        contact {
          phones {
            type
            number
            doNotContact
            privacy
          }
        }
      }
    }
    ceilingHeight {
      ft
      in
    }
    entranceWidth {
      ft
      in
    }
    floorLoading
    airConditioningType {
      nameEn
      nameZh
    }
    handsInfo {
      _id
      hands
      isCancelled
    }
    isWWW
    surveyorProposal {
      status
      date
    }
  }
}
`;
