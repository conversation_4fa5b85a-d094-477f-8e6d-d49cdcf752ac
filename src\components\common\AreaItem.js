import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import { injectIntl } from "react-intl";
import AreaTypeItem from "./AreaTypeItem";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    display: "flex",
    justifyContent: "flex-end",
  },
  areaName: {
    flex: 1,
    textAlign: "right",
    textOverflow: "ellipsis",
    overflow: "hidden",
    whiteSpace: "nowrap",
  },
};

function AreaItem(props) {
  const {
    classes,
    className,
    areaName,
    type1,
    type2,
    intl,
    ...other
  } = props;

  return (
    <div className={`${classes.root} ${className}`} {...other}>
      <div className={classes.areaName}>{areaName}</div>
      <AreaTypeItem typeData={type1} />
      <AreaTypeItem typeData={type2} />
    </div>
  );
}

AreaItem.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  areaName: PropTypes.string,
  type1: PropTypes.object,
  type2: PropTypes.object,
};

export default withStyles(styles)(injectIntl(AreaItem));
