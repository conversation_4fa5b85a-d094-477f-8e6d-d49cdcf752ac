import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import _ from "lodash";
import { withStyles } from "@material-ui/core/styles";
import FieldValArrBox from "../../../../common/FieldValArrBox";
import Grid from "@material-ui/core/Grid";
import SearchIcon from "@material-ui/icons/Search";
import { injectIntl } from "react-intl";
import CallItem from "../../../../common/CallItem";
import FormButtonInline from "../../../../common/FormButtonInline";
import {
  goToSearchResult,
  consolidateType,
  parseNameWithBr,
  parseNameWithDash,
} from "../../../../../helper/generalHelper";
import { clearStock } from "../../../../../actions/stock";
import { clearStockList } from "../../../../../actions/stocklist";
import { enableConsolidLandSearch } from "../../../../../config";

// We can inject some CSS into the DOM.
const styles = {
  root: {

  },
  currentBox: {
    backgroundColor: "rgba(255, 255, 100, .4)"
  },
  advanceBox: {
    backgroundColor: "rgba(255, 255, 100, .4)"
  },
  formerBox: {

  },
  phonesContainer: {
    paddingTop: 8
  },
  contact: {
    display: "flex",
    alignItems: "center",
    "& > *:nth-child(2)": {
      marginLeft: "2vw",
      flex: "0 0 auto",
    },
  },
};

function TenancyRecordBox(props) {
  const {
    classes,
    status,
    tenantEn,
    tenantZh,
    tenantId,
    floor = "---",
    shopNumber = "---",
    area = "---",
    tenancyPeriod = "---",
    rent = "---",
    options = [],
    company,
    companyId,
    business = "---",
    contactName,
    contactTitle,
    phones = [],
    mongoId,
    stockId,
    clearStockDetail,
    clearStockList,
    intl,
    ...others
  } = props;

  let displayStatus;
  let boxClass = "";
  if (status === "Advance") {
    displayStatus = intl.formatMessage({ id: "stock.advancetenancy" });
    boxClass = classes.advanceBox;
  }
  if (status === "Current") {
    displayStatus = intl.formatMessage({ id: "stock.currenttenancy" });
    boxClass = classes.currentBox;
  }
  if (status === "Previous")
    displayStatus = intl.formatMessage({ id: "stock.previoustenancy" });
  if (status === "Former") {
    displayStatus = intl.formatMessage({ id: "stock.formertenancy" });
    boxClass = classes.formerBox;
  }

  let contactNameFull =
    contactTitle && contactName
      ? contactTitle + " " + contactName
      : contactName || "---";

  let CallItems = phones.length > 0 && (
    <Grid container spacing={1} className={classes.phonesContainer}>
      {phones.map((v, i) => (
        <Grid item xs={6} key={i}>
          <CallItem
            type={v.type}
            number={v.number}
            mongoId={mongoId}
            stockId={stockId}
          />
        </Grid>
      ))}
    </Grid>
  );

  // make sure there are exactly three options
  let optionsAppended = options.concat({}, {}).slice(0, 2);
  let optionItems = [];
  optionsAppended.forEach((v, i) => {
    optionItems.push({
      field: intl.formatMessage({ id: "stock.option" }) + " (" + (i + 1)+ ")",
      val: optionsAppended[i].period || "---",
      xs: 6,
    });
    optionItems.push({
      field: intl.formatMessage({ id: "stock.rent" }) + " (" + (i + 1)+ ")",
      val: optionsAppended[i].rent || "---",
      xs: 6,
    });
  });

  const query = {
    company: [
      {
        value: tenantId,
        label: parseNameWithDash(tenantEn, tenantZh, intl),
      },
      {
        value: companyId,
        label: company,
      },
    ].filter(v => v.value && v.label && v.label !== "Unit Not Found").map(v => v.value),
    person: [contactName].filter(v => v),
    phone: _.uniqBy(phones, "number").map(v => v.number),
    limit: 50,
    offset: 0,
    ...consolidateType
  };
  const selectedData = {
    company: [
      {
        value: tenantId,
        label: parseNameWithDash(tenantEn, tenantZh, intl),
      },
      {
        value: companyId,
        label: company,
      },
    ].filter(v => v.value && v.label && v.label !== "Unit Not Found"),
    person: [contactName].filter(v => v).map(v => {
      return {
        value: v,
        label: v,
      }
    }),
    phone: _.uniqBy(phones, "number").map(v => {
      return {
        value: v.number,
        label: v.number,
      }
    }),
  };
  const goToSearchContactResult = () => {
    // console.log(query)
    // console.log(selectedData)
    clearStockDetail();
    clearStockList(false);
    goToSearchResult(query, selectedData, true);
  };

  const consolidateSearchBtn = enableConsolidLandSearch == "true" && (query.company.length > 0 || query.person.length > 0 || query.phone.length > 0) && (
    <FormButtonInline onClick={goToSearchContactResult} icon={<SearchIcon />}>
      {intl.formatMessage({
        id: "stock.consosearch",
      })}
    </FormButtonInline>
  );

  const items = [
    {
      field: intl.formatMessage(
        { id: "stock.tenant" },
        { status: displayStatus },
      ),
      val: parseNameWithBr(tenantEn, tenantZh, intl),
    },
    {
      field: intl.formatMessage({ id: "stock.floor" }),
      val: floor,
      xs: 6,
    },
    {
      field: intl.formatMessage({ id: "stock.shopnumber" }),
      val: shopNumber,
      xs: 6,
    },
    {
      field: intl.formatMessage({ id: "stock.tenancyperiod" }),
      val: tenancyPeriod,
    },
    {
      field: intl.formatMessage({ id: "stock.latestrent" }),
      val: rent,
    },
    {
      field: intl.formatMessage({ id: "stock.company" }),
      val: company || "---",
    },
    {
      field: intl.formatMessage({ id: "stock.industry" }),
      val: business,
    },
    {
      field: intl.formatMessage({ id: "stock.shoparea" }),
      val: area,
    },
    ...optionItems,
    {
      field: intl.formatMessage({ id: "stock.contact" }),
      val: <div className={classes.contact}><div>{contactNameFull}</div>{consolidateSearchBtn}</div>,
      extra: CallItems,
    },
  ];

  return (
    <FieldValArrBox
      className={`${classes.root} ${boxClass}`}
      items={items}
      {...others}
    />
  );
}

TenancyRecordBox.propTypes = {
  classes: PropTypes.object.isRequired,
  status: PropTypes.string.isRequired,
  tenantEn: PropTypes.string,
  tenantZh: PropTypes.string,
  tenantId: PropTypes.string,
  floor: PropTypes.string,
  shopNumber: PropTypes.string,
  area: PropTypes.string,
  tenancyPeriod: PropTypes.string,
  rent: PropTypes.string,
  options: PropTypes.arrayOf(PropTypes.object),
  company: PropTypes.string,
  companyId: PropTypes.string,
  business: PropTypes.string,
  contactName: PropTypes.string,
  contactTitle: PropTypes.string,
  phones: PropTypes.arrayOf(PropTypes.object),
  mongoId: PropTypes.string,
  stockId: PropTypes.number,
};

const mapDispatchToProps = dispatch => {
  return {
    clearStockDetail: () => dispatch(clearStock()),
    clearStockList: (...args) => dispatch(clearStockList(...args)),
  };
};

export default connect(
  null,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(TenancyRecordBox)));
