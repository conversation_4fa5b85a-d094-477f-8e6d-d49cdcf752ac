import React from "react";
import { withStyles } from "@material-ui/core/styles";
import TextField from "@material-ui/core/TextField";
import { injectIntl } from "react-intl";
import PropTypes from "prop-types";

import { getDisplayStockId } from "../../helper/generalHelper";

const styles = {
  root: {
    "& .Mui-disabled:before": {
      borderBottomStyle: "none",
    },
    "& .MuiInputBase-root": {
      marginTop: 20,
    },
    "& .MuiInputBase-input": {
      color: "rgba(0, 0, 0, 0.87)",
    },
    "& .MuiFormLabel-root": {
      color: "rgba(0, 0, 0, 0.54)",
      lineHeight: 1.5,
      fontSize: 18,
    },
  },
};

const StockIdField = ({ stockId, intl, ...props }) => (
  <TextField
    label={intl.formatMessage({ id: "search.header.stockid" })}
    value={getDisplayStockId(stockId)}
    disabled
    {...props}
  />
);

StockIdField.propTypes = {
  stockId: PropTypes.number.isRequired,
  intl: PropTypes.object.isRequired,
};

export default withStyles(styles)(injectIntl(StockIdField));
