import fetch from "node-fetch";
import _ from "lodash";

import { api, cloudwatchlog } from "@/config";

function resJson(res, status, error, data) {
  res.json({
    status,
    success: error ? false : true, 
    errors: error ? [{ message: error }] : null,
    data,
  });
  res.end();
}

const fetchData = async (api, body, headers = {}, extraOptions = {}) =>
  fetch(api, {
    method: "POST",
    headers: {
      "content-type": "application/json",
      ...headers,
    },
    body: JSON.stringify(body),
  }).then((res) => {
    if (extraOptions.responseNull) {
      return res;
    }
    return res.json();
  });


const handleFetchData = async (req, res, next, extraOptions) => {
  try {
    const { api, ...others } = req.body;

    const resp = await fetchData(api, others, {
      Authorization: req.headers["authorization"],
      "CAS-Authorization": req.headers["cas-authorization"],
    }, extraOptions);

    if (!_.isEmpty(_.get(resp, "errors")) || !_.isEmpty(_.get(resp, "error"))) {
      throw new Error(
        _.get(resp, "errors.0.message") || _.get(resp, "error") || "Internal server error",
      );
    }

    resJson(res, 200, null, _.get(resp, "data", null));
  } catch (e) {
    console.error(e);
    resJson(res, 300, e.message);
  }
}

export const addLog =  async (req, res, next) => {
  req.body.api = cloudwatchlog.url;
  return await handleFetchData(req, res, next, { responseNull: true });
}

export const districtGql = async (req, res, next) => {
  req.body.api = api.district;
  return await handleFetchData(req, res, next);
}

export const streetGql = async (req, res, next) => {
  req.body.api = api.street;
  return await handleFetchData(req, res, next);
}

export const contactGql = async (req, res, next) => {
  req.body.api = api.contact;
  return await handleFetchData(req, res, next);
}

export const stockGql = async (req, res, next) => {
  req.body.api = api.stock;
  return await handleFetchData(req, res, next);
}

export const employeeGql = async (req, res, next) => {
  req.body.api = api.employee;
  return await handleFetchData(req, res, next);
}

export const searchGql = async (req, res, next) => {
  req.body.api = api.search;
  return await handleFetchData(req, res, next);
}

export const wwwGql = async (req, res, next) => {
  req.body.api = api.www;
  return await handleFetchData(req, res, next);
}

export const buildingGql =  async (req, res, next) => {
  req.body.api = api.building;
  return await handleFetchData(req, res, next);
}

export const transactionGql =  async (req, res, next) => {
  req.body.api = api.transaction;
  return await handleFetchData(req, res, next);
}

export const mediaGql =  async (req, res, next) => {
  req.body.api = api.media;
  return await handleFetchData(req, res, next);
}

export const supplementGql =  async (req, res, next) => {
  req.body.api = api.supplement;
  return await handleFetchData(req, res, next);
}

export const landsearchGql =  async (req, res, next) => {
  req.body.api = api.landSearchDetails;
  return await handleFetchData(req, res, next);
}

