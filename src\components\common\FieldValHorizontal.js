import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";

// We can inject some CSS into the DOM.
const styles = {
  field: {
    lineHeight: 1.3,
    color: "#777",
    fontSize: "0.875em",
  },
  value: {
    lineHeight: 1.3,
    fontSize: "1.125em",
    wordBreak: "break-word",
  }
};

function FieldValHorizontal(props) {
  const { classes, field, children, className, ...other } = props;

  return (
    <div className={className} {...other}>
      <span className={classes.field}>{field}</span>{" "}
      <span className={classes.value}>{children}</span>
    </div>
  );
}

FieldValHorizontal.propTypes = {
  classes: PropTypes.object.isRequired,
  field: PropTypes.string,
  children: PropTypes.node,
  className: PropTypes.string
};

export default withStyles(styles)(FieldValHorizontal);
