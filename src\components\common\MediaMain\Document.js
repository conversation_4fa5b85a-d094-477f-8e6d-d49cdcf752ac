import React from "react";
import { createStyles, withStyles } from "@material-ui/styles";
import MediaHandleResultDialog from "./MediaHandleResultDialog";
import MediumProperties from "./MediumProperties";
import DocumentPopup from "./DocumentPopup";
import PdfImg from "./PdfImg";

const styles = createStyles({
  pdfImg: {
    width: "100%",
    position: "absolute",
    top: 0,
  },
  documentContainer: {
    overflow: "hidden",
    position: "relative",
    width: "100%",
    /* 通过 padding 创建 1:1 容器 */
    paddingTop: "100%",
    height: 0,
  },
});

/**
 * @param {{
 * file?: File | undefined;
 * classes: object;
 * record?: object | undefined;
 * deletable?: boolean;
 * handleOpenDeleteMediaDialog: () => void;
 * handleOpenPopup: () => void;
 * }} props
 */
function Document(props) {
  const {
    file,
    classes,
    record,
    deletable = false,
    handleOpenDeleteMediaDialog,
    handleOpenPopup,
    disablePopup = false,
  } = props;

  const [popupOpen, setPopupOpen] = React.useState(false);

  const src = React.useMemo(() => {
    if (record) {
      const { mediumRoot, originalFilename } = record;
      return `${mediumRoot}/${originalFilename}`;
    }
  }, [record]);

  const handleOnClick = React.useCallback(() => {
    if (src) {
      window.open(src);
    } else if (file) {
      window.open(URL.createObjectURL(file));
    }
  }, [src, file]);

  const handlePopupClose = React.useCallback(() => {
    setPopupOpen(false);
  }, []);

  const handlePopupShow = React.useCallback(() => { 
    if (disablePopup) return;
    if (handleOpenPopup) {
      handleOpenPopup();
    }
    setPopupOpen(true);
  }, []);

  return (
    <div className={classes.documentContainer}>
      <PdfImg src={src} file={file} page="1" className={classes.pdfImg} onClick={handlePopupShow} />
      {record && <MediumProperties medium={record} />}

      {popupOpen && <DocumentPopup
        document={record}
        src={src}
        file={file}
        deletable={deletable}
        handlePopupClose={handlePopupClose}
        handleDownload={handleOnClick}
        handleOpenDeleteMediaDialog={handleOpenDeleteMediaDialog}
      />}

      {record?.approval === "pending" &&
        <MediaHandleResultDialog
          media={record}
          mediaType={"document"}
        />}
    </div>
  );
};

export default withStyles(styles)(Document);
