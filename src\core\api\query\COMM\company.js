export const LIST_COMPANIES_QUERY = `
  query(
    $limit: Int,
    $offset: Int,
    $sort: [companyBookSorter],
    $companyName: [String],
  ) {
    companyBookCount(companyName: $companyName)
    companyBook (
      limit: $limit
      offset: $offset
      sort: $sort
      companyName: $companyName
    ){
      _id
      companyNameEn
      companyNameZh
      createDate
      updateDate
    }
  }`;

export const LIST_COMPANY_DETAIL_QUERY = `
  query($id: ID!) {
    companyBookContactDetails(_id: [$id]) {
      _id
      clientId
      source
      involvement
      createBy {
        emp_id
        name_en
      }
      createDate
      updateBy {
        emp_id
        name_en
      }
      updateDate
      nextUpdate
      companyNameEn
      companyNameZh
      holdingCompanyNameEn
      holdingCompanyNameZh
      companyContact {
        _id
        contactType
        phoneType
        phoneNo
      }
      contacts<PERSON>erson {
        _id
        contactsPersons {
          _id
          nameEn
          nameZh
          titleEn
          noMassFax
          noMassMail
          ownership
          personTypeId
          privacy
          doNotContact
          remarks
          contact {
            phones {
              type
              number
              remarks
              doNotContact
              privacy
            }
            emails {
              address
              remarks
              doNotContact
              privacy
            }
            urls {
              address
              remarks
              doNotContact
              privacy
            }
            addresses {
              address
              remarks
              doNotContact
              privacy
            }
          }
        }
        contactsCompanies {
          _id
          type
          nameEn
          nameZh
          businessId
          noMassFax
          noMassMail
          ownership
          privacy
          doNotContact
          remarks
          contact {
            phones {
              type
              number
              remarks
              doNotContact
              privacy
            }
            emails {
              address
              remarks
              doNotContact
              privacy
            }
            urls {
              address
              remarks
              doNotContact
              privacy
            }
            addresses {
              address
              remarks
              doNotContact
              privacy
            }
          }
        }
        happenDateTime
      }
      business
      district
      url
      brNo
      remarks
      addressZh
      addressEn
    }
  }`;

export const LIST_COMPANY_QUERY = `
  query(
    $companySearchApplicantId: String,
    $companyRegistrationNumber: String,
    $companyName: [String]
  ) {
    companyBook(
      companySearchApplicantId: $companySearchApplicantId,
      companyRegistrationNumber: $companyRegistrationNumber,
      companyName: $companyName
    ) {
      _id
      companyNameEn
      companyNameZh
      companyRegistrationNumber
      address
      companySearch {
        search_ref_no
        createPersonEmpId
        createPersonNameEn
        createPersonNameZh
        createPersonTeamCode
        createDate
      }
    }
  }`;

export const CONTACT_SOURCE_OPTIONS = `
  query(
    $limit: Int
    $offset: Int
    $sort: [sourceSorter]
  ){
    source(
      limit: $limit
      offset: $offset
      sort: $sort
    ){
      _id
      nameEn
      nameZh
    }
  }`;
