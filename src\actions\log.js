import fetch from "node-fetch";
import _ from "lodash";
import { listEmployees } from "./employee";

export function takeLog(msg) {
  return async (dispatch, getState, { takeLog }) => {
    const { emp_id } = getState().auth.user.login.info;
    const token = getState().auth.user.oauth;
    try {
      if (typeof msg !== "string") throw new Error("Log msg must be string");

      await takeLog(emp_id, msg, fetch, false, token);
    } catch (error) {
      console.log(error);
    }
  };
}

export function addActivityLog(functionName, actionName, logData) {
  return async (dispatch, getState, { graphqlRequest, api, getQuery, universalRequest }) => {
    const options = {
      headers: {
        "Content-Type": "application/json",
        Authorization: getState().auth.user.oauth,
        "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
      },
    };
    const data = {
      function: functionName,
      action: actionName,
      device: "mobile"
    };
    if (logData) {
      data["data"] = logData;
    }
    try {
      const resp = await universalRequest("/addActivityLog", {
        method: "POST",
        body: JSON.stringify(data),
        ...options
      })
      return resp;
    } catch (e) {
      console.log(e);
      return e;
    }
  };
}

export const takeLandsearchLog =
  (
    // stockId,
    // refNo,
    // docPath,
    // location,
    // status = null,
    // logSeq = null,
    // requireHkid = false,
    variables,
  ) =>
  async (dispatch, getState, { universalRequest }) => {
    const empInfo = getState().auth.user.login.info;

    try {
      const args = {
        // requireHkid,
        // location,
        // status,
        // stockId,
        // refNo,
        // docPath,
        // logSeq,
        ...variables,
        user: {
          empId: _.get(empInfo, "emp_id"),
          userId: _.get(empInfo, "user_id") || "",
          deptId: _.get(empInfo, "dept_id") || "",
        },
      };

      const { data, errors } = await universalRequest("/takeLandsearchLog", {
        method: "POST",
        headers: {
          "content-type": "application/json",
        },
        body: JSON.stringify(args),
      });
      if (!_.isNil(errors)) throw errors;
      return data;
    } catch (e) {
      console.log(e);
    }
  };

export function gtagHandler(eventName, obj) {
  return async (dispatch, getState) => {
    const empId = _.get(getState(), "auth.user.login.info.emp_id");

    if (!_.get(getState(), "employee.employees.0")) {
      await dispatch(listEmployees({ emp_id: [empId] }))
    }

    if (_.get(getState(), "employee.employees.0")) {
      let name = _.get(getState(), "employee.employees.0.nickname");
      let dept_code = _.get(getState(), "employee.employees.0.dept_code");

      if (window.gtag) {
        window.gtag("event", eventName, {
          Employee: `${dept_code} ${name}`,
          device: "mobile",
          ...obj,
        });
      }
    }
  };
}
