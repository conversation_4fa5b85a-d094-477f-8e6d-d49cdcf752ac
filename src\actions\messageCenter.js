import {
  LIST_MESSAGES_START,
  LIST_MORE_MESSAGES_START,
  LIST_MESSAGES_SUCCESS,
  LIST_MESSAGES_NULL_SUCCESS,
  LIST_MESSAGES_ERROR,
  LIST_MESSAGES_PRIVATE_START,
  LIST_MORE_MESSAGES_PRIVATE_START,
  LIST_MESSAGES_PRIVATE_SUCCESS,
  LIST_MESSAGES_PRIVATE_NULL_SUCCESS,
  CLEAR_MESSAGES_PRIVATE,
  CLEAR_MESSAGES
} from "../constants/messageCenter";
import { addActivityLog } from "./log";

export function listMessages(variables, isFetchingMore, messageType) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest }
  ) => {
    if (isFetchingMore) {
      dispatch({
        type: messageType === "private" ? LIST_MORE_MESSAGES_PRIVATE_START  : LIST_MORE_MESSAGES_START,
        payload: {
          variables
        }
      });
    } else {
      dispatch({
        type: messageType === "private" ? LIST_MESSAGES_PRIVATE_START : LIST_MESSAGES_START,
        payload: {
          variables
        },
        checkrefreshToken: true
      });
    }

    const functionName = messageType === "private" ? "message.search.private" : "message.search.public";
    dispatch(addActivityLog(functionName, "read", { ...variables }));

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          "Authorization": getState().auth.user.oauth,
          "CAS-Authorization":  localStorage.getItem('casAccessToken')
        } //getState().auth.user.casAccessToken  }
      };

      const query = await getQuery( messageType === "private" ? "LIST_MESSAGES_PRIVATE_QUERY" : "LIST_MESSAGES_QUERY");

      const resp = await universalRequest("/supplement/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: variables,
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (resp.errors) {
        throw new Error(resp.errors[0].message);
      }

      const { data } = resp;

      if(messageType === "private"){
        if (data.privateMessage && data.privateMessage.length > 0) {
          dispatch({
            type: LIST_MESSAGES_PRIVATE_SUCCESS,
            payload: {
              data
            }
          });
        } else {
          dispatch({
            type: LIST_MESSAGES_PRIVATE_NULL_SUCCESS,
            payload: {
              data
            }
          });
        }
      } else {
        if (data.messageCenter && data.messageCenter.length > 0) {
          dispatch({
            type: LIST_MESSAGES_SUCCESS,
            payload: {
              data
            }
          });
        } else {
          dispatch({
            type: LIST_MESSAGES_NULL_SUCCESS,
            payload: {
              data
            }
          });
        }
      }
    } catch (error) {
      dispatch({
        type: LIST_MESSAGES_ERROR,
        payload: {
          error
        }
      });
      // throw new Error(error);
    }

    // if (refreshing) {
    //   setTimeout(async function() {
    //     try {
    //       const options = {
    //         headers: { Authorization: getState().auth.user.oauth }
    //       };
    //       const { data } = await graphqlRequest(
    //         api.supplement,
    //         await getQuery("LIST_MESSAGES_QUERY"),
    //         variables,
    //         options
    //       );

    //       if (data.messageCenter && data.messageCenter.length > 0) {
    //         dispatch({
    //           type: LIST_MESSAGES_SUCCESS,
    //           payload: {
    //             data
    //           }
    //         });
    //       } else {
    //         dispatch({
    //           type: LIST_MESSAGES_NULL_SUCCESS,
    //           payload: {
    //             data
    //           }
    //         });
    //       }
    //     } catch (error) {
    //       dispatch({
    //         type: LIST_MESSAGES_ERROR,
    //         payload: {
    //           error
    //         }
    //       });
    //       // throw new Error(error);
    //     }
    //   }, 1500);
    // } else {
    //   try {
    //     const options = {
    //       headers: { Authorization: getState().auth.user.oauth }
    //     };
    //     const { data } = await graphqlRequest(
    //       api.supplement,
    //       await getQuery("LIST_MESSAGES_QUERY"),
    //       variables,
    //       options
    //     );

    //     if (data.messageCenter && data.messageCenter.length > 0) {
    //       dispatch({
    //         type: LIST_MESSAGES_SUCCESS,
    //         payload: {
    //           data
    //         }
    //       });
    //     } else {
    //       dispatch({
    //         type: LIST_MESSAGES_NULL_SUCCESS,
    //         payload: {
    //           data
    //         }
    //       });
    //     }
    //   } catch (error) {
    //     dispatch({
    //       type: LIST_MESSAGES_ERROR,
    //       payload: {
    //         error
    //       }
    //     });
    //     // throw new Error(error);
    //   }
    // }
  };
}

export function clearMessages() {
  return async dispatch => {
    dispatch({
      type: CLEAR_MESSAGES
    });
  };
}

export function clearPrivateMessages() {
  return async dispatch => {
    dispatch({
      type: CLEAR_MESSAGES_PRIVATE
    });
  };
}
