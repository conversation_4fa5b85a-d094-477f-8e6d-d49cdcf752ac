/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { submit, change } from "redux-form";
import { injectIntl } from "react-intl";
import TextField from "@material-ui/core/TextField";
import DialogFrame from "../../../../../common/DialogFrame";
import Dialog from "../../../../../common/Dialog";
import ProposalForm from "../../../Proposal/ProposalForm";
import {
  createProposal,
  clearCreateProposal,
} from "../../../../../../actions/proposal";
import {
  possessionLangIdMapping,
  floorTypeLangIdMapping,
  getDefaultRemarks,
} from "../../../Proposal/FormSection/selectOptions";
import langFile from "../../../../../../lang/IND/messages";
import SubmitDialog from "../../../../../common/SubmitDialog";
import {
  getLangKey,
  getProposalTenancyDesc,
  goToProposalList,
  parsePeriod,
} from "../../../../../../helper/generalHelper";
import {
  getFloorTypeInfoFromLangFile,
} from "../../../../../Saleskit/helpers";
import { sbu, generalProposalQuota } from "../../../../../../config";

class ProposalCreate extends React.Component {
  static propTypes = {
    stockData: PropTypes.object,
    media: PropTypes.object,
    buildingMedia: PropTypes.object,
    userInfo: PropTypes.object,
    createProposal: PropTypes.func.isRequired,
    creatingProposal: PropTypes.bool,
    createdProposal: PropTypes.bool,
    createProposalError: PropTypes.string,
    clearCreateProposal: PropTypes.func.isRequired,
    dispatchSubmitForm: PropTypes.func.isRequired,
    createProposalDialogOpen: PropTypes.bool,
    handleCloseCreateProposalDialog: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    const { stockData, media, buildingMedia } = props;
    this.state = {
      parsedStockData: this.stockDataToFormData(
        stockData,
        media,
        buildingMedia,
      ),
      proposalName: this.parseDefaultProposalName(stockData),
    };
  }

  submit = (values) => {
    // console.log(values);
    const { media, buildingMedia, stockData } = this.props;
    const photos = [].concat(media?.photo || [], buildingMedia?.photo || []);
    const videos = [].concat(media?.video || [], buildingMedia?.video || []);
    const getPhotoInfoFromRedux = (id) => {
      let photo = photos?.filter((v) => v.id === id)?.[0] || null;
      if (photo)
        photo = {
          id: photo.id,
          mediumRoot: photo.mediumRoot,
          photoContent: photo.photoContent,
        };
      return photo;
    };
    const getVideoInfoFromRedux = (id) => {
      let video = videos?.filter((v) => v.id === id)?.[0] || null;
      if (video)
        video = {
          id: video.id,
          mediumRoot: video.mediumRoot,
          youtubeId: video.youtubeMrId || video.youtubeHkpId || video.youtubeId,
        };
      return video;
    };
    const getPossessionInfoFromLangFile = (value) => {
      return {
        nameZh:
          value &&
          possessionLangIdMapping[value] &&
          langFile.zh[possessionLangIdMapping[value]]
            ? langFile.zh[possessionLangIdMapping[value]]
            : "",
        nameEn:
          value &&
          possessionLangIdMapping[value] &&
          langFile.en[possessionLangIdMapping[value]]
            ? langFile.en[possessionLangIdMapping[value]]
            : "",
      };
    };
    const { cargoLift, passengerLift } = this.getNumberOfLift(stockData);
    const containers = this.getContainers(stockData);

    const currTenancyRecords = stockData?.tenancyRecords?.filter(
      (v) => v.status === "Current" && !v.deleted,
    );

    // stock photo which has the main tag
    const mainStockPhotoId = media?.photo?.filter(
      (v) => v.tags && v.tags.includes("main"),
    )?.[0]?.id;
    // building photo which has the main tag
    const mainBuildingPhotoId = buildingMedia?.photo?.filter(
      (v) => v.tags && v.tags.includes("main"),
    )?.[0]?.id;
    // main stock photo has higher priority
    const mainPhoto = getPhotoInfoFromRedux(
      mainStockPhotoId || mainBuildingPhotoId || null,
    );

    let parsedData = {
      ...values,
      floorType: getFloorTypeInfoFromLangFile(values?.floorType),
      type: values?.type,
      floor: values?.floor?.[0],
      unit: values?.unit?.[0],
      avgPrice: {
        ...values?.avgPrice?.[0],
        value: values?.avgPrice?.[0]?.value || 0,
      },
      totalPrice: {
        ...values?.totalPrice?.[0],
        value: values?.totalPrice?.[0]?.value || 0,
      },
      bottomAvgPrice: values?.bottomAvgPrice || 0,
      bottomTotalPrice: values?.bottomTotalPrice || 0,
      avgRent: {
        ...values?.avgRent?.[0],
        value: values?.avgRent?.[0]?.value || 0,
      },
      totalRent: {
        ...values?.totalRent?.[0],
        value: values?.totalRent?.[0]?.value || 0,
      },
      bottomAvgRent: values?.bottomAvgRent || 0,
      bottomTotalRent: values?.bottomTotalRent || 0,
      customBuilding: {
        value: values?.customBuilding,
        isShow: true,
      },
      areaEfficiency: {
        ...values?.areaEfficiency?.[0],
        value: values?.areaEfficiency?.[0]?.value || 0,
      },
      areaGross: {
        ...values?.areaGross?.[0],
        value: values?.areaGross?.[0]?.value || 0,
      },
      areaNet: {
        ...values?.areaNet?.[0],
        value: values?.areaNet?.[0]?.value || 0,
      },
      possession: {
        ...values?.possession?.[0],
        value: getPossessionInfoFromLangFile(values?.possession?.[0]?.value),
      },
      managementFee: {
        ...values?.managementFee?.[0],
        value: values?.managementFee?.[0]?.value || 0,
      },
      decoration: {
        ...values?.decoration?.[0],
        value: stockData?.decoration,
      },
      unitView: {
        ...values?.unitView?.[0],
        value: stockData?.unitView,
      },
      availability: {
        ...values?.availability?.[0],
        value: stockData?.availability,
      },
      stockType: {
        ...values?.stockType?.[0],
        value: {
          nameEn: stockData?.stockType?.nameEn,
          nameZh: stockData?.stockType?.nameZh,
        },
      },
      ceilingHeight: {
        ...values?.ceilingHeight?.[0],
        ft: values?.ceilingHeight?.[0]?.ft || 0,
        in: values?.ceilingHeight?.[0]?.in || 0,
      },
      currentTenants: values?.currentTenants?.map((v, i) => {
        return {
          tenant: {
            nameEn: currTenancyRecords[i]?.tenant?.nameEn,
            nameZh: currTenancyRecords[i]?.tenant?.nameZh,
            isShow: v.tenantIsShow,
          },
          rentalFee: {
            value: v.rentalFee || 0,
            isShow: v.rentalFeeIsShow,
          },
          period: {
            min: currTenancyRecords[i]?.expiry?.minDate,
            max: currTenancyRecords[i]?.expiry?.maxDate,
            isShow: v.periodIsShow,
          },
          tenancy: {
            nameEn: getProposalTenancyDesc(currTenancyRecords[i], "en"),
            nameZh: getProposalTenancyDesc(currTenancyRecords[i], "zh"),
            isShow: v.tenancyIsShow,
          },
        };
      }),
      yield: {
        ...values?.yield?.[0],
        value: values?.yield?.[0]?.value || 0,
      },
      customTitle: values?.customTitle?.[0],
      hideEmployeePhoto: !!values?.hideEmployeePhoto?.[0]?.isShow,
      hideContact: !!values?.hideContact?.[0]?.isShow,
      photos: []
        .concat(values?.stockMedia || [], values?.buildingMedia || [])
        .map(getPhotoInfoFromRedux)
        .filter((v) => v !== null),
      videos: []
        .concat(values?.stockMedia || [], values?.buildingMedia || [])
        .map(getVideoInfoFromRedux)
        .filter((v) => v !== null),
      googleMap: {
        isPP: !!(values?.googleMapPhoto && values?.googleMapPhoto.length > 0),
        isMain1: values?.main1Photo === "map",
        isMain2: values?.main2Photo === "map",
      },
      main1Photo:
        values?.main1Photo && getPhotoInfoFromRedux(values?.main1Photo),
      main2Photo:
        values?.main2Photo && getPhotoInfoFromRedux(values?.main2Photo),
      mainPhoto: mainPhoto, // stock or building photo which has the main tag

      // building info
      usage: {
        ...values?.usage?.[0],
        value: stockData?.building?.buildingUsage,
      },
      title: {
        ...values?.title?.[0],
        value: stockData?.building?.ownership,
      },
      inTakeDate: {
        ...values?.inTakeDate?.[0],
        // value: moment(stockData?.building?.completionDate, "YYYY").toDate(),
        value: stockData?.building?.completionDate,
      },
      developers: values?.developers?.map((v, i) => {
        return {
          ...v,
          value: {
            nameEn: stockData?.building?.developers?.[i]?.nameEn,
            nameZh: stockData?.building?.developers?.[i]?.nameZh,
          },
        };
      }),
      managementCompany: {
        ...values?.managementCompany?.[0],
        value: {
          nameEn: stockData?.building?.managementCompany?.name,
          nameZh: stockData?.building?.managementCompany?.name,
        },
      },
      transport: {
        ...values?.transport?.[0],
        value: stockData?.building?.transports,
      },
      passengerLift: {
        ...values?.passengerLift?.[0],
        value: {
          nameEn: passengerLift.toString(),
          nameZh: passengerLift.toString(),
        },
      },
      cargoLift: {
        ...values?.cargoLift?.[0],
        value: {
          nameEn: cargoLift.toString(),
          nameZh: cargoLift.toString(),
        },
      },
      containers: values?.containers?.map((v, i) => {
        return {
          ...v,
          value: containers[i],
        };
      }),
      airConditioningType: {
        ...values?.airConditioningType?.[0],
        value: stockData?.building?.airConditioning?.type,
      },

      // additional data which is required by proposal but not shown in the form (or is not a field in the form)
      stockId: stockData?.unicorn?.id,
      stockMongoId: stockData?._id,
      sbu: sbu,
      isSoleagent: !!(
        stockData.soleagent &&
        stockData.soleagent.periodStart != null &&
        stockData.soleagent.periodEnd != null
      ),
      districtNameZh: stockData?.building?.district?.nameZh,
      districtNameEn: stockData?.building?.district?.nameEn,
      streetNameZh: stockData?.street?.street?.nameZh,
      streetNameEn: stockData?.street?.street?.nameEn,
      streetNo: stockData?.street?.number,
      buildingNameZh: stockData?.building?.nameZh,
      buildingNameEn: stockData?.building?.nameEn,
      buildingDistrictNameZh: stockData?.building?.district?.nameZh,
      buildingDistrictNameEn: stockData?.building?.district?.nameEn,
      lng: stockData?.building?.coordinates?.longitude,
      lat: stockData?.building?.coordinates?.latitude,
      isBuildingFieldsAllHide: !(
        values?.usage?.[0]?.isShow ||
        values?.title?.[0]?.isShow ||
        values?.inTakeDate?.[0]?.isShow ||
        values?.developers?.filter((v) => v.isShow).length > 0 ||
        values?.managementCompany?.[0]?.isShow ||
        values?.transport?.[0]?.isShow ||
        values?.passengerLift?.[0]?.isShow ||
        values?.cargoLift?.[0]?.isShow ||
        values?.containers?.filter((v) => v.isShow).length > 0 ||
        values?.airConditioningType?.[0]?.isShow
      ),
      proposalName: this.state.proposalName,
    };

    // these photo and video fields are merged into photos and videos
    delete parsedData.stockMedia;
    delete parsedData.buildingMedia;
    // googleMapPhoto is converted to includeGoogleMap field
    delete parsedData.googleMapPhoto;

    // console.log(parsedData)

    this.props.createProposal(parsedData);
  };

  getNumberOfLift = (detail) => {
    const lifts = detail?.building?.lifts || [];
    let cargoLift = "";
    let passengerLift = "";
    lifts.map((v, i) => {
      switch (v.type) {
        case "Cargo":
          cargoLift = v.quantity;
          break;
        case "Passenger":
          passengerLift = v.quantity;
          break;
      }
    });
    return { cargoLift, passengerLift };
  };

  getContainers = (detail) => {
    const entrances = detail?.building?.entrances || [];
    let containers = [];
    for (let i = 0; i < entrances.length; i++) {
      let containerDataEn = entrances?.[i]?.container?.nameEn || "";
      let containerDataZh = entrances?.[i]?.container?.nameZh || "";

      if (entrances[i]["haveLoadingBay"]) {
        if (containerDataEn) containerDataEn += " Loading Bay";
        if (containerDataZh) containerDataZh += " 貨台";
      }
      containers.push({
        nameEn: containerDataEn,
        nameZh: containerDataZh,
      });
    }
    return containers;
  };

  stockDataToFormData = (detail, media, buildingMedia) => {
    // console.log(detail)
    const langKey = getLangKey(this.props.intl);
    const area = detail && detail.area ? detail.area : null;

    const { cargoLift, passengerLift } = this.getNumberOfLift(detail);

    const containers = this.getContainers(detail).map((v) => {
      return {
        value: v[langKey] || "---",
        isShow: false,
      };
    });

    const currTenancyRecords = detail?.tenancyRecords?.filter(
      (v) => v.status === "Current" && !v.deleted,
    );

    let defaultType = "Sale";
    let defaultRemarks = getDefaultRemarks("Sale");
    switch (detail?.status?.nameEn) {
      case "Lease":
        defaultType = "Lease";
        defaultRemarks = getDefaultRemarks("Lease");
        break;
      case "Sale+Lease":
        defaultType = "SaleAndLease";
        defaultRemarks = getDefaultRemarks("SaleAndLease");
        break;
    }

    const possibleFeeType = ["/SqFt", "/Qtr", "/Month", "/SY"];
    const possiblePaidBy = ["Paid By Tenant", "Paid By Landlord"];
    return {
      floorType: "Actual Floor",
      type: defaultType,
      avgPrice: [
        {
          value: detail?.askingPrice?.average,
          isShow: defaultType === "Sale" || defaultType === "SaleAndLease",
        },
      ],
      totalPrice: [
        {
          value: detail?.askingPrice?.total,
          isShow: defaultType === "Sale" || defaultType === "SaleAndLease",
        },
      ],
      bottomAvgPrice: detail?.askingPrice?.details?.filter(
        (v) => v.type === "average_bottom",
      )[0]?.value,
      bottomTotalPrice: detail?.askingPrice?.details?.filter(
        (v) => v.type === "total_bottom",
      )[0]?.value,
      avgRent: [
        {
          value: detail?.askingRent?.average,
          isShow: defaultType === "Lease" || defaultType === "SaleAndLease",
        },
      ],
      totalRent: [
        {
          value: detail?.askingRent?.total,
          isShow: defaultType === "Lease" || defaultType === "SaleAndLease",
        },
      ],
      bottomAvgRent: detail?.askingRent?.details?.filter(
        (v) => v.type === "average_bottom",
      )[0]?.value,
      bottomTotalRent: detail?.askingRent?.details?.filter(
        (v) => v.type === "total_bottom",
      )[0]?.value,
      floor: [
        {
          value: detail?.floor,
          isShow: true,
        },
      ],
      unit: [
        {
          value: detail?.unit,
          isShow: !!detail?.unit,
        },
      ],
      customBuilding: detail?.building?.[langKey] || "---",
      customStreet: detail?.street?.street?.[langKey] || "---",
      customStreetNo: detail?.street?.number || "",
      customDistrict: detail?.building?.district?.[langKey] || "---",
      areaEfficiency: [
        {
          value: area?.efficiency || "",
          isShow: false, // all net area default unselect
        },
      ],
      areaGross: [
        {
          value: area?.size || "",
          isShow: !!area?.size,
        },
      ],
      areaNet: [
        {
          value:
            area?.sizes?.filter((v) => v.type === "SALEABLE")[0]?.value || "",
          isShow: false, // all net area default unselect
        },
      ],
      possession: [
        {
          value: detail?.possession?.nameEn,
          isShow: false,
        },
      ],
      managementFee: [
        {
          value: detail?.managementFee?.number || "",
          unit:
            possibleFeeType.indexOf(detail?.managementFee?.type) >= 0
              ? detail?.managementFee?.type
              : "",
          paidBy:
            possiblePaidBy.indexOf(detail?.managementFee?.paidBy) >= 0
              ? detail?.managementFee?.paidBy
              : "",
          isShow: false,
        },
      ],
      decoration: [
        {
          value: detail?.decoration?.[langKey] || "---",
          isShow: false,
        },
      ],
      unitView: [
        {
          value: detail?.unitView?.[langKey] || "---",
          isShow: false,
        },
      ],
      availability: [
        {
          value: detail?.availability || "---",
          isShow: false,
        },
      ],
      stockType: [
        {
          value: detail?.stockType?.[langKey],
          isShow: false,
        },
      ],
      ceilingHeight: [
        {
          ft: detail?.ceilingHeight?.ft || "",
          in: detail?.ceilingHeight?.in || "",
          isShow: false,
        },
      ],
      remarks: defaultRemarks,
      currentTenants: currTenancyRecords?.map((v) => {
        const minDate = v?.expiry?.minDate;
        const maxDate = v?.expiry?.maxDate;
        return {
          tenant: v?.tenant?.[langKey] || "---",
          tenantIsShow: false,
          rentalFee: v?.rent?.total,
          rentalFeeIsShow: false,
          period: parsePeriod(minDate, maxDate, this.props.intl),
          periodIsShow: false,
          tenancy: getProposalTenancyDesc(v, this.props.intl.locale),
          tenancyIsShow: false,
        };
      }),
      yield: [
        {
          value: detail?.yield || "",
          isShow: false,
        },
      ],
      customTitle: [
        {
          value: "",
          isShow: false,
        },
      ],
      hideEmployeePhoto: [
        {
          value: this.props.intl.formatMessage({
            id: "proposal.form.hideemployeephoto",
          }),
          isShow: false,
        },
      ],
      hideContact: [
        {
          value: this.props.intl.formatMessage({
            id: "proposal.form.hidecontact",
          }),
          isShow: false,
        },
      ],
      stockMedia: []
        .concat(media?.photo || [], media?.video || [])
        ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
        ?.map((v) => v.id),
      buildingMedia: []
        .concat(buildingMedia?.photo || [], buildingMedia?.video || [])
        ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
        ?.map((v) => v.id),
      main1Photo: "map",

      // building info
      usage: [
        {
          value: detail?.building?.buildingUsage?.[langKey] || "---",
          isShow: !!detail?.building?.buildingUsage?.[langKey],
        },
      ],
      title: [
        {
          value: detail?.building?.ownership?.[langKey] || "---",
          isShow: false,
        },
      ],
      inTakeDate: [
        {
          value: detail?.building?.completionDate || "---",
          isShow: !!detail?.building?.completionDate,
        },
      ],
      developers: detail?.building?.developers?.map((v) => {
        return {
          value: v[langKey] || "---",
          isShow: false,
        };
      }),
      managementCompany: [
        {
          value: detail?.building?.managementCompany?.name || "---",
          isShow: false,
        },
      ],
      transport: [
        {
          value: detail?.building?.transports?.[langKey] || "---",
          isShow: false,
        },
      ],
      passengerLift: [
        {
          value: passengerLift || "---",
          isShow: false,
        },
      ],
      cargoLift: [
        {
          value: cargoLift || "---",
          isShow: false,
        },
      ],
      containers: containers,
      airConditioningType: [
        {
          value: detail?.building?.airConditioning?.type?.[langKey] || "---",
          isShow: false,
        },
      ],
    };
  };

  parseDefaultProposalName = (detail) => {
    const langKey = getLangKey(this.props.intl);
    return detail.building && detail.building[langKey]
      ? detail.building[langKey]
      : "";
  };

  handleCloseSubmitDialog = () => {
    this.props.handleCloseCreateProposalDialog();
    this.props.clearCreateProposal();
  };

  submitDialogCallback = () => {
    goToProposalList();
  };

  handleNameChange = (e) => {
    this.setState({ proposalName: e.target.value });
  };

  render() {
    const {
      changeFieldValue,
      formFields,
      creatingProposal,
      createdProposal,
      createProposalError,
      dispatchSubmitForm,
      createProposalDialogOpen,
      exceedQuota,
      intl,
    } = this.props;
    const { proposalName } = this.state;

    return (
      <div>
        <div>
          <ProposalForm
            onSubmit={this.submit}
            initialValues={this.state.parsedStockData}
            changeFieldValue={changeFieldValue}
            formFields={formFields}
          />
        </div>
        {exceedQuota ? (
          <Dialog
            open={createProposalDialogOpen}
            handleClose={this.props.handleCloseCreateProposalDialog}
          >
            {intl.formatMessage(
              { id: "proposal.create.exceed" },
              { quota: generalProposalQuota },
            )}
            <DialogFrame
              buttonMain={intl.formatMessage({
                id: "common.ok",
              })}
              handleMain={this.handleCloseSubmitDialog}
            ></DialogFrame>
          </Dialog>
        ) : (
          <SubmitDialog
            dialogOpen={createProposalDialogOpen}
            handleCloseDialog={this.handleCloseSubmitDialog}
            succCallback={this.submitDialogCallback}
            submitting={creatingProposal}
            submitted={createdProposal}
            error={createProposalError}
            submit={dispatchSubmitForm}
            submitBtnText={intl.formatMessage({ id: "proposal.form.save" })}
            succMsg={intl.formatMessage(
              { id: "proposal.form.savesuccess" },
              { filename: proposalName },
            )}
          >
            <TextField
              label={intl.formatMessage({ id: "proposal.form.proposalname" })}
              value={proposalName}
              onChange={this.handleNameChange}
              variant="outlined"
              fullWidth
            />
          </SubmitDialog>
        )}
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  userInfo:
    state.auth &&
    state.auth.user &&
    state.auth.user.login &&
    state.auth.user.login.info
      ? state.auth.user.login.info
      : {},
  stockData: state.stock.detail ? state.stock.detail : {},
  media: state.stock.media ? state.stock.media : {},
  buildingMedia: state.building.media ? state.building.media : {},
  formFields:
    state.form.proposalForm && state.form.proposalForm.values !== undefined
      ? state.form.proposalForm.values
      : {},
  creatingProposal: state.proposal.creatingProposal
    ? state.proposal.creatingProposal
    : false,
  createdProposal: state.proposal.createdProposal
    ? state.proposal.createdProposal
    : false,
  createProposalError:
    state.proposal.createProposalError &&
    state.proposal.createProposalError.message
      ? state.proposal.createProposalError.message
      : null,
});

const mapDispatchToProps = (dispatch) => {
  return {
    dispatchSubmitForm: () => dispatch(submit("proposalForm")),
    changeFieldValue: (field, value) =>
      dispatch(change("proposalForm", field, value)),
    createProposal: (...args) => dispatch(createProposal(...args)),
    clearCreateProposal: (...args) => dispatch(clearCreateProposal(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(ProposalCreate));
