import {
  <PERSON><PERSON><PERSON><PERSON>_START,
  LOGIN_SENDCODE,
  LOGIN_VERIFYCODE_SUCCESS,
  LOGIN_VERIFYCODE_FAILED,
  LOGIN_ERROR,
  LOGOUT_SUCCESS,
  CHECK_AUTH_FAILED,
  REFRESHING_TOKEN,
  DONE_REFRESHING_TOKEN,
  FAIL_REFRESHING_TOKEN,
  CLEAR_AUTH,
  SET_LOGIN_INFO,
  GET_JWE_TOKEN_SUCCESS,
  GET_JWE_TOKEN_FAILED,
} from "../constants/auth";

const initialState = {
  authorized: false,
  refreshing: false,
};

export default function auth(state = initialState, action) {
  switch (action.type) {
    case LOGIN_START:
      return {
        ...state,
        authorized: false,
        authorizing: true,
      };
    case LOGIN_SENDCODE: {
      return {
        ...state,
        authorized: false,
        authorizing: false,
        user: action.payload,
        error: null,
      };
    }
    case LOGIN_VERIFYCODE_SUCCESS: {
      return {
        ...state,
        authorized: true,
        authorizing: false,
        user: action.payload,
        error: null,
      };
    }
    case LO<PERSON>N_VERIFYCODE_FAILED: {
      return {
        ...state,
        authorized: false,
        authorizing: false,
        error: action.payload.error,
      };
    }
    case LOGIN_ERROR:
      return {
        ...state,
        authorized: false,
        authorizing: false,
        user: null,
        error: action.payload.error,
      };
    case LOGOUT_SUCCESS:
      return {
        ...state,
        authorized: false,
        authorizing: false,
        token: null,
        user: null,
      };

    case CHECK_AUTH_FAILED:
      return {
        ...state,
        sessionvalid: false,
        error: action.payload.error,
      };
    case CLEAR_AUTH:
      return {
        authorized: false,
        authorizing: false,
        token: null,
        user: null,
      };
    case REFRESHING_TOKEN:
      return {
        ...state,
        refreshing: true,
      };
    case DONE_REFRESHING_TOKEN:
      return {
        ...state,
        user: {
          ...state.user,
          oauth: action.payload.token,
          datetime: new Date().getTime(),
          casAccessToken: action.payload.casAccessToken,
          casRefreshToken: action.payload.casRefreshToken
        },
        refreshing: false,
        authorized: true,
      };
    case FAIL_REFRESHING_TOKEN:
      return {
        ...state,
      };
    case SET_LOGIN_INFO:
      return {
        ...state,
        user: {
          ...state.user,
          login: action.payload.loginInfo,
        },
      };
    case GET_JWE_TOKEN_SUCCESS:
      return {
        ...state,
        jweToken: action.payload.jweToken
      };
    default:
      return state;
  }
}
