import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import { injectIntl } from "react-intl";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import FieldVal from "../../../../common/FieldVal";
import { getLangKey } from "../../../../../helper/generalHelper";

const styles = (theme) => ({
  root: {
    padding: "2vw",
  },
});

class BasicInfo extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langkey = getLangKey(intl);

    const buildingUsage =
      detail.buildingUsage && detail.buildingUsage[langkey]
        ? detail.buildingUsage[langkey]
        : "---";
    const buildingGrade = detail.buildingGrade || "---";
    const isCloseToMTR =
      detail.isCloseToMTR === true
        ? intl.formatMessage({
            id: "common.yes",
          })
        : detail.isCloseToMTR === false
        ? intl.formatMessage({
            id: "common.no",
          })
        : "---";
    const completionDate = detail.completionDate || "---";
    const managementFee =
      detail.management && detail.management.fee
        ? "$" + detail.management.fee + "/" + intl.formatMessage({ id: "common.sqft" })
        : "---";

    const mgtFeeUpdateDate = "---";
    const feeIncludeACBool = detail.management
      ? detail.management.isIncludeAirCondCharge
      : null;
    const feeIncludeAC =
      feeIncludeACBool === true
        ? intl.formatMessage({
            id: "common.include",
          })
        : feeIncludeACBool === false
        ? intl.formatMessage({
            id: "common.exclude",
          })
        : "---";
    const developers = detail.developers || [];
    let developer = developers.length === 0 ? "---" : "";
    developers.forEach((v, i) => {
      developer += (i > 0 ? ", " : "") + (v[langkey] || "---");
    });

    const managementCompany =
      detail.managementCompany && detail.managementCompany.name
        ? detail.managementCompany.name
        : "---";
    const managementCompanyContact =
      detail.managementCompany &&
      detail.managementCompany.contacts &&
      detail.managementCompany.contacts[0]
        ? detail.managementCompany.contacts[0]
        : {};
    const contactName = managementCompanyContact.contactName || "---";
    const contactPhones = managementCompanyContact.contactPhones || [];
    let phone = contactPhones.length === 0 ? "---" : "";
    contactPhones.forEach((v, i) => {
      phone += (i > 0 ? " / " : "") + v;
    });
    let generalMapping = {
      [intl.formatMessage({
        id: "stock.usage",
      })]: { value: buildingUsage, xs: 6 },
      [intl.formatMessage({
        id: "search.form.grade",
      })]: { value: buildingGrade, xs: 6 },
      [intl.formatMessage({
        id: "building.mtr",
      })]: { value: isCloseToMTR, xs: 6 },
      [intl.formatMessage({
        id: "building.competition",
      })]: { value: completionDate, xs: 6 },
      [intl.formatMessage({
        id: "stock.mgtfee",
      })]: { value: managementFee, xs: 6 },
      [intl.formatMessage({
        id: "building.mgtfeeupdatedate",
      })]: { value: mgtFeeUpdateDate, xs: 6 },
      [intl.formatMessage({
        id: "building.accharge",
      })]: { value: feeIncludeAC, xs: 6 },
      [intl.formatMessage({
        id: "building.mgtcompany",
      })]: { value: managementCompany, xs: 12 },
      [intl.formatMessage({
        id: "stock.contact",
      })]: { value: contactName, xs: 12 },
      [intl.formatMessage({
        id: "building.phone",
      })]: { value: phone, xs: 12 },
    };

    let developersArr = [];
    for (let item in generalMapping) {
      developersArr.push([item, generalMapping[item]]);
    }

    if (developers && developers.length > 0) {
      for (let i = developers.length - 1; i >= 0; i--) {
        const itemToPush = [
          intl.formatMessage({
            id: "building.developer",
          }) + ` ${i + 1}`,
          {
            value: developers[i][langkey],
            xs: 12,
          },
        ];
        developersArr.splice(7, 0, itemToPush);
      }
    }

    return (
      <div className={classes.root}>
        <Grid container spacing={2}>
          {developersArr.map((v, i) => {
            return (
              <Grid item xs={v[1].xs} key={v[0]}>
                <FieldVal field={v[0]}>{v[1].value}</FieldVal>
              </Grid>
            );
          })}
        </Grid>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(BasicInfo));
