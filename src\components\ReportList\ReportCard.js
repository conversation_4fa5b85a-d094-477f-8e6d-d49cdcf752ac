import React, { useState } from "react";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/core/styles";
import { injectIntl, FormattedMessage } from "react-intl";
import _ from "lodash";
import GetAppIcon from '@material-ui/icons/GetApp';
import { link, sbu } from "../../config";
import { gtagHandler, takeLog, addActivityLog } from "../../actions/log";
import { dateFormatter } from '@/helper/generalHelper';

const styles = (theme) => ({
  root: {
    borderRadius: 4,
    padding: "1vw 2vw",
    backgroundColor: "#FFF",
    display: "flex",
  },
  date: {
    fontSize: ".875em",
    color: "#777",
  },
  name: {
    fontSize: "1.175em",
    wordBreak: "break-word",
  },
  link: {
    color: "inherit",
    textDecoration: "none",
    flex: 1,
  },
  downloadIcon: {
    margin: "auto 0 auto 1vw",
  },
  centerText: {
    textAlign: "center",
  },
  listProposalTag: {
    backgroundColor: "#FFFC00",
    fontWeight: 400,
    fontSize: "14px",
    borderRadius: "4px",
    padding: "0 2vw",
    marginLeft: 5,
    color: "#000000",
  },
});

function ReportCard({
  classes,
  detail,
  className,
  takeLog,
  addActivityLog,
  gtagHandler,
  intl,
}) {

  const handleOnClickDownload = (report) => {
    console.log("Download", report)
    const a = document.createElement('a');
    a.href =`${link.s3File.reportPrefix}${report.downloadLink}`;
    a.download = report.reportName;
    a.target = '_blank';
    a.rel = 'noopener noreferrer';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    const msg = JSON.stringify({
      action: "download-report",
      report
    });
    takeLog(msg);
    addActivityLog("report.bigDealReport", "read", { report });
  };

  const reportName = intl.locale === "en" ? detail.reportName : detail.reportNameZh;
  const date = detail.date ? dateFormatter(detail.date): "";

  return (
    <div className={`${classes.root} ${className}`}>
      <div className={classes.link}>
        <div className={classes.name}>{reportName} </div>
        <div className={classes.date}>
          {date}
        </div>
      </div>

      <div className={classes.downloadIcon}>
        <GetAppIcon onClick={()=>{handleOnClickDownload(detail)}} />
      </div>
    </div>
  );
}

const mapDispatchToProps = (dispatch) => {
  return {
    takeLog: (msg) => dispatch(takeLog(msg)),
    addActivityLog: (...args) => dispatch(addActivityLog(...args)),
    gtagHandler: (...args) => dispatch(gtagHandler(...args)),
  };
};

export default connect(
  null,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(ReportCard)));
