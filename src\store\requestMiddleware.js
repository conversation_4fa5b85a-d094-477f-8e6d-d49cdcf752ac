import fetch from "node-fetch";
import {
  REFRESHING_TOKEN,
  DONE_REFRESHING_TOKEN,
  FAIL_REFRESHING_TOKEN,
} from "../constants/auth";
import { setLoginInfo, setTokensAndPermission } from "../actions/auth";
import {
  refresh_token_timeout,
  cas_refresh_token_host,
  cas_profile_host,
  cas_client_id,
  cas_client_secret,
} from "../config";
import { getEmployeePermission } from "../actions/employee";

export default function requestMiddleware() {
  return ({ dispatch, getState }) =>
    (next) =>
    (action) => {
      const { checkrefreshToken, promise, type, ...rest } = action;

      // skip getting new token if some payload is not given
      if (!checkrefreshToken) {
        return next(action);
      }

      const { emp_id } = getState().auth.user.login.info;
      // handleAuthPromise(emp_id, next, action);
      handleAuthPromise(
        emp_id,
        dispatch,
        next,
        action,
      );

      const { datetime } =
        getState().auth.user && getState().auth.user.datetime
          ? getState().auth.user
          : "";
      const expires_in = parseInt(refresh_token_timeout);

      const refreshing = getState().auth.refreshing;
      if (!refreshing) {
        // calculate expiry in using the data return from Kong when first login
        if (getState().auth.user && getState().auth.user.oauth) {
          let refreshThreshold = new Date(datetime);
          refreshThreshold.setSeconds(
            refreshThreshold.getSeconds() + expires_in,
          );
          // console.log("current is : " + new Date());
          // console.log("expire in : " + refreshThreshold);

          if (new Date().getTime() > refreshThreshold.getTime()) {
            // console.log("token is outdated, get new token from refresh token");
            refreshToken(dispatch).then(() => {
              return next(action);
            });
          } else {
            // token is still valid
            // console.log("token is still valid");
            return next(action);
          }
        }
      } else {
        return next(action);
      }
    };
}
/*
function refreshToken(dispatch) {
  const freshTokenPromise = fetchOauthToken()
    .then(t => {
      if (t.valid) {
         //refresh cas token
         fetchCASToken()
         .then( casToken =>{
            // dispatch a success
            if(casToken.valid){
              //  t.casAccessToken = casToken.access_token;
              //  t.casRefreshToken = casToken.refresh_token;
              localStorage.setItem('casAccessToken', casToken.access_token);
              localStorage.setItem('casRefreshToken', casToken.refresh_token);
            }

            dispatch({
              type: DONE_REFRESHING_TOKEN,
              payload: t
            });
         }).catch(e => {
          console.log("error refreshing cas token", e);
          dispatch({
            type: FAIL_REFRESHING_TOKEN
          });
          return Promise.reject(e);
        });


      } else {
        dispatch({
          type: FAIL_REFRESHING_TOKEN
        });
      }
    })
    .catch(e => {
      console.log("error refreshing token", e);
      dispatch({
        type: FAIL_REFRESHING_TOKEN
      });
      return Promise.reject(e);
    });

  dispatch({
    type: REFRESHING_TOKEN,
    // we want to keep track of token promise in the state so that we don't try to refresh
    // the token again while refreshing is in process
    freshTokenPromise
  });
  return freshTokenPromise;
}
*/

function refreshToken(dispatch) {
  const freshTokenPromise = fetchOauthToken()
    .then((t) => {
      if (t.valid) {
        //refresh cas token
        fetchCASToken()
          .then((casToken) => {
            // dispatch a success
            if (casToken.valid) {
              //  t.casAccessToken = casToken.access_token;
              //  t.casRefreshToken = casToken.refresh_token;
              dispatch(setTokensAndPermission(casToken));
            }

            dispatch({
              type: DONE_REFRESHING_TOKEN,
              payload: t,
            });
          })
          .catch((e) => {
            console.log("error refreshing cas token", e);
            dispatch({
              type: FAIL_REFRESHING_TOKEN,
            });
            return Promise.reject(e);
          });
      } else {
        dispatch({
          type: FAIL_REFRESHING_TOKEN,
        });
      }
    })
    .catch((e) => {
      console.log("error refreshing token", e);
      dispatch({
        type: FAIL_REFRESHING_TOKEN,
      });
      return Promise.reject(e);
    });

  dispatch({
    type: REFRESHING_TOKEN,
    // we want to keep track of token promise in the state so that we don't try to refresh
    // the token again while refreshing is in process
    freshTokenPromise,
  });
  return freshTokenPromise;
}

async function fetchCASToken() {
  //get new CAS access token from refresh token

  let response = await fetch("/castoken-refresh", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      refresh_token: localStorage.getItem("casRefreshToken"),
    }),
  });
  let data = await response.json();
  return data;
}

async function fetchOauthToken() {
  // get new token from refresh token
  // console.log("in fetchOauthToken...");
  // console.log("refresh token: " + refresh_token);
  let response = await fetch("/totp-refresh", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    // body: JSON.stringify({
    //   refresh_token: refresh_token
    // })
  });
  let data = await response.json();
  return data;

  //   return await fetch("/totp-refresh", {
  //     method: "POST",
  //     headers: {
  //       "Content-Type": "application/json"
  //     },
  //     body: JSON.stringify({
  //       refresh_token: refresh_token
  //     })
  //   })
  //     .then(data => data.json())
  //     .then(result => {
  //       console.log("after called refresh token");
  //       return result;
  //     });
}

function handleAuthPromise(emp_id, dispatch, next, action) {
  const handleAuthPromise = checkAuth(emp_id).then((t) => {
    if (t.UserSessionExist == false) {
      // delete current client session
      window.location.replace("/");
      return next(action);
    }
  });
  return handleAuthPromise;
}

async function checkAuth(emp_id) {
  // console.log("in checking auth...");
  const checklogin = await fetch("/checklogin", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
    body: JSON.stringify({
      emp_id,
    }),
  });
  const validlogin = await checklogin.json();
  return validlogin;
}
