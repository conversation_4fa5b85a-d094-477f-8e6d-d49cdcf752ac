import React from "react";
import { reduxForm } from "redux-form";
import { withStyles } from "@material-ui/core/styles";
import FormButton from "../../../common/FormButton";
import FieldSection from "./FormSection/FieldSection";
import { goToDefaultTransactionSearchResult, defaultTxQuery } from "../../../../helper/generalHelper";
import clsx from "clsx";
import { FormattedMessage } from "react-intl";

const styles = (theme) => ({
  root: {
    backgroundColor: "#FFF",
    // paddingBottom: "7vh"
  },
  searchbutton: {
    margin: "1vw",
    textTransform: "none",
    fontSize: "1.125em",
    height: "42px",
  },
  clearbutton: {
    background: "#6be6a1",
  },
  buttoncontainer: {
    display: "flex",
    justifyContent: "space-evenly",
    alignItems: "center",
    zIndex: "999",
    position: "fixed",
    left: "50vw",
    bottom: "2vh",
    transform: "translateX(-50%)",
  },
});

const validate = (values) => {
  const errors = {};
  if (values.dateMin && values.dateMax) {
    if (values.dateMin > values.dateMax) {
      errors.dateMin = errors.dateMax = "Input format is invalid";
    }
  }
  return errors;
};

let SearchForm = (props) => {
  const {
    classes,
    handleSubmit,
    error,
    submitting,
    reset,
    pristine,
    selectedData,
    setSelectedData,
    expanded,
    isSticky,
    initialize,
    initialValues,
    fromChildToParentCallback,
  } = props;

  const handleResetClick = () => {
    initialize(defaultTxQuery);
  };

  fromChildToParentCallback(initialize);

  return (
    <form onSubmit={handleSubmit} className={classes.root}>
      <FieldSection
        selectedData={selectedData}
        setSelectedData={setSelectedData}
        expanded={expanded}
        initialValues={initialValues}
      >
        <div className={classes.buttoncontainer}>
          <FormButton
            disabled={submitting}
            onClick={handleResetClick}
            className={clsx(classes.searchbutton, classes.clearbutton)}
          >
            <FormattedMessage id="search.button.clear" />
          </FormButton>
          <FormButton type="submit" className={classes.searchbutton}>
            <FormattedMessage id="search.button.search" />
          </FormButton>
        </div>
      </FieldSection>
    </form>
  );
};

SearchForm = reduxForm({
  form: "searchForm",
  enableReinitialize: true,
  keepDirtyOnReinitialize: true, // must add this to get the initialize function work
  // I think this a bug of redux form and this does not keep dirty fields actually
  validate,
  onChange: (values, dispatch, props, previousValues) => {
    if (props.expanded == false) {
      if (values.building !== previousValues.building) props.submit();
    }
  },
})(SearchForm);

export default withStyles(styles)(SearchForm);
