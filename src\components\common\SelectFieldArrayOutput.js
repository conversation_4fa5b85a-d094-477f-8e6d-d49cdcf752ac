import React from "react";
import TextField from "@material-ui/core/TextField";
import { makeStyles } from "@material-ui/core/styles";
import Select from "react-select";
import Paper from "@material-ui/core/Paper";

const useStyles = makeStyles((theme) => ({
  root: {
    // marginLeft: theme.spacing(1),
    // marginRight: theme.spacing(1)
    // width: '100%',
  },
  input: {
    display: "flex",
    // padding: "2px 0 2px 14px",
    // height: "auto",
    cursor: "pointer",
  },
  valueContainer: {
    display: "flex",
    flexWrap: "wrap",
    flex: 1,
    alignItems: "center",
    overflow: "hidden",
  },
  paper: {
    position: "absolute",
    marginTop: theme.spacing(1),
    left: 0,
    right: 0,
    zIndex: 10,
  },
  indicatorSeparator: {
    display: "none",
  },
}));

function Control(props) {
  const {
    children,
    innerProps,
    innerRef,
    selectProps: { classes, TextFieldProps },
  } = props;

  return (
    <TextField
      fullWidth
      InputProps={{
        inputComponent,
        inputProps: {
          className: classes.input,
          ref: innerRef,
          children,
          ...innerProps,
        },
      }}
      {...TextFieldProps}
    />
  );
}

function Menu(props) {
  return (
    <Paper
      square
      className={props.selectProps.classes.paper}
      {...props.innerProps}
    >
      {props.children}
    </Paper>
  );
}

function ValueContainer(props) {
  return (
    <div className={props.selectProps.classes.valueContainer}>
      {props.children}
    </div>
  );
}

function inputComponent({ inputRef, ...props }) {
  return <div ref={inputRef} {...props} />;
}

const components = {
  Control,
  Menu,
  ValueContainer,
  IndicatorSeparator: () => null,
};

function SelectFieldArrayOutput(props) {
  const {
    ranges,
    input,
    isClearable,
    syncValue,
    selectedData,
    setSelectedData,
    customInputProps,
    changeFieldValue,
    initialFormValues,
    extraHandleChange,
    disabled,
    ...custom
  } = props;
  
  const isArrayOutput =
    props.isArrayOutput !== undefined ? props.isArrayOutput : true;
  const { value, name, onChange, onBlur } = input;

  const { touched, invalid, error } = props.meta;

  let val = null;
  if (selectedData) {
    val = selectedData;
  } else if (value !== null && value !== undefined) {
    let s = isArrayOutput ? value[0] : value;
    if (typeof s === "object")
      val = s;
    else
      val = ranges && ranges.filter(v => v.value === s)[0] ? ranges.filter(v => v.value === s)[0] : null;
  }
  if (!val) val = ranges[0];

  const [single, setSingle] = React.useState(val);
  const classes = useStyles();
  React.useEffect(() => {
    setSingle(val);
  }, [val]);

  function handleChangeSingle(newValue) {
    if (extraHandleChange) extraHandleChange(newValue);
    setSingle(newValue);
    if (setSelectedData) setSelectedData(name, newValue);
    if (isArrayOutput) {
      if (newValue != null && newValue.value !== "") {
        onChange([newValue.value]);
      } else {
        onChange([]);
      }
    } else {
      if (newValue != null && newValue.value !== "") {
        onChange(newValue.value);
      } else {
        onChange("");
      }
    }
  }

  const selectStyles = {
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected ? "#33CCCC" : "#fff",
      ":active": {
        backgroundColor: state.isSelected ? "#33CCCC" : "#33CCCC",
      },
    }),
    placeholder: (styles, { isDisabled }) => ({
      ...styles,
      color: isDisabled ? "rgba(0, 0, 0, 0.38)" : "hsl(0,0%,20%)",
    }),
  };

  return (
    <div className={classes.root}>
      <Select
        classes={classes}
        styles={selectStyles}
        inputId={name || null}
        TextFieldProps={{
          label: props.label,
          error: touched && invalid,
          helperText: touched && error,
          InputLabelProps: {
            htmlFor: name || null,
            shrink: true,
          },
          margin: "normal",
          variant: "outlined",
          ...customInputProps,
        }}
        placeholder={props.placeholder || ""}
        components={components}
        value={syncValue ? syncValue : val}
        options={ranges}
        onChange={handleChangeSingle}
        isClearable={isClearable !== false}
        isDisabled={disabled}
        {...custom}
        onBlur={() => onBlur()}
      />
    </div>
  );
}

export default SelectFieldArrayOutput;
