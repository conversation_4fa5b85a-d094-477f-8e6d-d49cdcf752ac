import React from "react";
import {
  Box,
  Paper,
  Typography,
  makeStyles,
} from "@material-ui/core";
import Vector from "./asset/vector.svg";
import { convertCurrency, dateFormatter } from "@/helper/generalHelper";
import clsx from "clsx";

/** @typedef {"SBUdirector" | "DistrictDirector" | "Manager" | "Agent"} PersonalType */
const useStyles = makeStyles((theme) => ({
  performanceCard: {
    display: "flex",
    flexDirection: "column",
    padding: "16px 6px 8px 16px",
    gap: "16px",
    borderRadius: theme.spacing(1),
    border: "none",
    position: "relative",
    overflow: "hidden",
    background: "linear-gradient(90deg, rgba(245,236,215,1) 0%, rgba(240,211,149,1) 100%)",
    boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.1)",
    "&::after": {
      content: '""',
      position: "absolute",
      width: "184px",
      height: "184px",
      right: "-24px",
      top: "-25px",
      background: `url(${Vector}) no-repeat center center`,
      backgroundSize: "contain",
      zIndex: 0,
      opacity: 1
    }
  },
  performanceMetrics: {
    width: "100%",
    "display": "flex",
    "flexDirection": "column",
    "alignItems": "flex-start",
    "padding": "0px",
    "gap": "2px",
    "height": "63px",
    "flex": "none",
    "flexGrow": 0,
    "position": "relative",
    "zIndex": 1
  },
  metricsTitle: {
    "height": "20px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "16px",
    "lineHeight": "20px",
    "textAlign": "center",
    "letterSpacing": "0.02em",
    "color": "#522E17",
    "flex": "none",
    "flexGrow": 0
  },
  metricsValue: {
    "height": "41px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "32px",
    "lineHeight": "41px",
    "letterSpacing": "0.02em",
    "color": "#522E17",
    "flex": "none",
    "flexGrow": 0,
  },
  progressContainer: {
    padding: "0 17px 0 24px",
    position: "relative",
    gap: "4px",
    display: "grid",
    "zIndex": 1,
  },
  progressBar: {
    position: "relative",
    height: "6px", // 14px
    borderStyle: "inset",
    borderRadius: "24px",
    background: "#FFF",
  },
  progressBackground: {
    position: "absolute",
    width: "100%",
    height: 6,
    top: 4,
    backgroundColor: "#FFF",
    borderRadius: 8
  },
  progressFill: {
    transition: "width 0.6s ease",
    position: "absolute",
    height: 6,
    top: "0px",
    left: 0,
    backgroundColor: "#ffb000",
    borderRadius: 8
  },
  currentMarker: {
    position: "absolute",
    width: 14,
    height: 14,
    top: "-4px",
    backgroundColor: "white",
    borderRadius: "50%"
  },
  endMarker: {
    position: "absolute",
    width: 14,
    height: 14,
    top: "-4px",
    right: "-4px",
    backgroundColor: "white",
    borderRadius: 8
  },
  tierLabels: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
    height: "36px",
  },
  tierItem: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    width: 40,
    position: "absolute",
    "&:nth-child(1)": {
      position: "absolute",
      transform: "translate(-8.5px, 0)",
      left: "50%",
    },
    "&:nth-child(2)": {
      transform: "translate(-2.5px, 0)",
      position: "absolute",
      right: 0,
    },
  },
  tierName: {
    "height": "18px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "14px",
    "lineHeight": "18px",
    "textAlign": "center",
    "letterSpacing": "0.02em",
    "color": "#522E17",
    "flex": "none",
    "flexGrow": 0,
  },
  tierValue: {
    "height": "18px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "14px",
    "lineHeight": "18px",
    "letterSpacing": "0.02em",
    "color": "#522E17",
    "flex": "none",
    "flexGrow": 0,
  },
  statusContainer: {
    display: "flex",
    flexWrap: "wrap",
    alignItems: "baseline",
    justifyContent: "space-between",
    width: "100%",
    marginTop: "8px",
    "position": "relative",
    "zIndex": 1,
  },
  statusMessage: {
    "height": "20px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "16px",
    "lineHeight": "20px",
    "letterSpacing": "0.02em",
    "color": "#522E17",
    "flex": "none",
    "flexGrow": 1,
  },
  updateDate: {
    "height": "15px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "12px",
    "lineHeight": "15px",
    "textAlign": "right",
    "letterSpacing": "0.02em",
    "color": "#BEA061",
    "flex": "none",
    "flexGrow": 0,
  },
}));

/** 每個階段`markDot`的`color`*/
const markerColors = [
  "white",
  "#C0CEE1",
  "#e1b723",
];

/**
 * @typedef {Object} MembershipTier
 * @property {string} name - 会员级别`名称`
 * @property {string} color - 进度条`颜色`
 * @property {number} value - 到达该级别需要多少 `performance`
 */

/**
 * @typedef {Object} GoldCardProps
 * @property {string=} title - 示例："你的年度業績"
 * @property {string} metricsValue - 示例："$1.00M / 10單"
 * @property {number} currentPerformance - 当前业绩
 * @property {MembershipTier[]=} membershipTiers
 * @property {string=} progressMaxColor - 进度条最大值颜色
 * @property {string=} updateDate - 更新日期
 * @property {PersonalType=} personalType
 */

/**
 * @param {GoldCardProps} props 
 */
function GoldCard(props) {
  const classes = useStyles();

  const {
    personalType = "",
  } = props;

  const {
    title = "你的年度業績",
    metricsValue,
    currentPerformance,
    membershipTiers,
    progressMaxColor = "#C39126",
  } = props;

  const progressFillColor = React.useMemo(() => {
    if (!membershipTiers?.length) { return null; }
    if (currentPerformance >= membershipTiers[membershipTiers.length - 1].value) {
      return progressMaxColor;
    } else {
      const index = membershipTiers.findIndex((tier, index) => {
        if (currentPerformance < tier.value) {
          return;
        }
        if ((index >= membershipTiers.length - 2 || currentPerformance < membershipTiers[index + 1].value)) {
          return true;
        }
      });
      return membershipTiers[index + 1].color;
    }
  }, [progressMaxColor, currentPerformance, membershipTiers]);

  const progress = React.useMemo(() => {
    if (!membershipTiers?.length) { return 0; }
    const maxTiers = membershipTiers[membershipTiers.length - 1].value || 0;

    const maxPercent = membershipTiers.length === 1 ? 50 : 100;
    return Math.min((currentPerformance / maxTiers) * maxPercent, 100);
  }, [currentPerformance, membershipTiers]);

  const performanceData = React.useMemo(() => {
    const result = {
      remain: "",
      updateDate: dateFormatter(props.updateDate),
    };
    if (!membershipTiers?.length) { return result; }
    if (currentPerformance < membershipTiers[0].value) {
      result.remain = `離${membershipTiers[0].name}會員還需${convertCurrency(membershipTiers[0].value - currentPerformance)}!`;
    } else {
      for (let i = 0; i < membershipTiers.length; i++) {
        if (currentPerformance >= membershipTiers[i].value) {
          result.remain = `已達標${membershipTiers[i].name}會員資格！`;
        }
      }
    }
    return result;
  }, [currentPerformance, membershipTiers, props.updateDate]);

  /** - 目前只有 1 ~ 2 个 `dot`，第一个 `dot` 都是在中间 */
  const hasEndMarker = React.useMemo(() => {
    return membershipTiers?.length && membershipTiers.length >= 2;
  }, [membershipTiers]);

  const getMarkerColor = React.useCallback((markIndex = 0) => {
    const defaultColor = "white";
    if (personalType !== "Agent") {
      if (progress >= 50 || progress >= 100) {
        return progressFillColor;
      }
      return defaultColor;
    }

    let level = 1; // 默認和 level 1 一樣, 不要 level 0 了
    if (progress >= 100) {
      level = 2;
    } else if (progress >= 50) {
      level = 1;
    }

    switch (level) {
      case 0: return markerColors[0] || defaultColor;
      case 1: return markerColors[markIndex] || defaultColor;
      case 2: return markerColors[2] || defaultColor;
    }
    return markerColors[markIndex] || defaultColor;
  }, [progress, personalType, progressFillColor]);

  return (
    <>
      {/* 业绩卡片 */}
      <Paper className={classes.performanceCard} elevation={0}>
        {/* 业绩指标 */}
        <Box className={classes.performanceMetrics}>
          <Typography className={classes.metricsTitle}>
            {title}
          </Typography>
          <Typography className={classes.metricsValue}>
            {metricsValue}
          </Typography>
        </Box>

        {/* 进度条 */}
        {membershipTiers?.length && <Box className={clsx(classes.progressContainer, "goldCard-progressContainer")}>
          <Box className={classes.progressBar}>
            {/* 进度条背景 */}
            <Box className={classes.progressBackground} />

            {/* 进度条填充 */}
            <Box className={classes.progressFill} style={{ width: `${progress}%`, background: progressFillColor }} />

            {/* 当前位置标记 */}
            <Box
              className={classes.currentMarker}
              style={{ left: `${50}%`, backgroundColor: getMarkerColor(1) }}
            />

            {/* 终点标记 */}
            {hasEndMarker && <Box
              className={classes.endMarker}
              style={{ backgroundColor: getMarkerColor(2) }} />}
          </Box>

          {/* 等级标签 */}
          <Box className={clsx(classes.tierLabels, "goldCard-tierLabels")}>
            {membershipTiers.map((tier, index) => (
              <Box key={index} className={clsx(classes.tierItem, "goldCard-tierItem")}>
                <Typography className={classes.tierName}>
                  {tier.name}
                </Typography>
                <Typography className={classes.tierValue}>
                  {convertCurrency(tier.value || 0)}
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>}

        {/* 状态消息和更新日期 */}
        <Box className={classes.statusContainer}>
          <Typography className={classes.statusMessage}>
            {/* 離白金會員還需{performanceData.remain}M! */}
            {performanceData.remain}
          </Typography>

          <Typography className={classes.updateDate}>
            更新日期 {performanceData.updateDate}
          </Typography>
        </Box>
      </Paper>
    </>
  );
}

export default GoldCard;
