import {
  UPDATE_HEADER,
  UPDATE_HEADER_TITLE,
  UPDATE_HEADER_SUBTITLE,
} from "../constants/header";

export const updateHeader = (payload) => (dispatch, getState) => {
  const { title, subTitle } = getState().header;
  const { title: newTitle, subTitle: newSubTitle } = payload;

  // Only dispatch if title or subTitle has changed
  if (title !== newTitle || subTitle !== newSubTitle) {
    dispatch({
      type: UPDATE_HEADER,
      payload: { title: newTitle, subTitle: newSubTitle },
    });
  }
};

export const updateHeaderTitle = (title) => (dispatch, getState) => {
  const currentTitle = getState().header.title;
  if (currentTitle !== title) {
    dispatch({
      type: 'UPDATE_HEADER_TITLE',
      payload: title,
    });
  }
};

export const updateHeaderSubTitle = (subTitle) => (dispatch, getState) => {
  const currentSubTitle = getState().header.subTitle;
  if (currentSubTitle !== subTitle) {
    dispatch({
      type: 'UPDATE_HEADER_SUBTITLE',
      payload: subTitle,
    });
  }
};
