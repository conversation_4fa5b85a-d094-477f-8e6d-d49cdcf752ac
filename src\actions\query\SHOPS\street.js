export const LIST_STREETS_QUERY = `
  query ($name: String, $limit: Int, $offset: Int, $sorter: [streetSorter]) {
    streets(name: $name, limit: $limit, offset: $offset, sort: $sorter) {
      _id
      nameEn
      nameZh
    }
  }
  `;

export const SEARCH_STREETS_QUERY = `
query ($limit: Int, $offset: Int, $name: String) {
  streetSearch(limit: $limit, offset: $offset, name: $name) {
    _id
    nameZh
    nameEn
  }
}
`;

export const LIST_STREET_MEDIA_QUERY = `
  query($sid: ID!, $empId: String) {
    street(id: $sid) {
      id
      photo: media(type:"photo", empId: $empId) {
        id
        type
        tags
        markByWWW
        scoring 
        manualOffline 
        employeeId
        availableStartTime
        availableEndTime
        createdDate
        filename
        description
        originalFilename
        approval
        status
        ... on Photo {
          mediumRoot
          photoContent
        }
      }

      document: media(type: "document", empId: $empId) {
        id
        type
        tags
        markByWWW
        scoring 
        manualOffline 
        employeeId
        createdDate
        lastModified
        approval
        description
        processing
        status
        ...on Document {
          filename
          originalFilename
          mediumRoot
          documentContent
        }
      }
      video: media(type:["video","kol_video"], empId: $empId) {
        id
        type
        tags
        markByWWW
        scoring 
        manualOffline 
        employeeId
        availableStartTime
        availableEndTime
        createdDate
        filename
        description
        originalFilename
        approval
        processing
        status
        ... on Video {
          youtubeId
          youtubeMrId
          youtubeHkpId
          youkuId
          vimeoId
          thumbnail
        }
        ... on KolVideo {
          mediumRoot
          thumbnail
          characteristicZh
          characteristicEn
          buildingName
          address
          propertyRefId
          buildingId
          vimeoId
          operator {
            eName
          }
        }
      }
    }
  }
  `;
