import React, { useState } from "react";
import { Grid } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import _ from "lodash";
import moment from "moment";
import { FormattedMessage, injectIntl } from "react-intl";
import PropTypes from "prop-types";
import { connect } from "react-redux";

import FormButton from "@/components/common/FormButton";
import { takeLandsearchLog } from "@/actions/log";
import SearchConsentDialog from "@/components/common/SearchConsentDialog";

const useStyles = makeStyles({
  wrapper: {
    borderRadius: 4,
    backgroundColor: "rgba(132,132,132,.1)",
    padding: "1vh 2vw",
    marginBottom: 10,
  },
  doc: {
    lineHeight: 1.3,
    fontWeight: 500,
  },
  subtext: {
    fontSize: ".875em",
    color: "#777",
  },
  fileBtn: {
    minWidth: "50px",
    height: "auto",
  },
});

function CompanySearchCard({ doc, takeLog, intl }) {
  const classes = useStyles();

  const [consentFile, setConsentFile] = useState(null);

  const sendLog = (callback, requireHkId, log_seq = null, status = null) =>
    takeLog(
      _.get(doc, "search_ref_no") || "",
      consentFile,
      status,
      log_seq,
    ).then(callback);

  const downloadFile = () => {
    const a = document.createElement("a");
    a.href = consentFile;
    a.target = "_blank";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const renderConsentContent = () => (
    <>
      <p>就此次查閱查冊，本人謹此確認：</p>
      <p>
        (i) 無意及不會違反《個人資料(私隱)條例》(第 486 章)
        (《私隱條例》) 的情況下使用透過查閱獲取的個人資料(“該等資料”)；
      </p>
      <p>(ii) 該等資料只能用作查冊目的聲明的用途；及</p>
      <p>
        (iii) 明白本人的個人資料可能就此次查閱查冊會在《私隱條例》許可下被披露或轉交予執法機關。
      </p>
    </>
  );

  return (
    <Grid container className={classes.wrapper} direction="column">
      <Grid item container spacing={2} justify="space-between" wrap="nowrap">
        <Grid item container direction="column" className={classes.doc}>
          <Grid item className={classes.subtext}>
            {moment(_.get(doc, "action_date") || "").format("YYYY-MM-DD")}
          </Grid>
          <Grid item>{_.get(doc, "search_ref_no") || "---"}</Grid>
          {/* <Grid item>跟進者﹕PDRC Man Li</Grid> */}
          <Grid item>
            <FormattedMessage
              id="company.search.applicant"
              values={{
                applicant:
                  `${_.get(doc, "applicantTeamCode") || ""} ${_.get(
                    doc,
                    `applicantName${_.startCase(intl.locale)}`,
                  )}`.trim() || "---",
              }}
            />
          </Grid>
          {/* <Grid item>批核者﹕HKO3 Ceci Yeung</Grid> */}
        </Grid>
        {/* <Grid item style={{ whiteSpace: "nowrap", fontWeight: 600 }}>
          已處理
        </Grid> */}
      </Grid>

      <Grid item container spacing={1} justify="flex-end">
        {doc.list.map((file, index) => (
          <Grid item key={`file_${index}`}>
            <FormButton
              className={classes.fileBtn}
              disabled={false}
              onClick={() => setConsentFile(file)}
            >
              {intl.formatMessage({
                id: "stock.landsearch.file",
              })}{" "}
              {index + 1}
            </FormButton>
          </Grid>
        ))}
      </Grid>

      <SearchConsentDialog
        open={!_.isNull(consentFile)}
        handleClose={() => setConsentFile(null)}
        renderChildren={renderConsentContent}
        okMsg="確認及繼續查閱"
        cancelMsg="取消查閱"
        okCallback={downloadFile}
        sendLog={sendLog}
        isApplyConsent={false}
      />
    </Grid>
  );
}

CompanySearchCard.propTypes = {
  doc: PropTypes.object.isRequired,

  takeLog: PropTypes.func.isRequired,

  intl: PropTypes.object.isRequired,
};

const mapDispatchToProps = (dispatch) => ({
  takeLog: (refNo, docPath, status, logSeq) =>
    dispatch(
      takeLandsearchLog({
        stockId: "",
        refNo,
        docPath,
        location: "OPEN COMPANY SEARCH DOCUMENT",
        status,
        logSeq,
      }),
    ),
});

export default connect(null, mapDispatchToProps)(injectIntl(CompanySearchCard));
