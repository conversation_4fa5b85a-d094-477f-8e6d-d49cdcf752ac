import React from "react";
import PropTypes from "prop-types";
import BgmSelector from "./BgmSelector";

// 为redux-form创建的字段包装器
const FieldBgmSelector = ({ input, label, meta: { touched, error }, ...props }) => {
  const handleChange = (bgm) => {
    input.onChange(bgm.name);
  };

  return (
    <div>
      <BgmSelector
        value={input.value}
        onChange={handleChange}
        label={label}
        {...props}
      />
      {touched && error && <span style={{ color: "red" }}>{error}</span>}
    </div>
  );
};

FieldBgmSelector.propTypes = {
  input: PropTypes.object.isRequired,
  label: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  meta: PropTypes.object.isRequired,
};

export default FieldBgmSelector; 