import React, { useState } from "react";
import { FormattedMessage, injectIntl } from "react-intl";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/core/styles";
import { connect } from "react-redux";
import _ from "lodash";

import DetailBoxSection from "@/components/common/DetailBoxSection";
import ItemCount from "@/components/common/ItemCount";
import CompanySearchCard from "./CompanySearchCard";

const useStyles = makeStyles({
  wrapper: {
    marginTop: 10,
  },
  remarks: {
    lineHeight: 1.2,
    color: "rgba(0,0,0,.6)",
    fontSize: 13,
  },
  noData: {
    fontSize: "1.125em",
    paddingLeft: "2vw",
  },
});

function Search({ searchDocs, intl }) {
  const classes = useStyles();
  const [expanding, setExpanding] = useState(false);

  const docList = searchDocs.filter((doc) => doc.list.length > 0);

  return (
    <DetailBoxSection
      className={classes.wrapper}
      expandable
      isExpanding={expanding}
      callback={(value) => setExpanding(value)}
      text={intl.formatMessage({ id: "company.section.search" })}
    >
      {docList.length > 0 ? (
        <>
          <div className={classes.remarks}>
            <div>根據《地產代理條例》：</div>
            <div>
              同事在處理住宅物業買賣及租賃時必須填寫相關表格文件【表格 1, 3, 4
              (買賣)／表格 2, 5, 6 (租賃)】違者最高可被罰款 $300,000 或 撤銷牌照
            </div>
            <div>
              *
              如狀況為【處理中】並須取消該查冊申請時，請即致電2316-8800與查冊資訊部同事聯絡
              *
            </div>
          </div>
          <ItemCount count={docList.length} messageid="company.search.count" />
          {docList.map((doc) => (
            <CompanySearchCard key={_.get(doc, "file_name")} doc={doc} />
          ))}
        </>
      ) : (
        <div className={classes.noData}>
          <FormattedMessage id="stock.landsearch.null" />
        </div>
      )}
    </DetailBoxSection>
  );
}

Search.propTypes = {
  searchDocs: PropTypes.array.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  searchDocs: state.company.companySearch,
});

export default connect(mapStateToProps)(injectIntl(Search));
