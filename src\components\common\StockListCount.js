import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import { numberComma } from "../../helper/generalHelper";
import { injectIntl, FormattedMessage } from "react-intl";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    padding: "1vw 3vw",
  },
};

function StockListCount(props) {
  const {
    classes,
    stocksCount,
    className,
    queryvariables,
    intl,
    ...other
  } = props;

  let criteriaArr = [];
  if (queryvariables) {
    if (queryvariables.isNew)
      criteriaArr.push(intl.formatMessage({ id: "search.new" }));
    if (queryvariables.isMarketable)
      criteriaArr.push(intl.formatMessage({ id: "search.header.marketable" }));

    if (queryvariables.isMarketableOrNew)
      criteriaArr.push(intl.formatMessage({ id: "search.mktornew" }));
  }

  const count = stocksCount ? numberComma(stocksCount) : 0;
  const resultStr = stocksCount > 1 ? "stocks" : "stock";

  return (
    <div className={`${classes.root} ${className}`} {...other}>
      {count}{" "}
      {criteriaArr.map(function (item, i) {
        return (
          <span key={item}>
            {i !== 0 && ", "} {item}
          </span>
        );
      })}{" "}
      <FormattedMessage id="search.stocklist.count" values={{ resultStr }} />
    </div>
  );
}

StockListCount.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  stocksCount: PropTypes.number,
  queryvariables: PropTypes.object,
};

export default withStyles(styles)(injectIntl(StockListCount));
