import React from "react";
import PropTypes from "prop-types";
import _ from "lodash";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import DetailTabPanelFrame from "../../../../common/DetailTabPanelFrame";
import UpdateHistoryMain from "./UpdateHistoryMain";

const styles = (theme) => ({});

class UpdateHistory extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    listing: PropTypes.bool,
    detail: PropTypes.array,
    currentStock: PropTypes.string,
  };

  constructor(props) {
    super(props);
  }

  render() {
    const {
      classes,
      detail,
      currentStock,
      listed,
      listing,
      handController,
      hand,
      handsMapping,
    } = this.props;

    const stock = _.find(detail, (stock) => stock._id === currentStock) || {};

    const hasData = Object.keys(stock).length > 0;

    return (
      <DetailTabPanelFrame
        hasData={hasData}
        listing={listing}
        listed={listed}
        notFoundText="Stock not found"
      >
        {handController}
        <UpdateHistoryMain detail={stock} hand={hand} handsMapping={handsMapping} />
      </DetailTabPanelFrame>
    );
  }
}

const mapStateToProps = (state) => ({
  detail: state.stock.detail ? state.stock.detail : [],
  currentStock: state.stock.currentDetail ? state.stock.currentDetail : "",
  listed: state.stock.listed ? state.stock.listed : false,
  listing: state.stock.listing ? state.stock.listing : false,
});

export default connect(mapStateToProps)(withStyles(styles)(UpdateHistory));
