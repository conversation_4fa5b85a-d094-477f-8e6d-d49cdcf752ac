import path from "path";
import config from "../config";
import fs from "fs";
import mkdirp from "mkdirp";
import mongodb from "../data/mongodb";
import fetch from "node-fetch";
import FormData from "form-data";
import jo from "jpeg-autorotate";
import _ from "lodash";

function resJson(res, status, error, data) {
  res.json({
    status,
    errors: error ? [{ message: error }] : null,
    data,
  });
  res.end();
}

// function saveFile(req, res, callback) {
//   let file = req.file;
//   let tempPath = file.path;
//   let originalName = file.originalname;
//   let bits = originalName.split(".");
//   let filename = bits.length > 1 ? bits.slice(0, bits.length - 1).join(".") : bits[0];
//   let filenameExtension = bits.length > 1 ? "." + bits[bits.length - 1] : "";
//   let newFilename = Date.now() + "_" + filename.replace(/[^a-z0-9]/gi, '_').toLowerCase() + filenameExtension;
//   let destDir = path.join(__dirname, config.media.destDir);
//   let destPath = destDir + newFilename;
//   mkdirp(destDir, function (err) {
//     if (err) {
//       resJson(res, 300, "Failed create dest folder: " + err);
//       return;
//     }
//     fs.rename(tempPath, destPath, function(err) {
//       if (err) {
//         resJson(res, 300, "Failed to move file from temp to dest: " + err);
//         return;
//       }
//       fs.unlink(tempPath, function() {
//         console.log('File uploaded to ' + destPath);
//         callback(originalName, newFilename);
//       });
//     });
//   });
// }
//
// export default function(req, res, next) {
//   let payload = req.body;
//
//   if (!req.file) {
//     resJson(res, 300, "Missing file object");
//     return;
//   }
//
//   try {
//     payload = JSON.parse(payload.variables);
//   } catch (e) {
//     resJson(res, 300, "Failed to parse params");
//     return;
//   }
//
//   if (!payload.medium || !payload.medium.buildingId) {
//     resJson(res, 300, "Missing params");
//     return;
//   }
//
//   let medium = payload.medium;
//   if (!Number.isInteger(medium.buildingId) || (medium.stockId ? !Number.isInteger(medium.stockId) : false)) {
//     resJson(res, 300, "buildingId and stockId must be int");
//     return;
//   }
//
//   let userInfo = (req.user && req.user.login && req.user.login.info) ? req.user.login.info : {};
//
//   function callback(originalName, newFilename) {
//     let entry =  {
//       "buildingId" : medium.buildingId ? medium.buildingId.toString() : null,
//       "originalFilename" : originalName,
//       "filename" : newFilename,
//       "lastModified" : new Date(),
//       "operator" : {
//         "id" : userInfo.emp_id || null,
//         "branchId" : userInfo.dept_code || null,
//         "eName" : userInfo.short_name || null,
//         "email" : userInfo.email || null,
//       },
//     };
//     if (medium.stockId) entry.stockId = medium.stockId.toString();
//
//     mongodb.db
//       .collection(config.media.collectionName)
//       .insertOne(
//         entry,
//         function(err, data) {
//           if (err) {
//             resJson(res, 300, "Failed to save entry to DB");
//           } else {
//             resJson(res, 200, null, data);
//           }
//         }
//       );
//   }
//   saveFile(req, res, callback);
// }

export function saveMedia(req, res, next, getQuery) {
  let payload = req.body;

  if (!req.files || !req.files?.medium?.[0]?.originalname) {
    resJson(res, 300, "Missing file object");
    return;
  }

  let wrongFormat = false;
  let bits = req.files.medium[0].originalname.split(".");
  if (bits.length < 2) wrongFormat = true;
  let extension = bits[bits.length - 1];
  let acceptableFormat = config.media.acceptableFormat
    .split(",")
    .map((v) => v.trim());
  if (acceptableFormat.indexOf(extension.toLowerCase()) < 0) wrongFormat = true;
  if (wrongFormat) {
    resJson(res, 300, "Only image file is acceptable");
    return;
  }

  try {
    payload = JSON.parse(payload.variables);
  } catch (e) {
    resJson(res, 300, "Failed to parse params");
    return;
  }

  if (!payload.medium) {
    resJson(res, 300, "Missing params");
    return;
  }

  let medium = payload.medium;
  let type = formatToType(extension);
  if (medium.photoContent === 'kol_video') type = 'kol_video';
  if (type) {
    medium.type = type;
  } else {
    resJson(res, 300, "File format is unrecognizable");
    return;
  }
  if (
    (medium.buildingId ? !Number.isInteger(medium.buildingId) : false) ||
    (medium.stockId ? !Number.isInteger(medium.stockId) : false)
  ) {
    resJson(res, 300, "buildingId and stockId must be int");
    return;
  }

  if (medium.buildingId === null) delete medium.buildingId;
  if (medium.stockId === null) delete medium.stockId;

  sentToMedia(req, res, medium, getQuery, payload?.uuid);
}

async function sentToMedia(req, res, medium, getQuery, uuid) {
  let mediumFile = req.files.medium[0];
  let thumbnailFile = req.files?.thumbnail?.[0];
  let allowDuplicateFilename = true;

  // console.log("============= enter sentToMedia ============= ")
  // console.log(file)
  // console.log(medium)
  let mediumBuffer = mediumFile.buffer;
  let mediumCorrected = await correctImage(mediumFile);
  if (mediumCorrected) mediumBuffer = mediumCorrected;

  let thumbnailBuffer = null;
  let thumbnailCorrected = null;
  if (thumbnailFile) {
    thumbnailBuffer = thumbnailFile.buffer;
    thumbnailCorrected = await correctImage(thumbnailFile);
    if (thumbnailCorrected) thumbnailBuffer = thumbnailCorrected;
  }

  let userInfo =
    req.user && req.user.login && req.user.login.info
      ? req.user.login.info
      : {};

  try {
    // the user info stored in req is not enough to fill the employee field. need to get info from employee graphql
    let employee = await getEmployeeInfo(getQuery, userInfo.emp_id);

    medium.operator = {
      id: userInfo.emp_id ?? employee.emp_id ?? null,
      branchId: userInfo.dept_code ?? userInfo.dept ?? employee.dept_code ?? null,
      eName: userInfo.ename ?? employee.name_en ?? null,
      email: userInfo.email ?? employee.email ?? null,
    };

    // id, branchId, cName, cTitle and phone fields are notNull fields in the graphql of media server
    // if any of these 5 fields is missing, dont send employee (employee field can be null)
    // if (
    //   employee &&
    //   employee.emp_id &&
    //   employee.dept_code &&
    //   employee.name_zh &&
    //   employee.title_zh &&
    //   employee.phone
    // ) {
    //   medium.employeeId = employee.emp_id;
    //   medium.employee = {
    //     id: employee.emp_id || null,
    //     branchId: employee.dept_code || null,
    //     cName: employee.name_zh || null,
    //     eName: employee.name_en || null,
    //     cTitle: employee.title_zh || null,
    //     eTitle: employee.title_en || null,
    //     licence: employee.licence || null,
    //     phone: employee.phone || null,
    //     email: employee.email || null,
    //     sex: employee.sex || null,
    //   };
    // }
  } catch (e) {
    console.log(e);
  }

  const payload = new FormData();
  if (mediumFile) payload.append("medium", mediumBuffer, mediumFile.originalname);
  if (thumbnailFile) payload.append("thumbnail", thumbnailBuffer, thumbnailFile.originalname);
  payload.append(
    "query",
    `
    mutation($medium: MediumCreateBypass!, $allowDuplicateFilename: Boolean, $bypassCode: String!, $uuid: String) {
      createMediumBypass(medium: $medium, allowDuplicateFilename: $allowDuplicateFilename, bypassCode: $bypassCode, uuid: $uuid) {
        id
      }
    }
  `,
  );
  payload.append(
    "variables",
    JSON.stringify({
      medium,
      allowDuplicateFilename,
      bypassCode: config.mediaBypassCode,
      uuid: uuid,
    }),
  );

  try {
    // fetch(
    //   config.api.media,
    //   {
    //     method: 'post',
    //     body: payload,
    //     credentials: 'include',
    //   }
    // )
    //   .then(res => res.json())
    //   .then(json => console.log(json))
    //   .catch(e=>{console.log(e)});

    const resp = await fetch(config.api.media, {
      method: "post",
      body: payload,
      // @ts-ignore
      credentials: "include",
    });
    if (resp.status !== 200) throw new Error(resp.statusText);
    const { data, errors } = await resp.json();
    if (errors) throw new Error(errors[0].message);
    if (!data.createMediumBypass)
      throw new Error("No response from GraphQL API");
    if (!data.createMediumBypass.id)
      throw new Error("No ID returned from GraphQL API");

    // triggerJob(medium, data.createMediumBypass.id, file.originalname);

    resJson(res, 200, null, data);
  } catch (e) {
    console.log("e", e);
    resJson(res, 300, e.message);
  }
}

const getEmployeeInfo = async (getQuery, emp_id) => {
  const query = await getQuery("LIST_EMPLOYEES_QUERY");
  const variables = {
    emp_id: [emp_id],
  };
  const body = {
    query,
    variables,
  };

  try {
    const resp = await fetch(config.api.employeeInternal, {
      method: "POST",
      body: JSON.stringify(body),
    });
    const data = await resp.json();

    if (data.data && data.data.employees && data.data.employees[0])
      return data.data.employees[0];
    else return {};
  } catch (e) {
    console.log(e);
    return {};
  }
};

async function correctImage(file) {
  // console.log("============= enter correctImage ============= ");
  // console.log("processing " + file.originalname + " ...");

  const options = { quality: 95 };
  const path = file.buffer;
  try {
    const { buffer, orientation, dimensions, quality } = await jo.rotate(
      path,
      options,
    );

    // console.log(`Orientation was ${orientation}`);
    // console.log(`Dimensions after rotation: ${dimensions.width}x${dimensions.height}`);
    // console.log(`Quality: ${quality}`);

    return buffer;
  } catch (e) {
    console.log("An error occurred when rotating the file: " + e.message);
    return false;
  }
}

async function triggerJob(medium, filename, originalname) {
  let params = {
    sbu: "COMM",
    fileName: filename,
    buildingID: medium.buildingId,
    stockID: medium.stockId,
    staffID: medium.operator.id,
    originalFileName: originalname,
  };

  console.log("Send data to justin:");
  console.log(params);

  try {
    const resp = await fetch(config.api.s3MediaToLocal, {
      method: "post",
      body: JSON.stringify(params),
      // credentials: 'include',
    });
    if (resp.status !== 200) throw new Error(resp.statusText);
    const data = await resp.json();
    console.log("Receive data from justin:");
    console.log(data);
  } catch (e) {
    console.log(
      "Error occurs when trigger justin's job: (originalname=" +
        originalname +
        ")",
    );
    console.log(e);
    // TODO: log to somewhere else
  }
}

// remove medium
export async function removeMedia(req, res, next, getQuery) {
  const userInfo =
    req.user && req.user.login && req.user.login.info
      ? req.user.login.info
      : {};

  try {
    // the user info stored in req is not enough to fill the employee field. need to get info from employee graphql
    let employee = await getEmployeeInfo(getQuery, userInfo.emp_id);

    const payload = new FormData();
    payload.append(
      "query",
      `mutation ($id: ID!, $bypassCode: String!, $operator: OperatorInput!) {
        removeMediumBypass(id: $id, bypassCode: $bypassCode, operator: $operator) {
          id
        }
      }`,
    );
    payload.append(
      "variables",
      JSON.stringify({
        id: req.body.id,
        bypassCode: config.mediaBypassCode,
        operator: {
          id: userInfo.emp_id ?? employee.emp_id ?? null,
          branchId: userInfo.dept_code ?? userInfo.dept ?? employee.dept_code ?? null,
          eName: userInfo.ename ?? employee.name_en ?? null,
          email: userInfo.email ?? employee.email ?? null,
        },
      }),
    );

    const resp = await fetch(config.api.media, {
      method: "post",
      body: payload,
      // @ts-ignore
      credentials: "include",
    });

    if (resp.status !== 200) throw new Error(resp.statusText);
    const { data, errors } = await resp.json();

    console.log(data);

    if (errors) throw new Error(errors[0].message);
    if (!data.removeMediumBypass)
      throw new Error("No response from GraphQL API");
    if (!data.removeMediumBypass.id)
      throw new Error("No ID returned from GraphQL API");

    resJson(res, 200, null, data);
  } catch (e) {
    console.log(e);
    resJson(res, 300, e.message);
  }
}

// update medium
export async function updateMedia(req, res, next, getQuery) {
  const medium = { ...req.body };
  const userInfo =
    req.user && req.user.login && req.user.login.info
      ? req.user.login.info
      : {};

  try {
    // the user info stored in req is not enough to fill the employee field. need to get info from employee graphql
    let employee = await getEmployeeInfo(getQuery, userInfo.emp_id);
    medium.operator = {
      id: userInfo.emp_id ?? employee.emp_id ?? null,
      branchId: userInfo.dept_code ?? userInfo.dept ?? employee.dept_code ?? null,
      eName: userInfo.ename ?? employee.name_en ?? null,
      email: userInfo.email ?? employee.email ?? null,
    };

    const payload = new FormData();
    payload.append(
      "query",
      `mutation($medium: MediumUpdateBypass!, $bypassCode: String!) {
        updateMediumBypass(medium: $medium, bypassCode: $bypassCode) {
          id
        }
      }`,
    );
    payload.append(
      "variables",
      JSON.stringify({
        medium,
        bypassCode: config.mediaBypassCode,
      }),
    );

    const resp = await fetch(config.api.media, {
      method: "post",
      body: payload,
      // @ts-ignore
      credentials: "include",
    });

    if (resp.status !== 200) throw new Error(await resp.text());
    if (resp.status !== 200) throw new Error(resp.statusText);
    const { data, errors } = await resp.json();

    if (errors) throw new Error(errors[0].message);
    if (!data.updateMediumBypass)
      throw new Error("No response from GraphQL API");
    if (!data.updateMediumBypass.id)
      throw new Error("No ID returned from GraphQL API");

    resJson(res, 200, null, data);
  } catch (e) {
    console.log(e);
    resJson(res, 300, e.message);
  }
}

function formatToType(format) {
  if (typeof format !== "string") return null;

  const photoType = "photo";
  const videoType = "video";
  const documentType = "document";
  const mapping = {
    mov: videoType,
    mp4: videoType,
    jpg: photoType,
    png: photoType,
    jpeg: photoType,
    pdf: documentType,
  };
  return mapping[format.toLowerCase()] || null;
}
