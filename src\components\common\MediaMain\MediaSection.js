import React from "react";
import PropTypes from "prop-types";
import { Field, FieldArray } from "redux-form";
import { createStyles, withStyles } from "@material-ui/styles";
import Lightbox from "./Lightbox";
import DetailBoxSection from "../DetailBoxSection";
import { injectIntl } from "react-intl";
import Media from "./Media";
import SelectFieldArrayOutput from "../SelectFieldArrayOutput";
import { getMediaTypeOptions } from "./selectOptions";
import clsx from "clsx";

const styles = createStyles({
  mediaTypeSelect: {
    marginTop: -8,
  },
  subTitleClass: {
    fontSize: "0.75em",
  },
});

class MediaSection extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    media: PropTypes.array,
    mediaType: PropTypes.string,
    prefix: PropTypes.string,
    deletable: PropTypes.bool,
    handleOpenDeleteMediaDialog: PropTypes.func,
    titleClass: PropTypes.string,
    numClass: PropTypes.string,
    isProposal: PropTypes.bool,
    proposalFieldName: PropTypes.string,
    hideTypeSelect: PropTypes.bool,
    /** AWS-4305 Separate sections for document in media (Mobile) */
    isGroup: PropTypes.bool,
    subType: PropTypes.oneOf(['document', 'kol_video', 'video', 'photo']),
    viewType: PropTypes.oneOf(['grid', 'list']),
    renderCustomButton: PropTypes.func,
  };

  constructor(props) {
    super(props);
    this.state = {
      mediaId: 0,
      lightboxOpen: false,
      type: this.props.typeFilter,
    };
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.typeFilter !== this.props.typeFilter) {
      this.setState({ type: this.props.typeFilter });
    }
    if (!prevProps.subType && prevState.type !== this.state.type) {
      if (this.props.handleTypeFilterChange) {
        this.props.handleTypeFilterChange(this.state.type);
      }
    }
  }

  handleCloseLightbox = () => {
    this.setState({ lightboxOpen: false });
  };

  setMediaId = (id) => {
    this.setState({ mediaId: id });
  };

  openLightboxAndSetMediaId = (id) => {
    this.setState({ lightboxOpen: true, mediaId: id });
  };

  handleTypeChange = (value) => {
    this.setState({ type: value });
  };

  changeExpand = (expandType = false) => {
    this.props.expandChange && this.props.expandChange(this.props.mediaType, expandType)
  }

  filterMedia = (media) => {
    const typeFilter = this.state.type;
    const subType = this.props.subType;
    const isVrVideo = ['video', 'embedded_script'].includes(subType)
    if (isVrVideo) {
      return media.filter((v) => ['video', 'embedded_script'].includes(v.type) && (!typeFilter || v.type === typeFilter));
    }
    if (subType) {
      return media.filter((v) => v.type === subType && (!typeFilter || v.type === typeFilter));
    }
    if (!typeFilter) return media;

    return media.filter(
      (v) => v.type === "photo" && v.photoContent === typeFilter,
    );
  };

  render() {
    const { mediaId, lightboxOpen, type } = this.state;
    const {
      classes,
      media = [],
      mediaType,
      prefix,
      deletable,
      handleOpenDeleteMediaDialog,
      titleClass,
      numClass,
      isProposal,
      mediaPath,
      proposalFieldName,
      hideTypeSelect,
      intl,
      defaultExpand = false,
      viewType = 'grid',
      renderCustomButton,
      isListProposal
    } = this.props;

    let sectionTitle =
      [mediaType === "map", mediaType === "govMap"].includes(true)
        ? intl.formatMessage({ id: "stock.map" })
        : prefix + intl.formatMessage({ id: "stock.media" });
    if (this.props.subType) {
      const mediaTypeMapping = Object.freeze({
        video: "vrVideo",
        kol_video: "kol",
      });
      const mediaType = mediaTypeMapping[this.props.subType] || this.props.subType;
      sectionTitle = intl.formatMessage({ id: `media.${mediaType}` });
    }

    const mediaTypeOptions = getMediaTypeOptions(intl);

    let filteredMedia = this.filterMedia(media);

    return (
      <>
        <DetailBoxSection
          text={sectionTitle}
          num={this.props.subType ? null : media.length}
          expandable={true}
          titleClass={clsx(titleClass, this.props.subType && classes.subTitleClass)}
          numClass={numClass}
          isExpanding={defaultExpand}
          handleTypeChange={this.props.subType ? null : this.handleTypeChange}
          callback={this.changeExpand}
        >
          {!hideTypeSelect && media.length > 0 && (
            <SelectFieldArrayOutput
              label={intl.formatMessage({
                id: "proposal.form.type",
              })}
              className={classes.mediaTypeSelect}
              ranges={mediaTypeOptions}
              input={{
                value: type,
                onChange: this.handleTypeChange,
                onBlur: () => {},
              }}
              meta={{}}
              isArrayOutput={false}
              isClearable={false}
            />
          )}

          {isProposal ? (
            // @ts-ignore
            !this.props.isGroup && <FieldArray
              name={`${mediaPath}${proposalFieldName}`}
              // @ts-ignore
              component={Media}
              media={filteredMedia}
              openLightboxAndSetMediaId={this.openLightboxAndSetMediaId}
              isProposal={isProposal}
              mediaPath={mediaPath}
              deletable={deletable}
              handleOpenDeleteMediaDialog={handleOpenDeleteMediaDialog}
              isListProposal={isListProposal}
              viewType={viewType}
              renderCustomButton={renderCustomButton}
            />
          ) : (
            !this.props.isGroup && <Media
              media={filteredMedia}
              openLightboxAndSetMediaId={this.openLightboxAndSetMediaId}
              deletable={deletable}
              handleOpenDeleteMediaDialog={handleOpenDeleteMediaDialog}
              isListProposal={isListProposal}
              viewType={viewType}
              renderCustomButton={renderCustomButton}
            />
          )}
        </DetailBoxSection>

        {filteredMedia && filteredMedia.length > 0 && lightboxOpen && (
          <Lightbox
            media={filteredMedia}
            mediaId={mediaId}
            deletable={deletable}
            mediaType={mediaType}
            isProposal={isProposal}
            isListProposal={isListProposal}
            handleOpenDeleteMediaDialog={handleOpenDeleteMediaDialog}
            handleCloseLightbox={this.handleCloseLightbox}
            setMediaId={this.setMediaId}
            renderCustomButton={renderCustomButton}
          />
        )}
      </>
    );
  }
}

export default withStyles(styles)(injectIntl(MediaSection));
