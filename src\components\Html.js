/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import serialize from "serialize-javascript";
import config from "../config";

/* eslint-disable react/no-danger */

class Html extends React.Component {
  static propTypes = {
    title: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    styles: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.string.isRequired,
        cssText: PropTypes.string.isRequired
      }).isRequired
    ),
    scripts: PropTypes.arrayOf(PropTypes.string.isRequired),
    app: PropTypes.object, // eslint-disable-line
    children: PropTypes.string.isRequired
  };

  static defaultProps = {
    styles: [],
    scripts: []
  };

  render() {
    const {
      title,
      description,
      styles,
      scripts,
      app,
      children,
      css
    } = this.props;
    const emp_id =
      typeof app.state.auth.user !== "undefined" && app.state.auth.user
        ? app.state.auth.user.login.info.emp_id
        : "user";
    return (
      <html className="no-js" lang="en">
        <head>
          <meta charSet="utf-8" />
          <meta httpEquiv="x-ua-compatible" content="ie=edge" />
          <meta name="google" content="notranslate" />
          <meta name="apple-mobile-web-app-capable" content="yes" />
          <title>{title}</title>
          <meta name="description" content={description} />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          {scripts.map(script => (
            <link key={script} rel="preload" href={script} as="script" />
          ))}
          <link rel="manifest" href="/site.webmanifest" />
          <link rel="apple-touch-icon" href="/icon.png" />
          {styles.map(style => (
            <style
              key={style.id}
              id={style.id}
              dangerouslySetInnerHTML={{ __html: style.cssText }}
            />
          ))}
          <style id="jss-server-side">${css}</style>
        </head>
        <body>
          {config.analytics.googleTrackingId && (
            <script
              dangerouslySetInnerHTML={{
                __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({"gtm.start":new Date().getTime(),event:"gtm.js"});var f=d.getElementsByTagName(s)[0], j=d.createElement(s),dl=l!="dataLayer"?"&l="+l:"";j.async=true;j.src="https://www.googletagmanager.com/gtm.js?id="+i+dl;f.parentNode.insertBefore(j,f);})(window,document,"script","dataLayer",'${config.analytics.gtagMangerId}');`
              }}
            />
          )}

          {config.analytics.googleTrackingId && (
            <noscript
              dangerouslySetInnerHTML={{
                __html: `<iframe src="https://www.googletagmanager.com/ns.html?id='${config.analytics.gtagMangerId}'"height="0"width="0"style="display:none;visibility:hidden"></iframe>`
              }}
            />
          )}

          <div id="app" dangerouslySetInnerHTML={{ __html: children }} />
          <script
            dangerouslySetInnerHTML={{ __html: `window.App=${serialize(app)}` }}
          />

          {scripts.map(script => (
            <script key={script} src={script} />
          ))}
          {config.analytics.googleTrackingId && (
            <script
              dangerouslySetInnerHTML={{
                __html:
                  "window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}" +
                  `gtag('js', new Date()); gtag('config', '${config.analytics.googleTrackingId}'); gtag('set', {'user_id': '${emp_id}'}); `
              }}
            />
          )}
          {config.analytics.googleTrackingId && (
            <script
              src={
                "https://www.googletagmanager.com/gtag/js?id=" +
                config.analytics.googleTrackingId
              }
              async
              defer
            />
          )}
        </body>
      </html>
    );
  }
}

export default Html;
