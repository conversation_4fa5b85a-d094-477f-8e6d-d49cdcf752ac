import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import moment from "moment";
import { withStyles, createStyles } from "@material-ui/core/styles";
import Grid from "@material-ui/core/Grid";
import clsx from "clsx";
import { injectIntl } from "react-intl";
import _ from "lodash";

import UnlockDialogItem from "../../../common/UnlockDialogItem";
import DetailBoxTitle from "../../../common/DetailBoxTitle";
import DetailBoxSection from "../../../common/DetailBoxSection";
import VaryingVal from "../../../common/VaryingVal";
import GridSection from "../../../common/GridSection";
import { unlockStock } from "../../../../actions/stocklist";
import PropertyTagBar from "../../../common/PropertyTagBar";
import FavoriteButton from "../../../common/FavoriteButton";
import SearchCardTenantBar from "../../../common/SearchCardTenantBar";
import {
  numberComma,
  convertCurrency,
  paresFloorUnit,
} from "../../../../helper/generalHelper";
import history from "../../../../core/history";
import {
  enableConsolidLandSearch,
  permissions,
  generalDailyQuota,
  enableWWWScore,
} from "../../../../config";
import MarkStockButton from "../../../common/MarkStockButton";
import WWWScoreIcon from "@/components/common/WWWStockScoreIcon";
import WWWStockTag from "@/components/common/WWWStockTag";

const styles = createStyles((theme) => ({
  unlockedroot: {
    padding: "1vw 2vw",
    backgroundColor: "rgba(255, 255, 200, .6)",
    borderTop: "0px solid #fff",
    boxShadow: "none",
  },
  root: {
    padding: "1vw 2vw",
    backgroundColor: "rgba(255, 255, 255, .6)",
    borderTop: "0px solid #fff",
    boxShadow: "none",
  },
  priceRow: {
    display: "flex",
    justifyContent: "space-between",
    textAlign: "right",
    padding: "1vh 0 0.5vh 0",
  },
  commonItem: {
    display: "flex",
    fontSize: "1.5em",
    padding: "0 4px",
    borderRadius: 4,
    color: "#fff",
  },
  rentItem: {
    backgroundColor: "rgba(0, 197, 197, .75)",
  },
  priceItem: {
    backgroundColor: "rgba(232, 0, 0, .75)",
  },
  greyItem: {
    backgroundColor: "rgba(132, 132, 132, .1)",
  },
  rentItemNoValue: {
    backgroundColor: "rgba(140, 190, 190, 0.2)",
  },
  priceItemNoValue: {
    backgroundColor: "rgba(200, 170, 170, 0.2)",
  },
  fieldRow: {
    display: "flex",
    justifyContent: "space-between",
    marginTop: "-1vh",
  },
  fieldItem: {
    fontSize: "1.175em",
  },
  districtCode: {
    fontSize: "0.8em",
    fontWeight: 700,
    alignSelf: "center",
  },
  status: {
    fontWeight: 700,
    display: "inline-flex",
  },
  link: {
    textDecoration: "none",
    color: "#000",
  },
  DetailBoxSectionContent: {
    padding: 0,
  },
  totalItem: {
    display: "block",
    width: "100%",
    fontSize: "0.75em",
  },
  tagContainer: {
    padding: "0",
  },
  myfavoriteIcon: {
    padding: "0 1vw",
  },
}));

class SearchResultCard extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    result: PropTypes.object,
    count: PropTypes.number,
    max: PropTypes.number,
    unlockStock: PropTypes.func.isRequired,
  };
  constructor() {
    super();
    this.state = {
      dialogOpen: false,
      dialogOpenPurpose: "",
    };
  }

  handleClickOpenDialog = (purpose) => {
    this.setState({ dialogOpen: true, dialogOpenPurpose: purpose });
  };

  handleCloseDialog = () => {
    this.setState({ dialogOpen: false });
  };

  goStockDetail = () => {
    const id = [this.props.result._id];
    history.push("/stock?mode=indv&ids=" + encodeURIComponent(JSON.stringify(id)));
  };

  render() {
    const {
      classes,
      result,
      isUnlocked,
      favoriteStockIds,
      markStockIds,
      intl,
      uid,
    } = this.props;
    const statusLangKey = intl.locale === "zh" ? "statusZh" : "status";
    const currentTenantLangKey =
      intl.locale === "zh" ? "tenancyCurrentTenantZh" : "tenancyCurrentTenant";

    const id = result._id ? result._id : "---";
    const unicornid =
      result.unicorn && result.unicorn.stock ? result.unicorn.stock : "---";
    const building = result.building ? result.building : null;
    const buildingnameEn =
      building && building.nameEn ? building.nameEn : "---";
    const buildingnameZh =
      building && building.nameZh ? building.nameZh : "---";
    const districtabbr =
      result.district && result.district.abbr ? result.district.abbr : "---";
    const districtZh =
      result.district && result.district.nameZh
        ? result.district.nameZh
        : "---";
    const buildingRowEn = `${buildingnameEn}, ${districtabbr}`;
    const buildingRowZh = `${buildingnameZh}, ${districtZh}`;
    const titleUp = intl.locale === "zh" ? buildingRowZh : buildingRowEn;
    const titleDown = intl.locale === "zh" ? buildingRowEn : buildingRowZh;

    const status =
      result.status && result.status[statusLangKey]
        ? result.status[statusLangKey]
        : "---";
    let statusDisplay = result[statusLangKey] ? result[statusLangKey] : "---";

    const isOnline = _.get(result, 'isOnline');
    const wwwScoreTotal = _.get(result, 'wwwScoreTotal');
    const wwwChannelFull = _.get(result, 'wwwChannelFull', {});
    const wwwAgentInfos = _.get(result, 'wwwAgentInfos', []);
    const currentAgent = _.find(wwwAgentInfos, { wwwBy: uid });

    let size = result.searchArea ? numberComma(result.searchArea) : null;
    size = size
      ? intl.locale == "zh"
        ? (size = intl.formatMessage({ id: `stock.area.${result.searchAreaType.toLowerCase()}` }) + " " + size)
        : (size = size + " " + intl.formatMessage({ id: `stock.area.${result.searchAreaType.toLowerCase()}` }))
      : "---";

    const currentTenant = result[currentTenantLangKey]
      ? result[currentTenantLangKey]
      : "---";
    const tenancyExpireDate = result.tenancyExpireDate
      ? moment(result.tenancyExpireDate, "x").format("YYYY-MM-DD")
      : "---";

    let askingRent = result.askingRent ? result.askingRent.average : 0;
    let askingTotalRent = result.askingRent ? result.askingRent.total + " " : 0;
    const askingRentType =
      result.askingRent && result.askingRent.trend
        ? result.askingRent.trend
        : null;
    let askingPrice = result.askingPrice ? result.askingPrice.average + " " : 0;
    let askingTotalPrice = result.askingPrice
      ? result.askingPrice.total + " "
      : 0;
    const askingPriceType =
      result.askingPrice && result.askingPrice.trend
        ? result.askingPrice.trend
        : null;

    const floor =
      result.floor && result.floor.input ? result.floor.input : "---";
    const unit = result.unit ? result.unit : "---";

    let priceContainer, leaseContainer;
    switch (status) {
      case "S": //出售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        askingRent = 0;
        askingTotalRent = 0;
        break;
      case "L": //出租
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "SL": //連約售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        askingRent = 0;
        askingTotalRent = 0;
        break;
      case "S+L": //租及售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Pending": //封盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "TEL": //電話盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "SE": //查冊盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Don't Call": //拒致電
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Sold": //已售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Leased": //已租
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Fail": //錯誤盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "History": //舊資料
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Cancel": //錯盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      default:
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
    }

    const tagTerrace = !!result.haveTerrace;
    const tagRoof = !!result.haveRoof;
    const tagCockloft = !!result.haveCockloft;
    const tagsOverride = {
      [intl.formatMessage({ id: "stock.tag.terrace" })]: tagTerrace,
      [intl.formatMessage({ id: "stock.tag.roof" })]: tagRoof,
      [intl.formatMessage({ id: "stock.tag.cockloft" })]: tagCockloft,
    };

    return (
      <div>
        <div
          onClick={isUnlocked ? this.goStockDetail : this.handleClickOpenDialog}
          className={classes.link}
        >
          <GridSection
            className={isUnlocked ? classes.unlockedroot : classes.root}
          >
            <DetailBoxTitle
              subtitle={<span>{paresFloorUnit(floor, unit, intl)}</span>}
              rightIcon={
                <div className={classes.status}>
                  {enableWWWScore && <WWWScoreIcon
                    isOnline={isOnline}
                    score={wwwScoreTotal}
                    isCurrentAgent={currentAgent}
                  />}
                  <FavoriteButton
                    favoriteStockIds={favoriteStockIds}
                    mongoid={id}
                    stockid={unicornid}
                    className={classes.myfavoriteIcon}
                  />
                  <MarkStockButton
                    checked={markStockIds.includes(id)}
                    stockId={id}
                  />
                </div>
              }
            >
              <DetailBoxTitle
                subtitle={titleUp}
                rightIcon={<span className={classes.fieldItem}>{statusDisplay}</span>}
              />

              <DetailBoxTitle
                subtitle={titleDown}
                rightIcon={<span className={classes.fieldItem}>{size}</span>}
              />

              {enableConsolidLandSearch == "true" && (
                <SearchCardTenantBar date={tenancyExpireDate}>
                  {currentTenant}
                </SearchCardTenantBar>
              )}

              <DetailBoxSection
                contentClass={classes.DetailBoxSectionContent}
                noStrike={true}
              >
                <Grid container spacing={1} className={classes.priceRow}>
                  <Grid item xs={6}>
                    <VaryingVal
                      className={clsx(
                        classes.commonItem,
                        priceContainer,
                        askingPrice <= 0 && classes.priceItemNoValue,
                      )}
                      type={askingPriceType}
                    >
                      <span>
                        {askingPrice > 0
                          ? `@${numberComma(askingPrice, 0)}`
                          : "---"}
                        {askingPrice != 0 && askingTotalPrice != 0 ? (
                          <span className={classes.totalItem}>
                            ${convertCurrency(askingTotalPrice, undefined, { decimals: 3 })}
                          </span>
                        ) : (
                          <span className={classes.totalItem}>---</span>
                        )}
                      </span>
                    </VaryingVal>
                  </Grid>

                  <Grid item xs={6}>
                    <VaryingVal
                      className={clsx(
                        classes.commonItem,
                        leaseContainer,
                        askingRent <= 0 && classes.rentItemNoValue,
                      )}
                      type={askingRentType}
                      // label={"Rent"}
                    >
                      <span>
                        {askingRent > 0 ? `@${numberComma(askingRent, 2)}` : "---"}
                        {askingRent != 0 && askingTotalRent != 0 ? (
                          <span className={classes.totalItem}>
                            ${convertCurrency(askingTotalRent, undefined, { decimals: 3 })}
                          </span>
                        ) : (
                          <span className={classes.totalItem}>---</span>
                        )}
                      </span>
                    </VaryingVal>
                  </Grid>
                </Grid>

                {/* <div className={classes.priceRow}>
                  <div>
                    {askingPrice > 0 ? (
                      <VaryingVal
                        className={clsx(classes.commonItem, classes.priceItem)}
                        type={askingPriceType}
                        // label={"Price"}
                      >
                        <div>
                          <span>@{numberComma(askingPrice)}</span>
                          {askingTotalPrice != 0 && (
                            <span className={classes.totalItem}>
                              ${convertCurrency(askingTotalPrice)}
                            </span>
                          )}
                        </div>
                      </VaryingVal>
                    ) : (
                      <div></div>
                    )}
                  </div>

                  <div>
                    {askingRent > 0 ? (
                      <VaryingVal
                        className={clsx(classes.commonItem, classes.rentItem)}
                        type={askingRentType}
                        // label={"Rent"}
                      >
                        <div>
                          <span>@{numberComma(askingRent)}</span>
                          {askingTotalRent != 0 && (
                            <span className={classes.totalItem}>
                              ${convertCurrency(askingTotalRent)}
                            </span>
                          )}
                        </div>
                      </VaryingVal>
                    ) : (
                      <div></div>
                    )}
                  </div>
                </div> */}
                <div className={classes.tagContainer}>
                  <PropertyTagBar detail={result} tagsOverride={tagsOverride}/>
                </div>

                {enableWWWScore && <div className={classes.tagContainer}>
                  <WWWStockTag
                    uid={uid}
                    agents={wwwAgentInfos}
                    wwwChannelFull={wwwChannelFull}
                    mode="StockSearchPage"
                  />
                </div>}

              </DetailBoxSection>
            </DetailBoxTitle>
          </GridSection>
        </div>

        <UnlockDialogItem
          dialogOpen={this.state.dialogOpen}
          handleCloseDialog={this.handleCloseDialog}
          stockid={id}
          stockunicornid={unicornid}
        />
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  uid: state.auth.user.login.info.emp_id,
  count: state.stocklist.count ? state.stocklist.count : 0,
  max: !_.isEmpty(state.employee.permissions)
    ? !_.isNil(
        state.employee.permissions[permissions.PERMISSION_VIEW_STOCK_QUOTA],
      )
      ? state.employee.permissions[permissions.PERMISSION_VIEW_STOCK_QUOTA]
      : 0
    : generalDailyQuota,
  unlockfinished: state.stocklist.unlockfinished
    ? state.stocklist.unlockfinished
    : false,
  favoriteStockIds: state.stock.favoriteStockIds
    ? state.stock.favoriteStockIds
    : [],
  markStockIds: _.get(state, "stock.markStockIds") || [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    unlockStock: (id) => dispatch(unlockStock(id)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(SearchResultCard)));
