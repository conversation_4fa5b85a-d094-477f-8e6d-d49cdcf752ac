import React from "react";
import { Field } from "redux-form";
import PropTypes from "prop-types";
import { Grid } from "@material-ui/core";
import _ from "lodash";
import { injectIntl } from "react-intl";
import { sbu } from "../../config";

import PillCheckBox from "./PillCheckBox";

const CONFIG_FEE = [
  "managementFee",
  "gRent",
  "rates",
  ...(sbu === "COMM" ? ["acFee"] : [])
]

function InclusiveFeeGroup({
  includedFee,
  allInclusive,
  isMgtFeeOpenAC,
  changeFieldValue,
  fieldNamePrefix,
  intl,
}) {
  const handleChange = (field) => (e) => {
    changeFieldValue(`${fieldNamePrefix}${field}`, e.target.checked);
    if (sbu !== 'COMM' && field === "stock.allInclusive") {
      CONFIG_FEE.forEach(v => {
        changeFieldValue(`${fieldNamePrefix}stock.${v}[0].paidBy`, e.target.checked ? "Paid By Landlord" : "Paid By Tenant");
        changeFieldValue(`${fieldNamePrefix}stock.includedFee[0].${v}`, e.target.checked);
      });
    }
    if (sbu === 'COMM' && field === "stock.allInclusive") {
      CONFIG_FEE.forEach(v => {
        // changeFieldValue(`${fieldNamePrefix}stock.${v}[0].isShow`, e.target.checked);
        changeFieldValue(`${fieldNamePrefix}stock.includedFee[0].${v}`, e.target.checked);
      });
    }
  };

  return (
    <Grid style={{ padding: 10 }} container spacing={1}>
      {/* {sbu !== 'IND' && (
        <>
          <Grid item xs={6}>
            <Field
              name={`${fieldNamePrefix}stock.includedFee[0].managementFee`}
              component={PillCheckBox}
              input={{
                value: allInclusive || _.get(includedFee, "managementFee"),
                onChange: handleChange("stock.includedFee[0].managementFee"),
              }}
              text={intl.formatMessage({ id: "proposal.form.includedMgtFee" })} />
          </Grid><Grid item xs={6}>
            <Field
              name={`${fieldNamePrefix}stock.includedFee[0].rates`}
              component={PillCheckBox}
              input={{
                value: allInclusive || _.get(includedFee, "rates"),
                onChange: handleChange("stock.includedFee[0].rates"),
              }}
              text={intl.formatMessage({ id: "proposal.form.includedRates" })} />
          </Grid><Grid item xs={6}>
            <Field
              name={`${fieldNamePrefix}stock.includedFee[0].gRent`}
              component={PillCheckBox}
              input={{
                value: allInclusive || _.get(includedFee, "gRent"),
                onChange: handleChange("stock.includedFee[0].gRent"),
              }}
              text={intl.formatMessage({ id: "proposal.form.includedGRent" })} />
          </Grid><Grid item xs={6}>
            <Field
              name={`${fieldNamePrefix}stock.includedFee[0].acFee`}
              component={PillCheckBox}
              input={{
                value: allInclusive || _.get(includedFee, "acFee"),
                onChange: handleChange("stock.includedFee[0].acFee"),
              }}
              text={intl.formatMessage({ id: "proposal.form.includedAcFee" })} />
          </Grid>
        </>
      )
      } */}
      <Grid item xs={12}>
        <Field
          name={`${fieldNamePrefix}stock.allInclusive`}
          component={PillCheckBox}
          input={{
            value: allInclusive,
            onChange: handleChange("stock.allInclusive"),
          }}
          text={intl.formatMessage({ id: "proposal.form.allInclusive" })}
        />
      </Grid>
      {/* {sbu === 'COMM' && (
        <Grid item xs={12}>
          <Field
            name={`${fieldNamePrefix}stock.isMgtFeeOpenAC`}
            component={PillCheckBox}
            input={{
              value: isMgtFeeOpenAC,
              onChange: handleChange("stock.isMgtFeeOpenAC"),
            }}
            text={intl.formatMessage({ id: "proposal.form.mgtFeeOpenAC" })}
          />
        </Grid>
      )} */}
    </Grid>
  );
}
InclusiveFeeGroup.defaultProps = {
  fieldNamePrefix: "",
};

InclusiveFeeGroup.propTypes = {
  includedFee: PropTypes.shape({
    managementFee: PropTypes.bool,
    rates: PropTypes.bool,
    gRent: PropTypes.bool,
    acFee: PropTypes.bool,
  }).isRequired,
  allInclusive: PropTypes.bool.isRequired,
  isMgtFeeOpenAC: PropTypes.bool,
  fieldNamePrefix: PropTypes.string,
  changeFieldValue: PropTypes.func.isRequired,
  intl: PropTypes.object.isRequired,
};

export default injectIntl(InclusiveFeeGroup);
