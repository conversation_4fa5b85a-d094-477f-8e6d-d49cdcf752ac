import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import CircularProgress from "@material-ui/core/CircularProgress";


const styles = theme => ({
  root: {
    fontSize: "1.125em",
    textAlign: "center",
    padding: "7vh 0",
  },
  circularLoader: {
    textAlign: "center",
    "& > div": {
      color: "#FFF"
    }
  },
});

class StepThree extends React.Component {
  render() {
    const { classes } = this.props;

    return (
      <div className={classes.root}>
        <div className={classes.circularLoader} >
          <CircularProgress size="30vw" />
        </div>
      </div>
    );
  }
}

export default withStyles(styles)(StepThree);
