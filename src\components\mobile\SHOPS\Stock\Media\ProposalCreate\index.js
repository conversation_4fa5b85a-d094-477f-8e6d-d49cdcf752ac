/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React, { useState, useEffect } from "react";
import { withStyles } from "@material-ui/core/styles";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { submit, change } from "redux-form";
import { injectIntl } from "react-intl";
import TextField from "@material-ui/core/TextField";
import _ from "lodash";
import Checkbox from "@material-ui/core/Checkbox";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import FormGroup from "@material-ui/core/FormGroup";

import DialogFrame from "../../../../../common/DialogFrame";
import Dialog from "../../../../../common/Dialog";
import ProposalForm from "../../../Proposal/ProposalForm";
import {
  createProposal,
  clearCreateProposal,
} from "../../../../../../actions/proposal";
import {
  possessionLangIdMapping,
  getTypeOptions,
  getDefaultRemarks,
} from "../../../Proposal/FormSection/selectOptions";
import langFile from "../../../../../../lang/SHOPS/messages";
import SubmitDialog from "../../../../../common/SubmitDialog";
import {
  getLangKey,
  goToProposalList,
  getProposalTenancyDesc,
  parsePeriod,
} from "../../../../../../helper/generalHelper";
import { sbu, generalProposalQuota } from "../../../../../../config";
import history from "../../../../../../core/history";
import {
  clearCreateListProposal,
  createListProposal,
} from "../../../../../../actions/listProposal";
import SelectField from "../../../../../common/SelectField";

const CustomCheckbox = withStyles({
  root: {
    color: "white",
    "&$checked": {
      color: "#13CE66",
    },
  },
  checked: {},
})((props) => <Checkbox {...props} />);

function ProposalCreate({
  stockData,
  currentStock,
  media,
  buildingMedia,
  streetMedia,
  userInfo,
  createProposal,
  creatingProposal,
  createdProposal,
  createProposalError,
  clearCreateProposal,
  dispatchSubmitForm,
  createProposalDialogOpen,
  handleCloseCreateProposalDialog,
  changeFieldValue,
  form,
  exceedQuota,
  createListProposal,
  creatingListProposal,
  createdListProposal,
  createListProposalError,
  clearCreateListProposal,
  intl,
}) {
  const [proposalName, setProposalName] = useState("");
  const [listProposalType, setListProposalType] = useState("Sale");
  const [hideContact, setHideContact] = useState(false);
  const [hideEmployeePhoto, setHideEmployeePhoto] = useState(false);

  const stock = stockData[currentStock];
  const isListProposal = history.location.pathname === "/listProposal";

  useEffect(() => {
    setProposalName(parseDefaultProposalName());
  }, []);

  const parseDefaultProposalName = () => {
    const langKey = getLangKey(intl);
    const streetName = stock?.street?.street?.[langKey] || "";
    const streetNo = stock?.street?.number || "";
    return intl.locale === "zh"
      ? streetName + streetNo + "號"
      : streetNo + " " + streetName;
  };

  const handleCloseSubmitDialog = () => {
    handleCloseCreateProposalDialog();
    setProposalName(parseDefaultProposalName());
    setHideEmployeePhoto(false);
    setHideContact(false);
    setListProposalType("Sale");
    isListProposal ? clearCreateListProposal() : clearCreateProposal();
  };

  const submitDialogCallback = () => {
    goToProposalList();
  };

  const handleNameChange = (e) => {
    setProposalName(e.target.value);
  };

  const listProposalFields = [
    "unit",
    "floor",
    "floorType",
    "avgPrice",
    "totalPrice",
    "avgRent",
    "totalRent",
    "customBuilding",
    "customStreet",
    "customStreetNo",
    "customDistrict",
    "areaEfficiency",
    "areaGross",
    "areaNet",
    "areas",
    "area",
    "main1Photo",
    "possession",
    "ceilingHeight",
    "entranceWidth",
    "unitDepth",
    "remarks",
    "stockMedia",
    "buildingMedia",
  ];

  const formStockData = (s) => {
    const langKey = getLangKey(intl);
    const stockMediaData = media?.filter((m) => m.id === s?.unicorn?.id?.toString())?.[0]?.data || {};
    const buildingMediaData = buildingMedia?.filter((m) => m.id === s?.building?.unicorn?.id?.toString())?.[0]?.data || {};
    const streetMediaData = streetMedia?.filter((m) => m.id === s?.street?.street?.unicorn?.id?.toString())?.[0]?.data || {};
    const possibleFeeType = ["/SqFt", "/Qtr", "/Month", "/SY"];
    const possiblePaidBy = ["Paid By Tenant", "Paid By Landlord"];

    const areas = s?.area?.areas || [];
    let areaItems = [];
    let netAreaTotal = 0;
    areas.forEach((v) => {
      if (areaItems.filter((type) => type.areaName === v.areaName).length === 0)
        areaItems.push({
          areaName: v.areaName,
          areaNameEn: v.areaNameEn,
          gross: 0,
          grossIsShow: false,
          grossVerified: false,
          net: 0,
          netIsShow: false,
          netVerified: false,
        });

      let areaItem = areaItems.filter(
        (type) => type.areaName === v.areaName,
      )[0];
      let areaType = v.type.toLowerCase();
      if (areaType === "gross") {
        areaItem.gross = v.value;
        areaItem.grossIsShow = v.value && parseFloat(v.value) > 0;
        areaItem.grossVerified = v.verified;
        // area of 閣樓, 自建樓 is unselectable
        if (areaItem.areaName === "閣樓" || areaItem.areaName === "自建閣") {
          areaItem.grossIsShow = false;
        }
      }
      if (areaType === "net") {
        areaItem.net = v.value;
        areaItem.netIsShow = false; // all net area default unselect
        areaItem.netVerified = v.verified;
        netAreaTotal += v.value;
      }
    });

    const currTenancyRecords = s?.tenancyRecords?.filter(
      (v) => v.status === "Current" && !v.deleted,
    );

    let defaultType = "Sale";
    let defaultRemarks = getDefaultRemarks("Sale");
    switch (s?.status?.nameEn) {
      case "Lease":
        defaultType = "Lease";
        defaultRemarks = getDefaultRemarks("Lease");
        break;
      case "Sale+Lease":
        defaultType = "SaleAndLease";
        defaultRemarks = getDefaultRemarks("SaleAndLease");
        break;
    }

    const allFields = {
      streetType: 0,
      type: defaultType,
      avgPrice: [
        {
          value: s?.askingPrice?.average,
          isShow: false,
        },
      ],
      totalPrice: [
        {
          value: s?.askingPrice?.total,
          isShow: defaultType === "Sale" || defaultType === "SaleAndLease",
        },
      ],
      suggestedAvgPrice: [
        {
          value:
            s?.askingPrice?.details?.filter(
              (v) => v.type === "average_bottom",
            )[0]?.value || 0,
          isShow: false,
        },
      ],
      suggestedTotalPrice: [
        {
          value:
            s?.askingPrice?.details?.filter((v) => v.type === "total_bottom")[0]
              ?.value || 0,
          isShow:
            (defaultType === "Sale" || defaultType === "SaleAndLease") &&
            !!s?.askingPrice?.details?.filter(
              (v) => v.type === "total_bottom",
            )[0]?.value,
        },
      ],
      avgRent: [
        {
          value: s?.askingRent?.average,
          isShow: false,
        },
      ],
      totalRent: [
        {
          value: s?.askingRent?.total,
          isShow: defaultType === "Lease" || defaultType === "SaleAndLease",
        },
      ],
      suggestedAvgRent: [
        {
          value:
            s?.askingRent?.details?.filter(
              (v) => v.type === "average_bottom",
            )[0]?.value || 0,
          isShow: false,
        },
      ],
      suggestedTotalRent: [
        {
          value:
            s?.askingRent?.details?.filter((v) => v.type === "total_bottom")[0]
              ?.value || 0,
          isShow:
            (defaultType === "Lease" || defaultType === "SaleAndLease") &&
            !!s?.askingRent?.details?.filter(
              (v) => v.type === "total_bottom",
            )[0]?.value,
        },
      ],
      floor: [
        {
          value: s?.floor,
          isShow: true,
        },
      ],
      unit: [
        {
          value: s?.unit,
          isShow: true,
        },
      ],
      customBuilding: [
        {
          value: s?.building?.[langKey] || "---",
          isShow: !!s?.building?.[langKey],
        },
      ],
      customStreet: s?.street?.street?.[langKey] || "---",
      customStreetNo: s?.street?.number || "---",
      customDistrict: s?.district?.[langKey] || "---",
      areas: areaItems,
      area: [
        {
          areaName: "總面積",
          areaNameEn: "Total",
          gross: s?.area?.total,
          grossIsShow: !!s?.area?.total,
          grossVerified: false,
          net: netAreaTotal,
          netIsShow: false, // all net area default unselect
          netVerified: false,
        },
      ],
      possession: [
        {
          value: s?.possession?.nameEn,
          isShow: !!s?.possession?.nameEn,
        },
      ],
      managementFee: [
        {
          value: s?.managementFee?.number || "",
          unit:
            possibleFeeType.indexOf(s?.managementFee?.type) >= 0
              ? s?.managementFee?.type
              : "",
          paidBy:
            possiblePaidBy.indexOf(s?.managementFee?.paidBy) >= 0
              ? s?.managementFee?.paidBy
              : "",
          isShow: !!s?.managementFee?.number,
        },
      ],
      remarks: defaultRemarks,
      entranceWidth: [
        {
          ft: s?.doorWidth?.ft || "",
          in: s?.doorWidth?.in || "",
          isShow: !!(s?.doorWidth?.ft || s?.doorWidth?.in),
        },
      ],
      unitDepth: [
        {
          ft: s?.unitDepth?.ft || "",
          in: s?.unitDepth?.in || "",
          isShow: !!(s?.unitDepth?.ft || s?.unitDepth?.in),
        },
      ],
      ceilingHeight: [
        {
          ft: s?.ceilingHeight?.ft || "",
          in: s?.ceilingHeight?.in || "",
          isShow: false,
        },
      ],
      currentTenants: currTenancyRecords?.map((v) => {
        const minDate = v?.expiry?.minDate;
        const maxDate = v?.expiry?.maxDate;
        return {
          tenant: v?.tenant?.[langKey] || "---",
          tenantIsShow: !!v?.tenant?.[langKey],
          rentalFee: v?.rentalFee,
          rentalFeeIsShow: !!v?.rentalFee && !!v?.tenant?.[langKey],
          period: parsePeriod(minDate, maxDate, intl),
          periodIsShow: !!(minDate || maxDate) && !!v?.tenant?.[langKey],
          tenancy: getProposalTenancyDesc(v, intl.locale),
          tenancyIsShow: !!v?.tenant?.[langKey],
        };
      }),
      yield: [
        {
          value: s?.yield || "",
          isShow: !!s?.yield,
        },
      ],
      customTitle: [
        {
          value: "",
          isShow: false,
        },
      ],
      hideEmployeePhoto: [
        {
          value: intl.formatMessage({
            id: "proposal.form.hideemployeephoto",
          }),
          isShow: false,
        },
      ],
      hideContact: [
        {
          value: intl.formatMessage({
            id: "proposal.form.hidecontact",
          }),
          isShow: false,
        },
      ],
      companyTitle: 'midlandici',
      stockMedia: []
        .concat(stockMediaData?.photo || [], stockMediaData?.video || [])
        ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
        ?.map((v) => v.id),
      buildingMedia: []
        .concat(buildingMediaData?.photo || [], buildingMediaData?.video || [])
        ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
        ?.map((v) => v.id),
      streetMedia: []
        .concat(streetMediaData?.photo || [], streetMediaData?.video || [])
        ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
        ?.map((v) => v.id),
      main1Photo: "map",
    };

    // set default main photo
    if (isListProposal) {
      if (Object.keys(stockMediaData).length > 0 && stockMediaData.photo.length > 0)
        allFields.main1Photo = stockMediaData.photo[0].id;
      else if (
        Object.keys(buildingMediaData).length > 0 &&
        buildingMediaData.photo.length > 0
      )
        allFields.main1Photo = buildingMediaData.photo[0].id;
      else if (
        Object.keys(streetMediaData).length > 0 &&
        streetMediaData.photo.length > 0
      )
        allFields.main1Photo = streetMediaData.photo[0].id;
    }

    return isListProposal
      ? _.omitBy(allFields, (_, key) => !listProposalFields.includes(key))
      : allFields;
  };

  const getParsedProposal = (stock, mediaData, buildingMediaData, streetMediaData, values) => {
    const photos = [].concat(
      mediaData?.photo || [],
      buildingMediaData?.photo || [],
      streetMediaData?.photo || [],
    );
    const videos = [].concat(
      mediaData?.video || [],
      buildingMediaData?.video || [],
      streetMediaData?.video || [],
    );
    const getPhotoInfoFromRedux = (id) => {
      let photo = photos?.filter((v) => v.id === id)?.[0] || null;
      if (photo)
        photo = {
          id: photo.id,
          mediumRoot: photo.mediumRoot,
          photoContent: photo.photoContent,
        };
      return photo;
    };
    const getVideoInfoFromRedux = (id) => {
      let video = videos?.filter((v) => v.id === id)?.[0] || null;
      if (video)
        video = {
          id: video.id,
          mediumRoot: video.mediumRoot,
          youtubeId: video.youtubeMrId || video.youtubeHkpId || video.youtubeId,
        };
      return video;
    };
    const getPossessionInfoFromLangFile = (value) => {
      return {
        nameZh:
          value &&
          possessionLangIdMapping[value] &&
          langFile.zh[possessionLangIdMapping[value]]
            ? langFile.zh[possessionLangIdMapping[value]]
            : "",
        nameEn:
          value &&
          possessionLangIdMapping[value] &&
          langFile.en[possessionLangIdMapping[value]]
            ? langFile.en[possessionLangIdMapping[value]]
            : "",
      };
    };

    const currTenancyRecords = stock?.tenancyRecords?.filter(
      (v) => v.status === "Current" && !v.deleted,
    );

    // stock photo which has the main tag
    const mainStockPhotoId = mediaData?.photo?.filter(
      (v) => v.tags && v.tags.includes("main"),
    )?.[0]?.id;
    // building photo which has the main tag
    const mainBuildingPhotoId = buildingMediaData?.photo?.filter(
      (v) => v.tags && v.tags.includes("main"),
    )?.[0]?.id;
    // street photo which has the main tag
    const mainStreetPhotoId = streetMediaData?.photo?.filter(
      (v) => v.tags && v.tags.includes("main"),
    )?.[0]?.id;
    // main stock photo has higher priority
    const mainPhoto = getPhotoInfoFromRedux(
      mainStockPhotoId || mainBuildingPhotoId || mainStreetPhotoId || null,
    );

    let parsedData = {
      ...values,
      type: values?.type,
      floor: values?.floor?.[0],
      unit: values?.unit?.[0],
      avgPrice: {
        ...values?.avgPrice?.[0],
        value: values?.avgPrice?.[0]?.value || 0,
      },
      totalPrice: {
        ...values?.totalPrice?.[0],
        value: values?.totalPrice?.[0]?.value || 0,
      },
      suggestedAvgPrice: {
        ...values?.suggestedAvgPrice?.[0],
        value: values?.suggestedAvgPrice?.[0]?.value || 0,
      },
      suggestedTotalPrice: {
        ...values?.suggestedTotalPrice?.[0],
        value: values?.suggestedTotalPrice?.[0]?.value || 0,
      },
      avgRent: {
        ...values?.avgRent?.[0],
        value: values?.avgRent?.[0]?.value || 0,
      },
      totalRent: {
        ...values?.totalRent?.[0],
        value: values?.totalRent?.[0]?.value || 0,
      },
      suggestedAvgRent: {
        ...values?.suggestedAvgRent?.[0],
        value: values?.suggestedAvgRent?.[0]?.value || 0,
      },
      suggestedTotalRent: {
        ...values?.suggestedTotalRent?.[0],
        value: values?.suggestedTotalRent?.[0]?.value || 0,
      },
      customBuilding: values?.customBuilding?.[0],
      customStreet: stock?.streets?.[values?.streetType]?.street?.nameEn,
      customStreetNo: stock?.streets?.[values?.streetType]?.number,
      areas: values?.areas?.concat(values?.area).map((v) => {
        return {
          ...v,
          gross: v.gross || 0,
          net: v.net || 0,
        };
      }),
      possession: {
        ...values?.possession?.[0],
        value: getPossessionInfoFromLangFile(values?.possession?.[0]?.value),
      },
      managementFee: {
        ...values?.managementFee?.[0],
        value: values?.managementFee?.[0]?.value || 0,
      },
      entranceWidth: {
        ...values?.entranceWidth?.[0],
        ft: values?.entranceWidth?.[0]?.ft || 0,
        in: values?.entranceWidth?.[0]?.in || 0,
      },
      unitDepth: {
        ...values?.unitDepth?.[0],
        ft: values?.unitDepth?.[0]?.ft || 0,
        in: values?.unitDepth?.[0]?.in || 0,
      },
      ceilingHeight: {
        ...values?.ceilingHeight?.[0],
        ft: values?.ceilingHeight?.[0]?.ft || 0,
        in: values?.ceilingHeight?.[0]?.in || 0,
      },
      currentTenants: values?.currentTenants?.map((v, i) => {
        return {
          tenant: {
            nameEn: currTenancyRecords[i]?.tenant?.nameEn,
            nameZh: currTenancyRecords[i]?.tenant?.nameZh,
            isShow: v.tenantIsShow,
          },
          rentalFee: {
            value: v.rentalFee || 0,
            isShow: v.rentalFeeIsShow,
          },
          period: {
            min: currTenancyRecords[i]?.expiry?.minDate,
            max: currTenancyRecords[i]?.expiry?.maxDate,
            isShow: v.periodIsShow,
          },
          tenancy: {
            nameEn: getProposalTenancyDesc(currTenancyRecords[i], "en"),
            nameZh: getProposalTenancyDesc(currTenancyRecords[i], "zh"),
            isShow: v.tenancyIsShow,
          },
        };
      }),
      yield: {
        ...values?.yield?.[0],
        value: values?.yield?.[0]?.value || 0,
      },
      customTitle: values?.customTitle?.[0],
      hideEmployeePhoto: !!values?.hideEmployeePhoto?.[0]?.isShow,
      hideContact: !!values?.hideContact?.[0]?.isShow,
      photos: []
        .concat(values?.stockMedia || [], values?.buildingMedia || [], values?.streetMedia || [])
        .map(getPhotoInfoFromRedux)
        .filter((v) => v !== null),
      videos: []
        .concat(values?.stockMedia || [], values?.buildingMedia || [], values?.streetMedia || [])
        .map(getVideoInfoFromRedux)
        .filter((v) => v !== null),
      googleMap: {
        isPP: !!(values?.googleMapPhoto && values?.googleMapPhoto.length > 0),
        isMain1: values?.main1Photo === "map",
        isMain2: values?.main2Photo === "map",
      },
      main1Photo:
        values?.main1Photo && getPhotoInfoFromRedux(values?.main1Photo),
      main2Photo:
        values?.main2Photo && getPhotoInfoFromRedux(values?.main2Photo),
      mainPhoto: mainPhoto, // stock or building photo which has the main tag

      // additional data which is required by proposal but not shown in the form (or is not a field in the form)
      stockId: stock?.unicorn?.id,
      stockMongoId: stock?._id,
      sbu: sbu,
      isSoleagent: !!(
        (stock.soleagent &&
          stock.soleagent.saleData &&
          stock.soleagent.saleData.periodStart &&
          stock.soleagent.saleData.periodEnd) ||
        (stock.soleagent &&
          stock.soleagent.rentData &&
          stock.soleagent.rentData.periodStart &&
          stock.soleagent.rentData.periodEnd)
      ),
      districtNameZh: stock?.district?.nameZh,
      districtNameEn: stock?.district?.nameEn,
      streetNameZh: stock?.streets?.[values?.streetType]?.street?.nameZh,
      streetNameEn: stock?.streets?.[values?.streetType]?.street?.nameEn,
      streetNo: stock?.streets?.[values?.streetType]?.number,
      buildingNameZh: stock?.building?.nameZh,
      buildingNameEn: stock?.building?.nameEn,
      buildingDistrictNameZh: stock?.building?.district?.nameZh,
      buildingDistrictNameEn: stock?.building?.district?.nameEn,
      floorInChinese: stock?.floorInChinese,
      lng: stock?.building?.coordinates?.longitude,
      lat: stock?.building?.coordinates?.latitude,
      proposalName,
    };

    // these photo and video fields are merged into photos and videos
    delete parsedData.stockMedia;
    delete parsedData.buildingMedia;
    delete parsedData.streetMedia;
    // googleMapPhoto is converted to includeGoogleMap field
    delete parsedData.googleMapPhoto;
    // streetType is used to select the correct street name
    delete parsedData.streetType;
    // area is concatenated with areas
    delete parsedData.area;

    return parsedData;
  };

  const submitListProposal = () => {
    const proposalStocksId = stockData.map((s) => s._id);
    // retrieve stock proposal data
    const formArr = Object.keys(form)
      .map((id) => id.substr(5))
      .reduce((list, id) => {
        if (proposalStocksId.includes(id))
          return [...list, { id, ...form[`form_${id}`].values }];
      }, []);

    const parsedFormArr = formArr.map((formData) => {
      const stock = stockData.find((s) => s._id === formData.id);
      const stockMediaData = media?.filter((m) => m.id === stock?.unicorn?.id?.toString())?.[0]?.data || {};
      const buildingMediaData = buildingMedia?.filter((m) => m.id === stock?.building?.unicorn?.id?.toString())?.[0]?.data || {};
      const streetMediaData = streetMedia?.filter((m) => m.id === stock?.street?.street?.unicorn?.id?.toString())?.[0]?.data || {};
      delete formData.id;

      const parsedProposal = getParsedProposal(
        stock,
        stockMediaData,
        buildingMediaData,
        streetMediaData,
        formData,
      );
      // delete redundant fields
      delete parsedProposal.developers;
      delete parsedProposal.currentTenants;
      delete parsedProposal.containers;
      delete parsedProposal.inTakeDate;
      delete parsedProposal.isSoleagent;
      delete parsedProposal.isBuildingFieldsAllHide;
      delete parsedProposal.suggestedTotalPrice;
      delete parsedProposal.suggestedTotalRent;
      delete parsedProposal.suggestedAvgPrice;
      delete parsedProposal.suggestedAvgRent;
      delete parsedProposal.photos;
      delete parsedProposal.videos;
      delete parsedProposal.stockType;
      delete parsedProposal.managementCompany;
      delete parsedProposal.managementFee;
      delete parsedProposal.unitView;
      delete parsedProposal.title;
      delete parsedProposal.transport;
      delete parsedProposal.usage;
      delete parsedProposal.yield;
      delete parsedProposal.customTitle;
      delete parsedProposal.proposalName;
      delete parsedProposal.hideContact;
      delete parsedProposal.hideEmployeePhoto;
      delete parsedProposal.type;
      delete parsedProposal.sbu;

      if (!parsedProposal.mainPhoto) {
        if (parsedProposal.main1Photo)
          parsedProposal.mainPhoto = parsedProposal.main1Photo;
        else if (parsedProposal.main2Photo)
          parsedProposal.mainPhoto = parsedProposal.main2Photo;
      }
      delete parsedProposal.main1Photo;
      delete parsedProposal.main2Photo;

      // set useGGMapPhoto to true if googleMap.isMain1 is true
      parsedProposal.useGGMapPhoto = parsedProposal.googleMap.isMain1;
      delete parsedProposal.googleMap;

      const {
        buildingNameZh: nameZh,
        buildingNameEn: nameEn,
        buildingDistrictNameZh: districtNameZh,
        buildingDistrictNameEn: districtNameEn,
        lat,
        lng,
      } = parsedProposal;

      // building for SHOPS list proposal
      parsedProposal.building = {
        nameZh,
        nameEn,
        districtNameZh,
        districtNameEn,
        lat,
        lng,
      };

      delete parsedProposal.buildingNameZh;
      delete parsedProposal.buildingNameEn;
      delete parsedProposal.buildingDistrictNameZh;
      delete parsedProposal.buildingDistrictNameEn;
      delete parsedProposal.passengerLift;
      delete parsedProposal.cargoLift;
      delete parsedProposal.airConditioningType;
      delete parsedProposal.lat;
      delete parsedProposal.lng;

      // delete undefined fields
      Object.keys(parsedProposal).forEach(
        (key) => !parsedProposal[key] && delete parsedProposal[key],
      );

      return parsedProposal;
    });

    const payload = {
      type: listProposalType,
      sbu,
      hideContact,
      hideEmployeePhoto,
      proposalName,
      proposals: parsedFormArr,
    };
    createListProposal(payload);
  };

  const submit = (stockId, buildingId, streetId, values) => {
    const stockMediaData = media?.filter((m) => m.id === stockId.toString())?.[0]?.data || {};
    const buildingMediaData = buildingMedia?.filter((m) => m.id === buildingId.toString())?.[0]?.data || {};
    const streetMediaData = streetMedia?.filter((m) => m.id === streetId.toString())?.[0]?.data || {};
    const parsedData = getParsedProposal(
      stock,
      stockMediaData,
      buildingMediaData,
      streetMediaData,
      values,
    );

    createProposal(parsedData);
  };

  const renderForms = () =>
    stockData.map((s, idx) => (
      <ProposalForm
        key={s._id}
        stockId={s._id}
        show={currentStock === idx}
        onSubmit={(...values) =>
          submit(
            s.unicorn.id.toString(),
            s.building ? s.building.unicorn.id : "", // s.building can be null
            s.street ? s.street.street.unicorn.id : "",
            ...values,
          )
        }
        form={`form_${s._id}`}
        initialValues={formStockData(s)}
        changeFieldValue={(...args) =>
          changeFieldValue(`form_${s._id}`, ...args)
        }
      />
    ));

  return (
    <div>
      <div>{renderForms()}</div>

      {exceedQuota ? (
        <Dialog
          open={createProposalDialogOpen}
          handleClose={handleCloseCreateProposalDialog}
        >
          {intl.formatMessage(
            { id: "proposal.create.exceed" },
            { quota: generalProposalQuota },
          )}
          <DialogFrame
            buttonMain={intl.formatMessage({
              id: "common.ok",
            })}
            handleMain={handleCloseSubmitDialog}
          ></DialogFrame>
        </Dialog>
      ) : (
        <SubmitDialog
          dialogOpen={createProposalDialogOpen}
          handleCloseDialog={handleCloseSubmitDialog}
          succCallback={submitDialogCallback}
          submitting={isListProposal ? creatingListProposal : creatingProposal}
          submitted={isListProposal ? createdListProposal : createdProposal}
          error={isListProposal ? createListProposalError : createProposalError}
          submit={
            isListProposal
              ? submitListProposal
              : () => dispatchSubmitForm(`form_${stock._id}`)
          }
          submitBtnText={intl.formatMessage({ id: "proposal.form.save" })}
          succMsg={intl.formatMessage(
            { id: "proposal.form.savesuccess" },
            { filename: proposalName },
          )}
        >
          <TextField
            label={intl.formatMessage({ id: "proposal.form.proposalname" })}
            value={proposalName}
            onChange={handleNameChange}
            variant="outlined"
            fullWidth
          />
          {isListProposal && (
            <>
              <SelectField
                label={intl.formatMessage({ id: "proposal.form.type" })}
                input={{
                  value: listProposalType,
                  onChange: (e) => setListProposalType(e.target.value),
                }}
                ranges={getTypeOptions(intl)}
                meta={{}}
                variant="outlined"
                fullWidth
              />
              <FormGroup>
                <FormControlLabel
                  control={
                    <CustomCheckbox
                      checked={hideContact}
                      onChange={(e) => setHideContact(e.target.checked)}
                    />
                  }
                  label={intl.formatMessage({
                    id: "proposal.form.hidecontact",
                  })}
                />
                <FormControlLabel
                  control={
                    <CustomCheckbox
                      checked={hideEmployeePhoto}
                      onChange={(e) => setHideEmployeePhoto(e.target.checked)}
                    />
                  }
                  label={intl.formatMessage({
                    id: "proposal.form.hideemployeephoto",
                  })}
                />
              </FormGroup>
            </>
          )}
        </SubmitDialog>
      )}
    </div>
  );
}

ProposalCreate.propTypes = {
  stockData: PropTypes.array,
  currentStock: PropTypes.number,
  media: PropTypes.array,
  buildingMedia: PropTypes.array,
  streetMedia: PropTypes.array,
  createProposal: PropTypes.func,
  creatingProposal: PropTypes.bool,
  createdProposal: PropTypes.bool,
  createProposalError: PropTypes.string,
  clearCreateProposal: PropTypes.func,
  dispatchSubmitForm: PropTypes.func,
  createProposalDialogOpen: PropTypes.bool,
  handleCloseCreateProposalDialog: PropTypes.func,
  changeFieldValue: PropTypes.func,
  form: PropTypes.object,
  exceedQuota: PropTypes.bool,
  createListProposal: PropTypes.func.isRequired,
  creatingListProposal: PropTypes.bool,
  createdListProposal: PropTypes.bool,
  createListProposalError: PropTypes.string,
  clearCreateListProposal: PropTypes.func,
  intl: PropTypes.object,
};

const mapStateToProps = (state) => ({
  userInfo:
    state.auth &&
    state.auth.user &&
    state.auth.user.login &&
    state.auth.user.login.info
      ? state.auth.user.login.info
      : {},
  stockData: state.stock.detail ? state.stock.detail : {},
  currentStock: state.stock.currentDetail ? state.stock.currentDetail : 0,
  media: state.stock.media ? state.stock.media : [],
  buildingMedia: state.building.media ? state.building.media : [],
  streetMedia: state.street.media ? state.street.media : [],
  form: state.form ? state.form : {},
  creatingProposal: state.proposal.creatingProposal
    ? state.proposal.creatingProposal
    : false,
  createdProposal: state.proposal.createdProposal
    ? state.proposal.createdProposal
    : false,
  createProposalError:
    state.proposal.createProposalError &&
    state.proposal.createProposalError.message
      ? state.proposal.createProposalError.message
      : null,
  creatingListProposal: state.listProposal.creatingListProposal
    ? state.listProposal.creatingListProposal
    : false,
  createdListProposal: state.listProposal.createdListProposal
    ? state.listProposal.createdListProposal
    : false,
  createListProposalError: state.listProposal.createListProposalError
    ? state.listProposal.createListProposalError.message
    : null,
});

const mapDispatchToProps = (dispatch) => {
  return {
    dispatchSubmitForm: (form) => dispatch(submit(form)),
    changeFieldValue: (form, field, value) =>
      dispatch(change(form, field, value)),
    createProposal: (...args) => dispatch(createProposal(...args)),
    createListProposal: (listProposal) =>
      dispatch(createListProposal(listProposal)),
    clearCreateProposal: (...args) => dispatch(clearCreateProposal(...args)),
    clearCreateListProposal: () => dispatch(clearCreateListProposal()),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(ProposalCreate));
