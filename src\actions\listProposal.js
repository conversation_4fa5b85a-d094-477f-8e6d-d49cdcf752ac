import _ from "lodash";
import { getFormValues } from "redux-form";
import {
  CLEAR_CREATE_LIST_PROPOSAL,
  CREATE_LIST_PROPOSAL_ERROR,
  CREATE_LIST_PROPOSAL_START,
  CREATE_LIST_PROPOSAL_SUCCESS,
} from "../constants/listProposal";
import { parseFormToListPP } from "@/components/Saleskit/helpers";
import { getDisplayStockId } from "../helper/generalHelper";
import { addActivityLog, gtagHandler } from "./log";

export function createListProposal() {
  return async (dispatch, getState, { fetch, universalRequest, takeLog }) => {
    const state = await getState();
    const form = getFormValues("proposal")(state);

    const listProposal = parseFormToListPP(form, {
      ..._.pick(_.get(state, "stock"), [
        "possessions",
        "unitViews",
        "decorations",
        "media",
        "currentStates",
      ]),
      stocks: _.get(state, "stock.detail") || [],
      buildingMedia: _.get(state, "building.media") || [],
      streetMedia: _.get(state, "street.media") || [],
    });

    dispatch({
      type: CREATE_LIST_PROPOSAL_START,
      payload: {
        listProposal,
      },
    });

    try {
      const { data, errors } = await universalRequest("/createListProposal", {
        method: "POST",
        headers: {
          "content-type": "application/json",
        },
        body: JSON.stringify({ listProposal }),
      });

      dispatch(addActivityLog("proposal", "create", listProposal));

      if (errors) throw new Error(errors[0].message);

      const msg = JSON.stringify({
        action: "create-list-proposal",
        status: "success",
        parameters: listProposal,
      });
      await takeLog(state.auth.user.login.info, msg, fetch, true);

      const ids = _.map(_.get(listProposal, "proposals"), v => getDisplayStockId(v.stockId));
      dispatch(gtagHandler("Create Proposal", {
        StockID: _.join(ids, ","),
      }));

      dispatch({
        type: CREATE_LIST_PROPOSAL_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      console.error(error);
      dispatch({
        type: CREATE_LIST_PROPOSAL_ERROR,
        payload: {
          error,
        },
      });
    }
  };
}

export function clearCreateListProposal() {
  return (dispatch) => {
    dispatch({
      type: CLEAR_CREATE_LIST_PROPOSAL,
    });
  };
}
