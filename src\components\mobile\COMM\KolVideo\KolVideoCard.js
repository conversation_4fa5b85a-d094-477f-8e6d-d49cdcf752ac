import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import { injectIntl } from "react-intl";
import VideoPopup from "../../../common/MediaMain/VideoPopup";
import Checkbox from "@material-ui/core/Checkbox";
import moment from "moment";

const styles = {
  image: {
    width: "100%",
    cursor: "pointer",
    backgroundSize: "cover",
    backgroundPosition: "center",
    backgroundRepeat: "no-repeat",
  },
  checkbox: {
    padding: "2px 0",
    "&.Mui-checked": {
      color: "#13CE66",
    },
  },
  parent: {
    display: "grid",
    gridTemplateColumns: "10% 45% 45%",
    gridTemplateRows: "50% 50%",
    gridColumnGap: "0px",
    gridRowGap: "0px",
  }, 
  div1: { gridArea: "1 / 1 / 3 / 2", placeSelf: "center" },
  div2: { gridArea: "1 / 2 / 2 / 3" },
  div3: { gridArea: "1 / 3 / 2 / 4", textAlign: "right" },
  div4: { gridArea: "2 / 2 / 3 / 3", textOverflow: "ellipsis", overflow: "hidden", whiteSpace:"nowrap", wordBreak: "keep-all", width: "150%" },
  div5: { gridArea: "2 / 3 / 3 / 4", textAlign: "right" }
};

class KolVideoCard extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
    toggle: PropTypes.func.isRequired,
    selectedKolVideo: PropTypes.array,
    className: PropTypes.string,
  };

  constructor(props) {
    super(props);
    this.state = {
      popupOpen: false,
    };
  }

  handlePopupOpen = () => {
    this.setState({ popupOpen: true });
  };

  handlePopupClose = () => {
    this.setState({ popupOpen: false });
  };

  render() {
    const { classes, detail, toggle, selectedKolVideo, className, intl } = this.props;
    const { popupOpen } = this.state;

    return (
      <>
        <div>
          <div>
            <img
              className={classes.image}
              alt=""
              role="presentation"
              src={detail.thumbnail}
              onClick={this.handlePopupOpen}
            />
          </div>
          <div className={classes.parent}>
            <div className={classes.div1}>             
              <Checkbox
                className={classes.checkbox}
                value={detail.id}
                onChange={(event) => toggle(event, detail.id)}
                checked={selectedKolVideo.indexOf(detail.id) !== -1}
              /> 
            </div>
            <div className={classes.div2}> 樓盤ID:{detail.propertyRefId} </div>
            <div className={classes.div3}> {moment(detail.createdDate).format("HH:mm:ss YYYY-MM-DD")} </div>
            <div className={classes.div4}> {detail.originalFilename} </div>
            <div className={classes.div5}> {detail?.employee?.cName || ""} </div>
          </div>
        </div>
        {popupOpen && <VideoPopup
          video={detail}
          handlePopupClose={this.handlePopupClose}
          deletable={false}
        />}
      </>
    );
  }
}

export default withStyles(styles)(injectIntl(KolVideoCard));
