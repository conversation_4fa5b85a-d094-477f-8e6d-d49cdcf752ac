export const LIST_STOCK_DETAIL_QUERY = `
query ($_id: [ID], $backendKey: String!) {
  stocks(_id: $_id, backendKey: $backendKey) {
    _id
    unicorn {
      id
      landSearch
      landSearchDetail {
        search_ref_no
        address
        flat
        floor
      }
    }
    recordOperation {
      createDate
      lastUpdateDate
    }
    mortgagee
    status {
      nameEn
      nameZh
    }
    source {
      nameEn
      nameZh
    }
    stockType {
      nameEn
      nameZh
    }
    stockTypeId
    isShoppingMallStock
    isFrontAndRearPortion
    isSingleSideStock
    isWWW
    haveVideo
    singleSideType
    soleagent {
      saleData {
        periodStart
        periodEnd
        agent {
          emp_id
        }
      }
      rentData {
        periodStart
        periodEnd
        agent {
          emp_id
        }
      }
    }
    building {
      _id
      nameEn
      nameZh
      unicorn {
        id
      }
      district {
        nameEn
        nameZh
      }
      coordinates {
        latitude
        longitude
      }
    }
    coordinates {
      latitude
      longitude
    }
    street {
      street {
        nameEn
        nameZh
        unicorn {
          id
        }
      }
      number
    }
    streets {
      street {
        nameEn
        nameZh
        unicorn {
          id
        }
      }
      number
    }
    facingTo
    parity
    district {
      nameZh
      nameEn
    }
    floor
    floorInChinese
    unit
    possession {
      nameZh
      nameEn
    }
    currentState {
      nameZh
      nameEn
    }
    currentStateId
    availability
    area {
      total
      shop
      site
      areas {
        areaName
        areaNameEn
        value
        type
        verified
      }
    }
    askingRent {
      total
      average
      trend
      details {
        value
        type
      }
    }
    askingPrice {
      total
      average
      trend
      details {
        value
        type
      }
    }
    haveKey
    keyNumber
    remarks {
      quick
    }
    yield
    confirmorTransactionDate
    surveyorProposal {
      status
      date
    }
    currentHands
    vendors {
      hands
      handsId
      contactsPerson {
        _id
        nameEn
        nameZh
        titleEn
        titleZh
        remarks
        companies {
          _id
          nameEn
          nameZh
        }
        contact {
          phones {
            type
            number
            doNotContact
            privacy
          }
        }
      }
      contactsPersons {
        _id
        nameEn
        nameZh
        title
        remarks
        contact {
          phones {
            type
            number
            doNotContact
            privacy
          }
        }
      }
      contactsCompanies {
        _id
        nameEn
        nameZh
        remarks
        contact {
          phones {
            type
            number
            doNotContact
            privacy
          }
        }
      }
    }
    commission
    doorWidth {
      ft
      in
    }
    ceilingHeight {
      ft
      in
    }
    unitDepth {
      ft
      in
    }
    backDoor
    toilet
    signboard
    promote {
      boardStatus
      upBoardDate
      boardDescription
      newsStatus
      posterDate
      posterDescription
    }
    usage {
      nameZh
      nameEn
    }
    tenancy {
      rentFree
      rentFreeZh
      freePeriod {
        days
        months
      }
      depositInMonth
      prepaidInMonth
      leasePeriod {
        months
        years
        description
      }
      leaseOptions {
        months
        years
        feePrefix
        feeType
        rent {
          amount
          rate
        }
      }
    }
    ownerType {
      nameZh
      nameEn
    }
    managementFee {
      number
      type
      paidBy
    }
    isIncludeAirConditioning
    airCond {
      fee
      feeType
      feePaidBy
      type {
        nameZh
        nameEn
      }
    }
    rates {
      number
      type
      paidBy
    }
    governmentRent {
      number
      type
      paidBy
    }
    cleaningFee {
      number
      type
      paidBy
    }
    promoteFee {
      number
      type
      paidBy
    }
    consultantShares {
      employee {
        name_en
        dept_code
      }
      dateTime
      remarks
    }
    leasingInfo {
      isHandOver
    }
    propertyAdvertisements {
      saleData {
        minTotal
        maxTotal
        minDate
        maxDate
        consultant {
          name_en
          dept_code
        }
      }
      rentData {
        minTotal
        maxTotal
        minDate
        maxDate
        consultant {
          name_en
          dept_code
        }
      }
    }
    updateHistory {
      handsId
      isCancelled
      datetime
      employee {
        name_en
        dept_code
        valid_emp
      }
      status {
        nameEn
        nameZh
      }
      askingPrice {
        total
      }
      askingRent {
        total
      }
      description
      deleted
    }
    tenancyRecords {
      status
      deleted
      unit
      floor
      area
      tenants {
        isTenant
        contactsPerson {
          _id
          nameEn
          nameZh
          titleEn
          titleZh
          companies {
            _id
            nameEn
            nameZh
          }
          contact {
            phones {
              type
              number
              doNotContact
            }
          }
        }
        contactsPersons {
          _id
          nameEn
          nameZh
          title
          remarks
          isTenant
          contact {
            phones {
              type
              number
              doNotContact
            }
          }
        }
        contactsCompanies {
          _id
          type
          nameEn
          nameZh
          remarks
          contact {
            phones {
              type
              number
              doNotContact
            }
          }
        }
      }
      business {
        nameZh
        nameEn
      }
      expiry {
        minDate
        maxDate
      }
      rentalFee
      isIncludeGovernmentRent
      isIncludeRates
      isIncludeAirConditioning
      isIncludeManagementFee
      extends {
        minDate
        maxDate
        feePrefix
        feeType
        rent {
          amount
          rate
        }
      }
    }
    handsInfo {
      _id
      hands
      isCancelled
    }
    surveyorProposal {
      status
      date
    }
  }
}
`;
