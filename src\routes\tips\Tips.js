/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import { style } from "./style";
import PlayArrowIcon from "@material-ui/icons/PlayArrow";
import DetailBoxSection from "../../components/common/DetailBoxSection";
import FormButton from "../../components/common/FormButton";
import appicon from "./appicon.png";
import menu from "./menu.png";
import share from "./share.png";
import samsungshare from "./samsungshare.png";
import videothumbnail from "./videothumbnail.png";
import priceBox from "./priceBox.png";
import rentBox from "./rentBox.png";
import { FormattedMessage, injectIntl } from "react-intl";
import SearchIcon from "@material-ui/icons/Search";
import { sbu, generalDailyQuota, link, mode } from "../../config";

const Tips = (props) => {
  const { classes, intl } = props;
  let tipsArr, sortingArr;
  let s3Url = `${link.s3File.prefix}${mode === "production" ? "prod" : "uat"}`;
  switch (sbu) {
    case "COMM":
      tipsArr = [
        {
          tagAbbr: <FormattedMessage id="stock.tag.sole" />,
          tag: "tips.stocktag.sole",
          tagclassName: "propertyTagRed",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.equaity" />,
          tag: "tips.stocktag.equaity",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.carpark" />,
          tag: "tips.stocktag.carpark",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.mortgagee" />,
          tag: "tips.stocktag.mortgagee",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.eaa" />,
          tag: "tips.stocktag.eaa",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.new" />,
          tag: "tips.stocktag.new",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.www" />,
          tag: "tips.stocktag.www",
          tagclassName: "propertyTag",
        },
      ];
      sortingArr = [
        <FormattedMessage id="search.common.floor" />,
        <FormattedMessage id="search.common.rent" />,
        <FormattedMessage id="search.common.price" />,
        <FormattedMessage id="search.common.area" />,
      ];
      break;
    case "IND":
      tipsArr = [
        {
          tagAbbr: <FormattedMessage id="stock.tag.sole" />,
          tag: "tips.stocktag.sole",
          tagclassName: "propertyTagRed",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.carpark" />,
          tag: "tips.stocktag.carpark",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.www" />,
          tag: "tips.stocktag.www",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.mortgagee" />,
          tag: "tips.stocktag.mortgagee",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.eaa" />,
          tag: "tips.stocktag.eaa",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.new" />,
          tag: "tips.stocktag.new",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.equaity" />,
          tag: "search.form.saleequity",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.terrace" />,
          tag: "stock.terrace.full",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.roof" />,
          tag: "stock.roof",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.marketable" />,
          tag: "search.header.marketable",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.cockloft" />,
          tag: "stock.cockloft",
          tagclassName: "propertyTag",
        },
      ];
      sortingArr = [
        <FormattedMessage id="search.common.floor" />,
        <FormattedMessage id="search.common.rent" />,
        <FormattedMessage id="search.common.price" />,
        <FormattedMessage id="search.common.area" />,
      ];
      break;
    case "SHOPS":
      tipsArr = [
        {
          tagAbbr: <FormattedMessage id="stock.tag.mortgagee" />,
          tag: "search.form.mortgagee",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.eaa" />,
          tag: "tips.stocktag.eaa",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.soleagentbuy" />,
          tag: "tips.stocktag.soleagentbuy",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.soleagentrent" />,
          tag: "tips.stocktag.soleagentrent",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.shoppingmall" />,
          tag: "tips.stocktag.shoppingmall",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.singleside" />,
          tag: "tips.stocktag.singleside",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.frontandrear" />,
          tag: "tips.stocktag.frontandrear",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.key" />,
          tag: "stock.tag.key",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.replacement" />,
          tag: "tips.stocktag.replacement",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.tag.www" />,
          tag: "stock.tag.www",
          tagclassName: "propertyTag",
        },
        {
          tagAbbr: <FormattedMessage id="stock.video" />,
          tag: "stock.video",
          tagclassName: "propertyTag",
        },
      ];
      sortingArr = [
        <FormattedMessage id="search.form.streetnumber" />,
        <FormattedMessage id="search.common.rent" />,
        <FormattedMessage id="search.common.price" />,
        <FormattedMessage id="search.common.area" />,
      ];
      break;
    default:
      break;
  }

  return (
    <div className={classes.root}>
      <div className={classes.container}>
        <div className={classes.sectionBox}>
          <div className={classes.sectionTitleWithStrike}>
            <div>
              <FormattedMessage id="tips.tutorial" />
            </div>
          </div>

          <div className={classes.btnRow}>
            <a
              href={`${s3Url}/UserManual-${sbu}.pdf`}
              target="_blank"
              className={classes.link}
            >
              <FormButton className={classes.usermanualbtn}>
                <FormattedMessage id="tips.usermanual" />
              </FormButton>
            </a>
          </div>

          <div className={classes.sectionTitle}>
            <FormattedMessage id="tips.tutorial.basicfunction" />
          </div>

          <video
            width="100%"
            controls
            className={classes.tutorialVideo}
            poster={videothumbnail}
          >
            <source src={`${s3Url}/MSearch-${sbu}.mp4`} type="video/mp4" />
            <FormattedMessage id="tips.notsupport" />
          </video>

          <div>
            <div className={classes.sectionTitle}>
              <FormattedMessage id="tips.tutorial.otherfunction" />
            </div>
            <video
              width="100%"
              controls
              className={classes.tutorialVideo}
              poster={videothumbnail}
            >
              <source src={`${s3Url}/MSearch-PROPOSAL-${sbu}.mp4`} type="video/mp4" />
              <FormattedMessage id="tips.notsupport" />
            </video>
          </div>
        </div>

        <div className={classes.sectionBox}>
          <div className={classes.sectionTitleWithStrike}>
            <div>
              <FormattedMessage id="tips.stocktags" />
            </div>
          </div>

          {tipsArr.map((v, i) => {
            return (
              <div className={classes.descriptionRow} key={i}>
                <div className={classes[v.tagclassName]}>{v.tagAbbr}</div>
                <div className={classes.tagDescription}>
                  <FormattedMessage id={`${v.tag}`} />
                </div>
              </div>
            );
          })}
        </div>

        <div className={classes.sectionBox}>
          <div className={classes.sectionTitleWithStrike}>
            <div>
              <FormattedMessage id="tips.priceformat" />
            </div>
          </div>
          <div>
            <ul className={classes.orderlistText}>
              <li>
                <FormattedMessage id="search.common.price" />
                : <img src={priceBox} width="150" height="48" />
              </li>
              <li>
                <FormattedMessage id="search.common.rent" />
                : <img src={rentBox} width="150" height="48" />
              </li>
            </ul>
          </div>
        </div>

        <div className={classes.sectionBox}>
          <div className={classes.sectionTitleWithStrike}>
            <div>
              <FormattedMessage id="tips.photoformat.header" />
            </div>
          </div>
          <ul className={classes.orderlistText}>
            <li>
              <FormattedMessage id="tips.photoformat.content" />
            </li>
          </ul>
        </div>

        <div className={classes.sectionBox}>
          <div className={classes.sectionTitleWithStrike}>
            <div>
              <FormattedMessage id="tips.chinesesearch.header" />
            </div>
          </div>
          <ol className={classes.orderlistText}>
            <li>
              <FormattedMessage id="tips.chinesesearch.content1" />
            </li>
            <li>
              <FormattedMessage id="tips.chinesesearch.content2" />
            </li>
          </ol>
        </div>

        <div className={classes.sectionBox}>
          <div className={classes.sectionTitleWithStrike}>
            <div>
              <FormattedMessage id="tips.stockpricesign.header" />
            </div>
          </div>
          <div className={classes.descriptionRow}>
            <div className={classes.imgBox}>
              <PlayArrowIcon className={classes.increasing} />
            </div>
            <div className={classes.tagDescription}>
              <FormattedMessage id="tips.stockpricesign.up" />
            </div>
          </div>
          <div className={classes.descriptionRow}>
            <div className={classes.imgBox}>
              <PlayArrowIcon className={classes.decreasing} />
            </div>
            <div className={classes.tagDescription}>
              <FormattedMessage id="tips.stockpricesign.down" />
            </div>
          </div>
        </div>

        <div className={classes.sectionBox}>
          <div className={classes.sectionTitleWithStrike}>
            <div>
              <FormattedMessage id="tips.defaultsorting.header" />
            </div>
          </div>
          <div className={classes.font18}>
            <li>
              <FormattedMessage id="tips.defaultsorting.content1" />
            </li>
          </div>
          <ol className={classes.districtOrder}>
            {sortingArr.map((v, i) => {
              return <li key={i}>{v}</li>;
            })}
          </ol>
          <div className={classes.font18}>
            <li>
              <FormattedMessage
                id="tips.defaultsorting.content2"
                values={{ field: sortingArr[0] }}
              />
            </li>
          </div>
        </div>

        <div className={classes.sectionBox}>
          <div className={classes.sectionTitleWithStrike}>
            <div>PIN</div>
          </div>

          <div className={classes.font18}>
            <img src={appicon} width="48" height="48" />{" "}
            <FormattedMessage id="tips.pin.header" />
          </div>
          <ol className={classes.orderlistText}>
            <li>
              <FormattedMessage id="tips.pin.content1" />
            </li>
            <li>
              <FormattedMessage id="tips.pin.content2" />
            </li>
            <li>
              <FormattedMessage id="tips.pin.content3" />
            </li>
            <li>
              <FormattedMessage id="tips.pin.content4" />
            </li>
            <li>
              <FormattedMessage id="tips.pin.content5" />
            </li>
            <li>
              <FormattedMessage id="tips.pin.content6" />
            </li>
          </ol>
        </div>

        <div className={classes.sectionBox}>
          <div className={classes.sectionTitleWithStrike}>
            <div>
              <FormattedMessage id="tips.pricerentsorting.header" />
            </div>
          </div>
          <div className={classes.font18}>
            <li>
              <FormattedMessage id="tips.pricerentsorting.content1" />
            </li>
          </div>
        </div>

        <div className={classes.sectionBox}>
          <div className={classes.sectionTitleWithStrike}>
            <div>
              <FormattedMessage id="tips.advancedsearch.header" />
            </div>
          </div>
          <div className={classes.font18}>
            <div className={classes.paragraph}>
              <SearchIcon className={classes.searchicon} />
              {/* <img src={searchicon} width="32" height="36" alt="React" />{" "} */}
              <FormattedMessage id="tips.advancedsearch.content1" />
            </div>
            <div>
              <li>
                <FormattedMessage id="tips.advancedsearch.content2" />
              </li>
            </div>
          </div>
        </div>

        <div className={classes.sectionBox}>
          <div className={classes.sectionTitleWithStrike}>
            <div>
              <FormattedMessage id="tips.addtohomescreen.header" />
            </div>
          </div>
          <div>
            <ol className={classes.orderlistText}>
              <li>
                <FormattedMessage id="tips.addtohomescreen.content1" />
                <ol
                  style={{ listStyleType: "none" }}
                  className={classes.secondOrderContainer}
                >
                  <li>
                    <img src={menu} width="32" height="32" alt="React" />{" "}
                    <span>
                      <FormattedMessage id="tips.addtohomescreen.content2" />
                    </span>
                  </li>
                  <li>
                    <img src={share} width="32" height="32" alt="React" />{" "}
                    <FormattedMessage id="tips.addtohomescreen.content3" />
                  </li>
                  <li>
                    <img
                      src={samsungshare}
                      width="32"
                      height="32"
                      alt="React"
                    />{" "}
                    <FormattedMessage id="tips.addtohomescreen.content4" />
                  </li>
                </ol>
              </li>
              <li>
                <FormattedMessage id="tips.addtohomescreen.content5" />
              </li>
              <li>
                <FormattedMessage id="tips.addtohomescreen.content6" />
              </li>
            </ol>
          </div>
        </div>

        <div className={classes.sectionBox}>
          <div className={classes.sectionTitleWithStrike}>
            <div>
              <FormattedMessage id="tips.dailyquota.header" />
            </div>
          </div>
          <ul className={classes.orderlistText}>
            <li>
              <FormattedMessage
                id="tips.dailyquota.content1"
                values={{ quota: generalDailyQuota }}
              />
            </li>
            <li>
              <FormattedMessage id="tips.dailyquota.content2" />
            </li>
            <li>
              <FormattedMessage id="tips.dailyquota.content3" />
            </li>
          </ul>
        </div>

        <div className={classes.sectionBox}>
          <div className={classes.sectionTitleWithStrike}>
            <div>
              <FormattedMessage id="tips.updatepassword.header" />
            </div>
          </div>
          <ul className={classes.orderlistText}>
            <li>
              <FormattedMessage id="tips.updatepassword.content1" />
            </li>
            <li>
              <FormattedMessage id="tips.updatepassword.content2" />
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default withStyles(style)(Tips);
