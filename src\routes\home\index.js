/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import Home from "./Home";
import Layout from "../../components/Layout";

async function action({ store, fetch }) {
  const { auth } = store.getState();
  console.log(auth);
  if (!auth.user) {
    return { redirect: "/login" };
  } else if (auth.user.authorized == false) {
    return { redirect: "/login" };
  }

  let headerRef = React.createRef();

  return {
    title: "Home",
    chunks: ["home"],
    component: (
      <Layout
        headerRef={headerRef}
        hideHomeIcon={true}
        hideSearchIcon={true}
        isHome={true}
      >
        <Home headerRef={headerRef} />
      </Layout>
    ),
  };
}

export default action;
