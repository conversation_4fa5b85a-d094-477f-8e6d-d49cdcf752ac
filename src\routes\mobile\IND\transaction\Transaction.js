/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { submit } from "redux-form";
import withStyles from "isomorphic-style-loader/lib/withStyles";
import { FormattedMessage } from "react-intl";
import _ from "lodash";
import TransactionList from "../../../../components/mobile/IND/Transaction/List";
import s from "./Transaction.css";
import { listTransactions, clearTransactions } from "../../../../actions/transaction";
import { goToTransactionSearchResult } from "../../../../helper/generalHelper";
import Layout from "../../../../components/Layout";
import SearchForm from "../../../../components/mobile/IND/Transaction/SearchForm";
import { listDistricts } from "../../../../actions/district";

class Transaction extends React.Component {
  static propTypes = {
    listTransactions: PropTypes.func.isRequired,
    clearTransactions: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      selectedData: this.props.selectedData || {},
      expanded: false,
      isFirstFetch: true,
      appIsMounted: false,
      init: null,
    };
  }

  submit = (values) => {
    this.setState({ expanded: false });
    values.limit = 50;
    values.offset = 0;
    values = _.pickBy(values, (v) => v !== "");
    goToTransactionSearchResult(values, this.state.selectedData);
  };

  setSelectedData = (field, selectedData) => {
    this.setState({
      selectedData: {
        ...this.state.selectedData,
        [field]: selectedData ? selectedData : null,
      },
    });
  };

  componentDidMount() {
    requestAnimationFrame(() => {
      this.setState({ appIsMounted: true });
    });
    if (!(this.props.districts && this.props.districts.length > 0)) {
      // districts is null
      this.props.listDistricts();
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.selectedData !== this.props.selectedData) {
      this.setState({
        selectedData: this.props.selectedData,
        isFirstFetch: true,
      });
    }
  }

  toggleContent = () => {
    this.setState({ expanded: !this.state.expanded, isFirstFetch: false });

    if (this.state.expanded == true) {
      this.setState({
        selectedData: this.props.selectedData,
      });
      this.state.init(this.props.queryvariablesFromUrl);
      // this.props.dispatchSubmitForm();
    }
    window.scrollTo(0, 0);
  };

  onFormInit = (init) => {
    !this.state.init && this.setState({ init });
  };

  render() {
    const { queryvariablesFromUrl } = this.props;
    const { selectedData } = this.state;

    return (
      <div>
        {this.state.appIsMounted && (
          <Layout
            header={<FormattedMessage id="home.transaction" />}
            isAdvanced={true}
            toggleContent={this.toggleContent}
            isExpanded={this.state.expanded}
            isSticky={true}
          >
            <div className={this.state.expanded ? s.root : s.expandedroot}>
              <SearchForm
                onSubmit={this.submit}
                selectedData={selectedData}
                setSelectedData={this.setSelectedData}
                initialValues={queryvariablesFromUrl}
                toggleContent={this.toggleContent}
                expanded={this.state.expanded}
                fromChildToParentCallback={this.onFormInit}
              />
            </div>

            {!this.state.expanded && (
              <TransactionList
                queryvariablesFromUrl={queryvariablesFromUrl}
                selectedDataFromUrl={selectedData}
                expanded={this.state.expanded}
                isFirstFetch={this.state.isFirstFetch}
              />
            )}
          </Layout>
        )}
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  districts: state.district.districts ? state.district.districts : [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    dispatchSubmitForm: () => dispatch(submit("searchForm")),
    listTransactions: (...args) => dispatch(listTransactions(...args)),
    clearTransactions: () => dispatch(clearTransactions()),
    listDistricts: () => dispatch(listDistricts()),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(s)(Transaction));
