import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import DetailTabPanelFrame from "../../../../common/DetailTabPanelFrame";
import MediaMain from "../../../../common/MediaMain";
import { listBuildingMedia } from "../../../../../actions/building";
import { injectIntl } from "react-intl";
import { PERMISSIONS } from "@/constants/auth";
import _ from "lodash";


const styles = theme => ({});
const { VIEW_KOL } = PERMISSIONS;

class Media extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    media: PropTypes.object,
    listingMedia: PropTypes.bool,
    detail: PropTypes.object,
    listed: PropTypes.bool,
    userInfo: PropTypes.object,
    permissions: PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.state = {
      requestMedia: true
    };
  }

  componentDidUpdate() {
    const { detail, listed, listBuildingMedia, userInfo } = this.props;

    if (
      this.state.requestMedia &&
      listed &&
      detail &&
      Number.isInteger(detail.unicornId)
    ) {
      this.setState({ requestMedia: false });
      let variables = {
        sid: [detail.unicornId.toString()],
        empId: userInfo.emp_id
      };
      listBuildingMedia(variables);
    }
  }

  render() {
    const { classes, media, listingMedia, listedMedia, intl, permissions } = this.props;
    const buildingMedia = media?.[0]?.data || {};

    const hasData = Object.keys(buildingMedia).length > 0;
    const PERMISSION_ALLOW_TO_VIEW_KOL = _.get(permissions, VIEW_KOL);
    const groupSections = PERMISSION_ALLOW_TO_VIEW_KOL ? ['document', 'kol_video', 'video', 'photo'] : ['document', 'video', 'photo'];

    return (
      <DetailTabPanelFrame
        hasData={hasData}
        listing={listingMedia}
        listed={listedMedia}
        notFoundText="Building photo not found"
      >
        <MediaMain
          media={buildingMedia}
          mediaType="building"
          defaultExpand={true}
          isGroup
          groupSections={groupSections}
        />
      </DetailTabPanelFrame>
    );
  }
}

const mapStateToProps = state => ({
  userInfo:
    state.auth &&
    state.auth.user &&
    state.auth.user.login &&
    state.auth.user.login.info
      ? state.auth.user.login.info
      : {},
  media: state.building.media ? state.building.media : {},
  listedMedia: state.building.listedMedia ? state.building.listedMedia : false,
  listingMedia: state.building.listingMedia
    ? state.building.listingMedia
    : false,
  detail: state.building.detail ? state.building.detail : {},
  listed: state.building.listedDetail ? state.building.listedDetail : false,
  permissions: _.get(state, "employee.permissions") || {},
});

const mapDispatchToProps = dispatch => {
  return {
    listBuildingMedia: (...args) => dispatch(listBuildingMedia(...args))
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(Media)));
