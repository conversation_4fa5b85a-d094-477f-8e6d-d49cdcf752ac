import {
  LIST_STREETS_START,
  LIST_STREETS_SUCCESS,
  LIST_STREETS_ERROR,
  C<PERSON>AR_STREETS,
  SEARCH_STREETS_START,
  SEARCH_STREETS_SUCCESS,
  SEARCH_STREETS_ERROR,
  LIST_STREET_MEDIA_START,
  LIST_STREET_MEDIA_SUCCESS,
  LIST_STREET_MEDIA_ERROR,
  UPDATE_STREET_MEDIA_BY_INDEX,
  UPDATE_STREET_MEDIA_LIST,
} from "../constants/street";

const initialState = {
  streets: [],
  listed: false,
  listing: false,

  streetSearch: [],
  searching: false,
  searched: false,

  error: null,
};

export default function street(state = initialState, action) {
  switch (action.type) {
    case LIST_STREETS_START:
      return {
        ...state,
        listed: false,
        listing: true,
        districts: [],
        error: null,
      };
    case LIST_STREETS_SUCCESS:
      return {
        ...state,
        listed: true,
        listing: false,
        streets: action.payload.data.data.streets,
      };
    case LIST_STREETS_ERROR:
      return {
        ...state,
        listed: false,
        listing: false,
        error: action.payload.error,
      };
    case CLEAR_STREETS:
      return {
        ...state,
        listed: false,
        listing: false,
        districts: null,
      };

    case SEARCH_STREETS_START:
      return {
        ...state,
        // streetSearch: [],
        searching: false,
        searched: false,
        error: null,
      };
    case SEARCH_STREETS_SUCCESS:
      return {
        ...state,
        streetSearch: action.payload.data.data.streetSearch,
        searching: false,
        searched: true,
      };
    case SEARCH_STREETS_ERROR:
      return {
        ...state,
        streetSearch: [],
        searching: false,
        searched: false,
        error: action.payload.error,
      };
    case LIST_STREET_MEDIA_START:
      return {
        ...state,
        listedMedia: false,
        listingMedia: true,
        media: [],
      };
    case LIST_STREET_MEDIA_SUCCESS:
      return {
        ...state,
        listedMedia: true,
        listingMedia: false,
        media: action.payload.data,
      };
    case LIST_STREET_MEDIA_ERROR:
      return {
        ...state,
        listedMedia: false,
        listingMedia: false,
        errorMedia: action.payload.error,
      };
    case UPDATE_STREET_MEDIA_BY_INDEX: {
      const { index, mediaIndex, mediaType, media } = action.payload;
      if (index === -1 || mediaIndex === -1) {
        return state;
      }

      const newMedia = [...state.media];
      if (newMedia[index] && newMedia[index].data && newMedia[index].data[mediaType]) {
        const mediaArray = [...newMedia[index].data[mediaType]];
        mediaArray[mediaIndex] = {
          ...mediaArray[mediaIndex],
          ...media,
        };
        newMedia[index] = {
          ...newMedia[index],
          data: {
            ...newMedia[index].data,
            [mediaType]: mediaArray,
          },
        };
      }

      return {
        ...state,
        media: newMedia,
      };
    }
    case UPDATE_STREET_MEDIA_LIST:
      if (state.media && state.media.length > 0) {
        const { mediaType, mediaList } = action.payload;
        const newMedia = JSON.parse(JSON.stringify(state.media));
        
        if (newMedia[0] && newMedia[0].data) {
          newMedia[0].data = {
            ...newMedia[0].data,
            [mediaType]: mediaList
          };
          
          return {
            ...state,
            media: newMedia,
            listedMedia: true
          };
        }
      }
      return state;
    default:
      return state;
  }
}
