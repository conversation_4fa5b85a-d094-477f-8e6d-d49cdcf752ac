// ConnectedIntlProvider.js
import React from "react";
import { IntlProvider } from "react-intl";
import { sbu } from "./config";
import COMMlang from "./lang/COMM/messages";
import INDlang from "./lang/IND/messages";
import SHOPSlang from "./lang/SHOPS/messages";

const ConnectedIntlProvider = ({ children, locale, ...props }) => {
  let loadmessages;
  if (sbu == "COMM") {
    loadmessages = COMMlang[locale || "en"];
  } else if (sbu == "IND") {
    loadmessages = INDlang[locale || "en"];
  } else if (sbu == "SHOPS") {
    loadmessages = SHOPSlang[locale || "en"];
  }

  return (
    <IntlProvider
      key={locale}
      locale={locale}
      defaultLocale={locale}
      messages={loadmessages}
    >
      {children}
    </IntlProvider>
  );
};

export default ConnectedIntlProvider;
