import { createMuiTheme } from "@material-ui/core/styles";

const style = {
  root: {
    height: "150vh"
  },
  rightContainer: {
    minHeight: "100%",
    display: "flex",
    flexDirection: "column"
  },
  logoBox: {
    height: "10vh",
    padding: "1.5vh 0",
    backgroundColor: "#FFE401",
    display: "flex",
    alignItems: "center",
    justifyContent: "center"
  },
  logo: {
    maxWidth: "100%",
    maxHeight: "100%"
  },
  image: {
    backgroundImage:
      "url(https://www.midlandici.com.hk/ics/property/img/mr_ici_header_logo.svg)",
    backgroundRepeat: "no-repeat",
    backgroundPosition: "center"
  },
  formContainer: {
    padding: "2vh 7vw",
    flex: 1,
    background: "#fff"
  },
  text: {
    fontSize: "1.125em",
    fontWeight: "bold",
    textAlign: "center",
    margin: "0.5vh 0 2.5vh 0"
  },
  dialogroot: {
    padding: "40px 0"
  },
  form: {
    margin: "1vh 0"
  },
  submit: {
    fontSize: "1.125rem"
  },
  resend: {
    // backgroundColor: "#6BE6A1"
    "&:disabled": {
      backgroundColor: "#6BE6A1",
      color: "#fff"
    }
  },
  counter: {
    fontSize: "0.75em"
  },
  btnRow: {
    marginTop: "2vh",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    "& > *:not(:last-child)": {
      marginRight: "3vw"
    }
  },
  circularLoader: {
    textAlign: "center",
    marginTop: "2vh",
    "& > div": {
      // color: "#AAA"
    }
  },
  banner: {
    width: "100%",
    margin: "0 auto",
    display: "block",
    position: "fixed",
    bottom: "0",
    zIndex: "500"
  },
  font18: {
    fontSize: "1.125em",
    textAlign: "center"
  },
  dialogbtnContainer: {
    margin: "0",
    display: "flex",
    padding: "2vh 0",
    justifyContent: "center"
  }
};

const theme = createMuiTheme({
  overrides: {
    MuiOutlinedInput: {
      root: {
        "& $notchedOutline": {
          borderColor: "#FFE401"
        },
        "&:hover $notchedOutline": {
          borderColor: "#FFE401"
        },
        "&.Mui-focused $notchedOutline": {
          borderColor: "#FFE401"
        }
      }
    },
    MuiFormLabel: {
      root: {
        color: "#000",
        "&.Mui-focused": {
          color: "#000"
        }
      }
    }
  }
});

export { style, theme };
