/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import UniversalRouter from "universal-router";
import routes from "../routes";
import { sbu } from "../config";
import MobileDetect from "mobile-detect";

function customRouter(usageAgent) {
  console.log(usageAgent)
  return new UniversalRouter(routes[usageAgent][sbu], {
    // baseUrl: usageAgent == "desktop" ? "/d" : "/m",
    resolveRoute(context, params) {
      if (typeof context.route.load === "function") {
        return context.route
          .load()
          .then((action) => action.default(context, params));
      }
      if (typeof context.route.action === "function") {
        return context.route.action(context, params);
      }
      return undefined;
    },
  });
}

export default customRouter;
