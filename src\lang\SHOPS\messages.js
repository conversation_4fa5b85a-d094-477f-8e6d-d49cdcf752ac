export default {
  en: {
    // common
    "common.ok": "OK",
    "common.yes": "Yes",
    "common.no": "No",
    "common.cancel": "Cancel",
    "common.cancel2": "Cancel",
    "common.confirm": "OK",
    "common.remove": "Remove",
    "common.error": "Error",
    "common.timeout.error": "Request timeout.",
    "common.disconnect.error": "Network disconnected.",
    "common.refresh": "Refresh",
    "common.industrial": "Industrial",
    "common.office": "Office",
    "common.shop": "Shop",
    "common.commercial": "Commercial",
    "common.include": "Include",
    "common.exclude": "exclude",
    "common.lb": "Lbs",
    "common.lbpersqft": "Lbs/SqFt",
    "common.sqft": "SqFt",
    "common.qtr": "Qtr",
    "common.month": "Month",
    "common.sy": "SY",
    "common.more": "More",
    "common.na": "N/A",
    "common.from": "From",
    "common.to": "To",
    "common.inputs.cannotBeEmpty": "Mandatory field cannot be empty.",
    "common.field.required": "{field} cannot be empty",

    // login
    "login.message.confirmation": "4-digit PIN has been sent via SMS.",
    "login.input.department": "Department",
    "login.input.user": "User",
    "login.input.password": "Password",
    "login.button.sendpin": "Send PIN",
    "login.button.resend": "Re-send PIN",
    "login.button.signin": "Sign In",
    "login.message.sms": "MSearch 搵盤易 (測試版) 登入 PIN: ",
    "login.message.welcome": "Welcome to Msearch 搵盤易",
    "login.message.welcomeuat": "Welcome to MSearch 搵盤易 (Testing)",
    "login.message.clicktoprod":
      "If you would like to go to Official version, please click ",
    "login.message.prod": "here",
    "login.message.wrongcredential": "Login is invalid.",
    "login.message.wrongpin": "PIN is invalid.",

    // search
    "search.header.status": "Status",
    "search.header.marketable": "Marketable",
    "search.header.stock": "Stock",
    "search.header.shared": "Shared",
    "search.header.miscellaneous": "Miscellaneous",
    "search.header.stockid": "Stock ID",
    "search.header.parity": "Street Type",
    "search.header.consolidate": "Consolidate",
    "search.form.common.min": "Min",
    "search.form.common.max": "Max",
    "search.form.ageMin": "Age (min)",
    "search.form.ageMax": "Age (max)",
    "search.form.areaMin": "Area (min)",
    "search.form.areaMax": "Area (max)",
    "search.form.avgpriceMin": "Avg Price (min)",
    "search.form.avgpriceMax": "Avg Price (max)",
    "search.form.avgrentMin": "Avg Rent (min)",
    "search.form.avgrentMax": "Avg Rent (max)",
    "search.form.totalpriceMin": "Total Price (min)",
    "search.form.totalpriceMax": "Total Price (max)",
    "search.form.totalrentMin": "Total Rent (min)",
    "search.form.totalrentMax": "Total Rent (max)",
    "search.form.createdateMin": "Create Date (min)",
    "search.form.createdateMax": "Create Date (max)",
    "search.form.lastpdatedateMin": "Last Update Date (min)",
    "search.form.lastpdatedateMax": "Last Update Date (max)",
    "search.form.tenancyexpiredateMin": "Tenancy Expire Date (min)",
    "search.form.tenancyexpiredateMax": "Tenancy Expire Date (max)",
    "search.form.building": "Building",
    "search.form.district": "District",
    "search.form.street": "Street",
    "search.form.streetnumber": "Street Number",
    "search.form.select": "Select..",
    "search.form.streetMin": "St no. (min)",
    "search.form.streetMax": "St no. (max)",
    "search.form.floorMin": "Floor(min)",
    "search.form.floorMax": "Floor (max)",
    "search.form.grade": "Grade",
    "search.form.usage": "Usage",
    "search.form.ownertype": "Owner Type",
    "search.form.possession": "Possession",
    "search.form.currentState": "Current State",
    "search.form.inspection": "Inspection",
    "search.form.decoration": "Decoration",
    "search.form.view": "View",
    "search.common.floor": "Floor",
    "search.common.stage": "Stage",
    "search.common.block": "Block",
    "search.common.unit": "Unit",
    "search.common.rent": "Rent",
    "search.common.price": "Price",
    "search.common.area": "Area",
    "search.common.age": "Age",
    "search.common.createdate": "Create Date",
    "search.common.lastupdatedate": "Last Update Date",
    "search.common.tenancy": "Tenancy Expire",
    "search.common.date": "Date",
    "search.common.pricetotal": "Total",
    "search.common.priceavg": "Avg",
    "search.common.renttotal": "Total",
    "search.common.rentavg": "Avg",
    "search.unlockMessage": "View the selected stock?",
    "search.todayQuota": "Today Quota",
    "search.unlock.exceed": "Today Quota Exceeded",
    "search.button.view": "View",
    "search.button.search": "Search",
    "search.button.clear": "Clear",
    "search.button.save": "Save Criteria",
    "search.button.load": "Load Criteria",
    "search.save.name": "Search Criteria Name",
    "search.save.savesuccess": "Search criteria has been saved successfully.",
    "search.save.modifysuccess":
      "Search criteria has been edited successfully.",
    "search.stocklist.count": "{resultStr} found",
    "search.noresult": "No results found",
    "search.new": "New",
    "search.mktornew": "Mkt/New",
    "search.status.sale": "Sale",
    "search.status.lease": "Lease",
    "search.status.saleslease": "Tenanted",
    "search.status.salesandlease": "Sale+Lease",
    "search.status.pending": "Pending",
    "search.status.tel": "TEL",
    "search.status.se": "Search",
    "search.status.dontcall": "Don't Call",
    "search.status.fail": "Fail",
    "search.status.history": "History",
    "search.status.cancel": "Cancel",
    "search.status.leased": "Leased",
    "search.status.sold": "Sold",
    "search.status.cu": "CU",
    "search.form.all": "All",
    "search.form.cancel.all": "Cancel All",
    "search.form.mark.all": "Mark All",
    "search.form.unmark.all": "Unmark All",
    "search.form.none": "None",
    "search.form.invalidinput": "Input format is invalid",
    "search.form.type": "Type",
    "search.form.withkey": "With Key",
    "search.form.withoutkey": "Without Key",
    "search.form.advsconsent": "Adv.Consent",
    "search.form.inwater": "In Water",
    "search.form.outwater": "Out Water",
    "search.form.towngas": "Town Gas",
    "search.form.confirmor": "Confirmor",
    "search.form.saleequity": "Sale Equity",
    "search.form.passcode": "Passcode",
    "search.form.parentstock": "ParentStock",
    "search.form.childstock": "ChildStock",
    "search.form.industrial": "Industrial",
    "search.form.godown": "Godown",
    "search.form.io": "I/O",
    "search.form.hotel": "Hotel",
    "search.form.coldstorage": "Cold Storage",
    "search.form.singleowner": "Single Owner",
    "search.form.investor": "Investor",
    "search.form.stratatitle": "Strata Title",
    "search.form.cooperation": "Cooperation",
    "search.form.mortgagee": "Mortgagee",
    "search.form.developer": "Developer",
    "search.form.handover": "HandOver",
    "search.form.carpark": "Car Park",
    "search.form.groundshop": "Groud Shop",
    "search.form.wholeblock": "Whole Block",
    "search.form.unstarisshop": "Upstairs Shop",
    "search.form.basement": "Basement",
    "search.form.site": "Site",
    "search.form.cabinet": "Cabinet",
    "search.form.tradeandindustry": "Trade and Industry",
    "search.form.shoppingmallstock": "Mall",
    "search.form.singlesidestock": "Single Side",
    "search.form.frontandrearportion": "Front&Rear",
    "search.form.soleagentsale": "SoleA.Buy",
    "search.form.soleagentlease": "SoleA.Rent",
    "search.form.soleagentexpired": "Ed SA",
    "search.form.soleagentsoleexpired": "Sole Exp",
    "search.form.soleagentrentexpired": "SoleRentExp",
    "search.form.eaaBuy": "EAA(Buy)",
    "search.form.eaaRent": "EAA(Rent)",
    "search.form.eaaExpired": "EAA(Exp)",
    "search.form.proposal": "Proposal",
    "search.form.surveryorPP": "SurveyorPP",
    "search.form.pdfPP": "PDF PP",
    "search.form.vr": "VR",
    "search.form.haveStockVideo": "StockVideo",
    "search.form.haveBuildingVideo": "Build.Video",
    "search.form.eaa": "EAA",
    "search.form.landSearch": "Search",
    "search.form.privacylimit": "Privacy",
    "search.form.vacant": "Vacant",
    "search.form.immediatevacant": "Immediate Vacant",
    "search.form.negovacant": "Nego. Vacant",
    "search.form.vacantpossession": "Vacant Possession",
    "search.form.vacanttransaction": "Vacant Transaction",
    "search.form.salwwithta": "Sale With TA",
    "search.form.tenant": "Tenant Occupies",
    "search.form.occupied": "Owner Occupies",
    "search.form.construction": "Construction",
    "search.form.incomplete": "Incomplete",
    "search.form.saleandleaseback": "Sale & Leaseback",
    "search.form.datasource": "Data Source",
    "search.card.gross": "G",
    "search.card.shop": "Shop",
    "search.card.expireat": "Expire at",
    "search.checkbox.retainMarkedRecord": "Retain Marked Records",
    "search.checkbox.uncheckMarkedRecord":
      'Uncheck "Retain Marked Records" will remove all marked records and cannot be recovered. Confirm to uncheck?',

    "search.marketable.lease": "Mkt. Lease",
    "search.marketable.sale": "Mkt. Sale",
    "search.marketable.salelease": "Sale/Lease",
    "search.form.board": "Board",
    "search.form.news": "Adv.",
    "search.form.pricechange": "Price Change",
    "search.form.odd": "Odd",
    "search.form.even": "Even",
    "search.form.canthang": "Can't hang",
    "search.form.noboard": "No Board",
    "search.form.poster": "Poster",
    "search.form.noadv": "No Adv.",
    "search.form.cantposter": "No Poster",
    "search.form.cantadv": "Can't Adv.",
    "search.form.email": "Email",
    "search.form.company": "Company",
    "search.form.owner": "Owner",

    // stock
    "stock.stocktype": "Stock Usage",
    "stock.ownertype": "Owner Type",
    "stock.airconditioningfee": "A/C Fee",
    "stock.includeac": "Including A/C",
    "stock.paidbytenant": "Paid By Tenant",
    "stock.paidbylandlord": "Paid By Landlord",
    "stock.usage": "Usage",
    "stock.source": "Source",
    "stock.possession": "Possession",
    "stock.currentState": "Current State",
    "stock.view": "View",
    "stock.mgtfee": "Management Fee",
    "stock.totalMonthlyMgtfee": "Total Monthly Mgt Fee",
    "stock.availability": "Availability",
    "stock.decoration": "Decoration",
    "stock.keynumpw": "Key No./ Password",
    "stock.rates": "Rates",
    "stock.grent": "G Rent",
    "stock.cleaningfee": "Cleaning Fee",
    "stock.promotefee": "Promotion Fee",
    "stock.completion": "Completion",
    "stock.inspection": "Inspection",
    "stock.newrecorddate": "Create Date",
    "stock.updatedate": "Update Date",
    "stock.commission": "Commission",
    "stock.comment": "Comment",
    "stock.extracharge": "Extra Charges",
    "stock.contact": "Contact",
    "stock.vendor": "Vendor",
    "stock.tenancy": "Tenancy",
    "stock.rentfree": "Rent Free",
    "stock.negotiable": "Negotiable",
    "stock.nodata": "No data",
    "stock.yes": "Yes",
    "stock.no": "No",
    "stock.freeperiod": "Free Period",
    "stock.leaseperiod": "Lease Term",
    "stock.leaseperiodfreetext": "Lease Term (Free Text)",
    "stock.deposit": "Deposit",
    "stock.option": "Option",
    "stock.rent": "Rent",
    "stock.newrental": "New Rental",
    "stock.premium": "Permium",
    "stock.breaklease": "Break Lease",
    "stock.prepay": "Prepay",
    "stock.advancetenancy": "Advance ",
    "stock.currenttenancy": "Current ",
    "stock.previoustenancy": "Previous ",
    "stock.formertenancy": "Former ",
    "stock.tenant": "{status}Tenant",
    "stock.unit": "Unit",
    "stock.floor": "Floor",
    "stock.shopnumber": "Shop No.",
    "stock.tenancyperiod": "Tenancy Period",
    "stock.latestrent": "Rent",
    "stock.company": "Company",
    "stock.industry": "Industry",
    "stock.shoparea": "Shop Area",
    "stock.tenancy.increment": "increment",
    "stock.tenancy.marketrate": "At market rate",
    "stock.tenancy.proportion": "In proportion",
    "stock.tenancy.fix": "Fixed",
    "stock.tenancy.notgreater": "Not greater than",
    "stock.tenancy.notless": "Not less than",
    "stock.tenancy.option": "Option",
    "stock.tenancy.option1": "Option (1)",
    "stock.tenancy.option2": "Option (2)",
    "stock.tenancy.rent1": "Rent (1)",
    "stock.tenancy.rent2": "Rent (2)",
    "stock.tenancy.fixed": "Fixed",
    "stock.tenancy.unknown": "Unknown",
    "stock.tenancy.nego": "Nego",
    "stock.eaarecord": "EAA Records",
    "stock.effectivedate": "Effective date",
    "stock.handledby": "Consultant",
    "stock.remarks": "Remarks",
    "stock.surroundings": "Surroundings",
    "stock.landlordprovisions": "Landlord Provisions",
    "stock.internalremarks": "Internal Remarks",
    "stock.ceilingheight": "Ceiling Height",
    "stock.entrancewidth": "Entrance Width",
    "stock.airconditioningtype": "Air-Con Type",
    "stock.unitdepth": "Shop Depth",
    "stock.oddeven": "Odd/Even",
    "stock.backdoor": "Back Door",
    "stock.washroom": "Washroom",
    "stock.sign": "Sign",
    "stock.facilities": "Facilities",
    "stock.terrace": "Terrace",
    "stock.terrace.full": "Flat Roof",
    "stock.roof": "Roof",
    "stock.cockloft": "Cockloft",
    "stock.carpark": "Carparks",
    "stock.carparkNo": "Carpark No.",
    "stock.consultantshare": "Consultant Share",
    "stock.date": "Date",
    "stock.general": "General",
    "stock.updatehistory": "Update History",
    "stock.media": "Media",
    "stock.googlemap": "Google Map",
    "stock.map": "Map",
    "stock.photo": "Photo",
    "stock.saleskit": "Sales Kit",
    "stock.applySearch": "Apply Search",
    "stock.video": "Video",
    "stock.hand": "hand",
    "stock.photo.approval": "Personal Photo",
    "stock.photo.layout": "Layout",
    "stock.photo.interior": "Interior",
    "stock.photo.plan": "Plan",
    "stock.photo.lobby": "Lobby",
    "stock.photo.lift": "Lift",
    "stock.photo.entrance": "Entrance",
    "stock.photo.positionplan": "Position Plan",
    "stock.photo.floorplan": "Floor Plan",
    "stock.photo.areaplan": "Area Plan",
    "stock.photo.buildinginfo": "Building Info",
    "stock.photo.gfphoto": "G/F Photo",
    "stock.photo.ozp": "OZP",
    "stock.video.approval": "Personal Video",
    "stock.video.processing": "Video is being processed. Please refresh later.",
    "stock.bottom": "Bottom",
    // "stock.selectphoto": "Select Photo/Video",
    "stock.selectphoto": "Select Photo",
    "stock.selectvideo": "Select Video",
    "stock.selectdocument": "Select Document",
    "stock.selectkolcoverpicture": "Select Cover Picture",
    "stock.selectkol": "Select KOL",
    "stock.kol": "KOL Videos",
    "stock.kol.approve": "Approve",
    "stock.video.upload.restrictions": "Video Restrictions:<br/>Length: min. 30 sec and max. 10 mins<br/>Ratio: 16:9 landscape<br/>Min. Resolution: 1080p<br/>No background music<br/>No company logo<br/>Cover Image: 16:9, format: JPEG, JPG, HEIC, PNG<br/>Video format: MOV (iPhone), MP4 (Android)",
    "stock.kol.upload.restrictions": "Video Restrictions:<br/>Agent show up in video (KOL)<br/>Length: min. 30 sec and max. 10 mins<br/>Ratio: 16:9 landscape<br/>Min. Resolution: 1080p<br/>No background music<br/>No company logo<br/>Cover Image: 16:9, format: JPEG, JPG, HEIC, PNG<br/>Video format: MOV (iPhone), MP4 (Android)",
    "stock.kol.upload.uploadfail": "KOL video upload failed due to not match with restrictions:<br/><ul style='text-align: left;'><li>16:9 Horizontal</li><li>3-10 minutes</li><li>No background music in the video</li></ul>",
    "stock.kol.thumbnail.upload.uploadfail": "Thumbnail image upload failed: at least 1920*1080",
    "stock.kol.thumbnail.upload.null": "Thumbnail image cannot be null",
    "stock.kol.description": "Description",
    "stock.kol.characteristicZh": "Characteristic",
    "stock.kol.characteristicEn": "Characteristic(Eng)",
    "stock.kol.bgm": "Background Music",
    "stock.kol.isShowContact": "Add agent contact",
    "stock.kol.processed": "Processed",
    "stock.kol.unprocessed": "Unprocessed",
    "stock.upload.all": "Upload",
    "stock.upload": "Upload Media",
    "stock.upload.kol": "Upload KOL",
    "stock.info": "Info",
    "stock.photocontent": "Photo Content",
    "stock.videocontent": "Video Content",
    "stock.documentcontent": "Document Content",
    "stock.owner": "Owner",
    "stock.photo.uploadsuccess": "Media has been uploaded successully.",
    "stock.kol.uploadsuccess": "KOL video uploaded successfully, waiting for approval",
    "stock.photo.uploadfail": "Failed to upload media",
    "stock.photo.uploadformatmsg":
      "Only some photo formats can be selected. (.jpg, .jpeg, .png, .mp4, .mov, .pdf)",
    "stock.photo.uploadfailmsg": "Some errors occurred",
    "stock.photo.removesuccess": "Photo/Video has been removed successully.",
    "stock.photo.removemessage": "Remove the Photo/Video?",
    "stock.upload.message": "Upload (Private)",
    "stock.upload.topublicmessage": "Upload (Score)",
    "stock.uploadvideo.message": "Upload Video",
    "stock.uploadphoto.message": "Upload Photo",
    "stock.uploaddocument.message": "Upload Document",
    "stock.photo.maintag": "Main",
    "stock.photo.name": "Name",
    "stock.photo.content": "Photo content",
    "stock.area.total": "Total",
    "stock.area.site": "Site Area",
    "stock.area.gross": "G",
    "stock.area.net": "Net",
    "stock.area.verified": "V",
    "stock.noupdatehistory": "No update history found",
    "stock.yield": "Yield",
    "stock.facingto": "Facing To",
    "stock.singlesidetype": "Single Side Type",
    "stock.surveyorproposalstatus": "Surveyor Proposal",
    "stock.surveyorproposaldate": "Surveyor Proposal Date",
    "stock.years": " Years",
    "stock.months": " Months",
    "stock.days": " Days",
    "stock.building": "Building",
    "stock.stockcreate": "Create",
    "stock.stockupdate": "Update",
    "stock.transaction.null": "---",
    "stock.transaction.real": "Real",
    "stock.transaction.marketinfo": "Market Info",
    "stock.transaction.more": "More",
    "stock.boardorposter": "Board / Poster",
    "stock.boardstatus": "Board Status",
    "stock.boarddate": "Board Date",
    "stock.boarddescription": "Board Details",
    "stock.posterstatus": "Adv. Status",
    "stock.posterdate": "Poster Date",
    "stock.posterdescription": "Poster Details",
    "stock.parity.odd": "Odd",
    "stock.parity.even": "Even",
    "stock.parity.all": "All",
    "stock.big": "Big",
    "stock.small": "Small",
    "stock.cockloft.authorized": "Authorized",
    "stock.cockloft.selfbuilt": "Self-built",
    "stock.cockloft.authorizedandselfbuilt": "Auth/Self",
    "stock.numberofshop": "Shop No.",
    "stock.number": "No.",
    "stock.sold": "Sold",
    "stock.leased": "Leased",
    "stock.currenttenant": "Current Tenant",
    "stock.numbertype.office": "Office",
    "stock.numbertype.direct": "Direct",
    "stock.numbertype.residential": "Residential",
    "stock.numbertype.mobile": "Mobile",
    "stock.numbertype.pager": "Pager",
    "stock.numbertype.fax": "Fax",
    "stock.numbertype.x": "X",
    "stock.numbertype.unknown": "Unknown",
    "stock.numbertype.na": "N/A",
    "stock.numbertype.doNotContact": "Opt-Out List",
    "stock.numbertype.privacy": "Privacy",
    "stock.vendor.message.deniedCallBothHtml": "This number is in Opt-Out List.<br/ >此為拒絕服務名單中的號碼。",
    "stock.landsearch": "Land Search",
    "stock.landsearch.download": "Download",
    "stock.landsearch.files": "Files",
    "stock.landsearch.file": "File",
    "stock.landsearch.noFileMsg": "No Search files is available for download.",
    "stock.landsearch.null": "---",
    "stock.landsearchsearch": "Land Search",
    "landSearch.list.count": "{resultStr} found",
    "stock.landsearchsearch.memorial": "Memorial",
    "stock.landsearchsearch.occupationpermit": "Occupation Permit",
    "stock.landsearchsearch.governmentlease": "Government Lease",
    "stock.landsearchsearch.conditionandnewcrown": "Condition/New Crown",
    "stock.consosearch": "Conso. Search",
    "stock.dmoMsg": "This number is in Do Not Call list.",

    // apply search
    "stock.applySearch.applicant": "Applicant",
    "stock.applySearch.contactPhone": "Contact Phone",
    "stock.applySearch.searchType": "Search Type",
    "stock.applySearch.landSearch": "Land Search",
    "stock.applySearch.landItem": "Item",
    "stock.applySearch.propertyId": "Property No.",
    "stock.applySearch.address": "Address",
    "stock.applySearch.shopno": "Shop No.",
    "stock.applySearch.floor": "Floor",
    "stock.applySearch.confirm": "Confirm",
    "stock.applySearch.opType": "Occupation Permit Type",
    "stock.applySearch.memorialNo": "Memorial No.",
    "stock.applySearch.filmType": "Film Type",
    "stock.applySearch.landSearchItem": "Search Item",
    "stock.applySearch.ddType": "DD/Lot Type",
    "stock.applySearch.ddNumber": "DD/Lot No.",
    "stock.applySearch.conditionalNo": "Conditional No./New Grant No.",
    "stock.applySearch.submitSuccess":
      "Land Search application has been submitted successfully.",

    // building
    "building.header.id": "Building ID: ",
    "building.searchstock": "Search Stock",
    "building.mtr": "MTR",
    "building.competition": "Competition",
    "building.accharge": "AC Charge",
    "building.developer": "Developer",
    "building.mgtcompany": "Management Company",
    "building.mgtfeeupdatedate": "Manangement Fee Update Date",
    "building.phone": "Phone",
    "building.structure": "Structure",
    "building.lowfloor": "Low-Floor",
    "building.midfloor": "Mid-Floor",
    "building.highfloor": "High-Floor",
    "building.lobby": "Lobby",
    "building.wall": "Wall",
    "building.ceiling": "Ceiling",
    "building.totalfloor": "Total Floor",
    "building.lifts": "Lifts",
    "building.passengerlift": "Passenger Lifts",
    "building.cargolift": "Cargo Lifts",
    "building.carlift": "Car Lifts",
    "building.otherlift": "Other Lifts",
    "building.container": "Container",
    "building.loadingbay": "Loading Bay",
    "building.efficiency": "Efficiency",
    "building.carpark": "Carpark",
    "building.hourlyparking": "Hourly Parking",
    "building.monthlyparking": "Monthly Parking",
    "building.ceilinght": "Ceiling Ht",
    "building.loadingkpa": "Loading (KPA)",
    "building.airconditioning": "Air Conditioning",
    "building.airconditioningtime": "Air Conditioning Time",
    "building.extracharges": "Extra Charges",
    "building.extrainfo": "Extra Information",
    "building.liftzone": "Lift Distribution",
    "building.surroundings": "Surroundings",
    "building.substreet": "Side Street",
    "building.previousname": "Previous Name",
    "building.previousname.chi": "Previous Name (Chi)",
    "building.previousname.eng": "Previous Name (Eng)",

    // home
    "home.welcome": "Hi",
    "home.profileLink": "My www Stock",
    "home.quotaText": "Today Quota",
    "home.stock": "Stock",
    "home.transaction": "Transaction",
    "home.company": "Company",
    "home.companySearch": "Company Search",
    "home.help": "Help",
    "home.publicmessage": "Message",
    "home.signout": "Sign Out",
    "home.viewedstock": "Viewed Stock",
    "home.search": "Stock Search",
    "home.myfavorite": "My Favorite",
    "home.shelf": "大手推介",
    "home.firstHandStock": "一手開單易",
    "home.kol.video": "KOL Video",
    "home.approve.media": "Approve Media",
    "home.report": "Large-Amount Asset Tx Report",
    "home.wwwStock": "My www Stock",
    "report.report": "Large-Amount Asset Tx Report",

    // message center
    "publicmessage.header": "Public Message",
    "publicmessage.public": "Public Message",
    "publicmessage.private": "Private Message",

    // help page
    "tips.tutorial": "Tutorial",
    "tips.tutorial.basicfunction": "Basic Function",
    "tips.tutorial.otherfunction": "Proposal, Save Search, My Favourite",
    "tips.notsupport": "Your browser does not support the video tag.",
    "tips.usermanual": "User Manual",
    "tips.stocktags": "Stock Tags",
    "tips.priceformat": "Format of Price/Rent Shown in Result Pages",
    "tips.photoformat.header": "Photo Upload Format",
    "tips.photoformat.content": ".jpg, .jpeg, .png, .mp4",
    "tips.chinesesearch.header": "Search Stock in Chinese",
    "tips.chinesesearch.content1":
      "Enter the building, district and street in Chinese",
    "tips.chinesesearch.content2": "Press ‘space’ to find the options",
    "tips.stockpricesign.header": "Stock Price Sign",
    "tips.stockpricesign.up": "Recently increased",
    "tips.stockpricesign.down": "Recently decreased",
    "tips.defaultsorting.header": "Default Sorting Sequence",
    "tips.defaultsorting.content1":
      "Default search sequence in ascending order",
    "tips.defaultsorting.content2": "{field} can be sorted in descending order",
    "tips.pin.header": "PIN is also sent to 開單易",
    "tips.pin.content1": "Enter login details in MSearch login page",
    "tips.pin.content2": "Click ‘Send PIN’",
    "tips.pin.content3": "Open ‘開單易’",
    "tips.pin.content4": "Go to ‘個人訊息’",
    "tips.pin.content5": "Go to ‘系統訊息’ to get the 4 digit PIN",
    "tips.pin.content6": "Enter the PIN in login page",
    "tips.pricerentsorting.header": "Sorting by Price/Rent",
    "tips.pricerentsorting.content1":
      "Stocks without price/rent are at the end of the result list",
    "tips.advancedsearch.header": "Advanced Search",
    "tips.advancedsearch.content1": "More criteria for searching stock",
    "tips.advancedsearch.content2": "Multiple Building, District and Street",

    "tips.addtohomescreen.header": "Add to HOME Screen",
    "tips.addtohomescreen.content1": "Click ‘Browser Setting’",
    "tips.addtohomescreen.content2": "Android: Top right-hand corner",
    "tips.addtohomescreen.content3": "Iphone: Bottom bar",
    "tips.addtohomescreen.content4": "Samsung: Bottom bar",
    "tips.addtohomescreen.content5": "Select ‘Add to Home Screen’",
    "tips.addtohomescreen.content6": "App Icon will be shown in HOME screen",

    "tips.dailyquota.header": "Daily Quota",
    "tips.dailyquota.content1": "User can view {quota} stocks per day",
    "tips.dailyquota.content2":
      "Click ‘Quota button’ in HOME page can find the viewed list",
    "tips.dailyquota.content3":
      "Quota and viewed list will be reset on the next day",

    "tips.updatepassword.header": "Update Password",
    "tips.updatepassword.content1": "Update the password in Intranet if needed",
    "tips.updatepassword.content2":
      "Highly recommended to use a strong password",

    "tips.stocktag.sole": "Sole Agent",
    "tips.stocktag.equaity": "Sale Equity",
    "tips.stocktag.carpark": "Car Park",
    "tips.stocktag.mortgagee": "Mortgagee",
    "tips.stocktag.eaa": "With Signed EAA form",
    "tips.stocktag.new": "New created stock within 14 days",
    "tips.stocktag.www": "Marked for WWW stock",
    "tips.stocktag.soleagentbuy": "Sole Agent Buy",
    "tips.stocktag.soleagentrent": "Sole Agent Rent",
    "tips.stocktag.shoppingmall": "Shopping Mall Stock",
    "tips.stocktag.singleside": "Single Side Stock",
    "tips.stocktag.frontandrear": "Front and Rear",
    "tips.stocktag.boardhanged": "With Board",
    "tips.stocktag.replacement": "Replacement",

    // tags in PropertyTagBar
    "stock.tag.sole": "Sole",
    "stock.tag.equaity": "EQ",
    "stock.tag.carpark": "Car",
    "stock.tag.mortgagee": "Mortgagee",
    "stock.tag.eaa": "EAA",
    "stock.tag.new": "NEW",
    "stock.tag.www": "WWW",
    "stock.tag.terrace": "FR",
    "stock.tag.roof": "RF",
    "stock.tag.cockloft": "MF",
    "stock.tag.marketable": "Marketable",

    "stock.tag.key": "Key",
    "stock.tag.frontandrear": "Front&Rear",
    "stock.tag.shoppingmall": "Mall",
    "stock.tag.singleside": "SSS",
    "stock.tag.pending": "PD",
    "stock.tag.boardhanged": "BD",
    "stock.tag.soleagentrent": "SR",
    "stock.tag.soleagentbuy": "SB",
    "stock.tag.replacement": "RE",
    "stock.tag.haveSurverorPP": "S. PP",

    "search.form.transferByCompany": "TransByCo.",
    "stock.advertisment.isDoNotAllowBoard": "No Board",
    "stock.advertisment.isDoNotAllowPoster": "No Poster",
    "stock.advertisment.isDoNotAllowNewspaper": "No News",
    "stock.advertisment.isNoAds": "No WWW",
    "search.form.bigsinglesidestock": "SingleSideL",
    "search.form.smallsinglesidestock": "SingleSideS",
    "stock.inwater": "In Water",
    "stock.outwater": "Out Water",
    "stock.towngas": "Town Gas",
    "stock.LPower": "L Power",
    "stock.licensed": "Licensed",
    "stock.withCarpark": "With Carpark",
    "stock.with24hAC": "24h AC",
    "stock.is24hEnter": "24h Enter",
    "stock.saleWithLicenses": "Sale with licenses",
    "stock.isFactory": "Factory",
    "stock.isIncludeWaterElec": "Inclu. Water/Electric",
    "stock.bizTools": "Biz Equip",
    "stock.toilet": "Pri Toilet",
    // -------------------- tags end -------------------

    // company
    "company.search.companyName": "Company Name",
    "company.list.count": "{count} companies found",
    "company.list.apply": "Apply Com Search",
    "company.sorter.companyNameZh": "Name(Chi)",
    "company.sorter.companyNameEn": "Name(Eng)",
    "company.sorter.rightPerson": "決策人",
    "company.sorter.updateDate": "Update Date",
    "company.detail.clientId": "Client ID",
    "company.detail.source": "Source",
    "company.detail.createdBy": "Created By",
    "company.detail.updatedBy": "Updated By",
    "company.detail.nextUpdate": "Next Update",
    "company.detail.holdingCompanyZh": "Holding Company(Chinese)",
    "company.detail.holdingCompanyEn": "Holding Company(English)",
    "company.detail.business": "Business",
    "company.detail.BR": "BR#",
    "company.detail.district": "District",
    "company.detail.URL": "URL",
    "company.detail.addressZh": "Address(Chinese)",
    "company.detail.addressEn": "Address(English)",
    "company.detail.remarks": "Remarks",
    "company.detail.contact": "Contact",
    "company.detail.phoneLabel.privacy": "Privacy",
    "company.detail.phoneLabel.doNotContact": "Opt-Out List",
    "company.detail.companyType": "Company Type",
    "company.detail.companyNameEn": "Company Name (English)",
    "company.detail.companyNameZh": "Company Name (Chinese)",
    "company.detail.companyAddressEn": "Company Address (Eng)",
    "company.detail.companyAddressZh": "Company Address (Chi)",
    "company.detail.rightPerson": "話事人",
    "company.detail.directorName": "公司董事名稱",
    "company.detail.directorAddress": "董事申報地址",
    "company.detail.directorCompanyAddress": "董事所佔股份公司註冊地址",
    "company.detail.companyPhoneNumber": "Company Phone",
    "company.card.createDate": "Create Date {date}",
    "company.card.updateDate": "Update Date {date}",
    "company.tab.generalInfo": "General",
    "company.tab.applySearch": "Apply Search (This Com)",
    "company.section.search": "Company Search",
    "company.search.count": "{resultStr} found",
    "company.search.applicant": "Applicant: {applicant}",
    "company.applySearch.companyName": "公司名稱/董事名稱",
    "company.applySearch.companyId": "公司編號/身份證編號",
    "company.applySearch.declarations1": "聲明1",
    "company.applySearch.declarations2": "聲明2",
    "company.applySearch.atLeastCheckOne": "最少要勾選一項。",
    "company.applySearch.atLeastInputOne": "最少要輸入一項。",
    "company.applySearch.mustInput": "請輸入",
    "company.applySearch.success":
      "Company Search application has been submitted successfully.",

    // proposal
    "proposal.proposal": "Proposal",
    "proposal.indvProposal": "Indv. PP",
    "proposal.listProposal": "List PP",
    "proposal.listProposal.indv": "Indv. format",
    "proposal.create": "Create List PP",
    "proposal.listProposal.create": "List PP",
    "proposal.indvProposal.create": "Indv. PP",
    "proposal.listProposal.clearPPStocks": "Clear PP Stocks",
    "proposal.listProposal.clearPPStocksConfirm":
      "Confirm to remove all marked records?",
    "proposal.listProposal.emptyMarkStocks": "No marked stocks",
    "proposal.section.ppOptions": "PP Options",
    "proposal.section.stock": "Stock",
    "proposal.section.stockList": "Stock List",
    "proposal.form.stockinfo": "Stock Info",
    "proposal.form.otherinfo": "Other Info",
    "proposal.form.showemployeephoto": "Employee Photo",
    "proposal.form.showcontact": "Contact",
    "proposal.form.showMainPhoto": "Main Photo",
    "proposal.form.showTenancy": "Tenancy Column",
    "proposal.form.showPossession": "Possession Column",
    "proposal.form.exactFloor": "Exact Floor",
    "proposal.form.showUnit": "Unit",
    "proposal.form.showShopNo": "Shop No.",
    "proposal.form.currentState.column": "Current State Column",
    "proposal.form.buiding": "Building",
    "proposal.form.companyTitle": "Company Title",
    "proposal.form.midlandici": "Midland ICI",
    "proposal.form.hkp": "HKP ICI",
    "proposal.form.customtitle": "Custom Title",
    "proposal.form.type": "Type",
    "proposal.form.lang": "Display Language",
    "proposal.form.fontFamily": "Font Family",
    "proposal.form.default": "Default",
    "proposal.form.mingliu": "Mingliu",
    "proposal.form.kaiti": "Kaiti",
    "proposal.form.msjh": "MS JhengHei",
    "proposal.form.chiRemarks": "Terms Remarks (Chi)",
    "proposal.form.engRemarks": "Terms Remarks (Eng)",
    "proposal.form.engAddress": "Address (English)",
    "proposal.form.chiAddress": "Address (Chinese)",
    "proposal.form.forsale": "For Sale",
    "proposal.form.forlease": "For Lease",
    "proposal.form.salesandlease": "Sale+Lease",
    "proposal.form.streettype": "Street Type",
    "proposal.form.mainstreet": "Main Street",
    "proposal.form.substreet": "Side Street",
    "proposal.form.net": "Net",
    "proposal.form.gross": "Gross",
    "proposal.form.suggested": "Suggested",
    "proposal.form.reset": "Reset",
    "proposal.form.save": "Save",
    "proposal.form.confirmsave": "Save Proposal?",
    "proposal.form.proposalname": "Proposal Name",
    "proposal.form.savesuccess":
      "Proposal {filename} has been saved successfully.",
    "proposal.form.validation": `Proposal Name cannot contain whitespace or any of the following characters: \ / : * ? “ < > | % & #`,
    "proposal.form.remarks": "Remarks",
    "proposal.form.chineseremarks": "Chinese Remarks",
    "proposal.form.englishremarks": "English Remarks",
    "proposal.form.allInclusive": "All Included",
    "proposal.form.tenant": "Tenant",
    "proposal.form.tenantShop": "Shop Name",
    "proposal.form.currentTenants": "Current",
    "proposal.form.advanceTenants": "Advance",
    "proposal.form.previousTenants": "Previous",
    "proposal.form.formerTenants": "Former",
    "proposal.form.rent": "Rent",
    "proposal.form.tenancy": "Tenancy",
    "proposal.form.tenancyRemarks": "Tenancy Remarks",
    "proposal.form.tenancyperiod": "Tenancy Period",
    "proposal.form.possession": "Possession",
    "proposal.form.ordering": "Ordering",
    "proposal.form.orderingFinish": "Finish",
    "proposal.form.media.other": "Other ",
    "proposal.form.media.mainPhoto": "1",
    "proposal.form.media.sidePhoto": "2",
    "proposal.form.media.selected": "Selected",
    "proposal.form.multiImages": "Multi Images",
    "proposal.form.ceilingHeight": "Ceiling Height",
    "proposal.form.entranceWidth": "Entrance Width",
    "proposal.createpdf": "Create PDF",
    "proposal.preview": "Preview",
    "proposal.removepdf": "Remove {filename} PDF?",
    "proposal.removepdf.success": "Proposal has been removed successfully.",
    "proposal.list.count": "{resultStr} found",
    "proposal.create.exceed":
      "No. of proposals should not exceed {quota}. Please remove created proposal first.",
    "proposal.general.address": "Address",
    "proposal.general.language": "Display Language",
    "proposal.general.chinese": "Trad. Chi",
    "proposal.general.schinese": "Simp. Chi.",
    "proposal.general.english": "Eng.",
    "proposal.general.chiAndEng": "Trad. Chi. + Eng.",
    "proposal.general.schiAndEng": "Simp. Chi + Eng.",

    // contact
    "contact.title.mr": "Mr",
    "contact.title.ms": "Ms",
    "contact.title.mrs": "Mrs",
    "contact.title.miss": "Miss",
    "contact.title.dr": "Dr",
    "proposal.whole.block.permission.error": "Cannot select Whole Block stock for PP",

    // media
    "media.selectedFile": "Selected File",
    "media.addPhoto": "Add Photo",
    "media.document": "Document",
    "media.vrVideo": "Video",
    "media.photo": "Photo",
    "media.kol": "KOL Video",
    "media.photo.type": "Photo Type",
    "media.document.type": "Document Type",
    "media.video.type": "Video Type",
    "media.download": "Download",
    "media.update.shareToPublic": "Request WWW Approval",
    "media.message.confirmApprove": "Confirm to approve this media?",
    "media.message.confirmReject": "Confirm to reject this media?",
    "media.message.approve.ok": "Media has been approved successfully.",
    "media.message.reject.ok": "Media has been rejected successfully.",
    "media.message.update.ok": "Media has been updated successfully.",
    "media.message.update.failed": "Failed to update media.",
    "media.message.shareToPublic.ok": "Application submitted, waiting for approval",
    "media.video.error": "Video format does not support preview",
    "media.delete.confirm": "Confirm to delete {filename}?",
    "media.delete.success": "Media has been deleted successfully.",
    "media.maxFiles": "Upload up to {maxFiles} photos at a time.",

    // Mita Club
    "home.mitaClub": "Mita Club",

    "stock.approveMedia.approveSuccess": "Media approved",
    "stock.approveMedia.rejectSuccess": "Media rejected",
    "stock.approveMedia.noMediaToApprove": "No media to approve",

    "wwwStock.tag.eaaOwner": "EAA",
    "wwwStock.tag.soleAgent": "Agent",
    "wwwStock.tag.video": "Video",
    "wwwStock.tag.kolVideo": "KOL",
    "wwwStock.tag.photo": "Photo",
    "wwwStock.wwwStockList.count": "www stock found",
    "wwwStock.advertisement": "Advertisement",
    "wwwStock.mediaCutOffDate": "Start Date",
    "wwwStock.district": "District",
    "wwwStock.stockType": "Types",
    "wwwStock.building": "Building",
    "wwwStock.floor": "Floor",
    "wwwStock.unit": "Unit",
    "wwwStock.grossArea": "Area",
    "wwwStock.netArea": "Net Area",
    "wwwStock.price": "Price",
    "wwwStock.rent": "Rent",
    "wwwStock.isOnline": "WWW",
    "wwwStock.propertyScore": "Property Score",
    "wwwStock.exposureRate": "Exposure Rate",
    "wwwStock.eaaOwner": "EAA",
    "wwwStock.isSoleAgent": "Sole A",
    "wwwStock.haveVideo": "Video",
    "wwwStock.havePhoto": "Photo",
    "wwwStock.agent": "Contact Person",
    "wwwStock.kolVideo": "KOL",
    "wwwStock.featuresChi": "Features (Chi)",
    "wwwStock.featuresEng": "Features (Eng)",
    "wwwStock.area.gross": "Gross Area (SqFt)",
    "wwwStock.area.net": "Net Area (SqFt)",
    "wwwStock.area.saleable": "Saleable Area (SqFt)",
    "wwwStock.area.lettable": "Lettable Area (SqFt)",
    "wwwStock.area.areaForShop": "Shop Area (SqFt)",
    "wwwStock.area.area": "Area (SqFt)",
    "wwwStock.update.confirm": "Confirm",
    "wwwStock.update.cancel": "Cancel",
    "wwwStock.update.success.message" : "Update Success",
    "wwwStock.update.fail.message" : "Update Fail",
    "wwwStock.validation.invalid.mediaCutoffDate": "Invalid Start Date",
    "wwwStock.validation.maxLength.featuresChi": "Exceeding 70words limit",
    "wwwStock.validation.maxLength.featuresEng": "Exceeding 160words limit",
  },
  zh: {
    // common
    "common.ok": "確定",
    "common.yes": "是",
    "common.no": "否",
    "common.cancel": "返回",
    "common.cancel2": "取消",
    "common.confirm": "確認",
    "common.remove": "刪除",
    "common.error": "錯誤",
    "common.timeout.error": "連線逾時，請重新載入頁面",
    "common.disconnect.error": "無法連接網絡。",
    "common.refresh": "重新載入",
    "common.industrial": "工商",
    "common.office": "寫字樓",
    "common.shop": "商舖",
    "common.commercial": "商業",
    "common.include": "包括",
    "common.exclude": "不包括",
    "common.lb": "磅",
    "common.lbpersqft": "磅/呎",
    "common.sqft": "呎",
    "common.qtr": "季",
    "common.month": "月",
    "common.sy": "半年",
    "common.more": "更多",
    "common.na": "不適用",
    "common.from": "由",
    "common.to": "至",
    "common.inputs.cannotBeEmpty": "必填欄位不能空白。",
    "common.field.required": "{field}不能為空",

    // login
    "login.message.confirmation": "4位PIN已透過SMS發送。",
    "login.input.department": "部門",
    "login.input.user": "用戶",
    "login.input.password": "密碼",
    "login.button.sendpin": "發送PIN",
    "login.button.resend": "重新發送PIN",
    "login.button.signin": "登入",
    "login.message.sms": "MSearch 搵盤易 (測試版) 登入 PIN: ",
    "login.message.welcome": "歡迎使用 Msearch 搵盤易",
    "login.message.welcomeuat": "歡迎使用MSearch搵盤易 (測試版)",
    "login.message.clicktoprod": "如欲瀏覽正式版本請按此",
    "login.message.prod": "正式版本",
    "login.message.wrongcredential": "登入資料無效。",
    "login.message.wrongpin": "PIN無效。",

    // search
    "search.header.status": "狀況",
    "search.header.marketable": "筍盤",
    "search.header.stock": "樓盤",
    "search.header.shared": "共享",
    "search.header.miscellaneous": "性質",
    "search.header.stockid": "樓盤編號",
    "search.header.parity": "街號類別",
    "search.header.consolidate": "綜合搜尋",
    "search.form.common.min": "最小",
    "search.form.common.max": "最大",
    "search.form.ageMin": "樓齡(最小)",
    "search.form.ageMax": "樓齡(最大)",
    "search.form.areaMin": "面積(最小)",
    "search.form.areaMax": "面積(最大)",
    "search.form.avgpriceMin": "呎價(最小)",
    "search.form.avgpriceMax": "呎價(最大)",
    "search.form.avgrentMin": "呎租(最小)",
    "search.form.avgrentMax": "呎租(最大)",
    "search.form.totalpriceMin": "售價(最小)",
    "search.form.totalpriceMax": "售價(最大)",
    "search.form.totalrentMin": "租金(最小)",
    "search.form.totalrentMax": "租金(最大)",
    "search.form.createdateMin": "建立日期(最小)",
    "search.form.createdateMax": "建立日期(最大)",
    "search.form.lastpdatedateMin": "更新日期(最小)",
    "search.form.lastpdatedateMax": "更新日期(最大)",
    "search.form.tenancyexpiredateMin": "租約期至(最小)",
    "search.form.tenancyexpiredateMax": "租約期至(最大)",
    "search.form.building": "大廈",
    "search.form.district": "區域",
    "search.form.street": "街道",
    "search.form.streetnumber": "街號",
    "search.form.select": "選取..",
    "search.form.streetMin": "街號(最小)",
    "search.form.streetMax": "街號(最大)",
    "search.form.floorMin": "層數(最小)",
    "search.form.floorMax": "層數(最大)",
    "search.form.grade": "級別",
    "search.form.usage": "用途",
    "search.form.ownertype": "業主類別",
    "search.form.possession": "交易狀況",
    "search.form.currentState": "樓盤現況",
    "search.form.inspection": "驗樓",
    "search.form.decoration": "裝修",
    "search.form.view": "景觀",
    "search.common.floor": "層",
    "search.common.stage": "期數",
    "search.common.block": "座數",
    "search.common.unit": "單位",
    "search.common.rent": "租金",
    "search.common.price": "售價",
    "search.common.area": "總面積",
    "search.common.age": "樓齡",
    "search.common.createdate": "建立日期",
    "search.common.lastupdatedate": "更新日期",
    "search.common.tenancy": "租約期",
    "search.common.date": "日期",
    "search.common.pricetotal": "總價",
    "search.common.priceavg": "呎價",
    "search.common.renttotal": "總價",
    "search.common.rentavg": "呎價",
    "search.unlockMessage": "瀏覽已選樓盤？",
    "search.todayQuota": "今日限額",
    "search.unlock.exceed": "已超過今天的限額",
    "search.button.view": "瀏覽",
    "search.button.search": "搜尋",
    "search.button.clear": "清除",
    "search.button.save": "儲存條件",
    "search.button.load": "載入條件",
    "search.save.name": "搜尋條件名稱",
    "search.save.savesuccess": "成功儲存搜尋條件",
    "search.save.modifysuccess": "成功修改搜尋條件",
    "search.stocklist.count": "樓盤結果",
    "search.noresult": "沒有相關的結果",
    "search.new": "新盤",
    "search.mktornew": "新盤或筍盤",
    "search.status.sale": "現售",
    "search.status.lease": "現租",
    "search.status.saleslease": "連約售",
    "search.status.salesandlease": "現租售",
    "search.status.pending": "封盤",
    "search.status.tel": "電話盤",
    "search.status.se": "查冊盤",
    "search.status.dontcall": "勿聯絡",
    "search.status.fail": "錯誤盤",
    "search.status.history": "舊資料",
    "search.status.cancel": "錯盤",
    "search.status.leased": "已租",
    "search.status.sold": "已售",
    "search.status.cu": "未定",
    "search.form.all": "全選",
    "search.form.cancel.all": "取消選取",
    "search.form.mark.all": "全選",
    "search.form.unmark.all": "取消選取",
    "search.form.none": "不選",
    "search.form.invalidinput": "輸入格式無效。",
    "search.form.type": "舖位",
    "search.form.withkey": "有鎖匙",
    "search.form.withoutkey": "無鎖匙",
    "search.form.advsconsent": "廣告委託",
    "search.form.inwater": "來水",
    "search.form.outwater": "去水",
    "search.form.towngas": "煤氣",
    "search.form.confirmor": "摸貨",
    "search.form.saleequity": "可公司轉讓",
    "search.form.passcode": "有密碼",
    "search.form.parentstock": "主盤",
    "search.form.childstock": "分盤",
    "search.form.industrial": "工業",
    "search.form.godown": "貨倉",
    "search.form.io": "工商綜合",
    "search.form.hotel": "酒店",
    "search.form.coldstorage": "凍倉",
    "search.form.singleowner": "大業主",
    "search.form.investor": "技資者",
    "search.form.stratatitle": "散業主",
    "search.form.cooperation": "合作",
    "search.form.mortgagee": "銀主",
    "search.form.developer": "發展商",
    "search.form.handover": "頂手",
    "search.form.carpark": "車位",
    "search.form.groundshop": "地舖",
    "search.form.wholeblock": "全幢",
    "search.form.unstarisshop": "樓上舖",
    "search.form.basement": "地庫",
    "search.form.site": "地盤",
    "search.form.cabinet": "飾櫃",
    "search.form.office": "寫字樓",
    "search.form.tradeandindustry": "工貿",
    "search.form.shoppingmallstock": "商場舖",
    "search.form.singlesidestock": "單邊舖",
    "search.form.frontandrearportion": "前後舖",
    "search.form.soleagentsale": "獨家買",
    "search.form.soleagentlease": "獨家租",
    "search.form.soleagentexpired": "獨家已過期",
    "search.form.soleagentsoleexpired": "獨家賣過期",
    "search.form.soleagentrentexpired": "獨家租過期",
    "search.form.eaaBuy": "廣告買",
    "search.form.eaaRent": "廣告租",
    "search.form.eaaExpired": "廣告過期",
    "search.form.proposal": "測量建議書",
    "search.form.surveryorPP": "測量建議書",
    "search.form.pdfPP": "樓書",
    "search.form.vr": "VR",
    "search.form.haveStockVideo": "樓盤影片",
    "search.form.haveBuildingVideo": "大廈影片",
    "search.form.eaa": "廣告同意書",
    "search.form.landSearch": "土地查冊",
    "search.form.privacylimit": "受私隱限制",
    "search.form.vacant": "吉",
    "search.form.immediatevacant": "即吉",
    "search.form.negovacant": "吉(商議)",
    "search.form.vacantpossession": "交吉",
    "search.form.vacanttransaction": "交吉交易",
    "search.form.salwwithta": "連租約",
    "search.form.tenant": "租用中",
    "search.form.occupied": "自用",
    "search.form.construction": "興建中",
    "search.form.incomplete": "未收樓",
    "search.form.saleandleaseback": "售後租回",
    "search.form.datasource": "來源",
    "search.card.gross": "建",
    "search.card.shop": "舖",
    "search.card.expireat": "租約至",
    "search.checkbox.retainMarkedRecord": "保留已標記紀錄",
    "search.checkbox.uncheckMarkedRecord":
      "取消剔選「保留已標記紀錄」將會移除所有已標記的紀錄，並無法復原。確定取消剔選？",
    "search.marketable.lease": "筍租",
    "search.marketable.sale": "筍賣",
    "search.marketable.salelease": "筍賣/筍租",
    "search.form.board": "掛版",
    "search.form.news": "登報",
    "search.form.pricechange": "價錢變動",
    "search.form.odd": "單",
    "search.form.even": "雙",
    "search.form.canthang": "不可掛板",
    "search.form.noboard": "未掛板",
    "search.form.poster": "已貼海報",
    "search.form.noadv": "未登報紙",
    "search.form.cantposter": "不可貼海報",
    "search.form.cantadv": "不可登報",
    "search.form.email": "電郵",
    "search.form.company": "公司",
    "search.form.owner": "業主",

    // stock
    "stock.stocktype": "樓盤用途",
    "stock.ownertype": "業主類別",
    "stock.airconditioningfee": "冷氣費",
    "stock.includeac": "包冷氣",
    "stock.paidbytenant": "由租客支付",
    "stock.paidbylandlord": "由業主支付",
    "stock.usage": "用途",
    "stock.source": "盤源",
    "stock.possession": "交易狀況",
    "stock.currentState": "樓盤現況",
    "stock.view": "景觀",
    "stock.mgtfee": "管理費",
    "stock.totalMonthlyMgtfee": "每月總計管理費",
    "stock.availability": "交吉",
    "stock.decoration": "裝修",
    "stock.keynumpw": "鎖匙/密碼",
    "stock.rates": "差餉",
    "stock.grent": "地租",
    "stock.cleaningfee": "清潔費",
    "stock.promotefee": "宣傳費",
    "stock.completion": "入伙",
    "stock.inspection": "驗樓",
    "stock.newrecorddate": "新增日期",
    "stock.updatedate": "更新日期",
    "stock.commission": "佣金",
    "stock.comment": "備註",
    "stock.extracharge": "其他費用",
    "stock.contact": "聯絡人",
    "stock.vendor": "業主",
    "stock.tenancy": "租客",
    "stock.rentfree": "免租",
    "stock.negotiable": "可商議",
    "stock.nodata": "無資料",
    "stock.yes": "有",
    "stock.no": "無",
    "stock.freeperiod": "免租期",
    "stock.leaseperiod": "租期",
    "stock.leaseperiodfreetext": "租期(自由格式)",
    "stock.deposit": "按金",
    "stock.option": "續租",
    "stock.rent": "租金",
    "stock.newrental": "新租務",
    "stock.premium": "補地價",
    "stock.breaklease": "終止租約",
    "stock.prepay": "上期",
    "stock.advancetenancy": "預",
    "stock.currenttenancy": "現",
    "stock.previoustenancy": "前",
    "stock.formertenancy": "舊",
    "stock.tenant": "{status}租客",
    "stock.unit": "單位",
    "stock.floor": "層數",
    "stock.shopnumber": "舖號",
    "stock.tenancyperiod": "租約期至",
    "stock.latestrent": "最新租金",
    "stock.company": "公司",
    "stock.industry": "行業",
    "stock.shoparea": "舖面積",
    "stock.tenancy.increment": "加",
    "stock.tenancy.marketrate": "按市值",
    "stock.tenancy.proportion": "按比率",
    "stock.tenancy.fix": "固定",
    "stock.tenancy.notgreater": "不高於",
    "stock.tenancy.notless": "不低於",
    "stock.tenancy.option": "生約",
    "stock.tenancy.option1": "續租權",
    "stock.tenancy.option2": "再續租",
    "stock.tenancy.rent1": "續租金",
    "stock.tenancy.rent2": "續租金",
    "stock.tenancy.fixed": "死約",
    "stock.tenancy.unknown": "不詳",
    "stock.tenancy.nego": "可商議",
    "stock.eaarecord": "廣告同意書",
    "stock.effectivedate": "日期",
    "stock.handledby": "顧問",
    "stock.remarks": "備註",
    "stock.surroundings": "環境",
    "stock.landlordprovisions": "條款",
    "stock.internalremarks": "內部備註",
    "stock.ceilingheight": "樓底高度",
    "stock.entrancewidth": "門口闊度",
    "stock.airconditioningtype": "冷氣系統",
    "stock.unitdepth": "舖深",
    "stock.oddeven": "單雙",
    "stock.backdoor": "後門",
    "stock.washroom": "廁所",
    "stock.sign": "招牌",
    "stock.facilities": "設施",
    "stock.terrace": "平台",
    "stock.terrace.full": "平台",
    "stock.roof": "天台",
    "stock.cockloft": "閣樓",
    "stock.carpark": "車位數量",
    "stock.carparkNo": "車位號",
    "stock.consultantshare": "佣金資料",
    "stock.date": "日期",
    "stock.general": "一般資料",
    "stock.updatehistory": "更新紀錄",
    "stock.media": "多媒體",
    "stock.googlemap": "Google地圖",
    "stock.map": "地圖",
    "stock.photo": "相片",
    "stock.saleskit": "建議書",
    "stock.applySearch": "申請查冊",
    "stock.video": "影片",
    "stock.hand": "手",
    "stock.photo.approval": "個人相片",
    "stock.photo.layout": "外觀",
    "stock.photo.interior": "室內",
    "stock.photo.plan": "圖則",
    "stock.photo.lobby": "大堂",
    "stock.photo.lift": "電梯",
    "stock.photo.entrance": "入口",
    "stock.photo.positionplan": "位置圖",
    "stock.photo.floorplan": "平面圖",
    "stock.photo.areaplan": "面積圖",
    "stock.photo.buildinginfo": "大廈資料",
    "stock.photo.gfphoto": "地廠相",
    "stock.photo.ozp": "法定規劃圖",
    "stock.video.approval": "個人影片",
    "stock.video.processing": "正在處理影片，請稍後重新整理",
    "stock.bottom": "底價",
    // "stock.selectphoto": "選取相片/影片",
    "stock.selectphoto": "選取相片",
    "stock.selectvideo": "選取影片",
    "stock.selectdocument": "選取文檔",
    "stock.selectkolcoverpicture": "選擇封面圖",
    "stock.selectkol": "選擇KOL影片",
    "stock.kol": "KOL影片",
    "stock.kol.approve": "批准",
    "stock.video.upload.restrictions": "影片限制:<br/>時長: 最少30秒最多10分鐘<br/>比例：16:9橫向<br/>最低解析度：1080p<br/>短片內不可有背景音樂<br/>短片內不可有公司商標<br/>封面圖：16:9. 格式: JPEG, JPG, HEIC, PNG<br/>影片格式：MOV (iPhone), MP4 (Android)",
    "stock.kol.upload.restrictions": "影片限制:<br/>代理出現在影片中 (KOL)<br/>時長: 最少30秒最多10分鐘<br/>比例：16:9橫向<br/>最低解析度：1080p<br/>短片內不可有背景音樂<br/>短片內不可有公司商標<br/>封面圖：16:9. 格式: JPEG, JPG, HEIC, PNG<br/>影片格式：MOV (iPhone), MP4 (Android)",
    "stock.kol.upload.uploadfail": "KOL影片不符合限制條件，上傳失敗:<br/><ul style='text-align: left;'><li>16:9 橫向</li><li>最多3分鐘</li><li>短片內不可有背景音樂</li></ul>",
    "stock.kol.thumbnail.upload.uploadfail": "縮略圖上傳失敗: 最少1920*1080",
    "stock.kol.thumbnail.upload.null": "縮略圖不可為空，請重新選擇縮略圖",
    "stock.kol.description": "描述",
    "stock.kol.characteristicZh": "特點",
    "stock.kol.characteristicEn": "特點(英)",
    "stock.kol.bgm": "背景音樂",
    "stock.kol.isShowContact": "片尾加聯絡人資料",
    "stock.kol.processed": "已處理",
    "stock.kol.unprocessed": "未處理",
    "stock.upload.all": "上載",
    "stock.upload": "上載相片/影片",
    "stock.upload.kol": "上載KOL",
    "stock.info": "資料",
    "stock.photocontent": "相片內容",
    "stock.videocontent": "影片內容",
    "stock.documentcontent": "文檔內容",
    "stock.owner": "擁有人",
    "stock.photo.uploadsuccess": "上載成功",
    "stock.kol.uploadsuccess": "成功上載KOL影片，等待批核",
    "stock.photo.uploadfail": "多媒體上載失敗",
    "stock.photo.uploadformatmsg":
      "只能上載特定格式。(.jpg, .jpeg, .png, .mp4, .mov, .pdf)",
    "stock.photo.uploadfailmsg": "上載失敗",
    "stock.photo.removesuccess": "相片/影片刪除成功",
    "stock.photo.removemessage": "刪除相片/影片？",
    "stock.upload.message": "上載（私人）",
    "stock.upload.topublicmessage": "上載（計分）",
    "stock.uploadvideo.message": "上載影片",
    "stock.uploadphoto.message": "上載相片",
    "stock.uploaddocument.message": "上載文檔",
    "stock.photo.maintag": "主相",
    "stock.photo.name": "名稱",
    "stock.photo.content": "相片內容",
    "stock.area.total": "總面積",
    "stock.area.site": "地盤面積",
    "stock.area.gross": "建",
    "stock.area.net": "實",
    "stock.area.verified": "核",
    "stock.noupdatehistory": "沒有更新紀錄",
    "stock.yield": "回報率",
    "stock.facingto": "面向",
    "stock.singlesidetype": "單邊舖",
    "stock.surveyorproposalstatus": "測量部建議書",
    "stock.surveyorproposaldate": "測量部建議書日期",
    "stock.years": "年",
    "stock.months": "個月",
    "stock.days": "天",
    "stock.building": "大廈",
    "stock.stockcreate": "新增樓盤",
    "stock.stockupdate": "更新樓盤",
    "stock.transaction.null": "---",
    "stock.transaction.real": "真實",
    "stock.transaction.marketinfo": "市場資訊",
    "stock.transaction.more": "更多",
    "stock.boardorposter": "掛板/海報",
    "stock.boardstatus": "掛板狀況",
    "stock.boarddate": "掛板日期",
    "stock.boarddescription": "掛板細則",
    "stock.posterstatus": "廣告狀況",
    "stock.posterdate": "海報日期",
    "stock.posterdescription": "海報細則",
    "stock.parity.odd": "單數",
    "stock.parity.even": "雙數",
    "stock.parity.all": "連續",
    "stock.big": "大",
    "stock.small": "細",
    "stock.cockloft.authorized": "入則閣",
    "stock.cockloft.selfbuilt": "自建閣",
    "stock.cockloft.authorizedandselfbuilt": "入則/自建閣",
    "stock.numberofshop": "號舖",
    "stock.number": "號",
    "stock.sold": "售出",
    "stock.leased": "租出",
    "stock.currenttenant": "現租客",
    "stock.numbertype.office": "公司",
    "stock.numbertype.direct": "直線",
    "stock.numbertype.residential": "住宅",
    "stock.numbertype.mobile": "手提",
    "stock.numbertype.pager": "傳呼機",
    "stock.numbertype.fax": "傳真",
    "stock.numbertype.x": "X",
    "stock.numbertype.unknown": "未知",
    "stock.numbertype.na": "不適用",
    "stock.numbertype.doNotContact": "拒絕服務",
    "stock.numbertype.privacy": "不要聯絡",
    "stock.vendor.message.deniedCallBothHtml": "This number is in Opt-Out List.<br/ >此為拒絕服務名單中的號碼。",
    "stock.landsearch": "土地查冊",
    "stock.landsearch.download": "下載",
    "stock.landsearch.files": "文件",
    "stock.landsearch.file": "檔案",
    "stock.landsearch.noFileMsg": "沒有可供下載的查冊文件",
    "stock.landsearch.null": "---",
    "stock.landsearchsearch": "土地查冊",
    "landSearch.list.count": "土地查冊結果",
    "stock.landsearchsearch.memorial": "備忘查冊",
    "stock.landsearchsearch.occupationpermit": "入伙紙",
    "stock.landsearchsearch.governmentlease": "官契查冊",
    "stock.landsearchsearch.conditionandnewcrown": "條款查冊",
    "stock.consosearch": "綜合搜尋",
    "stock.dmoMsg": "此為拒收訊息名單中的號碼。",

    // apply search
    "stock.applySearch.applicant": "申請人",
    "stock.applySearch.contactPhone": "聯絡電話",
    "stock.applySearch.searchType": "申請類別",
    "stock.applySearch.landSearch": "土地查冊",
    "stock.applySearch.landItem": "項目",
    "stock.applySearch.propertyId": "樓盤編號",
    "stock.applySearch.address": "地址",
    "stock.applySearch.shopno": "舖號",
    "stock.applySearch.floor": "層數",
    "stock.applySearch.confirm": "確認",
    "stock.applySearch.opType": "物業用途查冊類別",
    "stock.applySearch.memorialNo": "備忘登記編號",
    "stock.applySearch.filmType": "底片類別",
    "stock.applySearch.landSearchItem": "查冊文件",
    "stock.applySearch.ddType": "丈量約份/地段類別",
    "stock.applySearch.ddNumber": "丈量約份/地段編號",
    "stock.applySearch.conditionalNo": "批地條件/新批地契約",
    "stock.applySearch.submitSuccess": "已成功遞交查冊申請。",

    // building
    "building.header.id": "大廈編號: ",
    "building.searchstock": "樓盤搜尋",
    "building.mtr": "地鐵",
    "building.competition": "入伙",
    "building.accharge": "冷氣費",
    "building.developer": "發展商",
    "building.mgtcompany": "管理公司",
    "building.mgtfeeupdatedate": "管理費更新日期",
    "building.phone": "電話",
    "building.structure": "建築",
    "building.lowfloor": "低層",
    "building.midfloor": "中層",
    "building.highfloor": "高層",
    "building.lobby": "大堂",
    "building.wall": "牆",
    "building.ceiling": "天花板",
    "building.totalfloor": "層數",
    "building.lifts": "電梯",
    "building.passengerlift": "客梯",
    "building.cargolift": "貨梯",
    "building.carlift": "車梯",
    "building.otherlift": "其他電梯",
    "building.container": "貨櫃",
    "building.loadingbay": "貨台",
    "building.efficiency": "實用率",
    "building.carpark": "車位數量",
    "building.hourlyparking": "時泊",
    "building.monthlyparking": "月泊",
    "building.ceilinght": "樓底高度",
    "building.loadingkpa": "負重 (KPA)",
    "building.airconditioning": "冷氣",
    "building.airconditioningtime": "冷氣時間",
    "building.extracharges": "額外費用",
    "building.extrainfo": "其他資料",
    "building.liftzone": "電梯分佈",
    "building.surroundings": "環境",
    "building.substreet": "副街",
    "building.previousname": "過往名稱",
    "building.previousname.chi": "過往中文名稱",
    "building.previousname.eng": "過往英文名稱",

    // home
    "home.welcome": "歡迎",
    "home.profileLink": "我的網盤",
    "home.quotaText": "今日限額",
    "home.stock": "搵盤",
    "home.transaction": "成交",
    "home.company": "公司",
    "home.companySearch": "公司搜尋",
    "home.help": "教學",
    "home.publicmessage": "訊息",
    "home.signout": "登出",
    "home.viewedstock": "已瀏覽樓盤",
    "home.search": "樓盤搜尋",
    "home.myfavorite": "我的最愛",
    "home.shelf": "大手推介",
    "home.firstHandStock": "一手開單易",
    "home.kol.video": "KOL 影片",
    "home.approve.media": "批核",
    "home.report": "大手成交報告",
    "home.wwwStock": "我的網盤",
    "report.report": "大手成交報告",

    // message center
    "publicmessage.header": "訊息",
    "publicmessage.public": "公開訊息",
    "publicmessage.private": "私人訊息",

    // help page
    "tips.tutorial": "教學",
    "tips.tutorial.basicfunction": "基本功能",
    "tips.tutorial.otherfunction": "建議書、儲存條件及我的最愛",
    "tips.notsupport": "瀏覽器不支援",
    "tips.usermanual": "使用手冊",
    "tips.stocktags": "樓盤標籤",
    "tips.priceformat": "搜尋結果頁面的售價/租金格式",
    "tips.photoformat.header": "支援的相片格式",
    "tips.photoformat.content": ".jpg, .jpeg, .png, .mp4",
    "tips.chinesesearch.header": "以中文輸入法搜尋樓盤",
    "tips.chinesesearch.content1": "以中文輸入法輸入大廈, 區域 和街道",
    "tips.chinesesearch.content2": "按一下「空白鍵」以搜尋樓盤",
    "tips.stockpricesign.header": "樓盤售價標示",
    "tips.stockpricesign.up": "最近上升",
    "tips.stockpricesign.down": "最近下降",
    "tips.defaultsorting.header": "預設排列次序",
    "tips.defaultsorting.content1": "預設排列次序 (遞增)",
    "tips.defaultsorting.content2": "{field}亦可以遞減方式排序。",
    "tips.pin.header": "PIN 碼會同時發送到開單易。",
    "tips.pin.content1": "於登錄畫面輸入內聯網帳號資料",
    "tips.pin.content2": "按「發送PIN」",
    "tips.pin.content3": "打開 「開單易」",
    "tips.pin.content4": "到 「個人訊息」",
    "tips.pin.content5": "到「系統訊息」 取得 4位PIN",
    "tips.pin.content6": "於登錄畫面輸入PIN",
    "tips.pricerentsorting.header": "以售價/租金排序",
    "tips.pricerentsorting.content1":
      "沒有售價或租金的樓盤會排列於樓盤搜尋結果的最後",
    "tips.advancedsearch.header": "進階搜尋",
    "tips.advancedsearch.content1": "以更多條件搜查樓盤 ",
    "tips.advancedsearch.content2": "用戶能選多個大廈、區域和街道",

    "tips.addtohomescreen.header": "加至主畫面",
    "tips.addtohomescreen.content1": "按 ‘瀏覽器設定’",
    "tips.addtohomescreen.content2": "Android (Chrome): 右上設定按鈕",
    "tips.addtohomescreen.content3": "Iphone: 底部導覽列",
    "tips.addtohomescreen.content4": "Samsung: 底部導覽列",
    "tips.addtohomescreen.content5": "選擇 「加入主畫面」",
    "tips.addtohomescreen.content6": "MSearch圖標便會顯示於主畫面",

    "tips.dailyquota.header": "每天限額",
    "tips.dailyquota.content1": "用戶每天能瀏覽{quota}個樓盤",
    "tips.dailyquota.content2": "按首頁的「今天限額」能查看已瀏覽的樓盤紀錄",
    "tips.dailyquota.content3": "限額和已瀏覽的樓盤紀錄將於下一天刷新",

    "tips.updatepassword.header": "更新密碼",
    "tips.updatepassword.content1": "如有需要，用戶可到內聯網更改密碼 ",
    "tips.updatepassword.content2": "建議用戶使用較強的密碼",

    "tips.stocktag.sole": "獨家盤",
    "tips.stocktag.equaity": "公司股權轉讓",
    "tips.stocktag.carpark": "車位",
    "tips.stocktag.mortgagee": "銀主盤",
    "tips.stocktag.eaa": "已簽EAA",
    "tips.stocktag.new": "2天內新盤",
    "tips.stocktag.www": "WWW盤",
    "tips.stocktag.soleagentbuy": "獨家買",
    "tips.stocktag.soleagentrent": "獨家租",
    "tips.stocktag.shoppingmall": "商場舖",
    "tips.stocktag.singleside": "單邊舖",
    "tips.stocktag.frontandrear": "前後舖",
    "tips.stocktag.boardhanged": "已掛板",
    "tips.stocktag.replacement": "頂手",

    // tags in PropertyTagBar
    "stock.tag.sole": "獨家",
    "stock.tag.equaity": "賣公司",
    "stock.tag.carpark": "車位",
    "stock.tag.mortgagee": "銀主盤",
    "stock.tag.eaa": "廣告",
    "stock.tag.new": "新盤",
    "stock.tag.www": "網盤",
    "stock.tag.terrace": "平台",
    "stock.tag.roof": "天台",
    "stock.tag.cockloft": "閣樓",
    "stock.tag.marketable": "筍盤",

    "stock.tag.key": "有匙",
    "stock.tag.frontandrear": "前後舖",
    "stock.tag.shoppingmall": "商場舖",
    "stock.tag.singleside": "單邊舖",
    "stock.tag.pending": "封盤",
    "stock.tag.boardhanged": "已掛板",
    "stock.tag.soleagentrent": "獨家租",
    "stock.tag.soleagentbuy": "獨家買",
    "stock.tag.replacement": "頂手",
    "stock.tag.haveSurverorPP": "測量書",
    
    "search.form.transferByCompany": "可公司買賣",
    "stock.advertisment.isDoNotAllowBoard": "不能掛板",
    "stock.advertisment.isDoNotAllowPoster": "不能貼海報",
    "stock.advertisment.isDoNotAllowNewspaper": "不能登報",
    "stock.advertisment.isNoAds": "不能出網盤",
    "search.form.bigsinglesidestock": "大單邊舖",
    "search.form.smallsinglesidestock": "細單邊舖",
    "stock.inwater": "來水",
    "stock.outwater": "去水",
    "stock.towngas": "煤氣",
    "stock.LPower": "大電",
    "stock.licensed": "有牌照",
    "stock.withCarpark": "連車位",
    "stock.with24hAC": "24小時冷氣",
    "stock.is24hEnter": "24小時出入",
    "stock.saleWithLicenses": "連牌出售",
    "stock.isFactory": "廠房",
    "stock.isIncludeWaterElec": "包水電",
    "stock.bizTools": "有生財工具",
    "stock.toilet": "獨立廁所",
    // -------------------- tags end -------------------

    // company
    "company.search.companyName": "公司名",
    "company.list.count": "{count} 公司結果",
    "company.list.apply": "申請公司查冊",
    "company.sorter.companyNameZh": "公司名(中)",
    "company.sorter.rightPerson": "決策人",
    "company.sorter.companyNameEn": "公司名(英)",
    "company.sorter.updateDate": "更新日期",
    "company.card.createDate": "建立日期 {date}",
    "company.card.updateDate": "更新日期 {date}",
    "company.detail.clientId": "客戶編號",
    "company.detail.source": "來源",
    "company.detail.createdBy": "建立人",
    "company.detail.updatedBy": "更新人",
    "company.detail.nextUpdate": "再聯絡",
    "company.detail.holdingCompanyZh": "控股公司(中文)",
    "company.detail.holdingCompanyEn": "控股公司(英文)",
    "company.detail.business": "行業",
    "company.detail.BR": "BR號碼",
    "company.detail.district": "區域",
    "company.detail.URL": "網址",
    "company.detail.addressZh": "地址(中文)",
    "company.detail.addressEn": "地址(英文)",
    "company.detail.remarks": "備註",
    "company.detail.contact": "聯絡",
    "company.detail.phoneLabel.privacy": "不要聯絡",
    "company.detail.phoneLabel.doNotContact": "(拒絕服務)",
    "company.detail.companyType": "公司類型",
    "company.detail.companyNameEn": "英文公司名",
    "company.detail.companyNameZh": "中文公司名",
    "company.detail.companyAddressEn": "英文地址",
    "company.detail.companyAddressZh": "中文地址",
    "company.detail.rightPerson": "話事人",
    "company.detail.directorName": "公司董事名稱",
    "company.detail.directorAddress": "董事申報地址",
    "company.detail.directorCompanyAddress": "董事所佔股份公司註冊地址",
    "company.detail.companyPhoneNumber": "公司電話",
    "company.tab.generalInfo": "一般資料",
    "company.tab.applySearch": "申請查冊(此公司)",
    "company.section.search": "公司查冊",
    "company.search.count": "公司查冊結果",
    "company.search.applicant": "申請者﹕{applicant}",
    "company.applySearch.companyName": "公司名稱/董事名稱",
    "company.applySearch.companyId": "公司編號/身份證編號",
    "company.applySearch.declarations1": "聲明1",
    "company.applySearch.declarations2": "聲明2",
    "company.applySearch.atLeastCheckOne": "最少要勾選一項。",
    "company.applySearch.atLeastInputOne": "最少要輸入一項。",
    "company.applySearch.mustInput": "請輸入",
    "company.applySearch.success": "已成功遞交公司查冊申請。",

    // proposal
    "proposal.proposal": "建議書",
    "proposal.indvProposal": "單盤",
    "proposal.listProposal": "多盤",
    "proposal.listProposal.indv": "單盤格式",
    "proposal.create": "建立多盤建議書",
    "proposal.listProposal.create": "多盤建議書",
    "proposal.indvProposal.create": "單盤建議書",
    "proposal.listProposal.clearPPStocks": "清除已標記紀錄",
    "proposal.listProposal.clearPPStocksConfirm": "確定清除全部已標記紀錄？",
    "proposal.listProposal.emptyMarkStocks": "沒有已標記紀錄",
    "proposal.section.ppOptions": "建議書資料",
    "proposal.section.stock": "物業資料",
    "proposal.section.stockList": "物業列表",
    "proposal.form.stockinfo": "樓盤資料",
    "proposal.form.otherinfo": "其他資料",
    "proposal.form.showemployeephoto": "代理相",
    "proposal.form.showcontact": "代理聯絡",
    "proposal.form.showMainPhoto": "主相",
    "proposal.form.showTenancy": "租約欄",
    "proposal.form.showPossession": "交易狀況欄",
    "proposal.form.exactFloor": "實際樓層",
    "proposal.form.showUnit": "單位",
    "proposal.form.showShopNo": "舖號",
    "proposal.form.currentState.column": "樓盤現況欄",
    "proposal.form.buiding": "大廈",
    "proposal.form.companyTitle": "公司名稱",
    "proposal.form.midlandici": "美聯工商舖",
    "proposal.form.hkp": "港置工商舖",
    "proposal.form.customtitle": "自訂標題",
    "proposal.form.type": "類別",
    "proposal.form.lang": "顯示語言",
    "proposal.form.fontFamily": "字體",
    "proposal.form.default": "預設",
    "proposal.form.mingliu": "新細明體",
    "proposal.form.kaiti": "標楷體",
    "proposal.form.msjh": "微軟正黑體",
    "proposal.form.chiRemarks": "條款備註(中文)",
    "proposal.form.engRemarks": "條款備註(英文)",
    "proposal.form.engAddress": "地址(英文)",
    "proposal.form.chiAddress": "地址(中文)",
    "proposal.form.forsale": "出售",
    "proposal.form.forlease": "出租",
    "proposal.form.salesandlease": "租及售",
    "proposal.form.streettype": "街道類型",
    "proposal.form.mainstreet": "主街",
    "proposal.form.substreet": "副街",
    "proposal.form.net": "實",
    "proposal.form.gross": "建",
    "proposal.form.suggested": "建議",
    "proposal.form.reset": "重設",
    "proposal.form.save": "儲存",
    "proposal.form.confirmsave": "儲存建議書？",
    "proposal.form.proposalname": "建議書名稱",
    "proposal.form.savesuccess": "成功儲存建議書 {filename}",
    "proposal.form.validation": `建議書名稱不能包含空白或以下任何字符: \ / ： * ？" < > | % & #`,
    "proposal.form.chineseremarks": "中文備註",
    "proposal.form.englishremarks": "英文備註",
    "proposal.form.allInclusive": "全包",
    "proposal.form.tenant": "租客",
    "proposal.form.tenantShop": "舖頭名",
    "proposal.form.currentTenants": "現租客",
    "proposal.form.advanceTenants": "預租客",
    "proposal.form.previousTenants": "前租客",
    "proposal.form.formerTenants": "舊租客",
    "proposal.form.rent": "租金",
    "proposal.form.tenancy": "租約",
    "proposal.form.tenancyRemarks": "租約備註",
    "proposal.form.remarks": "建議書備註",
    "proposal.form.tenancyperiod": "租約期",
    "proposal.form.possession": "交易狀況",
    "proposal.form.ordering": "排序",
    "proposal.form.orderingFinish": "完成",
    "proposal.form.media.other": "其他",
    "proposal.form.media.mainPhoto": "主",
    "proposal.form.media.sidePhoto": "副",
    "proposal.form.media.selected": "已揀選的",
    "proposal.form.ceilingHeight": "樓高",
    "proposal.form.entranceWidth": "門闊",
    "proposal.createpdf": "創建PDF",
    "proposal.preview": "預覽",
    "proposal.removepdf": "移除 {filename} PDF?",
    "proposal.removepdf.success": "成功移除建議書",
    "proposal.list.count": "建議書結果",
    "proposal.create.exceed": "已創建超過 {quota} 份建議書，請先移除建議書",
    "proposal.general.address": "地址",
    "proposal.general.language": "顯示語言",
    "proposal.form.multiImages": "一頁多圖",
    "proposal.general.chinese": "繁中",
    "proposal.general.schinese": "簡中",
    "proposal.general.english": "英文",
    "proposal.general.chiAndEng": "繁 + 英",
    "proposal.general.schiAndEng": "簡 + 英",

    // contact
    "contact.title.mr": "先生",
    "contact.title.ms": "女士",
    "contact.title.mrs": "太太",
    "contact.title.miss": "小姐",
    "contact.title.dr": "博士",

    "proposal.whole.block.permission.error": "不允許選擇[全幢]樓盤製作建議書",

    // media
    "media.selectedFile": "已選擇的文件",
    "media.addPhoto": "添加相片",
    "media.document": "文檔",
    "media.vrVideo": "影片",
    "media.photo": "相片",
    "media.kol": "KOL影片",
    "media.photo.type": "相片類別",
    "media.document.type": "文檔類別",
    "media.video.type": "影片類別",
    "media.download": "下載",
    "media.update.shareToPublic": "申請批核",
    "media.message.confirmApprove": "確定批准此媒體？",
    "media.message.confirmReject": "確定拒絕此媒體？",
    "media.message.approve.ok": "已成功批准媒體。",
    "media.message.reject.ok": "已成功拒絕媒體。",
    "media.message.update.ok": "成功更新媒體。",
    "media.message.update.failed": "更新媒體失敗。",
    "media.message.shareToPublic.ok": "申請成功，等待批准",
    "media.video.error": "視頻編碼不支援預覽",
    "media.delete.confirm": "確定刪除 {filename}？",
    "media.delete.success": "成功刪除媒體。",
    "media.maxFiles": "每次最多可上載{maxFiles}張相片。",

    // Mita Club
    "home.mitaClub": "Mita Club",

    "stock.approveMedia.approveSuccess": "媒體已批准",
    "stock.approveMedia.rejectSuccess": "媒體已拒絕",
    "stock.approveMedia.noMediaToApprove": "沒有待審核的媒體",

    "wwwStock.tag.eaaOwner": "廣告同意書",
    "wwwStock.tag.soleAgent": "獨家",
    "wwwStock.tag.video": "影片",
    "wwwStock.tag.kolVideo": "KOL影片",
    "wwwStock.tag.photo": "樓盤相片",
    "wwwStock.wwwStockList.count": "網盤結果",
    "wwwStock.advertisement": "網上放盤",
    "wwwStock.mediaCutOffDate": "開始日期",
    "wwwStock.district": "區域",
    "wwwStock.stockType": "類別",
    "wwwStock.building": "大廈名稱",
    "wwwStock.floor": "層數",
    "wwwStock.unit": "單位",
    "wwwStock.grossArea": "建筑面积",
    "wwwStock.netArea": "實用面積",
    "wwwStock.price": "售價",
    "wwwStock.rent": "租金",
    "wwwStock.isOnline": "狀態",
    "wwwStock.propertyScore": "盤分",
    "wwwStock.exposureRate": "出盤比率",
    "wwwStock.eaaOwner": "廣告同意書",
    "wwwStock.isSoleAgent": "獨家",
    "wwwStock.haveVideo": "影片",
    "wwwStock.havePhoto": "樓盤相片",
    "wwwStock.agent": "聯絡人",
    "wwwStock.kolVideo": "KOL片",
    "wwwStock.featuresChi": "特點(中文)",
    "wwwStock.featuresEng": "特點(英文)",
    "wwwStock.area.gross": "建築面積(平方呎)",
    "wwwStock.area.net": "實用面積(平方呎)",
    "wwwStock.area.saleable": "銷售面積(平方呎)",
    "wwwStock.area.lettable": "租用面積(平方呎)",
    "wwwStock.area.areaForShop": "舖面積(平方呎)",
    "wwwStock.area.area": "面积(平方呎)",
    "wwwStock.update.confirm": "確認",
    "wwwStock.update.cancel": "取消",
    "wwwStock.update.success.message" : "更新成功",
    "wwwStock.update.fail.message" : "更新失败",
    "wwwStock.validation.invalid.mediaCutoffDate": "開始日期為必填項",
    "wwwStock.validation.maxLength.featuresChi": "超出70字上限",
    "wwwStock.validation.maxLength.featuresEng": "超出160字上限",
  },
};
