import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import { injectIntl } from "react-intl";
import FieldVal from "../../../../common/FieldVal";
import { getLangKey } from "../../../../../helper/generalHelper";

const styles = (theme) => ({
  root: {
    padding: "2vw",
  },
});

class BasicInfo extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;

    const completionDate = detail.completionDate || "---";

    let generalMapping = {
      [intl.formatMessage({
        id: "building.competition",
      })]: { value: completionDate, xs: 6 },
    };

    return (
      <div className={classes.root}>
        <Grid container spacing={2}>
          {Object.keys(generalMapping).map((v, i) => (
            <Grid item xs={generalMapping[v].xs} key={v}>
              <FieldVal field={v} style={generalMapping[v].style}>
                {generalMapping[v].value}
              </FieldVal>
            </Grid>
          ))}
        </Grid>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(BasicInfo));
