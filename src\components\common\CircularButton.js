import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import FormButton from "./FormButton";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    width: 75,
    height: 75,
    lineHeight: 1.2,
    borderRadius: "100%",
    textTransform: "none",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    minWidth: 0,
    "&:hover": {
      background: "#13CE66",
      color: "#fff",
    },
  }
};

function CircularButton(props) {
  const { classes, children, className, ...other } = props;

  return (
    <FormButton className={clsx(classes.root, className)} {...other}>
      <div>
        {children}
      </div>
    </FormButton>
  );
}

CircularButton.propTypes = {
  children: PropTypes.node,
  classes: PropTypes.object.isRequired,
  className: PropTypes.string
};

export default withStyles(styles)(CircularButton);
