{"name": "icims", "version": "0.0.0", "private": true, "engines": {"node": ">= 12.0.0", "npm": ">= 6.0.0"}, "browserslist": [">1%", "last 4 versions", "Firefox ESR", "ie 11", "not ie < 9"], "dependencies": {"@amcharts/amcharts4": "^4.9.19", "@babel/polyfill": "^7.0.0", "@material-ui/core": "4.12.4", "@material-ui/icons": "^4.9.1", "@material-ui/lab": "^4.0.0-alpha.61", "abort-controller": "^3.0.0", "body-parser": "^1.18.3", "clsx": "^1.1.1", "connect-mongo": "^3.0.0", "cookie-parser": "^1.4.3", "cors": "^2.8.5", "dotenv-webpack": "^7.0.3", "emoji-picker-react": "^4.13.2", "exif-js": "^2.3.0", "express": "^4.16.3", "express-session": "^1.16.2", "form-data": "^3.0.0", "fs": "0.0.1-security", "graphql": "^0.13.2", "helmet": "^3.21.2", "history": "^4.7.2", "isomorphic-style-loader": "^4.0.0", "jose": "2.0.2", "jpeg-autorotate": "^5.0.2", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.15", "mobile-detect": "^1.4.4", "moment": "^2.24.0", "mongodb": "^3.6.2", "multer": "^1.4.2", "node-fetch": "^2.2.0", "node-schedule": "^1.3.2", "normalize.css": "^8.0.0", "ol": "^6.15.1", "passport": "^0.4.0", "passport-local": "^1.0.0", "pretty-error": "^2.1.1", "prop-types": "^15.6.2", "query-string": "5.1.1", "react": "^16.14.0", "react-beautiful-dnd": "^13.0.0", "react-dom": "^16.14.0", "react-google-recaptcha-v3": "^1.5.4", "react-image-lightbox": "^5.1.0", "react-infinite-scroll-component": "^4.5.3", "react-intl": "^5.6.9", "react-pdf": "^4.2.0", "react-player": "^2.9.0", "react-redux": "^7.2.0", "react-select": "^3.0.5", "react-sticky": "^6.0.3", "redux": "^4.1.2", "redux-devtools-extension": "^2.13.2", "redux-form": "^8.3.8", "redux-logger": "^3.0.6", "redux-thunk": "^2.4.1", "serialize-javascript": "^1.5.0", "smoothscroll-polyfill": "^0.4.4", "source-map-support": "^0.5.9", "speakeasy": "^2.0.0", "universal-cookie": "^4.0.3", "universal-router": "^6.0.0", "uuid": "^3.3.3", "whatwg-fetch": "^3.0.0", "xml2js": "^0.4.22"}, "devDependencies": {"@babel/core": "^7.16.5", "@babel/node": "^7.16.5", "@babel/plugin-proposal-class-properties": "^7.16.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-react-constant-elements": "^7.16.5", "@babel/plugin-transform-react-inline-elements": "^7.16.5", "@babel/preset-env": "^7.16.5", "@babel/preset-flow": "^7.16.5", "@babel/preset-react": "^7.16.5", "babel-eslint": "^9.0.0", "babel-jest": "^23.6.0", "babel-loader": "^8.2.3", "babel-plugin-transform-react-remove-prop-types": "^0.4.18", "browser-sync": "^2.24.7", "chokidar": "^2.0.4", "css-loader": "^1.0.0", "enzyme": "^3.6.0", "eslint": "^5.6.0", "eslint-config-airbnb": "^17.1.0", "eslint-config-prettier": "^3.0.1", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-node": "^0.3.2", "eslint-loader": "^2.1.1", "eslint-plugin-flowtype": "^2.50.1", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.1.1", "eslint-plugin-prettier": "^2.6.2", "eslint-plugin-react": "^7.11.1", "event-source-polyfill": "^1.0.8", "eventsource-polyfill": "^0.9.6", "file-loader": "^2.0.0", "flow-bin": "^0.81.0", "front-matter": "^2.3.0", "glob": "^7.1.3", "husky": "^1.0.0-rc.15", "identity-obj-proxy": "^3.0.0", "jest": "^23.6.0", "lint-staged": "^7.3.0", "markdown-it": "^8.4.2", "mkdirp": "^0.5.1", "null-loader": "^0.1.1", "opn-cli": "^3.1.0", "pixrem": "^4.0.1", "pleeease-filters": "^4.0.0", "postcss": "^7.0.2", "postcss-calc": "^6.0.1", "postcss-flexbugs-fixes": "^4.1.0", "postcss-import": "^12.0.0", "postcss-loader": "^3.0.0", "postcss-preset-env": "^6.6.0", "postcss-pseudoelements": "^5.0.0", "prettier": "^2.5.1", "raw-loader": "^0.5.1", "react-deep-force-update": "^2.1.3", "react-dev-utils": "^5.0.2", "react-error-overlay": "^4.0.1", "react-test-renderer": "^16.5.2", "redux-mock-store": "^1.4.0", "rimraf": "^2.6.2", "stylelint": "^9.5.0", "stylelint-config-standard": "^18.2.0", "stylelint-order": "^1.0.0", "svg-url-loader": "^2.3.2", "url-loader": "^1.1.1", "webpack": "^4.19.1", "webpack-assets-manifest": "^3.0.2", "webpack-bundle-analyzer": "^3.0.2", "webpack-dev-middleware": "^3.3.0", "webpack-hot-middleware": "^2.24.2", "webpack-node-externals": "^1.7.2"}, "lint-staged": {"ignore": ["package.json"], "linters": {"*.{js,jsx}": ["eslint --no-ignore --fix", "git add --force"], "*.{json,md,graphql}": ["prettier --write", "git add --force"], "*.{css,less,styl,scss,sass,sss}": ["stylelint --fix", "git add --force"]}}, "scripts": {"precommit": "lint-staged", "lint-js": "eslint --ignore-path .gitignore --ignore-pattern \"!**/.*\" .", "lint-css": "stylelint \"src/**/*.{css,less,styl,scss,sass,sss}\"", "lint": "yarn run lint-js && yarn run lint-css", "fix-js": "yarn run lint-js --fix", "fix-css": "yarn run lint-css --fix", "fix": "yarn run fix-js && yarn run fix-css", "flow": "flow", "flow:check": "flow check", "test": "jest", "test-watch": "yarn run test --watch --notify", "test-cover": "yarn run test --coverage", "coverage": "yarn run test-cover && opn coverage/lcov-report/index.html", "clean": "babel-node tools/run clean", "copy": "babel-node tools/run copy", "bundle": "babel-node tools/run bundle", "build": "babel-node tools/run build", "build-stats": "yarn run build --release --analyse", "deploy": "babel-node tools/run deploy", "render": "babel-node tools/run render", "serve": "babel-node tools/run runServer", "start": "babel-node tools/run start"}}