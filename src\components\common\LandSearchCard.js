import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import Grid from "@material-ui/core/Grid";
import { makeStyles } from "@material-ui/core/styles";
import { connect } from "react-redux";
import { injectIntl } from "react-intl";
import moment from "moment";
import _ from "lodash";

import FormButton from "./FormButton";
import LandSearchPdfDialog from './LandSearchPdfDialog';
import { listLandSearchPdf } from '@/actions/stock';

const useStyles = makeStyles({
  root: {
    borderRadius: 4,
    padding: 8,
    backgroundColor: "rgba(132,132,132,.1)",
  },
  sourceRow: {
    fontSize: ".875em",
    color: "#777",
    marginBottom: ".5vh",
    display: "flex",
    justifyContent: "space-between",
  },
  unitRow: {
    fontSize: "1.125em",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  },
  content: {
    fontSize: "1.125em",
    marginBottom: ".5vh",
  },
  subtype: {
    fontWeight: "700",
    color: "#000",
  },
  priceRow: {
    fontSize: "1.125em",
    textAlign: "right",
  },
  val: {
    display: "inline-flex",
  },
  alignToright: {
    textAlign: "right",
  },
  cardPriceRow: {
    padding: "0 4px",
    borderRadius: 4,
    backgroundColor: "rgba(140, 190, 190, 0.2)",
  },
  cardRentRow: {
    padding: "0 4px",
    borderRadius: 4,
    backgroundColor: "rgba(200, 170, 170, 0.2)",
  },
  greyBox: {
    display: "inline",
    backgroundColor: "#8A8A8A",
    color: "#fff",
    padding: 4,
    margin: 4,
    borderRadius: 4,
  },
  buttonMain: {
    minWidth: "50px",
    height: "auto",
    margin: "1vw",
    padding: "0 6px",
  },
  link: {
    textDecoration: "none",
  },
  subtitleRow: {
    display: "flex",
    justifyContent: "space-between",
  },
  error: {
    color: "#ff3333",
  },
});

function LandSearchCard({ detail, landSearch, listLandSearchPdf, intl }) {
  const classes = useStyles();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedLandSearchRefNo, setSelectedLandSearchRefNo] = useState("");

  useEffect(() => {
    let selectedLandSearch = detail.landSearch.find(item => item.search_ref_no === selectedLandSearchRefNo)
    if (!_.isEmpty(selectedLandSearch?.list) && _.get(selectedLandSearch, "search_ref_no")) {
      setIsLoading(true)
      listLandSearchPdf({
        list: selectedLandSearch.list,
        unicornId: detail.unicorn.id,
        landSearchRefNo: selectedLandSearchRefNo
      }).then(() => {
        setIsLoading(false)
      })
    }
  }, [selectedLandSearchRefNo]);

  const date = landSearch.create_date
    ? new Date(landSearch.create_date)
    : landSearch.action_date
    ? new Date(landSearch.action_date)
    : "---";

  const subType = landSearch.sub_type === "H" ? "Historical" : "Current";

  // only SHOPS have mutiple values of search_type
  let searchType;
  switch (landSearch.search_type) {
    case "Memorial":
      searchType = intl.formatMessage({
        id: "stock.landsearchsearch.memorial",
      });
      break;
    case "Occupation Permit":
      searchType = intl.formatMessage({
        id: "stock.landsearchsearch.occupationpermit",
      });
      break;
    case "Government Lease":
      searchType = intl.formatMessage({
        id: "stock.landsearchsearch.governmentlease",
      });
      break;
    case "Condition/New Crown":
      searchType = intl.formatMessage({
        id: "stock.landsearchsearch.conditionandnewcrown",
      });
      break;
    default:
      searchType = intl.formatMessage({ id: "stock.landsearchsearch" });
      break;
  }

  const landSearchDetail =
    detail.unicorn.landSearchDetail !== null
      ? detail.unicorn.landSearchDetail.find(
          (search) => search.search_ref_no === landSearch.search_ref_no,
        )
      : {};

  const unit =
    landSearchDetail !== undefined && landSearchDetail.flat
      ? intl.formatMessage({
          id: "search.common.unit",
        }) +
        " " +
        detail.unit
      : "";

  const floor =
    landSearchDetail !== undefined && landSearchDetail.floor !== null
      ? intl.formatMessage({ id: "search.common.floor" }) + detail.floor + ", "
      : "";

  const address =
    landSearchDetail !== undefined && landSearchDetail.address.trim !== ""
      ? landSearchDetail.address
      : "---";

  return (
    <div className={`${classes.root}`}>
      <div className={`${classes.subtitleRow}`}>
        <div className={classes.sourceRow}>
          <span>
            {moment(date, "YYYY-MM-DD").format("YYYY-MM-DD")} {searchType}
          </span>
        </div>
        <div className={classes.subtype}>
          <span>{subType}</span>
        </div>
      </div>

      <div>{landSearch.search_ref_no}</div>
      <div>
        <div className={classes.content}>
          <div>
            {floor} {unit}
          </div>
          <div>{address}</div>
        </div>
        <Grid container spacing={1} justify="flex-end">
          <FormButton
            className={classes.buttonMain}
            disabled={false}
            id="downloadButton"
            onClick={() => {
              setSelectedLandSearchRefNo(landSearch.search_ref_no)
              setIsDialogOpen(true)
            }}
          >
            {intl.formatMessage({
              id: "stock.landsearch.download",
            })}{" "}
          </FormButton>
        </Grid>
      </div>
      <LandSearchPdfDialog
        open={isDialogOpen}
        close={() => setIsDialogOpen(false)}
        isLoading={isLoading}
        landSearchRefNo={selectedLandSearchRefNo}
        stockId={_.get(detail, "unicorn.landSearch")}
      />
    </div>
  );
}

LandSearchCard.propTypes = {
  detail: PropTypes.object,
  className: PropTypes.string,
  landSearch: PropTypes.object.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapDispatchToProps = (dispatch) => ({
  listLandSearchPdf: (args) => dispatch(listLandSearchPdf(args)),
});

export default connect(null, mapDispatchToProps)(injectIntl(LandSearchCard));
