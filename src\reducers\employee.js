import _ from "lodash";
import {
  LIST_EMPLOYEES_START,
  LIST_EMPLOYEES_SUCCESS,
  LIST_EMPLOYEES_ERROR,
  LIST_VALID_EMPLOYEES_START,
  LIST_VALID_EMPLOYEES_SUCCESS,
  LIST_VALID_EMPLOYEES_ERROR,
  <PERSON><PERSON><PERSON>OYEES_MITACLUB_START,
  EMPLOYEES_MITACLUB_SUCCESS,
  EMPLOYEES_MITACLUB_MANAGER_START,
  EMPLOYEES_MITACLUB_MANAGER_SUCCESS,
  <PERSON>MPLOYEES_MITACLUB_MANAGER_ERROR,
  <PERSON><PERSON><PERSON><PERSON>YEE<PERSON>_MITACLUB_ERROR,

  MITACLUB_MANAGER_AGENT_LIST_START,
  MITACLUB_MANAGER_AGENT_LIST_SUCCESS,
  MITACLUB_MANAGER_AGENT_LIST_ERROR,

  SET_MITACLUB_AGENT_DISTRICT_DIRECTOR_LIST,

  SET_MITACLUB_AGENT_SBU_DIRECTOR,
  SET_MITACLUB_AGENT_SBU_DIRECTOR_START,
  SET_MITACLUB_AGENT_SBU_DIRECTOR_ERROR,

  SET_MITACLUB_AGENT_MANAGER_LIST_START,
  SET_MITACLUB_AGENT_MANAGER_LIST,
  SET_MITACLUB_AGENT_MANAGER_LIST_ERROR,

  SET_MITACLUB_AGENTS_WITH_TEAM_START,
  SET_MITACLUB_AGENTS_WITH_TEAM,
  SET_MITACLUB_AGENTS_WITH_TEAM_ERROR,

  CLEAR_EMPLOYEES,
  SET_PERMISSION
} from "../constants/employee";

const initialState = {
  listed: false,
  listing: false,
  mitaclubInfo: [], // {},
  mitaclubError: null,
  mitaclubManagerInfo: [],
  mitaclubManagerInfoError: null,
  /** @type {Record<string, Object>} - { [teamCode]: AgentObj } */
  mitaclubAgentMap: {},
  mitaclubAgentError: null,

  mitaclubManagers: [],
  mitaclubManagersError: null,

  mitaclubAgentsWithTeam: [],
  mitaclubAgentsWithTeamError: null,

  mitaclubAgent: {},
  // mitaclubAgentError: null,
  permissions: {}
};

export default function employee(state = initialState, action) {
  switch (action.type) {
    case LIST_EMPLOYEES_START:
      return {
        ...state,
        listed: false,
        listing: true,
        employees: []
      };
    case LIST_EMPLOYEES_SUCCESS:
      return {
        ...state,
        listed: true,
        listing: false,
        employees: action.payload.data.data.employees
      };
    case LIST_EMPLOYEES_ERROR:
      return {
        ...state,
        listed: false,
        listing: false,
        error: action.payload.error
      };
    case LIST_VALID_EMPLOYEES_START:
      return {
        ...state,
        listed: false,
        listing: true,
        validEmployees: []
      };
    case LIST_VALID_EMPLOYEES_SUCCESS:
      return {
        ...state,
        listed: true,
        listing: false,
        validEmployees: [...state.employees, ...action.payload.data.data.employees]
      };
    case LIST_VALID_EMPLOYEES_ERROR:
      return {
        ...state,
        listed: false,
        listing: false,
        error: action.payload.error
      };
    case CLEAR_EMPLOYEES:
      return {
        ...state,
        listed: false,
        listing: false,
        employees: null,
        validEmployees: null
      };
    case SET_PERMISSION:
      const newState = {
        ...state,
        permissions: action.payload
      };
      return newState;

    case EMPLOYEES_MITACLUB_START:
      return {
        ...state,
        mitaclubInfo: initialState.mitaclubInfo,
      };
    case EMPLOYEES_MITACLUB_SUCCESS:
      return {
        ...state,
        mitaclubInfo: action.payload.data.data[action.payload.personalType],
        ...(
          action.payload.personalType === "mitaclub_district_manager"
            ? {
              mitaclubTeams: _.sortBy(
                _.get(action, "payload.data.data.mitaclub_district_manager"),
                row => parseFloat(row.AccumulatedSalesAmount),
              ).reverse(),
            }
            : {}
        )
      };
    case SET_MITACLUB_AGENT_DISTRICT_DIRECTOR_LIST:
      const groupedData = _.sortBy(
        _.map(
          _.groupBy(_.get(action, "payload.data.mitaclub_district_manager", []), "EmpId"),
          (items, empId) => {
            // Extract overlapped TeamCodes for EmpId "S2101962"
            const otherTeamCodes = _.flatMap(
              _.filter(_.get(action, "payload.data.mitaclub_district_manager", []), (item) => item.EmpId !== "S2101962"),
              (item) => Array.isArray(item.TeamCode) ? item.TeamCode : [item.TeamCode]
            );
            // Filter TeamCodes for EmpId "S2101962" to exclude those in otherTeamCodes
            const filteredItems = empId === "S2101962"
              ? _.filter(items, (item) =>
                !_.includes(otherTeamCodes, item.TeamCode)
              )
              : items;

            return {
              EmpId: empId,
              EnglishName: items[0].EnglishName,
              AccumulatedSalesAmount: _.sumBy(filteredItems, (item) => parseFloat(item.AccumulatedSalesAmount) || 0),
              AccumulatedSalesCase: _.sumBy(filteredItems, (item) => parseFloat(item.AccumulatedSalesCase) || 0),
              TeamCode: _.uniq(
                _.flatMap(filteredItems, (item) =>
                  Array.isArray(item.TeamCode) ? item.TeamCode : [item.TeamCode]
                )
              ),
            };
          }
        ),
        (row) => parseFloat(String(row.AccumulatedSalesAmount))
      ).reverse();
      return {
        ...state,
        mitaclubTeams: _.orderBy(groupedData, ["AccumulatedSalesAmount"], ["desc"]),
        mitaclubDistrictDirectors: _.get(action, "payload.data.mitaclub_district_manager", []),
      };

    case SET_MITACLUB_AGENT_SBU_DIRECTOR_START:
      return {
        ...state,
        mitaclubAgent: initialState.mitaclubAgent,
      };
    case SET_MITACLUB_AGENT_SBU_DIRECTOR:
      const accumulatedSBUDirector = _.reduce(
        _.get(action, "payload.data.mitaclub_district_manager"),
        (acc, row) => {
          acc.EmpId = row.EmpId;
          acc.EnglishName = row.EnglishName;
          acc.ExecutionDate = row.ExecutionDate;
          acc.ClubYear = row.ClubYear;
          acc.ClubMonth = row.ClubMonth;
          acc.AccumulatedSalesAmount +=
            parseFloat(row.AccumulatedSalesAmount) || 0;
          acc.AccumulatedSalesCase += parseFloat(row.AccumulatedSalesCase) || 0;
          acc.TeamCode = _.uniq([
            ...acc.TeamCode,
            ...(Array.isArray(row.TeamCode) ? row.TeamCode : [row.TeamCode]),
          ]);
          return acc;
        },
        {
          EmpId: "",
          EnglishName: "",
          AccumulatedSalesAmount: 0,
          AccumulatedSalesCase: 0,
          TeamCode: [],
          ExecutionDate: "",
          ClubYear: "",
          ClubMonth: "",
        },
      );

      return {
        ...state,
        mitaclubAgent: accumulatedSBUDirector,
      };
    case SET_MITACLUB_AGENT_SBU_DIRECTOR_ERROR:
      return {
        ...state,
        mitaclubAgentError: action.payload.error,
      };


    case SET_MITACLUB_AGENT_MANAGER_LIST_START:
      return {
        ...state,
        mitaclubAgentManagerList: initialState.mitaclubAgentManagerList,
      };
    case SET_MITACLUB_AGENT_MANAGER_LIST:
      return {
        ...state,
        mitaclubManagers: _.sortBy(
          _.get(action, "payload.data.mitaclub_manager", {}),
          row => parseFloat(row.AccumulatedSalesAmount),
        ).reverse(),
      };
    case SET_MITACLUB_AGENT_MANAGER_LIST_ERROR:
      return {
        ...state,
        mitaclubAgentManagerListError: action.payload.error,
      };

    case SET_MITACLUB_AGENTS_WITH_TEAM_START:
      return {
        ...state,
        mitaclubAgentsWithTeam: initialState.mitaclubAgentsWithTeam,
      };
    case SET_MITACLUB_AGENTS_WITH_TEAM:
      return {
        ...state,
        mitaclubAgentsWithTeam: _.get(action, "payload.data.mitaclub_agent", []),
      };
    case SET_MITACLUB_AGENTS_WITH_TEAM_ERROR:
      return {
        ...state,
        mitaclubAgentsWithTeamError: action.payload.error,
      };
    /** -------------------------- */
    case EMPLOYEES_MITACLUB_MANAGER_START:
      return {
        ...state,
        mitaclubManagerInfo: initialState.mitaclubManagerInfo,
      };
    case EMPLOYEES_MITACLUB_MANAGER_SUCCESS:
      return {
        ...state,
        mitaclubManagerInfo: action.payload.data.data['mitaclub_manager'],
      };
    case EMPLOYEES_MITACLUB_MANAGER_ERROR:
      return {
        ...state,
        mitaclubManagerInfoError: action.payload.error
      };
    case EMPLOYEES_MITACLUB_ERROR:
      return {
        ...state,
        mitaclubError: action.payload.error
      };
    case MITACLUB_MANAGER_AGENT_LIST_START:
      return {
        ...state,
        mitaclubAgentMap: initialState.mitaclubAgentMap,
      };
    case MITACLUB_MANAGER_AGENT_LIST_SUCCESS:
      const mitaclubAgentMap = action.payload.data.data.mitaclub_agent.reduce((prev, agent) => {
        if (!prev[agent.TeamCode]) {
          prev[agent.TeamCode] = [];
        }
        prev[agent.TeamCode].push(agent);
        return prev;
      }, {});
      return {
        ...state,
        mitaclubAgentMap,
      };
    case MITACLUB_MANAGER_AGENT_LIST_ERROR:
      return {
        ...state,
        mitaclubAgentError: action.payload.error
      };
    default:
      return state;
  }
}
