import React from "react";
import PropTypes from "prop-types";
import Dialog from "./Dialog";
import DialogFrame from "./DialogFrame";
import { MuiThemeProvider, createMuiTheme } from "@material-ui/core/styles";
import { withStyles } from "@material-ui/styles";
import CircularProgress from "@material-ui/core/CircularProgress";
import { injectIntl } from "react-intl";

const theme = createMuiTheme({
  overrides: {
    MuiOutlinedInput: {
      root: {
        "& $notchedOutline": {
          borderColor: "#FFF"
        },
        "&:hover $notchedOutline": {
          borderColor: "#FFF"
        },
        "&.Mui-focused $notchedOutline": {
          borderColor: "#FFF"
        }
      }
    },
    MuiInputBase: {
      root: {
        color: "#FFF"
      }
    },
    MuiFormLabel: {
      root: {
        color: "#FFF",
        "&.Mui-focused": {
          color: "#FFF"
        }
      }
    },
    MuiSelect: {
      icon: {
        color: "#FFF"
      }
    },
    circularLoader: {
      textAlign: "center",
      "& > div": {
        color: "#FFF"
      }
    }
  }
});

const styles = theme => ({
  container: {
    paddingTop: "2vh",
  },
  font18: {
    fontSize: "1.125em",
    textAlign: "center"
  },
  circularLoader: {
    marginTop: "2vh",
    textAlign: "center",
    "& > div": {
      color: "#FFF"
    }
  },
  errorText: {
    color: "#F44336",
    fontSize: "0.875em",
    textAlign: "center",
    marginTop: "2vh",
    maxHeight: "30vh",
    overflowY: "auto",
    overflowX: "hidden",
  }
});

class SubmitDialog extends React.Component {
  static propTypes = {
    dialogOpen: PropTypes.bool,
    handleCloseDialog: PropTypes.func,
    submitting: PropTypes.bool,
    submitted: PropTypes.bool,
    error: PropTypes.string,
    submit: PropTypes.func.isRequired,
    succCallback: PropTypes.func,
    errCallback: PropTypes.func,
    confirmMsg: PropTypes.string,
    submitBtnText: PropTypes.string,
    succMsg: PropTypes.string,
    children: PropTypes.node,
    mainButtonProps: PropTypes.object,
  };

  resetAndClose = () => {
    this.props.handleCloseDialog();
    this.props.submitted && this.props.succCallback && this.props.succCallback();
    this.props.error && this.props.errCallback && this.props.errCallback();
  };

  render() {
    const {
      classes,
      dialogOpen,
      submit,
      submitting,
      submitted,
      error,
      confirmMsg,
      submitBtnText,
      succMsg,
      children,
      mainButtonProps,
      intl,
    } = this.props;

    return (
      <Dialog
        open={dialogOpen}
        handleClose={this.resetAndClose}
        fullWidth={true}
      >
        <MuiThemeProvider theme={theme}>
          <div className={classes.container}>
            {submitted || error ? (
              <DialogFrame
                buttonMain={intl.formatMessage({
                  id: "common.ok"
                })}
                handleMain={this.resetAndClose}
              >
                {submitted && (
                  <div className={classes.font18}>
                    {succMsg}
                  </div>
                )}
                {error && (
                  <div className={classes.errorText}>
                    {error}
                  </div>
                )}
              </DialogFrame>
            ) : (
              <DialogFrame
                buttonMain={!submitting && submitBtnText}
                handleMain={submit}
                buttonMainProps={mainButtonProps}
              >
                <div className={classes.font18}>
                  {confirmMsg}
                </div>
                {submitting ? (
                  <div className={classes.circularLoader}>
                    <CircularProgress />
                  </div>
                ) : (
                  children
                )}
              </DialogFrame>
            )}
          </div>
        </MuiThemeProvider>
      </Dialog>
    );
  }
}

export default withStyles(styles)(injectIntl(SubmitDialog));
