import React from "react";
import { connect } from "react-redux";
import _ from "lodash";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import { injectIntl } from "react-intl";
import FieldValArrBox from "./FieldValArrBox";
import CallItem from "./CallItem";
import Grid from "@material-ui/core/Grid";
import SearchIcon from "@material-ui/icons/Search";
import FormButtonInline from "./FormButtonInline";
import {
  consolidateType,
  getLangKey,
  getReverseLangKey,
  goToSearchResult,
  parseNameWithBr,
  parseNameWithDash
} from "../../helper/generalHelper";
import { clearStock } from "../../actions/stock";
import { clearStockList } from "../../actions/stocklist";
import { enableConsolidLandSearch } from "../../config";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    padding: 0,
    backgroundColor: "transparent",
  },
  phonesContainer: {
    paddingTop: 8
  },
  contact: {
    display: "flex",
    alignItems: "center",
    "& > *:nth-child(2)": {
      marginLeft: "2vw",
      flex: "0 0 auto",
    },
  },
  hr: {
    borderTop: "1px solid #BBB",
    paddingBottom: 8,
  }
};

function TenancyRecordBoxPerson(props) {
  const {
    classes,
    field,
    tenant,
    mongoId,
    stockId,
    clearStockDetail,
    clearStockList,
    intl,
    ...others
  } = props;
  const langKey = getLangKey(intl);
  const reverseLangKey = getReverseLangKey(intl);

  const persons = tenant?.contactsPersons || [];
  let companies = tenant?.contactsCompanies || [];
  if (companies.length === 0) companies = [{}];
  const allPhones = [];
  persons.forEach(v => allPhones.push(...(v?.contact?.phones || [])));
  companies.forEach(v => allPhones.push(...(v?.contact?.phones || [])));

  const query = {
    contactsCompany: companies.map(v => {
      return {
        value: v._id,
        label: parseNameWithDash(v.nameEn, v.nameZh, intl),
      }
    }).filter(v => v.value && v.label && v.label !== "Unit Not Found").map(v => v.value),
    contactsPerson: persons
      .map(v => v[langKey] || v[reverseLangKey])
      .filter((v) => v && v !== "false"),
    contactsPhone: _.uniqBy(allPhones, "number").map(v => v.number),
    limit: 50,
    offset: 0,
    ...consolidateType
  };
  const selectedData = {
    contactsCompany: companies.map(v => {
      return {
        value: v._id,
        label: parseNameWithDash(v.nameEn, v.nameZh, intl),
      }
    }).filter(v => v.value && v.label && v.label !== "Unit Not Found"),
    contactsPerson: persons
      .map(v => v[langKey] || v[reverseLangKey])
      .filter((v) => v && v !== "false")
      .map(v => {
        return {
          value: v,
          label: v,
        }
      }),
    contactsPhone: _.uniqBy(allPhones, "number").map(v => {
      return {
        value: v.number,
        label: v.number,
      }
    }),
  };
  const goToSearchContactResult = () => {
    // console.log(query)
    // console.log(selectedData)
    clearStockDetail();
    clearStockList(false);
    goToSearchResult(query, selectedData, true, "consolidate");
  };
  const consolidateSearchBtn = enableConsolidLandSearch == "true" &&
    (query.contactsCompany.length > 0 || query.contactsPerson.length > 0 || query.contactsPhone.length > 0) && (
      <FormButtonInline onClick={goToSearchContactResult} icon={<SearchIcon />}>
        {intl.formatMessage({
          id: "stock.consosearch",
        })}
      </FormButtonInline>
    );

  const items = [
    {
      field: "",
      val: "",
      extra: consolidateSearchBtn,
    },
  ];

  let tenantPersons = persons.filter(v => v.isTenant);
  if (tenantPersons.length === 0) tenantPersons = [{}];
  let contactPersons = persons.filter(v => !v.isTenant);
  if (contactPersons.length === 0) contactPersons = [{}];

  const titleMapping = {
    Mr: intl.formatMessage({ id: "contact.title.mr" }),
    Ms: intl.formatMessage({ id: "contact.title.ms" }),
    Mrs: intl.formatMessage({ id: "contact.title.mrs" }),
    Miss: intl.formatMessage({ id: "contact.title.miss" }),
    Dr: intl.formatMessage({ id: "contact.title.dr" }),
  };

  const personDataToItem = (fieldName) => (person) => {
    const contactName = person[langKey] || person[reverseLangKey];
    const contactTitle = titleMapping[person.title];
    const phones = person.contact?.phones || [];
    let contactNameFull =
      contactTitle && contactName
        ? intl.locale === "zh"
        ? `${contactName} ${contactTitle}`
        : `${contactTitle} ${contactName}`
        : contactName || "---";
    if (contactName === "false") contactNameFull = "---";
    let CallItems = phones.length > 0 && (
      <Grid container spacing={1} className={classes.phonesContainer}>
        {phones.map((v, i) => (
          <Grid item xs={6} key={i}>
            <CallItem
              type={v.type}
              number={v.number}
              mongoId={mongoId}
              stockId={stockId}
              isDeniedCall={false}
            />
          </Grid>
        ))}
      </Grid>
    );

    items.push(
      {
        field: fieldName,
        val: contactNameFull,
        extra: CallItems,
      },
    );
  };

  tenantPersons.forEach(personDataToItem(field));
  contactPersons.forEach(personDataToItem(intl.formatMessage({ id: "stock.contact" })));

  companies.forEach(company => {
    const phones = company.contact?.phones || [];
    let CallItems = phones.length > 0 && (
      <Grid container spacing={1} className={classes.phonesContainer}>
        {phones.map((v, i) => (
          <Grid item xs={6} key={i}>
            <CallItem
              type={v.type}
              number={v.number}
              mongoId={mongoId}
              stockId={stockId}
              isDeniedCall={false}
            />
          </Grid>
        ))}
      </Grid>
    );

    items.push(
      {
        field: intl.formatMessage({ id: "stock.company" }),
        val: parseNameWithBr(company.nameEn, company.nameZh, intl),
        extra: CallItems,
      },
    );
  });

  return (
    <>
      <div className={classes.hr} />
      <FieldValArrBox
        className={classes.root}
        items={items}
        {...others}
      />
    </>
  );
}

TenancyRecordBoxPerson.propTypes = {
  classes: PropTypes.object.isRequired,
  tenant: PropTypes.object.isRequired,
  field: PropTypes.string,
  mongoId: PropTypes.string,
  stockId: PropTypes.number,
};

const mapDispatchToProps = dispatch => {
  return {
    clearStockDetail: () => dispatch(clearStock()),
    clearStockList: (...args) => dispatch(clearStockList(...args)),
  };
};

export default connect(
  null,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(TenancyRecordBoxPerson)));
