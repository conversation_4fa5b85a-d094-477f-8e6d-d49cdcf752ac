import React, { useEffect } from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { getFormValues } from "redux-form";
import { injectIntl } from "react-intl";
import _ from "lodash";

import langFile from "../../../../lang/COMM/messages";
import {
  clearCreateListProposal,
  createListProposal,
} from "../../../../actions/listProposal";
import ListProposalForm from "../../../Saleskit/Forms/ListProposalForm";
import FieldSection from "./FormSection/FieldSection";
import {
  findZhEnFromOptions,
  getDefaultRemarks,
  getFloorTypeInfoFromLangFile,
  getProposalName,
  getStockFloorType,
  langKey,
  parseMedia,
  parseTenancyRecords,
  titleMapping,
  getContainers,
  getFullAddress,
  getAreaType,
  getOriginalfloorTypeEnValue,
  getNotNullIsShow,
  mergeTenants,
  getStockStatus
} from "../../../Saleskit/helpers";
import { getLangKey, checkExpiredDate } from "../../../../helper/generalHelper";
import { sbu } from "../../../../config";

function List({
  detail,
  media,
  buildingMedia,
  stockUnitViews,
  stockDecorations,
  stockPossessions,
  stockCurrentStates,
  reCreatePPStocks,
  createDialogOpen,
  closeDialog,
  mode,
  createListProposal,
  clearCreateListProposal,
  intl,
  formState,
}) {
  useEffect(() => () => clearCreateListProposal(), []);

  const getStockInitialValues = (stockId) => {
    const stock = _.find(detail, (s) => s._id === stockId);

    const stockMediaData = _.find(
      media,
      (m) => m.id === _.get(stock, "unicorn.id").toString(),
    );
    // building can be null
    const buildingMediaData = _.find(
      buildingMedia,
      (m) => m.id === _.get(stock, "building.unicorn.id", "").toString(),
    );

    if (_.isEmpty(stock) || _.isNil(stockMediaData)) return {};
    let ppHistoryContent = null;
    if (reCreatePPStocks?.proposals?.length > 0) {
      ppHistoryContent = _.find(reCreatePPStocks.proposals, (p) => p.stockMongoId === stockId);
    } else if (!_.isEmpty(reCreatePPStocks)) {
      ppHistoryContent = reCreatePPStocks;
    }

    // determine stock type
    let type = null;
    if (_.get(ppHistoryContent, "type")) {
      type = _.get(ppHistoryContent, "type");
    } else if (mode !== "list") {
      type = getStockStatus(stock?.status?.nameEn)
    } else {
      type = "SaleAndLease";
    }

    const mainPhoto = []
      .concat(
        _.get(buildingMediaData, "data.photo", []),
        _.get(stockMediaData, "data.photo", []),
      )
      .find((m) => m?.tags?.indexOf("main") >= 0);

    const currentTenants = mergeTenants(
      parseTenancyRecords(_.get(stock, "tenancyRecords", []), intl, "Current"),
      ppHistoryContent?.currentTenants || []
    );

    const possibleFeeType = ["/SqFt", "/Qtr", "/Month", "/SY"];
    const possiblePaidBy = ["Paid By Tenant", "Paid By Landlord"];

    const areas = _(_.get(stock, "area.sizes", [])).reduce(
      (areaObj, item) => {
        if (!Object.keys(areaObj).includes(item.type)) return areaObj;
        return { ...areaObj, [item.type]: item.value || 0 };
      },
      { GROSS: 0, NET: 0, SALEABLE: 0, LETTABLE: 0 },
    );

    const getAreaValues = () => {
      const areaGross = _.get(ppHistoryContent, "areaGross.value", _.get(areas, "GROSS")) || 0;
      const areaNet = _.get(ppHistoryContent, "areaNet.value", _.get(areas, "NET")) || 0;
      const areaSaleable = _.get(ppHistoryContent, "areaSaleable.value", _.get(areas, "SALEABLE")) || 0;
      const areaLettable = _.get(ppHistoryContent, "areaLettable.value", _.get(areas, "LETTABLE")) || 0;

      const areaGrossIsShow = _.get(ppHistoryContent, "areaGross.isShow", !!areaGross) || false;
      const areaNetIsShow = _.get(ppHistoryContent, "areaNet.isShow", !!areaNet) || false;
      const areaSaleableIsShow = _.get(ppHistoryContent, "areaSaleable.isShow", !!areaSaleable) || false;
      const areaLettableIsShow = _.get(ppHistoryContent, "areaLettable.isShow", !!areaLettable) || false;

      const areaValues = {
        areaGross: [{ value: areaGross, isShow: areaGrossIsShow }],
        areaNet: [{ value: areaNet, isShow: areaNetIsShow }],
        areaSaleable: [{ value: areaSaleable, isShow: areaSaleableIsShow }],
        areaLettable: [{ value: areaLettable, isShow: areaLettableIsShow }],
      };

      return {
        ...areaValues,
      }
    };

    const { areaGross, areaLettable, areaNet, areaSaleable } = getAreaValues();

    const containers = getContainers(stock).map((v) => ({
      value: v[langKey(intl.locale, "name")] || "---",
      haveLoadingBay: v.haveLoadingBay,
      isShow: !!v[langKey(intl.locale, "name")],
    }));

    const floor = _.get(stock, "floor") || "";
    const floorType = "Actual Floor";
    // const floorType = !_.isNaN(parseInt(floor, 10))
    //   ? "Actual Floor"
    //   : getStockFloorType(
    //       _.get(stock, "building.floors") || [],
    //       parseInt(floor, 10),
    //     );

    const floors = _.get(stock, "building.floors") || [];
    const floorTmp = !isNaN(parseInt(_.get(stock, "floor")))
      ? parseInt(_.get(stock, "floor"))
      : _.get(stock, "floor");
    const correspondingFloor = _.find(floors, v => {
      if (!v.name || typeof v.name !== "string") return;

      if (typeof floorTmp === "number") {
        const min = parseInt(v.name.split("-")[0]);
        const max = parseInt(v.name.split("-")[1]);
        if (!isNaN(min) && !isNaN(max)) {
          if (min <= floorTmp && max >= floorTmp) return true;
        }

        const num = parseInt(v.name);
        if (!isNaN(num)) {
          if (num === floorTmp) return true;
        }
      }

      if (typeof floorTmp === "string" && floorTmp !== "") {
        if (v.name === floorTmp) return true;
      }
    });

    const managementFeeTemp = _.get(stock, "managementFee.number") ? _.get(stock, "managementFee.number") : correspondingFloor?.managementFee ? correspondingFloor?.managementFee : parseFloat(_.get(stock, `building.management.fee`)?.replace('@', '') || 0);

    const managementFeeUnit = _.get(ppHistoryContent, "managementFee.unit") ||
    (_.indexOf(possibleFeeType, _.get(stock, "managementFee.type")) >
      -1
      ? _.get(stock, "managementFee.type", "")
      : "");

    const totalMonthlyMgtFee = managementFeeUnit === "/SqFt" ? parseFloat(managementFeeTemp) * parseFloat(_.get(areaGross, "[0].value", 0)) : 0;
    const photos = (ppHistoryContent?.photos || []).filter(p => p?.id);
    const photoIds = photos.map(obj => obj.id);
    return {
      stock: {
        stockId: _.get(stock, "unicorn.id"),
        show: true,
        avgPrice: [
          {
            value: _.get(ppHistoryContent, "avgPrice.value") || _.get(stock, "askingPrice.average", 0),
            // Proposal type is Sale by default
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "avgPrice.isShow"), (type === "Sale" || type === "SaleAndLease")),
          },
        ],
        totalPrice: [
          {
            value: _.get(ppHistoryContent, "totalPrice.value") || _.get(stock, "askingPrice.total", 0),
            // Proposal type is Sale by default
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "totalPrice.isShow"), (type === "Sale" || type === "SaleAndLease")),
            isNego: ppHistoryContent ? _.get(ppHistoryContent, "totalPrice.isNego") || false : false,
          },
        ],
        bottomAvgPrice: [
          {
            value: _.get(ppHistoryContent, "bottomAvgPrice.value") || _.get(
              _.find(_.get(stock, "askingPrice.details", []), [
                "type",
                "average_bottom",
              ]),
              "value",
              0,
            ),
            isShow: _.get(ppHistoryContent, "bottomAvgPrice.isShow") || false,
          }
        ],
        bottomTotalPrice: [
          {
            value: _.get(ppHistoryContent, "bottomTotalPrice.value") || _.get(
              _.find(_.get(stock, "askingPrice.details", []), [
                "type",
                "total_bottom",
              ]),
              "value",
              0,
            ),
            isShow: _.get(ppHistoryContent, "bottomTotalPrice.value") || false
          }
        ],
        avgRent: [
          {
            value: _.get(ppHistoryContent, "avgRent.value") || _.get(stock, "askingRent.average", 0),
            // Proposal type is Sale by default
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "avgRent.isShow"), (type === "Lease" || type === "SaleAndLease")),
          },
        ],
        totalRent: [
          {
            value: _.get(ppHistoryContent, "totalRent.value") || _.get(stock, "askingRent.total", 0),
            // Proposal type is Sale by default
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "totalRent.isShow"), (type === "Lease" || type === "SaleAndLease")),
            isNego: _.get(ppHistoryContent, "totalRent.isNego") || false,
          },
        ],
        bottomAvgRent: [
          {
            value: _.get(ppHistoryContent, "bottomAvgRent.value") || _.get(
              _.find(_.get(stock, "askingRent.details", []), [
                "type",
                "average_bottom",
              ]),
              "value",
              0,
            ),
            isShow: _.get(ppHistoryContent, "bottomAvgRent.isShow") || false,
          }
        ],
        bottomTotalRent: [
          {
            value: _.get(ppHistoryContent, "bottomTotalRent.value") || _.get(
              _.find(_.get(stock, "askingRent.details", []), [
                "type",
                "total_bottom",
              ]),
              "value",
              0,
            ),
            isShow: _.get(ppHistoryContent, "bottomTotalRent.isShow") || false,
          }
        ],
        stockType: [
          {
            value: _.get(ppHistoryContent, `stockType.value.${langKey(intl.locale, "name")}`) || _.get(stock, `stockType.${langKey(intl.locale, "name")}`),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "stockType.isShow"), true),
          },
        ],
        floor: [
          {
            value: _.get(ppHistoryContent, `floor.value`) || floor,
            isShow: getNotNullIsShow(_.get(ppHistoryContent, `floor.isShow`), true),
          },
        ],
        unit: [
          {
            value: _.get(ppHistoryContent, `unit.value`) || _.get(stock, "unit", ""),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, `unit.isShow`), true),
          },
        ],
        customAddressZh: _.get(ppHistoryContent, "customAddressZh") || getFullAddress({ ...intl, locale: "zh" }, stock),
        customAddressEn: _.get(ppHistoryContent, "customAddressEn") || getFullAddress({ ...intl, locale: "en" }, stock),
        customBuilding: {
          value: _.get(ppHistoryContent, `customBuilding.value`) || _.get(
            stock,
            `building.${langKey(intl.locale, "name")}`,
            "---",
          ),
          isShow: getNotNullIsShow(_.get(ppHistoryContent, `customBuilding.isShow`), true),
        },
        customStreet: _.get(ppHistoryContent, `customStreet`) || _.get(
          stock,
          `building.street.street.${langKey(intl.locale, "name")}`,
          "",
        ),
        customStreetNo: _.get(ppHistoryContent, `customStreetNo`) || _.get(stock, "building.street.number", ""),
        customDistrict: _.get(ppHistoryContent, `customDistrict`) || _.get(
          stock,
          `building.district.${langKey(intl.locale, "name")}`,
          "---",
        ),
        areaEfficiency: [
          {
            value: _.get(ppHistoryContent, `areaEfficiency.value`) || _.get(stock, "area.efficiency", 0),
            isShow: _.get(ppHistoryContent, `areaEfficiency.isShow`) || false,
          },
        ],
        areaGross,
        areaNet,
        areaSaleable,
        areaLettable,
        possession: [
          {
            value: _.get(ppHistoryContent?.possession?.value, "nameEn") || _.get(stock, `possession.nameEn`, ""),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "possession.isShow"), !(type === 'Lease')),
          },
        ],
        ceilingHeight: [
          {
            ft: _.get(ppHistoryContent, "ceilingHeight.ft") || _.get(stock, "ceilingHeight.ft") || "",
            in: _.get(ppHistoryContent, "ceilingHeight.in") || _.get(stock, "ceilingHeight.in") || "",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "ceilingHeight.isShow"), true),
          },
        ],
        managementFee: [
          {
            value: _.get(ppHistoryContent, "managementFee.value") || managementFeeTemp,
            unit: managementFeeUnit,
            paidBy: _.get(ppHistoryContent, "managementFee.paidBy") ||
              (_.indexOf(possiblePaidBy, _.get(stock, "managementFee.paidBy")) >
                -1
                ? _.get(stock, "managementFee.paidBy", "")
                : ""),
            paidByIsShow: (ppHistoryContent ? _.get(ppHistoryContent, "managementFee.paidByIsShow", _.get(ppHistoryContent, `includedFee.managementFee`))
              : _.get(stock, "managementFee.paidBy", "") === "Paid By Landlord") || false,
            isShow: (ppHistoryContent ? _.get(ppHistoryContent, `managementFee.isShow`) : !!managementFeeTemp) || false,
            totalMonthlyMgtFee: _.get(ppHistoryContent, "managementFee.totalMonthlyMgtFee", Number.isInteger(totalMonthlyMgtFee) ? totalMonthlyMgtFee : Math.round(totalMonthlyMgtFee + 0.5)),
            totalMonthlyMgtFeeIsShow: (ppHistoryContent ? _.get(ppHistoryContent, `managementFee.totalMonthlyMgtFeeIsShow`)
              : _.get(stock, "managementFee.totalMonthlyMgtFeeIsShow")) || false,
          },
        ],
        gRent: [
          {
            value: _.get(ppHistoryContent, "gRent.value") || _.get(stock, "governmentRent.number") || 0,
            paidBy: _.get(ppHistoryContent, "gRent.paidBy") || _.get(stock, "governmentRent.paidBy") || "",
            paidByIsShow: getNotNullIsShow(_.get(ppHistoryContent, "gRent.paidByIsShow"),
              _.get(ppHistoryContent, `includedFee.gRent`, _.get(stock, "governmentRent.paidBy", "") === "Paid By Landlord")),
            unit: _.get(ppHistoryContent, "gRent.unit") || _.get(stock, "governmentRent.type") || "",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "gRent.isShow"), !!_.get(stock, "governmentRent.number") || false),
          }
        ],
        rates: [
          {
            value: _.get(ppHistoryContent, "rates.value", _.get(stock, "rates.number") || 0),
            paidBy: _.get(ppHistoryContent, "rates.paidBy", _.get(stock, "rates.paidBy")) || "",
            paidByIsShow: getNotNullIsShow(_.get(ppHistoryContent, "rates.paidByIsShow"),
              _.get(ppHistoryContent, `includedFee.rates`, _.get(stock, "rates.paidBy", "") === "Paid By Landlord")),
            unit: _.get(ppHistoryContent, "rates.unit") || _.get(stock, "rates.type") || "",
            isShow: _.get(ppHistoryContent, "rates.isShow") || false,
          }
        ],
        acFee: [
          {
            value: _.get(ppHistoryContent, "acFee.value", _.get(stock, "airConditioningFee.number") || 0),
            paidBy: _.get(ppHistoryContent, "acFee.paidBy") ||
              _.get(stock, "airConditioningFee.paidBy") || "",
            paidByIsShow: getNotNullIsShow(_.get(ppHistoryContent, "acFee.paidByIsShow"),
              _.get(ppHistoryContent, `includedFee.acFee`, _.get(stock, "airConditioningFee.paidBy", "") === "Paid By Landlord")),
            unit: _.get(ppHistoryContent, "acFee.unit") || _.get(stock, "airConditioningFee.type") || "",
            isShow: _.get(ppHistoryContent, "acFee.isShow", !!_.get(stock, "airCond.fee")) || false,
          }
        ],
        includedFee: [
          {
            managementFee: getNotNullIsShow(_.get(ppHistoryContent, "includedFee.managementFee"),
              _.get(stock, "managementFee.paidBy", "") === "Paid By Landlord"),
            rates: getNotNullIsShow(_.get(ppHistoryContent, "includedFee.rates"),
              _.get(stock, "rates.paidBy", "") === "Paid By Landlord"),
            gRent: getNotNullIsShow(_.get(ppHistoryContent, "includedFee.gRent"),
              _.get(stock, "governmentRent.paidBy", "") === "Paid By Landlord"),
            acFee: getNotNullIsShow(_.get(ppHistoryContent, "includedFee.acFee"),
              _.get(stock, "airConditioningFee.paidBy", "") === "Paid By Landlord"),
          },
        ],
        currentState: [
          {
            value: _.get(ppHistoryContent, "currentState.value.nameEn") || _.get(stock, `currentState.nameEn`, ""),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "currentState.isShow"), true),
          }
        ],
        areaTerrace: [
          {
            value: _.get(ppHistoryContent, "areaTerrace.value", _.get(stock, "area.terrace")),
            isShow: _.get(ppHistoryContent, "areaTerrace.isShow") || false,
          }
        ],
        areaRoof: [
          {
            value: _.get(ppHistoryContent, "areaRoof.value", _.get(stock, "area.roof")),
            isShow: _.get(ppHistoryContent, "areaRoof.isShow") || false,
          }
        ],
        floorLoading: [
          {
            value: _.get(ppHistoryContent, "floorLoading.value", _.get(stock, "floorLoading")),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "floorLoading.isShow"), true),
          }
        ],
        decoration: [
          {
            value: _.get(ppHistoryContent, "decoration.value.nameEn", _.get(stock, `decoration.nameEn`)),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "decoration.isShow"), !!_.get(stock, `decoration.nameEn`)),
          },
        ],
        unitView: [
          {
            value: _.get(ppHistoryContent, "unitView.value.nameEn", _.get(stock, `unitView.nameEn`)),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "unitView.isShow"), !!_.get(stock, `unitView.nameEn`)),
          },
        ],
        availability: [
          {
            value: _.get(ppHistoryContent, "availability.value", _.get(stock, "availability")),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "availability.isShow"), !!_.get(stock, "availability")),
          },
        ],
        remarks: _.get(ppHistoryContent, "remarks") || "",
        yield: [
          {
            value: _.get(ppHistoryContent, "yield.value", _.get(stock, "yield") || 0),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "yield.isShow"), !!_.get(stock, "yield")),
          },
        ],
        usage: [
          {
            value: _.get(ppHistoryContent, `building.usage.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.buildingUsage.${langKey(intl.locale, "name")}`,
              ) || "---",
            isShow: _.get(ppHistoryContent, "building.usage.isShow") || false,//!!_.get(
            //   stock,
            //   `building.buildingUsage.${langKey(intl.locale, "name")}`,
            // ),
          },
        ],
        title: [
          {
            value: _.get(ppHistoryContent, `building.title.value.${langKey(intl.locale)}`) ||
              _.get(
                _.get(titleMapping, _.get(stock, "building.title", ""), {}),
                langKey(intl.locale, "name"),
              ) || "---",
            isShow: _.get(ppHistoryContent, "building.title.isShow") || false,
          },
        ],
        inTakeDate: [
          {
            value: _.get(ppHistoryContent, "building.inTakeDate.value") ||
              _.defaultTo(_.get(stock, "building.completionDate"), "---"),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "building.inTakeDate.isShow"), !!_.get(stock, "building.completionDate")),
          },
        ],
        managementCompany: [
          {
            value: _.get(ppHistoryContent, `building.managementCompany.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.managementCompany.managementCompany.${langKey(
                  intl.locale,
                  "name",
                )}`,
              ) || "---",
            isShow: _.get(ppHistoryContent, "building.managementCompany.isShow") || false,
          },
        ],
        transport: [
          {
            value: _.get(ppHistoryContent, `building.transport.value.${langKey(intl.locale)}`) ||
              _.get(stock, `building.${langKey(intl.locale, "transport")}`) ||
              "---",
            isShow: _.get(ppHistoryContent, "building.transport.isShow") || false,
          },
        ],
        allInclusive: getNotNullIsShow(_.get(ppHistoryContent, "allInclusive"), _.every(
          [
            _.get(stock, "managementFee.paidBy", "") === "Paid By Landlord",
            _.get(stock, "rates.paidBy", "") === "Paid By Landlord",
            _.get(stock, "governmentRent.paidBy", "") === "Paid By Landlord",
          ],
          Boolean,
        )),
        passengerLift: [
          {
            value: _.get(ppHistoryContent, `building.passengerLift.value.${langKey(intl.locale)}`) ||
              _.get(
                _.find(_.get(stock, "building.lifts"), {
                  type: "Passenger",
                }),
                "quantity",
              ) || "---",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "building.passengerLift.isShow"), true),
          },
        ],
        cargoLift: [
          {
            value: _.get(ppHistoryContent, `building.cargoLift.value.${langKey(intl.locale)}`) ||
              _.get(
                _.find(_.get(stock, "building.lifts"), {
                  type: "Cargo",
                }),
                "quantity",
              ) || "---",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "building.cargoLift.isShow"), true),
          },
        ],
        containers: _.get(ppHistoryContent, "building.containers", containers),
        airConditioningType: [
          {
            value: _.get(ppHistoryContent, `building.airConditioningType.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.airConditioning.type.${langKey(intl.locale, "name")}`,
              ) || "---",
            isShow: _.get(ppHistoryContent, "building.airConditioningType.isShow") || true,
          },
        ],
        airConditioningOpeningTime: [
          {
            value: _.get(ppHistoryContent, `building.airConditioningOpeningTime.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.airConditioning.openingTime.${langKey(intl.locale, "name")}`,
              ) || "---",
            isShow: _.get(ppHistoryContent, "building.airConditioningOpeningTime.isShow") || true,
          },
        ],
        airConditioningExtraCharges: [
          {
            value: _.get(ppHistoryContent, `building.airConditioningExtraCharges.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.airConditioning.extraCharges.${langKey(intl.locale, "name")}`,
              ) || "---",
            isShow: _.get(ppHistoryContent, "building.airConditioningExtraCharges.isShow") || false,
          },
        ],
        currentTenants,
        floorType: getOriginalfloorTypeEnValue(_.get(ppHistoryContent, `floorType.${langKey('en')}`, floorType)),
        termRemarks: _.get(ppHistoryContent, "termRemarks") || getDefaultRemarks(type),
        customTitle: {
          value: _.get(ppHistoryContent, "customTitle.value") || "",
          isShow: _.get(ppHistoryContent, "customTitle.isShow") || false,
        },
      },
      media: {
        ...(mode === "indv"
          ? {
              mainPhoto: _.get(ppHistoryContent, "mainPhoto.id") || mainPhoto?.id || null,
              main1Photo: _.get(ppHistoryContent, "main1Photo.id") || null,
            }
          : {
              mainPhoto: _.get(ppHistoryContent, "mainPhoto.id") || _.get(ppHistoryContent, "main1Photo.id") || mainPhoto?.id,
            }),
        googleMapPhoto: photoIds.includes('map') ? ['map'] : [],
        selectedMedia: photos.length ? photoIds : [],
        buildingMedia: !ppHistoryContent ? [] : []
          .concat(buildingMedia?.photo || [], buildingMedia?.video || [])
          ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
          ?.map((v) => v.id),
        stockMedia: !ppHistoryContent ? [] : _.map(_.get(ppHistoryContent, "photos", []), (p) => _.get(p, "id")) || []
          .concat(media?.photo || [], media?.video || [])
          ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
          ?.map((v) => v.id),
        lat: _.get(ppHistoryContent, "lat", _.get(stock, "building.coordinates.latitude")),
        lng: _.get(ppHistoryContent, "lng", _.get(stock, "building.coordinates.longitude")),
        govLat: _.get(ppHistoryContent, "govLat", _.get(stock, "building.coordinates.latitude")),
        govLng: _.get(ppHistoryContent, "govLng", _.get(stock, "building.coordinates.longitude")),
        attachmentMultiImgConfig: photos.length ? _.reduce(_.get(ppHistoryContent, "photos", []), (result, photo) => {
          if (!photo?.id || (photo.id === "map" && !_.get(ppHistoryContent, "googleMap.isPP", false))) {
            return result;
          }
          result[photo.id] = photo.multiImg;
          return result;
        }, {}) : {},
      },
    };
  };

  const customSort = (a, b) => {
    const districtA = _.get(a, `building.district.nameZh`);
    const districtB = _.get(b, `building.district.nameZh`);
    const buildingA = _.get(a, `building.nameZh`);
    const buildingB = _.get(b, `building.nameZh`);
    let floorA = _.get(a, 'floor');
    let floorB = _.get(b, 'floor');
    let unitA = _.get(a, 'unit');
    let unitB = _.get(b, 'unit');
    if (districtA?.localeCompare(districtB) !== 0) {
      return districtA?.localeCompare(districtB, 'zh-Hant', { numeric: true })
    } else if (buildingA?.localeCompare(buildingB) !== 0) {
      return buildingA?.localeCompare(buildingB, 'zh-Hant', { numeric: true })
    } else if (floorA !== floorB) {
      return floorA?.localeCompare(floorB, undefined, { numeric: true })
    } else {
      return unitA?.localeCompare(unitB, undefined, { numeric: true })
    }
  }

  const initializeFormState = () => {
    const ppHistoryContent = reCreatePPStocks;
    // determine stock type
    let type = null;
    if (_.get(ppHistoryContent, "type")) {
      type = _.get(ppHistoryContent, "type");
    } else if (mode !== "list") {
      type = getStockStatus(_.get(detail, "0.status.nameEn"))
    } else {
      type = "SaleAndLease";
    }

    const termRemarks = _.get(ppHistoryContent, "termRemarks") || getDefaultRemarks(type);

    const form = {
      stocks: detail.reduce((stockForms, stock) => {
        const stockId = _.get(stock, "_id");
        if (_.isNull(stockId)) return stockForms;

        return {
          ...stockForms,
          [stockId]: getStockInitialValues(stockId),
        };
      }, {}),
      //order: detail.map((stock) => stock._id),
      order: detail.sort(customSort).map(stock => stock._id),
    };

    const floorNum = parseInt(_.get(detail, "0.floor"), 10);
    const floorType = "Actual Floor";
    // const floorType = _.isNaN(floorNum)
    //   ? "Actual Floor"
    //   : getStockFloorType(_.get(detail, "0.building.floors") || [], floorNum);

    form.general = {
      type,
      multiImg: _.get(ppHistoryContent, "multiImg", "FOUR") || "FOUR",
      showEmployeePhoto: [
        {
          value: intl.formatMessage({
            id: "proposal.form.showemployeephoto",
          }),
          isShow: getNotNullIsShow(_.get(ppHistoryContent, "showEmployeePhoto"), true),
        },
      ],
      showContact: [
        {
          value: intl.formatMessage({
            id: "proposal.form.showcontact",
          }),
          isShow: getNotNullIsShow(_.get(ppHistoryContent, "showContact"), true),
        },
      ],
      exactFloor: [
        {
          value: intl.formatMessage({
            id: "proposal.form.exactFloor",
          }),
          isShow: getNotNullIsShow(_.get(ppHistoryContent, "exactFloor"), true),
        },
      ],
      showUnit: [
        {
          value: intl.formatMessage({
            id: `proposal.form.showUnit`,
          }),
          isShow: true,
        },
      ],
      ...(mode === "list" ? {
      showMainPhoto: [
        {
          value: intl.formatMessage({
            id: "proposal.form.showMainPhoto",
          }),
          isShow: true,
        },
      ],
      showTenancy: [
        {
          value: intl.formatMessage({
            id: "proposal.form.showTenancy",
          }),
          isShow: false,
        },
      ],
      showPossession: [
        {
          value: intl.formatMessage({
            id: "proposal.form.showPossession",
          }),
          isShow: true,
        },
      ],
      showCurrentState: [
        {
          value: intl.formatMessage({
            id: "proposal.form.currentState.column",
          }),
          isShow: true,
        },
      ],
      } : {}),
      proposalName: _.get(ppHistoryContent, "proposalName") || getProposalName(intl, _.get(detail, "0"), floorType),
      // companyTitle: _.get(ppHistoryContent, "companyTitle") || "midlandici", // List PP 沒有該屬性
      lang: _.get(ppHistoryContent, "lang") || "CHI",
      customTitle: {
        value: _.get(ppHistoryContent, "customTitle.value") || "",
        isShow: _.get(ppHistoryContent, "customTitle.isShow") || false,
      },
      termRemarks,
      mode,
    };
    return form;
  };

  // const parseProposal = (form, stock) => {
  //   const { stock: stockValue, media: mediaValue } = form;

  //   const containers = getContainers(stock);

  //   const stockMediaData = _.get(
  //     media.find((m) => m.id === _.get(stock, "unicorn.id").toString()),
  //     "data.photo",
  //     [],
  //   );

  //   // building can be null
  //   const buildingMediaData = _.get(
  //     buildingMedia.find(
  //       (m) => m.id === _.get(stock, "building.unicorn.id", "").toString(),
  //     ),
  //     "data.photo",
  //     [],
  //   );
  //   const mediaData = [].concat(stockMediaData, buildingMediaData);

  //   const parsedForm = {
  //     stockId: _.get(stock, "unicorn.id"),
  //     stockMongoId: _.get(stock, "_id"),
  //     floor: _.get(stockValue, "floor[0]"),
  //     // wf unit display in CHI will be handled on saleskit
  //     // parse back to wf if CHI wf is used
  //     unit: {
  //       ..._.get(stockValue, "unit[0]", {}),
  //       value:
  //         _.get(stockValue, "unit[0].value") === "全層"
  //           ? "WF"
  //           : _.get(stockValue, "unit[0].value"),
  //     },
  //     avgPrice: {
  //       ..._.get(stockValue, "avgPrice[0]"),
  //       value: _.get(stockValue, "avgPrice[0].value", 0),
  //     },
  //     totalPrice: {
  //       ..._.get(stockValue, "totalPrice[0]"),
  //       value: _.get(stockValue, "totalPrice[0].value", 0),
  //     },
  //     avgRent: {
  //       ..._.get(stockValue, "avgRent[0]"),
  //       value: _.get(stockValue, "avgRent[0].value", 0),
  //     },
  //     totalRent: {
  //       ..._.get(stockValue, "totalRent[0]"),
  //       value: _.get(stockValue, "totalRent[0].value", 0),
  //     },

  //     areaEfficiency: _.get(stockValue, "areaEfficiency[0]"),
  //     areaGross: _.get(stockValue, "areaGross[0]"),
  //     areaNet: _.get(stockValue, "areaNet[0]"),
  //     areaSaleable: _.get(stockValue, "areaSaleable[0]"),
  //     areaLettable: _.get(stockValue, "areaLettable[0]"),

  //     possession: {
  //       value: _.defaultTo(
  //         findZhEnFromOptions(
  //           stockPossessions,
  //           _.get(stockValue, "possession[0].value"),
  //         ),
  //         null,
  //       ),
  //       isShow: _.get(stockValue, "possession[0].isShow"),
  //     },
  //     decoration: {
  //       value: _.defaultTo(
  //         findZhEnFromOptions(
  //           stockDecorations,
  //           _.get(stockValue, "decoration[0].value"),
  //         ),
  //         null,
  //       ),
  //       isShow: _.get(stockValue, "decoration[0].isShow"),
  //     },
  //     unitView: {
  //       value: _.defaultTo(
  //         findZhEnFromOptions(
  //           stockUnitViews,
  //           _.get(stockValue, "unitView[0].value"),
  //         ),
  //         null,
  //       ),
  //       isShow: _.get(stockValue, "unitView[0].isShow"),
  //     },
  //     yield: {
  //       ..._.get(stockValue, "yield[0]", {}),
  //       value: parseFloat(_.get(stockValue, "yield[0].value") || "0"),
  //     },
  //     stockType: {
  //       ..._.get(stockValue, "stockType[0]", {}),
  //       value: {
  //         ..._.pick(_.get(stock, "stockType"), ["nameEn", "nameZh"]),
  //       },
  //     },
  //     ceilingHeight: {
  //       isShow: _.get(stockValue, "ceilingHeight[0].isShow") || false,
  //       ft: parseInt(_.get(stockValue, "ceilingHeight[0].ft", 0), 10) || 0,
  //       in: parseInt(_.get(stockValue, "ceilingHeight[0].in", 0), 10) || 0,
  //     },
  //     managementFee: _.get(stockValue, "managementFee[0]"),
  //     availability: _.get(stockValue, "availability[0]"),
  //     includedFee: _.get(stockValue, "includedFee[0]"),
  //     allInclusive: _.get(stockValue, "allInclusive"),
  //     remarks: _.get(stockValue, "remarks"),
  //     floorType: getFloorTypeInfoFromLangFile(langFile, stockValue?.floorType),
  //     districtNameZh: _.get(stock, "building.district.nameZh", ""),
  //     districtNameEn: _.get(stock, "building.district.nameEn", ""),
  //     streetNameZh: _.get(stock, "building.street.street.nameZh", ""),
  //     streetNameEn: _.get(stock, "building.street.street.nameEn", ""),
  //     streetNo: _.get(stock, "building.street.number", ""),
  //     mainPhoto:
  //       _.get(mediaValue, "main1Photo") === "map"
  //         ? null
  //         : parseMedia(mediaData, _.get(mediaValue, "main1Photo")),
  //     photos: _.concat(
  //       [],
  //       _.map(_.get(mediaValue, "stockMedia", []), (m) =>
  //         parseMedia(mediaData, m),
  //       ),
  //       _.map(_.get(mediaValue, "buildingMedia", []), (m) =>
  //         parseMedia(mediaData, m),
  //       ),
  //     ),
  //     includeGGMap: _.get(mediaValue, "googleMapPhoto", []).length > 0,
  //     useGGMapPhoto: _.get(mediaValue, "main1Photo") === "map",
  //     building: {
  //       nameZh: _.get(stock, "building.nameZh", ""),
  //       nameEn: _.get(stock, "building.nameEn", ""),
  //       districtNameZh: _.get(stock, "building.district.nameZh", ""),
  //       districtNameEn: _.get(stock, "building.district.nameEn", ""),
  //       usage: {
  //         ..._.get(stockValue, "usage[0]", {}),
  //         value: {
  //           nameZh: _.get(stock, "building.buildingUsage.nameZh", ""),
  //           nameEn: _.get(stock, "building.buildingUsage.nameEn", ""),
  //         },
  //       },
  //       title: {
  //         ..._.get(stockValue, "title[0]", {}),
  //         value: {
  //           nameZh:
  //             _.get(
  //               _.get(titleMapping, _.get(stock, "building.title", ""), {}),
  //               "nameZh",
  //             ) || "---",
  //           nameEn:
  //             _.get(
  //               _.get(titleMapping, _.get(stock, "building.title", ""), {}),
  //               "nameEn",
  //             ) || "---",
  //         },
  //       },
  //       containers: _(_.get(stockValue, "containers", [])).map((v, i) => ({
  //         ...v,
  //         value: containers[i],
  //       })),
  //       managementCompany: {
  //         ..._.get(stockValue, "managementCompany[0]", {}),
  //         value: {
  //           nameZh:
  //             _.get(
  //               stock,
  //               "building.managementCompany.managementCompany.nameZh",
  //             ) || "---",
  //           nameEn:
  //             _.get(
  //               stock,
  //               "building.managementCompany.managementCompany.nameEn",
  //             ) || "---",
  //         },
  //       },
  //       transport: {
  //         ..._.get(stockValue, "transport[0]", {}),
  //         value: {
  //           nameZh: _.get(stock, `building.transportZh`) || "---",
  //           nameEn: _.get(stock, `building.transportEn`) || "---",
  //         },
  //       },
  //       passengerLift: {
  //         ..._.get(stockValue, "passengerLift[0]", {}),
  //         value: {
  //           nameZh: _.get(stockValue, "passengerLift[0].value", "---"),
  //           nameEn: _.get(stockValue, "passengerLift[0].value", "---"),
  //         },
  //       },
  //       cargoLift: {
  //         ..._.get(stockValue, "cargoLift[0]", {}),
  //         value: {
  //           nameZh: _.get(stockValue, "cargoLift[0].value", "---"),
  //           nameEn: _.get(stockValue, "cargoLift[0].value", "---"),
  //         },
  //       },
  //       airConditioningType: {
  //         ..._.get(stockValue, "airConditioningType[0]", {}),
  //         value: {
  //           nameZh: _.get(stock, "building.airConditioning.type.nameZh", "---"),
  //           nameEn: _.get(stock, "building.airConditioning.type.nameEn", "---"),
  //         },
  //       },
  //       lat: _.get(stock, "building.coordinates.latitude"),
  //       lng: _.get(stock, "building.coordinates.longitude"),
  //     },
  //     isSoleagent:
  //       !_.isNil(_.get(stock, "soleagent.periodStart")) &&
  //       !_.isNil(_.get(stock, "soleagent.periodEnd")) &&
  //       checkExpiredDate(
  //         _.get(stock, "soleagent.periodStart"),
  //         _.get(stock, "soleagent.periodEnd"),
  //       ),
  //     currentTenants: _.map(_.get(stockValue, "currentTenants", []), (v) => ({
  //       tenant: {
  //         ...(_.get(v, "tenant") || {}),
  //         isShow: v.tenantIsShow,
  //       },
  //       rentalFee: {
  //         value: v.rentalFee || 0,
  //         isShow: v.rentalFeeIsShow,
  //       },
  //       period: {
  //         ...(_.get(v, "period") || {}),
  //         isShow: v.periodIsShow,
  //       },
  //       tenancy: {
  //         ...(_.get(v, "tenancy") || {}),
  //         isShow: v.tenancyIsShow,
  //       },
  //     })),
  //   };
  //   return parsedForm;
  // };

  // const handleSubmit = (values) => {
  //   const { stocks: stocksForm, general, order } = values;

  //   const parsedStockForms = order
  //     .map((stockId) =>
  //       _.get(stocksForm, `${stockId}.stock.show`)
  //         ? parseProposal(
  //             stocksForm[stockId],
  //             detail.find((s) => s._id === stockId),
  //           )
  //         : null,
  //     )
  //     .filter((form) => !!form);

  //   const variables = {
  //     ...general,
  //     sbu,
  //     hideEmployeePhoto: _.get(general, "hideEmployeePhoto[0].isShow"),
  //     hideContact: _.get(general, "hideContact[0].isShow"),
  //     hideMainPhoto: _.get(general, "hideMainPhoto[0].isShow"),
  //     hideTenancy: _.get(general, "hideTenancy[0].isShow"),
  //     proposals: parsedStockForms,
  //   };

  //   createListProposal(variables);
  // };

  return (
    <ListProposalForm
      onSubmit={() => createListProposal(mode)}
      initialValues={initializeFormState()}
      FormComponent={FieldSection}
      createDialogOpen={createDialogOpen}
      closeDialog={closeDialog}
      mode={mode}
    />
  );
}

List.propTypes = {
  detail: PropTypes.array.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  detail: _.get(state, "stock.detail", []),
  media: _.get(state, "stock.media", []),
  buildingMedia: _.get(state, "building.media", []),
  stockUnitViews: _.get(state, "stock.unitViews", []),
  stockDecorations: _.get(state, "stock.decorations", []),
  stockPossessions: _.get(state, "stock.possessions", []),
  stockCurrentStates: _.get(state, "stock.currentStates", []),
  reCreatePPStocks: _.get(state, "proposal.reCreatePPStocks", []),
  formState: getFormValues("proposal")(state),
});

const mapDispatchToProps = (dispatch) => ({
  createListProposal: (mode) => dispatch(createListProposal(mode)),
  clearCreateListProposal: () => dispatch(clearCreateListProposal()),
});

export default connect(mapStateToProps, mapDispatchToProps)(injectIntl(List));
