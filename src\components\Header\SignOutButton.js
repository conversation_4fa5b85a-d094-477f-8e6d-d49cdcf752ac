import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import FormButton from "../common/FormButton";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    fontSize: "1.1em",
    backgroundColor: "#2076AD",
    boxShadow: "none",
    minWidth: "56px",
    "&:hover": {
      color: "#2076AD"
    },
  }
};

function SignOutButton(props) {
  const { classes, children, className, ...other } = props;

  return (
    <FormButton className={clsx(classes.root, className)} {...other}>
      {children}
    </FormButton>
  );
}

SignOutButton.propTypes = {
  children: PropTypes.node,
  classes: PropTypes.object.isRequired,
  className: PropTypes.string
};

export default withStyles(styles)(SignOutButton);
