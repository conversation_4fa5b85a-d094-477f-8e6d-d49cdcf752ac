import {
  LIST_SEARCHRESULT_START,
  LIST_MORESEARCHRESULT_START,
  LIST_SEARCHRESULT_SUCCESS,
  LIST_SEARCHRESULT_ERROR,
  LIST_SEARCHRESULTNULL_SUCCESS,
  LIST_UNLOCKEDSEARCHRESULT_START,
  LIST_<PERSON><PERSON><PERSON>UNLOCKEDSEARCHRESULT_START,
  LIST_UNLOCKEDSEARCHRESULT_SUCCESS,
  LIST_UNLOCKEDSEARCHRESULT_ERROR,
  LIST_UNLOCKEDSEARCHRESULTNULL_SUCCESS,
  LIST_SEARCHRESULTBYKEYWORDS_SUCCESS,
  LIST_SEARCHRESULTBYKEYWORDSNULL_SUCCESS,
  LIST_DECORATION_START,
  LIST_DECORATION_SUCCESS,
  LIST_DECORATION_ERROR,
  LIST_POSSESSION_START,
  LIST_POSSESSION_SUCCESS,
  LIST_POSSESSION_ERROR,
  LIST_USAGE_START,
  LIST_USAGE_SUCCESS,
  LIST_USAGE_ERROR,
  LIST_CURRENTSTATE_START,
  LIST_CURRENTSTATE_SUCCESS,
  LIST_CURRENTSTATE_ERROR,
  LIST_STOCKTYPE_START,
  LIST_STOCKTYPE_SUCCESS,
  LIST_STOCKTYPE_ERROR,
  UNLOCK_STOCK_START,
  UNLOCK_STOCK_SUCCESS,
  UNLOCK_STOCK_ERROR,
  GET_UNLOCKCOUNT_SUCCESS,
  CLEAR_SEARCHRESULT,
  CLEAR_UNLOCKEDSEARCHRESULT,
  LIST_QUERY_SAVEHISTORY,
  GET_SEARCHHISTORY,
  GET_STREET_SEARCHHISTORY,
  SET_SEARCHRESULT_ANCHOR,
  CLEAR_SEARCHRESULT_ANCHOR,
  SAVE_SEARCH_START,
  SAVE_SEARCH_SUCCESS,
  SAVE_SEARCH_UPSERT_SUCCESS,
  SAVE_SEARCH_ERROR,
  CLEAR_SAVE_SEARCH,
  LIST_SEARCH_START,
  LIST_SEARCH_SUCCESS,
  LIST_SEARCH_ERROR,
  CLEAR_LIST_SEARCH,
  LIST_COMPANY_START,
  LIST_COMPANY_SUCCESS,
  LIST_COMPANY_ERROR,
  CLEAR_LIST_COMPANY,
} from "../constants/stocklist";

import _ from "lodash";
import Cookies from "universal-cookie";

const initialState = {
  listed: false,
  valid: false,
  stocks: [],
  searches: [],
  unlockedListed: false,
  unlockedStocks: [],
  unlockedStockIds: [],
  fieldhistory: [],
  streethistory: [],
  companies: [],
  listedCompany: false,
  listingCompany: false,
};

export default function stock(state = initialState, action) {
  switch (action.type) {
    case LIST_SEARCHRESULT_START: {
      return {
        ...state,
        listing: true,
        // listed: false,
        stocks: [],
        stocksCount: 0,
        hasMore: true,
        queryvariables: action.payload.variables,
        selectedData: action.payload.selectedData,
        uuid: action.payload.uuid,
      };
    }
    case LIST_MORESEARCHRESULT_START:
      return {
        ...state,
        listing: true,
        hasMore: true,
        queryvariables: action.payload.variables,
        selectedData: action.payload.selectedData,
        uuid: action.payload.uuid,
      };
    case LIST_SEARCHRESULT_SUCCESS:
      if (state.uuid !== action.payload.uuid) {
        return state;
      }
      return {
        ...state,
        listing: false,
        listed: true,
        hasMore:
          state.stocks.length + action.payload.data.stocks.length <
          action.payload.data.stocksCount,
        stocks: state.stocks.concat(action.payload.data.stocks),
        stocksCount: action.payload.data.stocksCount,
      };
    case LIST_SEARCHRESULTNULL_SUCCESS:
      if (state.uuid !== action.payload.uuid) {
        return state;
      }
      return {
        ...state,
        listing: false,
        listed: true,
        hasMore: false,
        stocks: state.stocks.concat(action.payload.data.stocks),
        stocksCount: action.payload.data.stocksCount,
      };
    case LIST_UNLOCKEDSEARCHRESULT_START: {
      return {
        ...state,
        unlockedListing: true,
        unlockedStocks: [],
        unlockedStocksCount: 0,
        unlockedHasMore: true,
        unlockedQueryvariables: action.payload.variables,
      };
    }
    case LIST_MOREUNLOCKEDSEARCHRESULT_START:
      return {
        ...state,
        unlockedListing: true,
        unlockedHasMore: true,
        unlockedQueryvariables: action.payload.variables,
      };
    case LIST_UNLOCKEDSEARCHRESULT_SUCCESS:
      return {
        ...state,
        unlockedListing: false,
        unlockedListed: true,
        unlockedHasMore:
          state.unlockedStocks.length + action.payload.data.stocks.length <
          action.payload.data.stocksCount,
        unlockedStocks: state.unlockedStocks.concat(action.payload.data.stocks),
        unlockedStocksCount: action.payload.data.stocksCount,
      };
    case LIST_UNLOCKEDSEARCHRESULTNULL_SUCCESS:
      return {
        ...state,
        unlockedListing: false,
        unlockedListed: true,
        unlockedHasMore: false,
        unlockedStocks: state.unlockedStocks.concat(action.payload.data.stocks),
        unlockedStocksCount: action.payload.data.stocksCount,
      };
    case LIST_SEARCHRESULTBYKEYWORDS_SUCCESS:
      return {
        ...state,
        listing: false,
        listed: true,
        hasMore: true,
        stocks: state.stocks.concat(action.payload.data.stocksOr),
        stocksCount: action.payload.data.stocksOrCount,
      };
    case LIST_SEARCHRESULTBYKEYWORDSNULL_SUCCESS:
      return {
        ...state,
        listing: false,
        listed: true,
        hasMore: false,
        stocks: state.stocks.concat(action.payload.data.stocksOr),
        stocksCount: action.payload.data.stocksOrCount,
      };
    case LIST_DECORATION_START:
      return {
        ...state,
      };
    case LIST_DECORATION_SUCCESS:
      return {
        ...state,
        decoration: action.payload.data.data.decorations,
      };
    case LIST_DECORATION_ERROR:
      return {
        ...state,
      };
    case LIST_POSSESSION_START:
      return {
        ...state,
      };
    case LIST_POSSESSION_SUCCESS:
      return {
        ...state,
        possession: action.payload.data.data.possessions,
      };
    case LIST_POSSESSION_ERROR:
      return {
        ...state,
      };
    case LIST_USAGE_START:
      return {
        ...state,
      };
    case LIST_USAGE_SUCCESS:
      return {
        ...state,
        usage: action.payload.data.data.usages,
      };
    case LIST_USAGE_ERROR:
      return {
        ...state,
      };
    case LIST_CURRENTSTATE_START:
      return {
        ...state,
      };
    case LIST_CURRENTSTATE_SUCCESS:
      return {
        ...state,
        currentState: action.payload.data.data.currentState,
      };
    case LIST_CURRENTSTATE_ERROR:
      return {
        ...state,
      };
    case LIST_STOCKTYPE_START:
      return {
        ...state,
      };
    case LIST_STOCKTYPE_SUCCESS:
      return {
        ...state,
        stockType: action.payload.data.data.stockTypes,
      };
    case LIST_STOCKTYPE_ERROR:
      return {
        ...state,
      }
    case LIST_SEARCHRESULT_ERROR:
      return {
        ...state,
        unlockedListing: false,
        error: action.payload.error,
      };
    case LIST_UNLOCKEDSEARCHRESULT_ERROR:
      return {
        ...state,
        listing: false,
        error: action.payload.error,
      };
    case UNLOCK_STOCK_START:
      return {
        ...state,
        unlockfinished: false,
      };
    case UNLOCK_STOCK_SUCCESS: {
      const newUnlockedList = state.unlockedStockIds.concat(
        action.payload.data.stockid,
      );
      return {
        ...state,
        unlockedStockIds: newUnlockedList,
        count: newUnlockedList.length,
        unlockfinished: true,
      };
    }
    case UNLOCK_STOCK_ERROR:
      return {
        ...state,
      };
    case GET_UNLOCKCOUNT_SUCCESS:
      return {
        ...state,
        count: action.payload.data.count,
        unlockedStockIds: action.payload.data.unlockedStockIds,
      };
    case CLEAR_SEARCHRESULT:
      return {
        ...state,
        listing: false,
        listed: false,
        stocks: [],
        stocksCount: 0,
        queryvariables: null,
        selectedData: null,
        error: null,
      };
    case CLEAR_UNLOCKEDSEARCHRESULT:
      return {
        ...state,
        unlockedListing: false,
        unlockedListed: false,
        unlockedStocks: [],
        unlockedStocksCount: 0,
        unlockedQueryvariables: null,
        error: null,
      };
    case LIST_QUERY_SAVEHISTORY:
      const cookies = new Cookies();
      let searchHistoryfromCookies = cookies.get("searchHistory");
      let itemExistfromCookies;
      if (searchHistoryfromCookies !== undefined) {
        itemExistfromCookies = searchHistoryfromCookies.find((item) => {
          return item._id === action.payload._id;
        });
        // set in browser cookies
        cookies.set(
          "searchHistory",
          itemExistfromCookies === undefined
            ? searchHistoryfromCookies.concat(action.payload)
            : searchHistoryfromCookies,
        );
      }

      // set in redux store
      const itemExist = state.fieldhistory.find((item) => {
        return item._id === action.payload._id;
      });

      return {
        ...state,
        fieldhistory:
          itemExist === undefined
            ? state.fieldhistory.concat(action.payload)
            : state.fieldhistory,
      };
    case GET_SEARCHHISTORY: {
      const cookies = new Cookies();
      let searchHistoryfromCookies = cookies.get("searchHistory");
      if (searchHistoryfromCookies == undefined) {
        searchHistoryfromCookies = [];
      }

      return {
        ...state,
        fieldhistory: searchHistoryfromCookies,
      };
    }
    case SET_SEARCHRESULT_ANCHOR: {
      return {
        ...state,
        searchResultAnchor: action.payload.px,
      };
    }
    case CLEAR_SEARCHRESULT_ANCHOR: {
      return {
        ...state,
        searchResultAnchor: null,
      };
    }
    case SAVE_SEARCH_START:
      return {
        ...state,
        savingSearch: true,
        savedSearch: false,
      };
    case SAVE_SEARCH_SUCCESS:
      const searches = Array.isArray(state.searches) ? state.searches : [];
      return {
        ...state,
        savingSearch: false,
        savedSearch: true,
        searches: searches.concat(action.payload || []),
        modifiedSearch: true,
      };
    case SAVE_SEARCH_UPSERT_SUCCESS:
      return {
        ...state,
        savingSearch: false,
        savedSearch: true,
        searches: state.searches.map((v) =>
        v.name === action.payload.name
          ? { ...v, query: action.payload.query }
          : v,
      ),
        upsertedSearch: true,
      };
    case SAVE_SEARCH_ERROR:
      return {
        ...state,
        savingSearch: false,
        savedSearch: false,
        saveSearchError: action.payload.error,
      };
    case CLEAR_SAVE_SEARCH:
      return {
        ...state,
        savingSearch: false,
        savedSearch: false,
        upsertedSearch: false,
        modifiedSearch: false,
        saveSearchError: null,
      };
    case LIST_SEARCH_START:
      return {
        ...state,
        listingSearch: true,
        listedSearch: false,
      };
    case LIST_SEARCH_SUCCESS:
      return {
        ...state,
        listingSearch: false,
        listedSearch: true,
        searches: action.payload.data,
      };
    case LIST_SEARCH_ERROR:
      return {
        ...state,
        listingSearch: false,
        listedSearch: false,
        listSearchError: action.payload.error,
      };
    case CLEAR_LIST_SEARCH:
      return {
        ...state,
        listingSearch: false,
        listedSearch: false,
        listSearchError: null,
        searches: [],
      };
    case LIST_COMPANY_START:
      return {
        ...state,
        listedCompany: false,
        listingCompany: true,
      };
    case LIST_COMPANY_SUCCESS:
      return {
        ...state,
        listedCompany: true,
        listingCompany: false,
        companies: action.payload.data.data.contactsCompanies,
      };
    case LIST_COMPANY_ERROR:
      return {
        ...state,
        listedCompany: false,
        listingCompany: false,
      };
    case CLEAR_LIST_COMPANY:
      return {
        ...state,
        listedCompany: false,
        listingCompany: false,
        companies: [],
      };
    default:
      return state;
  }
}
