import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import ButtonGroup from "@material-ui/core/ButtonGroup";
import { injectIntl, FormattedMessage } from "react-intl";
import DetailTabPanelFrame from "../../../../common/DetailTabPanelFrame";
import { listStockMedia } from "../../../../../actions/stock";
import { getProposalCount } from "../../../../../actions/proposal";
import { listBuildingMedia } from "../../../../../actions/building";
import FormButton from "../../../../common/FormButton";
import ProposalCreate from "./ProposalCreate";
import { getDisplayStockId } from "../../../../../helper/generalHelper";
import { generalProposalQuota } from "../../../../../config";

const styles = (theme) => ({
  wrapper: {
    backgroundColor: "#f5f5f5",
    minHeight: "calc(100vh - 88px - 2vw)",
  },
  fixBtnContainer: {
    width: "100%",
    zIndex: "999",
    position: "fixed",
    left: 0,
    bottom: 0,
  },
  fixBtn: {
    textTransform: "none",
    fontSize: "1.125em",
    height: "60px",
    minWidth: "auto",
    lineHeight: "1em",
  },
  sectionWrapper: {
    paddingTop: "1vh",
  },
  title: {
    color: "#EC1F26",
  },
  num: {
    backgroundColor: "#EC1F26",
  },
  link: {
    textDecoration: "none",
  },
  formContainer: {
    paddingBottom: "8vh",
  },
});

class Media extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    media: PropTypes.object,
    listedMedia: PropTypes.bool,
    listingMedia: PropTypes.bool,
    buildingMedia: PropTypes.object,
    listedBuildingMedia: PropTypes.bool,
    listingBuildingMedia: PropTypes.bool,
    detail: PropTypes.object,
    listed: PropTypes.bool,
    listStockMedia: PropTypes.func,
    listBuildingMedia: PropTypes.func,
    getProposalCount: PropTypes.func,
    handleClickOpenDialog: PropTypes.func,
    userInfo: PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.state = {
      requestMedia: true,
      createProposalDialogOpen: false,
      exceedQuota: false
    };
  }

  listStockMediaAfterIdReturned = () => {
    const {
      detail,
      listed,
      listStockMedia,
      listBuildingMedia,
      getProposalCount,
      userInfo,
    } = this.props;

    if (
      this.state.requestMedia &&
      listed &&
      detail &&
      detail.unicorn &&
      Number.isInteger(detail.unicorn.id)
    ) {
      this.setState({ requestMedia: false });
      let variables = {
        sid: detail.unicorn.id.toString(),
        empId: userInfo.emp_id,
      };
      listStockMedia(variables);
      getProposalCount();
    }

    if (
      this.state.requestMedia &&
      listed &&
      detail &&
      detail.building &&
      detail.unicorn &&
      Number.isInteger(detail.building.unicorn.id)
    ) {
      this.setState({ requestMedia: false });
      let variables = {
        sid: detail.building.unicorn.id.toString(),
        empId: userInfo.emp_id,
      };
      listBuildingMedia(variables);
    }
  };

  componentDidMount() {
    this.listStockMediaAfterIdReturned();
  }

  componentDidUpdate() {
    this.listStockMediaAfterIdReturned();
  }

  handleOpenCreateProposalDialog = () => {
    const proposalscount = this.props.createdPdf;
    if (proposalscount >= generalProposalQuota) {
      this.setState({ exceedQuota: true });
    } else {
      this.setState({ exceedQuota: false });
    }
    this.setState({ createProposalDialogOpen: true });
    window.dataLayer.push({
      stockId: getDisplayStockId(this.props.detail.unicorn.id),
    });
  };

  handleCloseCreateProposalDialog = () => {
    this.setState({ createProposalDialogOpen: false });
  };

  render() {
    const {
      classes,
      detail,
      listing: listingStockData,
      listed: listedStockData,
      media,
      listingMedia,
      listedMedia,
      buildingMedia,
      listingBuildingMedia,
      listedBuildingMedia,
      handleClickOpenDialog,
      intl,
    } = this.props;
    const { createProposalDialogOpen, exceedQuota } = this.state;

    const stockId = detail._id;
    const hasStockData = Object.keys(media).length > 0;
    const hasBuildingData = Object.keys(buildingMedia).length > 0;

    const hasData = hasStockData || hasBuildingData;
    const listing = listingStockData || listingMedia || listingBuildingMedia;
    const listed = listedStockData && (listedMedia || listedBuildingMedia);

    return (
      <DetailTabPanelFrame
        wrapperProps={{
          className: classes.wrapper,
        }}
        hasData={hasData}
        listing={listing}
        listed={listed}
        notFoundText="Stock media not found"
      >
        <div className={classes.formContainer}>
          <ProposalCreate
            createProposalDialogOpen={createProposalDialogOpen}
            exceedQuota={exceedQuota}
            handleCloseCreateProposalDialog={
              this.handleCloseCreateProposalDialog
            }
          />
        </div>

        <div className={classes.fixBtnContainer}>
          <ButtonGroup fullWidth>
            <FormButton
              className={classes.fixBtn}
              onClick={this.handleOpenCreateProposalDialog}
              id={"createProposalButton"}
            >
              <FormattedMessage id="proposal.createpdf" />
            </FormButton>
            <FormButton
              className={classes.fixBtn}
              onClick={handleClickOpenDialog}
            >
              <FormattedMessage id="stock.upload" />
            </FormButton>
          </ButtonGroup>
        </div>
      </DetailTabPanelFrame>
    );
  }
}

const mapStateToProps = (state) => ({
  userInfo:
    state.auth &&
    state.auth.user &&
    state.auth.user.login &&
    state.auth.user.login.info
      ? state.auth.user.login.info
      : {},
  media: state.stock.media ? state.stock.media : {},
  listedMedia: state.stock.listedMedia ? state.stock.listedMedia : false,
  listingMedia: state.stock.listingMedia ? state.stock.listingMedia : false,
  buildingMedia: state.building.media ? state.building.media : {},
  listedBuildingMedia: state.building.listedMedia
    ? state.building.listedMedia
    : false,
  listingBuildingMedia: state.building.listingMedia
    ? state.building.listingMedia
    : false,
  detail: state.stock.detail ? state.stock.detail : {},
  listing: state.stock.listing ? state.stock.listing : false,
  listed: state.stock.listed ? state.stock.listed : false,
  createdPdf: state.proposal.createdPdf ? state.proposal.createdPdf : 0,
});

const mapDispatchToProps = (dispatch) => {
  return {
    listStockMedia: (...args) => dispatch(listStockMedia(...args)),
    listBuildingMedia: (...args) => dispatch(listBuildingMedia(...args)),
    getProposalCount: () => dispatch(getProposalCount()),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(Media)));
