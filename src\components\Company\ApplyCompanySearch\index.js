import React, { useState } from "react";
import PropTypes from "prop-types";
import { Box, DialogContent } from "@material-ui/core";
import { FormattedMessage, injectIntl } from "react-intl";
import { connect } from "react-redux";
import { getFormSyncErrors, submit } from "redux-form";
import _ from "lodash";

import DetailTabPanelFrame from "@/components/common/DetailTabPanelFrame";
import ApplyCompanySearchForm from "./form";
import InputErrorDialog from "../../common/InputErrorDialog";
import { takeLandsearchLog } from "@/actions/log";
import SearchConsentDialog from "@/components/common/SearchConsentDialog";
import CustomizedDialog from "@/components/common/Dialog";
import { clearApplyCompanySearch } from "@/actions/company";
import ErrorsDialog from "@/components/common/ErrorsDialog";

function ApplyCompanySearch({
  submitForm,
  inputErrors,
  applying,
  applyOk,
  applyErrors,
  clearApplySearch,
  takeLog,
  intl,
  isListApply = false
}) {
  const [showInputError, setShowInputError] = useState(false);
  const [showConsent, setShowConsent] = useState(false);

  const sendLog = (
    callback,
    requireHkId = false,
    log_seq = null,
    status = null,
  ) => takeLog(requireHkId, log_seq, status).then(callback);

  const renderConsentContent = (logRes) => (
    <>
      <p>就此次查冊申請，本人謹此確認：</p>
      <p>
        (i) 同意僱主公司披露本人的姓名[
        {_.get(logRes, "emp_eng_name")}]及身分證明文件編號[
        {_.get(logRes, "hkid")}
        ]予公司以提供予土地註冊處作查冊申請；
      </p>
      <p>
        (ii) 無意及不會違反《個人資料(私隱)條例》(第 486 章) (《私隱條例》)
        的情況下使用透過查冊獲取的個人資料(“該等資料”)；
      </p>
      <p>
        (iii)
        無意及不會將該等資料用於與備存及供公眾人士查閱相關紀錄的宗旨無關之目的；及
      </p>
      <p>
        (iv)
        明白本人的個人資料可能會在《私隱條例》許可下被披露或轉交予執法機關。
      </p>
    </>
  );

  return (
    <DetailTabPanelFrame
      bottomButtons={[
        {
          label: intl.formatMessage({ id: "common.ok" }),
          onClick: () => {
            if (!_.isEmpty(inputErrors)) {
              setShowInputError(true);
            } else {
              setShowConsent(true);
            }
          },
        },
      ]}
    >
      <Box>
        <ApplyCompanySearchForm isListApply={isListApply} />
      </Box>
      <SearchConsentDialog
        open={showConsent}
        handleClose={() => setShowConsent(false)}
        renderChildren={renderConsentContent}
        okMsg="確認及繼續申請"
        cancelMsg="取消申請"
        okCallback={submitForm}
        sendLog={sendLog}
        isApplyConsent
      />

      <CustomizedDialog
        open={!applying && applyOk}
        handleClose={() => clearApplySearch()}
      >
        <DialogContent style={{ padding: "10px 20px" }}>
          {applyOk && <FormattedMessage id="company.applySearch.success" />}
        </DialogContent>
      </CustomizedDialog>
      <InputErrorDialog
        open={showInputError}
        handleClose={() => setShowInputError(false)}
        errors={_.groupBy(Object.values(inputErrors), "categoryMsgId")}
      />
      <ErrorsDialog
        errors={_.map(
          Array.isArray(applyErrors) ? applyErrors : [applyErrors],
          (e) => ({
            message: e.message,
            btn: intl.formatMessage({ id: "common.cancel" }),
            btnHandleClick: () => clearApplySearch(false),
          }),
        )}
      />
    </DetailTabPanelFrame>
  );
}

ApplyCompanySearch.propTypes = {
  submitForm: PropTypes.func.isRequired,
  inputErrors: PropTypes.object.isRequired,

  applying: PropTypes.bool.isRequired,
  applyOk: PropTypes.bool.isRequired,
  applyErrors: PropTypes.array.isRequired,

  clearApplySearch: PropTypes.func.isRequired,
  takeLog: PropTypes.func.isRequired,
  intl: PropTypes.object.isRequired,
  isListApply: PropTypes.bool
};

const mapDispatchToProps = (dispatch) => ({
  submitForm: () => dispatch(submit("applyCompanySearch")),
  takeLog: (requireHkid, logSeq, status) =>
    dispatch(
      takeLandsearchLog({
        stockId: "",
        refNo: null,
        docPath: null,
        location: "Apply Company Search",
        status,
        logSeq,
        requireHkid,
      }),
    ),
  clearApplySearch: () => dispatch(clearApplyCompanySearch()),
});

const mapStateToProps = (state) => ({
  inputErrors: getFormSyncErrors("applyCompanySearch")(state),

  applying: state.company.applyingSearch,
  applyOk: state.company.applySearchOk,
  applyErrors: state.company.applySearchErrors,
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(ApplyCompanySearch));
