import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import moment from "moment";
import UUID from "uuid/v1";
import {
  submit,
  touch,
  getFormSyncErrors,
  formValueSelector,
  change
} from "redux-form";
import Dialog from "../Dialog";
import Form from "./Form";
import { clearProgressingMedium, createMedium } from "../../../actions/medium";
import DialogFrame from "../DialogFrame";
import StepOne from "./StepOne";
import StepTwo from "./StepTwo";
import StepThree from "./StepThree";
import StepFour from "./StepFour";
import { MuiThemeProvider, createMuiTheme } from "@material-ui/core/styles";
import { injectIntl } from "react-intl";
import { sbu } from "../../../config";
import {
  getDisplayStockId,
} from "../../../../src/helper/generalHelper";
import _ from "lodash";
import { PERMISSIONS } from "@/constants/auth";
const { AUTOMATICALLY_SHARE_TO_PUBLIC } = PERMISSIONS;

const theme = createMuiTheme({
  overrides: {
    MuiOutlinedInput: {
      root: {
        "& $notchedOutline": {
          borderColor: "#FFF"
        },
        "&:hover $notchedOutline": {
          borderColor: "#FFF"
        },
        "&.Mui-focused $notchedOutline": {
          borderColor: "#FFF"
        }
      }
    },
    MuiInputBase: {
      root: {
        color: "#FFF"
      }
    },
    MuiFormLabel: {
      root: {
        color: "#FFF",
        "&.Mui-focused": {
          color: "#FFF"
        }
      }
    },
    MuiSelect: {
      icon: {
        color: "#FFF"
      }
    }
  }
});
const acceptMap = Object.freeze({
  kol_video: ".mp4, .mov",
  document: ".pdf",
  photo: ".jpg, .png, .jpeg",
  video: ".mov, .mp4",
});
class UploadMediaDialog extends React.Component {
  static propTypes = {
    dialogOpen: PropTypes.bool,
    handleCloseDialog: PropTypes.func,
    clearProgressingMedium: PropTypes.func.isRequired,
    createMedium: PropTypes.func.isRequired,
    dispatchSubmitForm: PropTypes.func.isRequired,
    touchForm: PropTypes.func.isRequired,
    changeForm: PropTypes.func.isRequired,
    formSynchronousError: PropTypes.object.isRequired,
    allProgressingMedia: PropTypes.array,
    validEmployees: PropTypes.array.isRequired,
    mediaType: PropTypes.oneOf(['document', 'kol_video', 'video', 'photo']),
    AUTOMATICALLY_SHARE_TO_PUBLIC: PropTypes.bool,
    callback: PropTypes.func
  };

  constructor(props) {
    super(props);
    this.state = {
      step: 1,
      processingUuid: null,
      approval: 'pending',
    };
  }

  getPhotoContent = data => {
    let photoContent = undefined;
    if (data.isBuildingOutlook) photoContent = "layout";
    if (data.isInterior) photoContent = "interior";
    if (data.isFloorPlan) photoContent = "plan";
    if (data.isLobby) photoContent = "lobby";
    if (data.isEntry) photoContent = "entrance";
    return photoContent;
  };

  handleSubmit = data => {
    const { buildingId, streetId, stockId, validEmployees } = this.props;
    const availableStartTime = data.availableStartTime
      ? moment(data.availableStartTime, "YYYY-MM-DD").format("DD/MM/YYYY")
      : undefined;
    const availableEndTime = data.availableEndTime
      ? moment(data.availableEndTime, "YYYY-MM-DD").format("DD/MM/YYYY")
      : undefined;
    
    // 基础媒体对象属性
    const baseMedium = {
      availableStartTime,
      availableEndTime,
      status: "invalid",
      approval: this.state.approval,
      type: this.props.initialValues.mediaType,
    };

    if (data.noDateLimit) {
      delete baseMedium.availableStartTime;
      delete baseMedium.availableEndTime;
    }

    // 添加上传目标
    _.merge(baseMedium, {
      stock: { stockId },
      building: { buildingId },
      street: { streetId },
    }[data.uploadTo]);

    if (data.uploadTo === "Unit" && Number.isInteger(stockId)) {
      baseMedium.stockId = stockId;
    }
    // 处理媒体内容类型
    if (this.props.initialValues.mediaType === "document") {
      _.set(baseMedium, "documentContent", data.documentContent);
    } else {
      _.set(baseMedium, "photoContent", data.photoContent || undefined);
    }
    // if (this.props.AUTOMATICALLY_SHARE_TO_PUBLIC) {
    //   baseMedium.approval = "approved";
    // }

    if (data.employeeId) {
      const employee = validEmployees.find(e => e.emp_id === data.employeeId) || {};
      baseMedium.employeeId = data.employeeId;
      baseMedium.employee = {
        id: employee.emp_id || "",
        branchId: employee.dept_code || "",
        cName: employee.name_zh || "",
        eName: employee.name_en || "",
        cTitle: employee.cTitle || "",
        eTitle: employee.eTitle || "",
        licence: employee.licence || "",
        phone: employee.phone || "",
        email: employee.email || "",
        sex: employee.sex || "",
      };
    }
    
    // if (data.photographer) {
    //   medium.employee = {
    //     id: data.photographer.id,
    //     branchId: data.photographer.branchId,
    //     cName: data.photographer.cName,
    //     eName: data.photographer.eName,
    //     cTitle: data.photographer.cTitle,
    //     eTitle: data.photographer.eTitle,
    //     licence: data.photographer.licence,
    //     phone: data.photographer.phone,
    //     email: data.photographer.email,
    //     sex: data.photographer.sex,
    //   };
    //   medium.employeeId = data.photographer.id;
    // }

    // the uuid is used to identify a set of files which are uploaded together, not to identify each single file
    const uuid = UUID();
    this.setState({ processingUuid: uuid });

    this.toStep(3);

    // 处理每个文件的上传
    if (this.props.initialValues.mediaType === 'kol_video') {
      // KOL视频仍然保持单文件上传
      const medium = {
        ...baseMedium,
        characteristicEn: data.characteristicEn || undefined,
        characteristicZh: data.characteristicZh || undefined,
        description: data.description || undefined,
        buildingName: data.buildingName || undefined,
        address: data.address || undefined,
        propertyRefId: getDisplayStockId(stockId) || undefined,
        bgm: data.bgm || undefined,
        isShowContact: data.isShowContact || undefined,
      };
      return Promise.all([
        this.props.createMedium(medium, [data.files[0], data?.thumbnail?.[0] || null], uuid)
      ]);
    } else {
      // 其他类型支持多文件上传，并且每个文件有独立的属性
      return Promise.all(
        Array.from(data.files).map(file => {
          const fileProps = file.fileProperties || {};
          const medium = {
            ...baseMedium,
            description: fileProps.description || file.name,
          };
          
          // 对于视频类型，添加额外属性
          if (['video'].includes(this.props.initialValues.mediaType)) {
            medium.propertyRefId = getDisplayStockId(stockId) || undefined;
            medium.characteristicEn = fileProps.characteristicEn || undefined;
            medium.characteristicZh = fileProps.characteristicZh || undefined;
            medium.address = fileProps.address || undefined;
            medium.buildingName = fileProps.buildingName || data.buildingName || undefined;
            medium.bgm = fileProps.bgm || undefined;
            medium.isShowContact = fileProps.isShowContact;
            
            // 为每个文件使用其独立的缩略图或默认缩略图
            const thumbnail = fileProps.thumbnail || (data.thumbnail ? data.thumbnail[0] : null);
            return this.props.createMedium(medium, [file, thumbnail], uuid);
          } else {
            // 对于文档和照片类型
            return this.props.createMedium(medium, [file, null], uuid);
          }
        })
      );
    }
  };

  clearCacheAndCloseDialog = () => {
    this.setState({ processingUuid: null });
    // this.props.clearProgressingMedium();
    this.props.handleCloseDialog();
  };

  toStep = stepNo => {
    if (stepNo > 0) this.setState({ step: stepNo });
  };

  checkStepOne = () => {
    const errorFields = Object.keys(this.props.formSynchronousError);
    if (errorFields.length === 0) this.toStep(2);
    else this.props.touchForm(errorFields);
  };

  submitForm = (approval) => () => {
    this.setState(
      { approval: approval },
      () => {
        this.props.dispatchSubmitForm();
      }
    )
  };

  handleSubmitSuccess = () => {
    this.toStep(4);
  };

  cancelSubmit = () => {
    // TODO: cancel
    console.log("cancel");
  };

  resetAndClose = () => {
    if (this.state.step === 3) return; // dont let the user close the loading box unless he clicks 'cancel'
    this.clearCacheAndCloseDialog();
    this.setState({ step: 1 });

    if (this.state.step === 4) this.props.callback(); // load newly uploaded images
  };

  gotoView = () => {
    // TODO: goto media page
    this.resetAndClose();
  };

  render() {
    const {
      dialogOpen,
      formFiles,
      formNoDateLimit,
      allProgressingMedia,
      validEmployees,
      initialValues,
      changeForm,
      formSynchronousError,
      intl
    } = this.props;
    const { step, processingUuid } = this.state;
    const { photoContent, mediaType } = this.props.initialValues;
    const files = formFiles ? Array.from(formFiles) : [];
    const progressingMedia = allProgressingMedia.filter(
      v => processingUuid === v.uuid
    );

    return (
      <Dialog
        open={dialogOpen}
        handleClose={this.resetAndClose}
        fullWidth={true}
      >
        <MuiThemeProvider theme={theme}>
          {/*use render props to pass the props from redux-form to StepOne and StepTwo. ref: https://reactjs.org/docs/render-props.html*/}
          <Form
            initialValues={initialValues}
            onSubmit={this.handleSubmit}
            onSubmitSuccess={this.handleSubmitSuccess}
            render={({ submitting }) => (
              <>
                {step === 1 && (
                  <DialogFrame
                    {...(mediaType === 'photo' && files.length ? {
                      buttonMain: intl.formatMessage({id: `${this.props.AUTOMATICALLY_SHARE_TO_PUBLIC ? "stock.upload.all" : "stock.upload.topublicmessage"}`}),
                      handleMain: this.submitForm('waiting'),
                      ...(this.props.AUTOMATICALLY_SHARE_TO_PUBLIC ? {} : {
                        buttonBack: intl.formatMessage({ id: "stock.upload.message"}),
                        handleBack: this.submitForm('pending'),
                      }),
                    } : {
                      buttonMain:
                        files.length
                          ? intl.formatMessage({
                              // id: `stock.upload${sbu === "IND" ? mediaType : ""}.message`
                              id: `${mediaType === 'kol_video' ? 'stock.upload.kol' : `stock.upload${mediaType}.message`}`
                            })
                          : null,
                      handleMain:this.submitForm('waiting'),
                      buttonMainProps:{ id: `${mediaType === 'kol_video' ? 'uploadKolMediaButton' : 'uploadMediaButton'}` },
                      /* buttonBack={
                        files.length && (sbu === "COMM" || sbu === "SHOPS")
                          ? intl.formatMessage({
                            id: "stock.upload.topublicmessage"
                          })
                          : null
                      } */
                      buttonBack: mediaType === 'kol_video' && files.length ? intl.formatMessage({id: "common.cancel"}) : null,
                      handleBack: mediaType === 'kol_video' ? this.resetAndClose : this.submitForm('pending'),
                      buttonBackProps: { id: `${mediaType === 'kol_video' ? 'uploadPublicKolMediaButton' : 'uploadPublicMediaButton'}` },
                    })}
                  >
                    <StepOne
                      files={files}
                      changeForm={changeForm}
                      syncError={formSynchronousError}
                      mediaType={mediaType}
                      accept={acceptMap[mediaType]}
                    />
                  </DialogFrame>
                )}

                {/*{step === 2 && <DialogFrame buttonBack="Back" buttonMain="Upload" handleBack={() => this.toStep(1)} handleMain={this.submitForm}>*/}
                {/*  <StepTwo files={files} noDateLimit={formNoDateLimit} />*/}
                {/*</DialogFrame>}*/}

                {step === 3 && (
                  <DialogFrame
                    buttonMain={
                      !files.length
                      ? 
                        intl.formatMessage({
                          id: "common.cancel"
                        })
                      : null
                    }
                    handleMain={this.cancelSubmit}
                  >
                    <StepThree />
                  </DialogFrame>
                )}

                {step === 4 && (
                  <DialogFrame
                    buttonMain={intl.formatMessage({
                      id: "common.ok"
                    })}
                    handleMain={this.resetAndClose}
                  >
                    <StepFour progressingMedia={progressingMedia} isKol={mediaType === 'kol_video'} />
                  </DialogFrame>
                )}
              </>
            )}
          />
        </MuiThemeProvider>
      </Dialog>
    );
  }
}

const selector = formValueSelector("uploadMediaForm");
const mapStateToProps = state => ({
  // ref: https://stackoverflow.com/questions/48819138/how-do-you-get-syncerrors-out-of-state-using-redux-form-selectors
  formSynchronousError: getFormSyncErrors("uploadMediaForm")(state),
  formFiles: selector(state, "files"),
  formNoDateLimit: selector(state, "noDateLimit"),
  allProgressingMedia: state.medium.progressingMedia || [],
  validEmployees: _.get(state, "employee.validEmployees", []),
  AUTOMATICALLY_SHARE_TO_PUBLIC: _.get(state, `employee.permissions[${AUTOMATICALLY_SHARE_TO_PUBLIC}]`),
});

const mapDispatchToProps = dispatch => {
  return {
    // ref: https://redux-form.com/6.6.3/examples/remotesubmit/
    dispatchSubmitForm: () => dispatch(submit("uploadMediaForm")),
    // ref: https://stackoverflow.com/questions/39272731/redux-form-how-to-set-fields-as-touched
    touchForm: fields => dispatch(touch("uploadMediaForm", ...fields)),
    changeForm: (field, val) => dispatch(change("uploadMediaForm", field, val)),
    clearProgressingMedium: (...args) =>
      dispatch(clearProgressingMedium()),
    createMedium: (...args) => dispatch(createMedium(...args))
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(injectIntl(UploadMediaDialog));
