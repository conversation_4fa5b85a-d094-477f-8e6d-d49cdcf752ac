import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import FormButton from "./FormButton";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    height: 22,
    lineHeight: "22px",
    // fontSize: ".875em",
    padding: "0 10px",
    background: "#27CC6B",
    minWidth: 0,
    display: "flex",
    "& svg": {
      width: 16,
      height: 16,
      marginLeft: 4,
    }
  },
};

function FormButtonInline(props) {
  const { classes, children, className, icon, ...other } = props;

  return (
    <FormButton className={clsx(classes.root, className)} {...other}>
      {children}{icon}
    </FormButton>
  );
}

FormButtonInline.propTypes = {
  children: PropTypes.node,
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  icon: PropTypes.node,
};

export default withStyles(styles)(FormButtonInline);
