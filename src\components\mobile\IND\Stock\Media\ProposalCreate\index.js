/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React, { useState, useEffect } from "react";
import { withStyles } from "@material-ui/core/styles";

import PropTypes from "prop-types";
import { connect } from "react-redux";
import { submit, change } from "redux-form";
import { injectIntl } from "react-intl";
import TextField from "@material-ui/core/TextField";
import _ from "lodash";
import Checkbox from "@material-ui/core/Checkbox";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import FormGroup from "@material-ui/core/FormGroup";

import DialogFrame from "../../../../../common/DialogFrame";
import Dialog from "../../../../../common/Dialog";
import ProposalForm from "../../../Proposal/ProposalForm";
import {
  createProposal,
  clearCreateProposal,
} from "../../../../../../actions/proposal";
import {
  possessionLangIdMapping,
  floorTypeLangIdMapping,
  getDefaultRemarks,
  getTypeOptions,
} from "../../../Proposal/FormSection/selectOptions";
import langFile from "../../../../../../lang/IND/messages";
import SubmitDialog from "../../../../../common/SubmitDialog";
import {
  getLangKey,
  getProposalTenancyDesc,
  goToProposalList,
  parsePeriod,
} from "../../../../../../helper/generalHelper";
import {
  getFloorTypeInfoFromLangFile,
} from "../../../../../Saleskit/helpers";
import { sbu, generalProposalQuota } from "../../../../../../config";
import history from "../../../../../../core/history";
import {
  clearCreateListProposal,
  createListProposal,
} from "../../../../../../actions/listProposal";
import SelectField from "../../../../../common/SelectField";

const CustomCheckbox = withStyles({
  root: {
    color: "white",
    "&$checked": {
      color: "#13CE66",
    },
  },
  checked: {},
})((props) => <Checkbox {...props} />);

const titleMapping = {
  unified: {
    nameEn: "Unified",
    nameZh: "統一業權",
  },
  spinoff: {
    nameEn: "Spin-off",
    nameZh: "分散業權",
  },
  developer: {
    nameEn: "Developer",
    nameZh: "發展商",
  },
};

function ProposalCreate({
  stockData,
  currentStock,
  media,
  buildingMedia,
  userInfo,
  createProposal,
  creatingProposal,
  createdProposal,
  createProposalError,
  clearCreateProposal,
  dispatchSubmitForm,
  createProposalDialogOpen,
  handleCloseCreateProposalDialog,
  changeFieldValue,
  form,
  exceedQuota,
  createListProposal,
  creatingListProposal,
  createdListProposal,
  createListProposalError,
  clearCreateListProposal,
  intl,
}) {
  const [proposalName, setProposalName] = useState("");
  const [listProposalType, setListProposalType] = useState("Sale");
  const [hideContact, setHideContact] = useState(false);
  const [hideEmployeePhoto, setHideEmployeePhoto] = useState(false);

  const stock = stockData[currentStock];
  const isListProposal = history.location.pathname === "/listProposal";

  useEffect(() => {
    setProposalName(parseDefaultProposalName());
  }, []);

  const parseDefaultProposalName = () => {
    const langKey = getLangKey(intl);
    return stock.building && stock.building[langKey]
      ? stock.building[langKey]
      : "";
  };

  const handleCloseSubmitDialog = () => {
    handleCloseCreateProposalDialog();
    setProposalName(parseDefaultProposalName());
    setHideEmployeePhoto(false);
    setHideContact(false);
    setListProposalType("Sale");
    isListProposal ? clearCreateListProposal() : clearCreateProposal();
  };

  const submitDialogCallback = () => {
    goToProposalList();
  };

  const handleNameChange = (e) => {
    setProposalName(e.target.value);
  };

  const getNumberOfLift = (s) => {
    const lifts = s?.building?.lifts || [];
    let cargoLift = "";
    let passengerLift = "";
    lifts.map((v) => {
      switch (v.type) {
        case "Cargo":
          cargoLift = v.quantity;
          break;
        case "Passenger":
          passengerLift = v.quantity;
          break;
      }
    });
    return { cargoLift, passengerLift };
  };

  const getContainers = (s) => {
    const entrances = s?.building?.entrances || [];
    let containers = [];
    for (let i = 0; i < entrances.length; i++) {
      let containerDataEn = entrances?.[i]?.container?.nameEn || "";
      let containerDataZh = entrances?.[i]?.container?.nameZh || "";

      if (entrances[i]["haveLoadingBay"]) {
        if (containerDataEn) containerDataEn += " Loading Bay";
        if (containerDataZh) containerDataZh += " 貨台";
      }
      containers.push({
        nameEn: containerDataEn,
        nameZh: containerDataZh,
      });
    }
    return containers;
  };

  const listProposalFields = [
    "unit",
    "floor",
    "floorType",
    "avgPrice",
    "totalPrice",
    "avgRent",
    "totalRent",
    "customBuilding",
    "customStreet",
    "customStreetNo",
    "customDistrict",
    "areaEfficiency",
    "areaGross",
    "areaNet",
    "main1Photo",
    "possession",
    "decoration",
    "ceilingHeight",
    "remarks",
    "stockMedia",
    "buildingMedia",
    "passengerLift",
    "cargoLift",
    "airConditioningType",
  ];

  const formStockData = (s) => {
    const langKey = getLangKey(intl);
    const transportLangKey = getLangKey(intl, "transport");
    const area = s && s.area ? s.area : null;
    const stockMediaData =
      media?.filter((m) => m.id === s?.unicorn?.id?.toString())?.[0]?.data ||
      {};
    const buildingMediaData =
      buildingMedia?.filter(
        (m) => m.id === s?.building?.unicorn?.id?.toString(),
      )?.[0]?.data || {};

    const { cargoLift, passengerLift } = getNumberOfLift(s);
    const containers = getContainers(s).map((v) => ({
      value: v[langKey] || "---",
      isShow: false,
    }));

    const currTenancyRecords = s?.tenancyRecords?.filter(
      (v) => v.status === "Current" && !v.deleted,
    );

    let defaultType = "Sale";
    let defaultRemarks = getDefaultRemarks("Sale");
    switch (s?.status?.nameEn) {
      case "Lease":
        defaultType = "Lease";
        defaultRemarks = getDefaultRemarks("Lease");
        break;
      case "Sale+Lease":
        defaultType = "SaleAndLease";
        defaultRemarks = getDefaultRemarks("SaleAndLease");
        break;
    }

    const possibleFeeType = ["/SqFt", "/Qtr", "/Month", "/SY"];
    const possiblePaidBy = ["Paid By Tenant", "Paid By Landlord"];

    const allFields = {
      floorType: "Actual Floor",
      type: defaultType,
      avgPrice: [
        {
          value: s?.askingPrice?.average,
          isShow: defaultType === "Sale" || defaultType === "SaleAndLease",
        },
      ],
      totalPrice: [
        {
          value: s?.askingPrice?.total,
          isShow: defaultType === "Sale" || defaultType === "SaleAndLease",
        },
      ],
      bottomAvgPrice: s?.askingPrice?.details?.filter(
        (v) => v.type === "average_bottom",
      )[0]?.value,
      bottomTotalPrice: s?.askingPrice?.details?.filter(
        (v) => v.type === "total_bottom",
      )[0]?.value,
      avgRent: [
        {
          value: s?.askingRent?.average,
          isShow: defaultType === "Lease" || defaultType === "SaleAndLease",
        },
      ],
      totalRent: [
        {
          value: s?.askingRent?.total,
          isShow: defaultType === "Lease" || defaultType === "SaleAndLease",
        },
      ],
      bottomAvgRent: s?.askingRent?.details?.filter(
        (v) => v.type === "average_bottom",
      )[0]?.value,
      bottomTotalRent: s?.askingRent?.details?.filter(
        (v) => v.type === "total_bottom",
      )[0]?.value,
      floor: [
        {
          value: s?.floor,
          isShow: true,
        },
      ],
      unit: [
        {
          value: s?.unit,
          isShow: !!s?.unit,
        },
      ],
      customBuilding: s?.building?.[langKey] || "---",
      customStreet: s?.street?.street?.[langKey] || "---",
      customStreetNo: s?.street?.number || "",
      customDistrict: s?.building?.district?.[langKey] || "---",
      areaEfficiency: [
        {
          value: area?.efficiency || "",
          isShow: false, // all net area default unselect
        },
      ],
      areaGross: [
        {
          value: area?.size || "",
          isShow: !!area?.size,
        },
      ],
      areaNet: [
        {
          value:
            area?.sizes?.filter((v) => v.type === "SALEABLE")[0]?.value || "",
          isShow: false, // all net area default unselect
        },
      ],
      possession: [
        {
          value: s?.possession?.nameEn,
          isShow: false,
        },
      ],
      managementFee: [
        {
          value: s?.managementFee?.number || "",
          unit:
            possibleFeeType.indexOf(s?.managementFee?.type) >= 0
              ? s?.managementFee?.type
              : "",
          paidBy:
            possiblePaidBy.indexOf(s?.managementFee?.paidBy) >= 0
              ? s?.managementFee?.paidBy
              : "",
          isShow: false,
        },
      ],
      decoration: [
        {
          value: s?.decoration?.[langKey] || "---",
          isShow: false,
        },
      ],
      unitView: [
        {
          value: s?.unitView?.[langKey] || "---",
          isShow: false,
        },
      ],
      availability: [
        {
          value: s?.availability || "---",
          isShow: false,
        },
      ],
      stockType: [
        {
          value: s?.stockType?.[langKey],
          isShow: false,
        },
      ],
      ceilingHeight: [
        {
          ft: s?.ceilingHeight?.ft || "",
          in: s?.ceilingHeight?.in || "",
          isShow: false,
        },
      ],
      remarks: defaultRemarks,
      currentTenants: currTenancyRecords?.map((v) => {
        const minDate = v?.expiry?.minDate;
        const maxDate = v?.expiry?.maxDate;
        return {
          tenant: v?.tenant?.[langKey] || "---",
          tenantIsShow: false,
          rentalFee: v?.rentalFee,
          rentalFeeIsShow: false,
          period: parsePeriod(minDate, maxDate, intl),
          periodIsShow: false,
          tenancy: getProposalTenancyDesc(v, intl.locale),
          tenancyIsShow: false,
        };
      }),
      yield: [
        {
          value: s?.yield || "",
          isShow: false,
        },
      ],
      customTitle: [
        {
          value: "",
          isShow: false,
        },
      ],
      hideEmployeePhoto: [
        {
          value: intl.formatMessage({
            id: "proposal.form.hideemployeephoto",
          }),
          isShow: false,
        },
      ],
      hideContact: [
        {
          value: intl.formatMessage({
            id: "proposal.form.hidecontact",
          }),
          isShow: false,
        },
      ],
      companyTitle: "midlandici",
      stockMedia: []
        .concat(stockMediaData?.photo || [], stockMediaData?.video || [])
        ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
        ?.map((v) => v.id),
      buildingMedia: []
        .concat(buildingMediaData?.photo || [], buildingMediaData?.video || [])
        ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
        ?.map((v) => v.id),
      main1Photo: "map",

      // building info
      usage: [
        {
          value: s?.building?.buildingUsage?.[langKey] || "---",
          isShow: !!s?.building?.buildingUsage?.[langKey],
        },
      ],
      title: [
        {
          value: titleMapping[s?.building?.title]?.[langKey] || "---",
          isShow: false,
        },
      ],
      inTakeDate: [
        {
          value: s?.building?.completionDate || "---",
          isShow: !!s?.building?.completionDate,
        },
      ],
      developers: s?.building?.developers?.map((v) => {
        return {
          value: v[langKey] || "---",
          isShow: false,
        };
      }),
      managementCompany: [
        {
          value: s?.building?.managementCompany?.name || "---",
          isShow: false,
        },
      ],
      transport: [
        {
          value: s?.building?.[transportLangKey] || "---",
          isShow: false,
        },
      ],
      passengerLift: [
        {
          value: passengerLift || "---",
          isShow: false,
        },
      ],
      cargoLift: [
        {
          value: cargoLift || "---",
          isShow: false,
        },
      ],
      containers: containers,
      airConditioningType: [
        {
          value: s?.building?.airConditioning?.type?.[langKey] || "---",
          isShow: false,
        },
      ],
    };

    // set default main photo
    if (isListProposal) {
      if (
        Object.keys(stockMediaData).length > 0 &&
        stockMediaData.photo.length > 0
      )
        allFields.main1Photo = stockMediaData.photo[0].id;
      else if (
        Object.keys(buildingMediaData).length > 0 &&
        buildingMediaData.photo.length > 0
      )
        allFields.main1Photo = buildingMediaData.photo[0].id;
    }

    return isListProposal
      ? _.omitBy(allFields, (_, key) => !listProposalFields.includes(key))
      : allFields;
  };

  const getParsedProposal = (stock, mediaData, buildingMediaData, values) => {
    const photos = [].concat(
      mediaData?.photo || [],
      buildingMediaData?.photo || [],
    );
    const videos = [].concat(
      mediaData?.video || [],
      buildingMediaData?.video || [],
    );
    const getPhotoInfoFromRedux = (id) => {
      let photo = photos?.filter((v) => v.id === id)?.[0] || null;
      if (photo)
        photo = {
          id: photo.id,
          mediumRoot: photo.mediumRoot,
          photoContent: photo.photoContent,
        };
      return photo;
    };
    const getVideoInfoFromRedux = (id) => {
      let video = videos?.filter((v) => v.id === id)?.[0] || null;
      if (video)
        video = {
          id: video.id,
          mediumRoot: video.mediumRoot,
          youtubeId: video.youtubeMrId || video.youtubeHkpId || video.youtubeId,
        };
      return video;
    };
    const getPossessionInfoFromLangFile = (value) => {
      return {
        nameZh:
          value &&
          possessionLangIdMapping[value] &&
          langFile.zh[possessionLangIdMapping[value]]
            ? langFile.zh[possessionLangIdMapping[value]]
            : "",
        nameEn:
          value &&
          possessionLangIdMapping[value] &&
          langFile.en[possessionLangIdMapping[value]]
            ? langFile.en[possessionLangIdMapping[value]]
            : "",
      };
    };
    const { cargoLift, passengerLift } = getNumberOfLift(stock);
    const containers = getContainers(stock);

    const currTenancyRecords = stock?.tenancyRecords?.filter(
      (v) => v.status === "Current" && !v.deleted,
    );

    // stock photo which has the main tag
    const mainStockPhotoId = mediaData?.photo?.filter(
      (v) => v.tags && v.tags.includes("main"),
    )?.[0]?.id;
    // building photo which has the main tag
    const mainBuildingPhotoId = buildingMediaData?.photo?.filter(
      (v) => v.tags && v.tags.includes("main"),
    )?.[0]?.id;
    // main stock photo has higher priority
    const mainPhoto = getPhotoInfoFromRedux(
      mainStockPhotoId || mainBuildingPhotoId || null,
    );

    let parsedData = {
      ...values,
      floorType: getFloorTypeInfoFromLangFile(values?.floorType),
      type: values?.type,
      floor: values?.floor?.[0],
      unit: values?.unit?.[0],
      avgPrice: {
        ...values?.avgPrice?.[0],
        value: values?.avgPrice?.[0]?.value || 0,
      },
      totalPrice: {
        ...values?.totalPrice?.[0],
        value: values?.totalPrice?.[0]?.value || 0,
      },
      bottomAvgPrice: values?.bottomAvgPrice || 0,
      bottomTotalPrice: values?.bottomTotalPrice || 0,
      avgRent: {
        ...values?.avgRent?.[0],
        value: values?.avgRent?.[0]?.value || 0,
      },
      totalRent: {
        ...values?.totalRent?.[0],
        value: values?.totalRent?.[0]?.value || 0,
      },
      bottomAvgRent: values?.bottomAvgRent || 0,
      bottomTotalRent: values?.bottomTotalRent || 0,
      customBuilding: {
        value: values?.customBuilding,
        isShow: true,
      },
      areaEfficiency: {
        ...values?.areaEfficiency?.[0],
        value: values?.areaEfficiency?.[0]?.value || 0,
      },
      areaGross: {
        ...values?.areaGross?.[0],
        value: values?.areaGross?.[0]?.value || 0,
      },
      areaNet: {
        ...values?.areaNet?.[0],
        value: values?.areaNet?.[0]?.value || 0,
      },
      possession: {
        ...values?.possession?.[0],
        value: getPossessionInfoFromLangFile(values?.possession?.[0]?.value),
      },
      managementFee: {
        ...values?.managementFee?.[0],
        value: values?.managementFee?.[0]?.value || 0,
      },
      decoration: {
        ...values?.decoration?.[0],
        value: stock?.decoration,
      },
      unitView: {
        ...values?.unitView?.[0],
        value: stock?.unitView,
      },
      availability: {
        ...values?.availability?.[0],
        value: stock?.availability,
      },
      stockType: {
        ...values?.stockType?.[0],
        value: {
          nameEn: stock?.stockType?.nameEn,
          nameZh: stock?.stockType?.nameZh,
        },
      },
      ceilingHeight: {
        ...values?.ceilingHeight?.[0],
        ft: values?.ceilingHeight?.[0]?.ft || 0,
        in: values?.ceilingHeight?.[0]?.in || 0,
      },
      currentTenants: values?.currentTenants?.map((v, i) => {
        return {
          tenant: {
            nameEn: currTenancyRecords[i]?.tenant?.nameEn,
            nameZh: currTenancyRecords[i]?.tenant?.nameZh,
            isShow: v.tenantIsShow,
          },
          rentalFee: {
            value: v.rentalFee || 0,
            isShow: v.rentalFeeIsShow,
          },
          period: {
            min: currTenancyRecords[i]?.expiry?.minDate,
            max: currTenancyRecords[i]?.expiry?.maxDate,
            isShow: v.periodIsShow,
          },
          tenancy: {
            nameEn: getProposalTenancyDesc(currTenancyRecords[i], "en"),
            nameZh: getProposalTenancyDesc(currTenancyRecords[i], "zh"),
            isShow: v.tenancyIsShow,
          },
        };
      }),
      yield: {
        ...values?.yield?.[0],
        value: values?.yield?.[0]?.value || 0,
      },
      customTitle: values?.customTitle?.[0],
      hideEmployeePhoto: !!values?.hideEmployeePhoto?.[0]?.isShow,
      hideContact: !!values?.hideContact?.[0]?.isShow,
      photos: []
        .concat(values?.stockMedia || [], values?.buildingMedia || [])
        .map(getPhotoInfoFromRedux)
        .filter((v) => v !== null),
      videos: []
        .concat(values?.stockMedia || [], values?.buildingMedia || [])
        .map(getVideoInfoFromRedux)
        .filter((v) => v !== null),
      googleMap: {
        isPP: !!(values?.googleMapPhoto && values?.googleMapPhoto.length > 0),
        isMain1: values?.main1Photo === "map",
        isMain2: values?.main2Photo === "map",
      },
      main1Photo:
        values?.main1Photo && getPhotoInfoFromRedux(values?.main1Photo),
      main2Photo:
        values?.main2Photo && getPhotoInfoFromRedux(values?.main2Photo),
      mainPhoto: mainPhoto, // stock or building photo which has the main tag

      // building info
      usage: {
        ...values?.usage?.[0],
        value: stock?.building?.buildingUsage,
      },
      title: {
        ...values?.title?.[0],
        value: titleMapping[stock?.building?.title],
      },
      inTakeDate: {
        ...values?.inTakeDate?.[0],
        // value: moment(stockData?.building?.completionDate, "YYYY").toDate(),
        value: stock?.building?.completionDate,
      },
      developers: values?.developers?.map((v, i) => {
        return {
          ...v,
          value: {
            nameEn: stock?.building?.developers?.[i]?.nameEn,
            nameZh: stock?.building?.developers?.[i]?.nameZh,
          },
        };
      }),
      managementCompany: {
        ...values?.managementCompany?.[0],
        value: {
          nameEn: stock?.building?.managementCompany?.name,
          nameZh: stock?.building?.managementCompany?.name,
        },
      },
      transport: {
        ...values?.transport?.[0],
        value: {
          nameEn: stock?.building?.transportEn,
          nameZh: stock?.building?.transportZh,
        },
      },
      passengerLift: {
        ...values?.passengerLift?.[0],
        value: {
          nameEn: passengerLift.toString(),
          nameZh: passengerLift.toString(),
        },
      },
      cargoLift: {
        ...values?.cargoLift?.[0],
        value: {
          nameEn: cargoLift.toString(),
          nameZh: cargoLift.toString(),
        },
      },
      containers: values?.containers?.map((v, i) => {
        return {
          ...v,
          value: containers[i],
        };
      }),
      airConditioningType: {
        ...values?.airConditioningType?.[0],
        value: stock?.building?.airConditioning?.type,
      },

      // additional data which is required by proposal but not shown in the form (or is not a field in the form)
      stockId: stock?.unicorn?.id,
      stockMongoId: stock?._id,
      sbu: sbu,
      isSoleagent: !!(
        stock.soleagent &&
        stock.soleagent.periodStart != null &&
        stock.soleagent.periodEnd != null
      ),
      districtNameZh: stock?.building?.district?.nameZh,
      districtNameEn: stock?.building?.district?.nameEn,
      streetNameZh: stock?.street?.street?.nameZh,
      streetNameEn: stock?.street?.street?.nameEn,
      streetNo: stock?.street?.number,
      buildingNameZh: stock?.building?.nameZh,
      buildingNameEn: stock?.building?.nameEn,
      buildingDistrictNameZh: stock?.building?.district?.nameZh,
      buildingDistrictNameEn: stock?.building?.district?.nameEn,
      lng: stock?.building?.coordinates?.longitude,
      lat: stock?.building?.coordinates?.latitude,
      isBuildingFieldsAllHide: !(
        values?.usage?.[0]?.isShow ||
        values?.title?.[0]?.isShow ||
        values?.inTakeDate?.[0]?.isShow ||
        values?.developers?.filter((v) => v.isShow).length > 0 ||
        values?.managementCompany?.[0]?.isShow ||
        values?.transport?.[0]?.isShow ||
        values?.passengerLift?.[0]?.isShow ||
        values?.cargoLift?.[0]?.isShow ||
        values?.containers?.filter((v) => v.isShow).length > 0 ||
        values?.airConditioningType?.[0]?.isShow
      ),
      proposalName,
    };

    // these photo and video fields are merged into photos and videos
    delete parsedData.stockMedia;
    delete parsedData.buildingMedia;
    // googleMapPhoto is converted to includeGoogleMap field
    delete parsedData.googleMapPhoto;

    return parsedData;
  };

  const submitListProposal = () => {
    const proposalStocksId = stockData.map((s) => s._id);
    // retrieve stock proposal data
    const formArr = Object.keys(form)
      .map((id) => id.substr(5))
      .reduce((list, id) => {
        if (proposalStocksId.includes(id))
          return [...list, { id, ...form[`form_${id}`].values }];
      }, []);

    const parsedFormArr = formArr.map((formData) => {
      const stock = stockData.find((s) => s._id === formData.id);
      const stockMediaData =
        media?.filter((m) => m.id === stock?.unicorn?.id?.toString())?.[0]
          ?.data || {};
      const buildingMediaData =
        buildingMedia?.filter(
          (m) => m.id === stock?.building?.unicorn?.id?.toString(),
        )?.[0]?.data || {};
      delete formData.id;

      const parsedProposal = getParsedProposal(
        stock,
        stockMediaData,
        buildingMediaData,
        formData,
      );
      // delete redundant fields
      delete parsedProposal.availability;
      delete parsedProposal.developers;
      delete parsedProposal.currentTenants;
      delete parsedProposal.containers;
      delete parsedProposal.inTakeDate;
      delete parsedProposal.isSoleagent;
      delete parsedProposal.isBuildingFieldsAllHide;
      delete parsedProposal.photos;
      delete parsedProposal.videos;
      delete parsedProposal.stockType;
      delete parsedProposal.managementCompany;
      delete parsedProposal.managementFee;
      delete parsedProposal.unitView;
      delete parsedProposal.title;
      delete parsedProposal.transport;
      delete parsedProposal.usage;
      delete parsedProposal.yield;
      delete parsedProposal.customTitle;
      delete parsedProposal.proposalName;
      delete parsedProposal.hideContact;
      delete parsedProposal.hideEmployeePhoto;
      delete parsedProposal.type;
      delete parsedProposal.sbu;

      if (!parsedProposal.mainPhoto) {
        if (parsedProposal.main1Photo)
          parsedProposal.mainPhoto = parsedProposal.main1Photo;
        else if (parsedProposal.main2Photo)
          parsedProposal.mainPhoto = parsedProposal.main2Photo;
      }
      delete parsedProposal.main1Photo;
      delete parsedProposal.main2Photo;

      // set useGGMapPhoto to true if googleMap.isMain1 is true
      parsedProposal.useGGMapPhoto = parsedProposal.googleMap.isMain1;
      delete parsedProposal.googleMap;

      const {
        buildingNameZh: nameZh,
        buildingNameEn: nameEn,
        buildingDistrictNameZh: districtNameZh,
        buildingDistrictNameEn: districtNameEn,
        passengerLift,
        cargoLift,
        airConditioningType,
        lat,
        lng,
      } = parsedProposal;

      // building for IND list proposal
      parsedProposal.building = {
        nameZh,
        nameEn,
        districtNameZh,
        districtNameEn,
        passengerLift,
        cargoLift,
        airConditioningType,
        lat,
        lng,
      };

      delete parsedProposal.buildingNameZh;
      delete parsedProposal.buildingNameEn;
      delete parsedProposal.buildingDistrictNameZh;
      delete parsedProposal.buildingDistrictNameEn;
      delete parsedProposal.passengerLift;
      delete parsedProposal.cargoLift;
      delete parsedProposal.airConditioningType;
      delete parsedProposal.lat;
      delete parsedProposal.lng;

      // delete undefined fields
      Object.keys(parsedProposal).forEach(
        (key) => !parsedProposal[key] && delete parsedProposal[key],
      );

      return parsedProposal;
    });

    const payload = {
      type: listProposalType,
      sbu,
      hideContact,
      hideEmployeePhoto,
      proposalName,
      proposals: parsedFormArr,
    };
    createListProposal(payload);
  };

  const submit = (stockId, buildingId, values) => {
    const stockMediaData =
      media?.filter((m) => m.id === stockId.toString())?.[0]?.data || {};
    const buildingMediaData =
      buildingMedia?.filter((m) => m.id === buildingId.toString())?.[0]?.data ||
      {};

    const parsedData = getParsedProposal(
      stock,
      stockMediaData,
      buildingMediaData,
      values,
    );

    createProposal(parsedData);
  };

  const renderForms = () =>
    stockData.map((s, idx) => (
      <ProposalForm
        key={s._id}
        stockId={s._id}
        show={currentStock === idx}
        onSubmit={(...values) =>
          submit(s.unicorn.id.toString(), s.building.unicorn.id, ...values)
        }
        form={`form_${s._id}`}
        initialValues={formStockData(s)}
        changeFieldValue={(...args) =>
          changeFieldValue(`form_${s._id}`, ...args)
        }
      />
    ));

  return (
    <div>
      <div>{renderForms()}</div>
      {exceedQuota ? (
        <Dialog
          open={createProposalDialogOpen}
          handleClose={handleCloseCreateProposalDialog}
        >
          {intl.formatMessage(
            { id: "proposal.create.exceed" },
            { quota: generalProposalQuota },
          )}
          <DialogFrame
            buttonMain={intl.formatMessage({
              id: "common.ok",
            })}
            handleMain={handleCloseSubmitDialog}
          ></DialogFrame>
        </Dialog>
      ) : (
        <SubmitDialog
          dialogOpen={createProposalDialogOpen}
          handleCloseDialog={handleCloseSubmitDialog}
          succCallback={submitDialogCallback}
          submitting={isListProposal ? creatingListProposal : creatingProposal}
          submitted={isListProposal ? createdListProposal : createdProposal}
          error={isListProposal ? createListProposalError : createProposalError}
          submit={
            isListProposal
              ? submitListProposal
              : () => dispatchSubmitForm(`form_${stock._id}`)
          }
          submitBtnText={intl.formatMessage({ id: "proposal.form.save" })}
          succMsg={intl.formatMessage(
            { id: "proposal.form.savesuccess" },
            { filename: proposalName },
          )}
        >
          <TextField
            label={intl.formatMessage({ id: "proposal.form.proposalname" })}
            value={proposalName}
            onChange={handleNameChange}
            variant="outlined"
            fullWidth
          />
          {isListProposal && (
            <>
              <SelectField
                label={intl.formatMessage({ id: "proposal.form.type" })}
                input={{
                  value: listProposalType,
                  onChange: (e) => setListProposalType(e.target.value),
                }}
                ranges={getTypeOptions(intl)}
                meta={{}}
                variant="outlined"
                fullWidth
              />
              <FormGroup>
                <FormControlLabel
                  control={
                    <CustomCheckbox
                      checked={hideContact}
                      onChange={(e) => setHideContact(e.target.checked)}
                    />
                  }
                  label={intl.formatMessage({
                    id: "proposal.form.hidecontact",
                  })}
                />
                <FormControlLabel
                  control={
                    <CustomCheckbox
                      checked={hideEmployeePhoto}
                      onChange={(e) => setHideEmployeePhoto(e.target.checked)}
                    />
                  }
                  label={intl.formatMessage({
                    id: "proposal.form.hideemployeephoto",
                  })}
                />
              </FormGroup>
            </>
          )}
        </SubmitDialog>
      )}
    </div>
  );
}

ProposalCreate.propTypes = {
  stockData: PropTypes.array,
  currentStock: PropTypes.number,
  media: PropTypes.array,
  buildingMedia: PropTypes.array,
  createProposal: PropTypes.func,
  creatingProposal: PropTypes.bool,
  createdProposal: PropTypes.bool,
  createProposalError: PropTypes.string,
  clearCreateProposal: PropTypes.func,
  dispatchSubmitForm: PropTypes.func,
  createProposalDialogOpen: PropTypes.bool,
  handleCloseCreateProposalDialog: PropTypes.func,
  changeFieldValue: PropTypes.func,
  form: PropTypes.object,
  exceedQuota: PropTypes.bool,
  createListProposal: PropTypes.func.isRequired,
  creatingListProposal: PropTypes.bool,
  createdListProposal: PropTypes.bool,
  createListProposalError: PropTypes.string,
  clearCreateListProposal: PropTypes.func,
  intl: PropTypes.object,
};

const mapStateToProps = (state) => ({
  userInfo:
    state.auth &&
    state.auth.user &&
    state.auth.user.login &&
    state.auth.user.login.info
      ? state.auth.user.login.info
      : {},
  stockData: state.stock.detail ? state.stock.detail : [],
  currentStock: state.stock.currentDetail ? state.stock.currentDetail : 0,
  media: state.stock.media ? state.stock.media : [],
  buildingMedia: state.building.media ? state.building.media : [],
  form: state.form ? state.form : {},
  creatingProposal: state.proposal.creatingProposal
    ? state.proposal.creatingProposal
    : false,
  createdProposal: state.proposal.createdProposal
    ? state.proposal.createdProposal
    : false,
  createProposalError:
    state.proposal.createProposalError &&
    state.proposal.createProposalError.message
      ? state.proposal.createProposalError.message
      : null,
  creatingListProposal: state.listProposal.creatingListProposal
    ? state.listProposal.creatingListProposal
    : false,
  createdListProposal: state.listProposal.createdListProposal
    ? state.listProposal.createdListProposal
    : false,
  createListProposalError: state.listProposal.createListProposalError
    ? state.listProposal.createListProposalError.message
    : null,
});

const mapDispatchToProps = (dispatch) => {
  return {
    dispatchSubmitForm: (form) => dispatch(submit(form)),
    changeFieldValue: (form, field, value) =>
      dispatch(change(form, field, value)),
    createProposal: (...args) => dispatch(createProposal(...args)),
    createListProposal: (listProposal) =>
      dispatch(createListProposal(listProposal)),
    clearCreateProposal: (...args) => dispatch(clearCreateProposal(...args)),
    clearCreateListProposal: () => dispatch(clearCreateListProposal()),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(ProposalCreate));
