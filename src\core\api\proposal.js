import fetch from "node-fetch";
import _ from "lodash";

import { api, sbu, proposalBypassCode } from "../../config";

export function resJson(res, status, error, data) {
  res.json({
    status,
    errors: error ? [{ message: error }] : null,
    data,
  });
  res.end();
}

export const createProposal = async (req, res, next, getQuery) => {
  if (!req.user) {
    next();
  } else {
    const stockId = req.body.proposal && req.body.proposal.stockId;
    if (!stockId) {
      resJson(res, 300, "Missing stock ID");
      return;
    }

    let proposal = req.body.proposal;
    console.log("in createProposal api...");
    const casAccessToken = req.headers["cas-authorization"]; // req.user.casAccessToken
    console.log(casAccessToken);
    let userInfo =
      req.user && req.user.login && req.user.login.info
        ? req.user.login.info
        : {};
    try {
      // the user info stored in req is not enough to fill the employee field. need to get info from employee graphql
      let employee = await getEmployeeInfo(getQuery, userInfo.emp_id);

      proposal.salesman = {
        id: employee.emp_id,
        nameZh: employee.name_long_zh,
        nameEn: employee.name_long_en,
        titleNameZh: employee.title_zh,
        titleNameEn: employee.title_en,
        licence: employee.licence,
        tele: employee.phone,
        email: employee.email,
        mitaclubed: employee.mitaclubed,
        company: employee.company,
        ics: sbu,
        empType: employee.emp_type,
        wechatQr: employee.wechat_qr,
        isManager: employee.is_manager,
        nickname: employee.nickname,
        department: {
          code: employee.dept_code,
          tele:
            employee.dept && employee.dept.phone ? employee.dept.phone : null,
          email:
            employee.dept && employee.dept.email ? employee.dept.email : null,
          nameZh:
            employee.dept && employee.dept.name_zh
              ? employee.dept.name_zh
              : null,
          nameEn:
            employee.dept && employee.dept.name_en
              ? employee.dept.name_en
              : null,
          addrZh:
            employee.dept && employee.dept.addr_full_zh
              ? employee.dept.addr_full_zh
              : null,
          addrEn:
            employee.dept && employee.dept.addr_full_en
              ? employee.dept.addr_full_en
              : null,
          compLicenceNo:
            employee.dept && employee.dept.comp_licence_no
              ? employee.dept.comp_licence_no
              : null,
        },
      };
    } catch (e) {
      console.log(e);
      resJson(res, 300, "Failed to fetch employee info");
      return;
    }

    const query = await getQuery("CREATE_PROPOSAL_QUERY");
    const variables = {
      proposal,
      bypassCode: proposalBypassCode,
    };
    const body = {
      query,
      variables,
    };

    try {
      const resp = await fetch(`${api.proposal}/graphql`, {
        method: "post",
        headers: {
          "Content-Type": "application/json",
          "cas-authorization": casAccessToken, // localStorage.getItem("casAccessToken")
        },
        body: JSON.stringify(body),
      });
      if (resp.status !== 200 && resp.status !== 400)
        throw new Error(resp.statusText);
      const { data, errors } = await resp.json();
      if (errors) throw new Error(errors[0].message);

      resJson(res, 200, null, data);
    } catch (e) {
      console.log(e);
      resJson(res, 300, e.message);
    }
  }
};

export const createPreview = async (req, res, next, getQuery) => {
  if (!req.user) {
    next();
  } else {
    // const stockId = req.body.proposal && req.body.proposal.stockId;
    // if (!stockId) {
    //   resJson(res, 300, "Missing stock ID");
    //   return;
    // }

    const { type, empId } = req.body;

    if (!_.has(req.body, type) || _.isEmpty(req.body, type))
      return resJson(res, 300, "missing preview object");

    try {
      const employee = await getEmployeeInfo(getQuery, empId);
      req.body[type].salesman = {
        id: employee.emp_id,
        nameZh: employee.name_long_zh,
        nameEn: employee.name_long_en,
        titleNameZh: employee.title_zh,
        titleNameEn: employee.title_en,
        licence: employee.licence,
        tele: employee.phone,
        email: employee.email,
        mitaclubed: employee.mitaclubed,
        company: employee.company,
        ics: sbu,
        empType: employee.emp_type,
        wechatQr: employee.wechat_qr,
        isManager: employee.is_manager,
        nickname: employee.nickname,
        department: {
          code: employee.dept_code,
          tele:
            employee.dept && employee.dept.phone ? employee.dept.phone : null,
          email:
            employee.dept && employee.dept.email ? employee.dept.email : null,
          nameZh:
            employee.dept && employee.dept.name_zh
              ? employee.dept.name_zh
              : null,
          nameEn:
            employee.dept && employee.dept.name_en
              ? employee.dept.name_en
              : null,
          addrZh:
            employee.dept && employee.dept.addr_full_zh
              ? employee.dept.addr_full_zh
              : null,
          addrEn:
            employee.dept && employee.dept.addr_full_en
              ? employee.dept.addr_full_en
              : null,
          compLicenceNo:
            employee.dept && employee.dept.comp_licence_no
              ? employee.dept.comp_licence_no
              : null,
        },
      };
    } catch (e) {
      console.error(e);
      resJson(res, 300, `failed to fetch employeeInfo. id: ${empId}`);
      return;
    }

    const query = await getQuery("CREATE_PREVIEW");
    const variables = {
      type,
      [type]: { ...req.body[type], sbu },
      empId,
      bypassCode: proposalBypassCode,
    };

    try {
      const resp = await fetch(`${api.proposal}/graphql`, {
        method: "POST",
        headers: {
          "content-type": "application/json",
        },
        body: JSON.stringify({
          query,
          variables,
        }),
      });

      if (resp.status !== 200 && resp.status !== 400)
        throw new Error(resp.statusText);

      const { data, errors } = await resp.json();

      if (errors) throw new Error(errors[0].message);

      resJson(res, 200, null, data);
    } catch (e) {
      console.error(e);
      resJson(res, 300, e.message);
    }
  }
};

export const getEmployeeInfo = async (getQuery, emp_id) => {
  const query = await getQuery("LIST_EMPLOYEES_QUERY");
  const variables = {
    emp_id: [emp_id],
  };
  const body = {
    query,
    variables,
  };

  try {
    const resp = await fetch(api.employeeInternal, {
      method: "POST",
      headers: {
        Authorization: "Bearer rVEk4fDSvbVGCL93ANHto8LR0KQIIP6v"
      },
      body: JSON.stringify(body),
    });
    const data = await resp.json();

    if (data.data && data.data.employees && data.data.employees[0])
      return data.data.employees[0];
    else return {};
  } catch (e) {
    console.log(e);
    return {};
  }
};

export const listProposal = async (req, res, next, getQuery) => {
  if (!req.user) {
    next();
  } else {
    const { limit, offset } = req.body.variables;
    const userInfo =
      req.user && req.user.login && req.user.login.info
        ? req.user.login.info
        : {};

    const proposalsQuery = await getQuery("LIST_PROPOSALS_QUERY");
    const variables = {
      emp_id: userInfo.emp_id,
      sbu,
      bypassCode: proposalBypassCode,
    };
    const proposalsBody = {
      query: proposalsQuery,
      variables,
    };

    try {
      const proposalsResp = await fetch(`${api.proposal}/graphql`, {
        method: "post",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(proposalsBody),
      });

      if (proposalsResp.status !== 200 && proposalsResp.status !== 400)
        throw new Error(proposalsResp.statusText);

      const { data, errors: proposalsError } = await proposalsResp.json();
      const { proposals, proposalsCount, listProposals, listProposalsCount } =
        data;

      if (proposalsError) throw new Error(proposalsError[0].message);

      const sortedProposals = [
        ...proposals,
        ...listProposals.map((p) => ({ ...p, isListProposal: true })),
      ]
        .sort((a, b) => new Date(b.createdDate) - new Date(a.createdDate))
        .slice(offset, limit);
      const totalCount = proposalsCount + listProposalsCount;

      resJson(res, 200, null, {
        proposals: sortedProposals,
        proposalsCount: totalCount,
      });
    } catch (e) {
      console.log(e);
      resJson(res, 300, e.message);
    }
  }
};

export const getProposalCount = async (req, res, next, getQuery) => {
  if (!req.user) {
    next();
  } else {
    let variables = {};
    let userInfo =
      req.user && req.user.login && req.user.login.info
        ? req.user.login.info
        : {};

    const query = await getQuery("GET_PROPOSAL_COUNT");
    variables.emp_id = userInfo.emp_id;
    variables.sbu = sbu;
    variables.bypassCode = proposalBypassCode;
    const body = {
      query,
      variables,
    };

    try {
      const resp = await fetch(`${api.proposal}/graphql`, {
        method: "post",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      if (resp.status !== 200 && resp.status !== 400)
        throw new Error(resp.statusText);
      const { data, errors } = await resp.json();
      if (errors) throw new Error(errors[0].message);

      resJson(res, 200, null, [].concat(data.proposals, data.listProposals));
    } catch (e) {
      console.log(e);
      resJson(res, 300, e.message);
    }
  }
};

export const getReCreatePP = async (req, res, next, getQuery) => {
  if (!req.body?.hash) {
    next();
  } else {
    const variables = {
      hash: req.body.hash
    };
    const query = `
    query($hash: String!){
      offerLetter(hash: $hash) {
        type
        offerLetterName
        noOfOffer {
          value
          isShow
        }
        language
        stockId
        stockMongoId
        customStreet
        customStreetNo
        customDistrict
        customAddressZh
        customAddressEn
        districtNameZh
        districtNameEn
        streetNameZh
        streetNameEn
        streetNo
        photos {
          id
          photoContent
          mediumRoot
          multiImg
        }
        googleMap {
          isPP
          isMain
          isMain1
          isMain2
        }
        main1Photo {
          id
          photoContent
          mediumRoot
        }
        main2Photo {
          id
          photoContent
          mediumRoot
        }
        mainPhoto {
          id
          photoContent
          mediumRoot
        }
        floor
        unit {
          value
          isShow
        }
        area {
          value
          isShow
        }
        areaType
        remarks {
          descriptionEn
          descriptionZh
          remarkEn
          remarkZh
        }
        contactPerson
        contactTitle
        company
        address
        manager
        managerTitle
        managerSex
        managerZh
        managerEn
        managerTitleZh
        managerTitleEn
        managerLicenceNo
        avgPrice {
          value
          isShow
        }
        totalPrice {
          value
          isShow
        }
        possession {
          value {
            nameEn
            nameZh
          }
          freeTextValue
          isShow
          freeTextValueIsShow
        }
        completion {
          value
          isShow
        }
        stampDuty {
          value {
            nameEn
            nameZh
          }
          isShow
        }
        saleAgencyFee {
          p
          exact
          pIsShow
          exactIsShow
        }
        uponProvisional {
          p
          s
          pIsShow
          sIsShow
        }
        uponFormal {
          p
          s
          pIsShow
          sIsShow
          afterProvisionalDays
          beforeProvisionalDays
          afterProvisionalDaysIsShow
          beforeProvisionalDaysIsShow
        }
        uponCompletion {
          p
          s
          pIsShow
          sIsShow
          afterFormalDays
          afterFormalMths
          beforeFormalDays
          afterFormalDaysIsShow
          beforeFormalDaysIsShow
        }
        avgRent {
          value
          isShow
        }
        totalRent {
          value
          isShow
        }
        allInclusive
        includedFee {
          managementFee
          rates
          gRent
          acFee
        }
        managementFee {
          value
          isShow
          unit
          paidBy
          paidByIsShow
        }
        rates {
          value
          isShow
          unit
          paidBy
          paidByIsShow
        }
        gRent {
          value
          isShow
          unit
          paidBy
          paidByIsShow
        }
        acFee {
          value
          isShow
          unit
          paidBy
          paidByIsShow
        }
        leaseTerm {
          yrs
          mths
          leaseCommercingDate
          yrsIsShow
          leaseCommercingDateIsShow
        }
        optionTerm {
          value
          isShow
        }
        newRental {
          s
          p
          pIsShow
        }
        rentFreePeriod {
          days
          mths
          startDate
          endDate
          bothDaysInclusive
          freeTextValue
          daysIsShow
          startDateIsShow
          freeTextValueIsShow
        }
        leaseAgencyFee {
          mths
          exact
          mthsIsShow
          exactIsShow
        }
        isMgtFeeOpenAC
        uponSigning {
          rentalMths
          rentalMthsIsShow
          isMgtFeeOpenAC
          managementFee
          managementFeeIsShow
          rates
          ratesIsShow
          gRent
          gRentIsShow
          acFee
          acFeeIsShow
        }
        uponSigningFormal {
          rentalMths
          rentalMthsIsShow
          isMgtFeeOpenAC
          managementFee
          managementFeeIsShow
          rates
          ratesIsShow
          gRent
          gRentIsShow
          acFee
          acFeeIsShow
        }
        advanceRental {
          value
          isShow
        }
        dateOfSignFormalTenancyAgreement {
          value
          isShow
        }
      }
      listProposal(hash: $hash){
        proposalName
        type
        hideEmployeePhoto
        hideContact
        hideMainPhoto
        hideTenancy
        showEmployeePhoto
        showContact
        showMainPhoto
        showTenancy
        showPossession
        showCurrentState
        showUnit
        termRemarks {
          isShow
          nameZh
          nameEn
        }
        customTitle {
          value
          isShow
        }
        lang
        multiImg
        fontSize
        ...proposals
      }
      proposal(hash: $hash){
        proposalName
        type
        hideEmployeePhoto
        hideContact
        showEmployeePhoto
        showContact
        termRemarks {
          isShow
          nameZh
          nameEn
        }
        customTitle {
          value
          isShow
        }
        lang
        multiImg
        fontSize
        stockId
        stockMongoId
        exactFloor
        avgPrice {
          ...floatIsShow
        }
        totalPrice{
          ...floatIsShowIsNego
        }
        bottomAvgPrice{
          ...floatIsShow
        }
        bottomTotalPrice{
          ...floatIsShow
        }
        suggestedAvgPrice{
          ...floatIsShow
        }
        suggestedTotalPrice{
          ...floatIsShow
        }
        avgRent{
          ...floatIsShow
        }
        totalRent{
          ...floatIsShowIsNego
        }
        bottomAvgRent{
          ...floatIsShow
        }
        bottomTotalRent{
          ...floatIsShow
        }
        suggestedAvgRent{
          ...floatIsShow
        }
        suggestedTotalRent{
          ...floatIsShow
        }
        floor{
          ...stringIsShow
        }
        floorInChinese
        floorType{
          nameZh
          nameEn
        }
        isMgtFeeOpenAC
        unit{
          ...stringIsShow
        }
        floorLoading {
          ...floatIsShow
        }
        customBuilding{
          ...stringIsShow
        }
        customStreet
        customStreetNo
        customDistrict
        customAddressZh
        customAddressEn
        areaEfficiency{
          ...floatIsShow
        }
        areaGross{
          ...floatIsShow
        }
        areaNet{
          ...floatIsShow
        }
        areaSaleable{
          ...floatIsShow
        }
        areaLettable{
          ...floatIsShow
        }
        areaTerrace {
          ...floatIsShow
        }
        areaRoof {
          ...floatIsShow
        }
        areas {
          areaName
          areaNameEn
          gross
          grossIsShow
          grossVerified
          net
          netIsShow
          netVerified
        }
        stockType {
          ...nameZhEnIsShow
        }
        possession{
          ...nameZhEnIsShow
        }
        currentState {
          ...nameZhEnIsShow
        }
        managementFee {
          value
          isShow
          paidBy
          paidByIsShow
          unit
          totalMonthlyMgtFee
          totalMonthlyMgtFeeIsShow
        }
        rates {
          value
          isShow
          unit
          paidBy
          paidByIsShow
        }
        gRent {
          value
          isShow
          unit
          paidBy
          paidByIsShow
        }
        acFee {
          value
          isShow
          unit
          paidBy
          paidByIsShow
        }
        decoration{
          ... nameZhEnIsShow
        }
        unitView{
          ... nameZhEnIsShow
        }
        remarks
        includedFee {
          managementFee
          rates
          gRent
          acFee
        }
        allInclusive
        isTenanted
        isSelfuseOrTenant
        entranceWidth{
          ...ftInIsShow
        }
        unitDepth{
          ...ftInIsShow
        }
        ceilingHeight{
          ...ftInIsShow
        }
        availability {
          ...stringIsShow
        }
        isSoleagent
        districtNameZh
        districtNameEn
        streetNameZh
        streetNameEn
        streetNo
        mainPhoto{
          id
          photoContent
          mediumRoot
        }
        main1Photo{
          id
          photoContent
          mediumRoot
        }
        main2Photo{
          id
          photoContent
          mediumRoot
        }
        building {
          ...building
        }
        photos {
          id
          photoContent
          mediumRoot
          multiImg
        }
        currentTenants {
          tenant {
            ...flatNameZhEnIsShow
          }
          rentalFee {
            ...floatIsShow
          }
          period {
            ...minMaxIsShow
          }
          tenancy {
            ...flatNameZhEnIsShow
          }
          tenancyRemarks {
            ...stringIsShow
          }
        }
        yield {
          ...floatIsShow
        }
        termRemarks {
          isShow
          nameEn
          nameZh
        }
        customTitle {
          value
          isShow
        }
        googleMap {
          isMain
          isMain1
          isMain2
          isPP
        }
      }
    }
    fragment proposals on ListProposal{
      proposals{
        stockId
        stockMongoId
        avgPrice {
          ...floatIsShow
        }
        totalPrice{
          ...floatIsShowIsNego
        }
        bottomAvgPrice{
          ...floatIsShow
        }
        bottomTotalPrice{
          ...floatIsShow
        }
        suggestedAvgPrice{
          ...floatIsShow
        }
        suggestedTotalPrice{
          ...floatIsShow
        }
        avgRent{
          ...floatIsShow
        }
        totalRent{
          ...floatIsShowIsNego
        }
        bottomAvgRent{
          ...floatIsShow
        }
        bottomTotalRent{
          ...floatIsShow
        }
        suggestedAvgRent{
          ...floatIsShow
        }
        suggestedTotalRent{
          ...floatIsShow
        }
        floor{
          ...stringIsShow
        }
        floorInChinese
        floorType{
          nameZh
          nameEn
        }
        isMgtFeeOpenAC
        unit{
          ...stringIsShow
        }
        floorLoading {
          ...floatIsShow
        }
        customBuilding{
          ...stringIsShow
        }
        customStreet
        customStreetNo
        customDistrict
        customAddressZh
        customAddressEn
        areaEfficiency{
          ...floatIsShow
        }
        areaGross{
          ...floatIsShow
        }
        areaNet{
          ...floatIsShow
        }
        areaSaleable{
          ...floatIsShow
        }
        areaLettable{
          ...floatIsShow
        }
        areaTerrace {
          ...floatIsShow
        }
        areaRoof {
          ...floatIsShow
        }
        areas {
          areaName
          areaNameEn
          gross
          grossIsShow
          grossVerified
          net
          netIsShow
          netVerified
        }
        stockType {
          ...nameZhEnIsShow
        }
        possession{
          ...nameZhEnIsShow
        }
        currentState {
          ...nameZhEnIsShow
        }
        managementFee {
          value
          isShow
          paidBy
          paidByIsShow
          unit
          totalMonthlyMgtFee
          totalMonthlyMgtFeeIsShow
        }
        rates {
          value
          isShow
          unit
          paidBy
          paidByIsShow
        }
        gRent {
          value
          isShow
          unit
          paidBy
          paidByIsShow
        }
        acFee {
          value
          isShow
          unit
          paidBy
          paidByIsShow
        }
        decoration{
          ... nameZhEnIsShow
        }
        unitView{
          ... nameZhEnIsShow
        }
        remarks
        includedFee {
          managementFee
          rates
          gRent
          acFee
        }
        allInclusive
        isTenanted
        isSelfuseOrTenant
        entranceWidth{
          ...ftInIsShow
        }
        unitDepth{
          ...ftInIsShow
        }
        ceilingHeight{
          ...ftInIsShow
        }
        availability {
          ...stringIsShow
        }
        isSoleagent
        districtNameZh
        districtNameEn
        streetNameZh
        streetNameEn
        streetNo
        mainPhoto{
          id
          photoContent
          mediumRoot
        }
        main1Photo{
          id
          photoContent
          mediumRoot
        }
        main2Photo{
          id
          photoContent
          mediumRoot
        }
        useGGMapPhoto
        building {
          ...building
        }
        photos {
          id
          photoContent
          mediumRoot
          multiImg
        }
        includeGGMap
        currentTenants {
          tenant {
            ...flatNameZhEnIsShow
          }
          rentalFee {
            ...floatIsShow
          }
          period {
            ...minMaxIsShow
          }
          tenancy {
            ...flatNameZhEnIsShow
          }
          tenancyRemarks {
            ...stringIsShow
          }
        }
        yield {
          ...floatIsShow
        }
        termRemarks {
          isShow
          nameEn
          nameZh
        }
        customTitle {
          value
          isShow
        }
        googleMap {
          isMain
          isMain1
          isMain2
          isPP
        }
      }
    }
    fragment minMaxIsShow on MinMaxIsShow {
      min
      max
      isShow
    }
    fragment flatNameZhEnIsShow on FlatNameZhEnIsShow {
      nameZh
      nameEn
      isShow
    }
    fragment stringIsShow on StringIsShow{
      value
      isShow
    }
    fragment floatIsShow on FloatIsShow{
      value
      isShow
    }
    fragment floatIsShowIsNego on FloatIsShowIsNego{
      value
      isShow
      isNego
    }
    fragment nameZhEnIsShow on NameZhEnIsShow{
      value{
        nameZh
        nameEn
      }
      isShow
    }
    fragment ftInIsShow on FtInIsShow{
      ft
      in
      isShow
    }
    fragment building on ProposalBuilding{
      id
      pisId
      nameZh
      nameEn
      districtNameZh
      districtNameEn
      descriptionZh
      descriptionEn
      passengerLift{
        ...nameZhEnIsShow
      }
      cargoLift{
        ...nameZhEnIsShow
      }
      airConditioningType{
        ...nameZhEnIsShow
      }
      airConditioningOpeningTime{
        ...nameZhEnIsShow
      }
      airConditioningExtraCharges{
        ...nameZhEnIsShow
      }
      haveCarPark{
        ...nameZhEnIsShow
      }
      title {
        ...nameZhEnIsShow
      }
      inTakeDate {
        ...stringIsShow
      }
      managementCompany {
        ...nameZhEnIsShow
      }
      transport {
        ...nameZhEnIsShow
      }
      containers {
        isShow
        value {
          nameZh
          nameEn
          haveLoadingBay
        }
      }
      usage {
        ...nameZhEnIsShow
      }
      lng
      lat
      isBuildingFieldsAllHide
    }
    `;
    const body = {
      query,
      variables,
    };
    try {
      const resp = await fetch(`${api.proposal}/graphql`, {
        method: "post",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      if (resp.status !== 200 && resp.status !== 400)
        throw new Error(resp.statusText);
      const { data, errors } = await resp.json();
      if (errors) throw new Error(errors[0].message);
      resJson(res, 200, null, data);
    } catch (e) {
      console.log(e);
      resJson(res, 300, e.message);
    }
  }
};

export const removeProposal = async (req, res, next, getQuery) => {
  if (!req.user) {
    next();
  } else {
    const { variables } = req.body;
    let userInfo =
      req.user && req.user.login && req.user.login.info
        ? req.user.login.info
        : {};

    const query = await getQuery(
      variables.isListProposal
        ? "REMOVE_LIST_PROPOSAL_QUERY"
        : "REMOVE_PROPOSAL_QUERY",
    );

    variables.salesmanId = userInfo.emp_id;
    variables.bypassCode = proposalBypassCode;
    delete variables.isListProposal;
    const body = {
      query,
      variables,
    };

    try {
      const resp = await fetch(`${api.proposal}/graphql`, {
        method: "post",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      if (resp.status !== 200 && resp.status !== 400)
        throw new Error(resp.statusText);
      const { data, errors } = await resp.json();

      if (errors) throw new Error(errors[0].message);
      resJson(res, 200, null, data);
    } catch (e) {
      console.log(e);
      resJson(res, 300, e.message);
    }
  }
};
