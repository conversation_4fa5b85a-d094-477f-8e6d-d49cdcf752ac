import React from "react";
import PropTypes from "prop-types";
import moment from "moment";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import { injectIntl } from "react-intl";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import FieldVal from "../../../../common/FieldVal";

const styles = theme => ({
  root: {
    padding: "2vw"
  }
});

class BasicInfo extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;

    const buildingUsage = detail.usage && detail.usage.length ? detail.usage.join(", ") : "---";
    const buildingGrade = detail.grade || "---";
    const isCloseToMTR =
      detail.closeToMTR === true
        ? "Yes"
        : detail.closeToMTR === false
        ? "No"
        : "---";
    const completionDate = detail.inTakeDate ? moment(parseInt(detail.inTakeDate)).format("YYYY") : "---";
    const managementFee = detail.managementFeeEn || "---";
    const feeIncludeACBool = detail.isIncludeAirCondCharge;
    const feeIncludeAC =
      feeIncludeACBool === true
        ? "Include"
        : feeIncludeACBool === false
        ? "Exclude"
        : "---";
    const developers = detail.developers || [];
    let developer = developers.length === 0 ? "---" : "";
    developers.forEach((v, i) => {
      developer += (i > 0 ? ", " : "") + (v.nameEn || "---");
    });
    const managementCompany =
      detail.managementCompanies && detail.managementCompanies[0]
        ? detail.managementCompanies[0]
        : {};
    const managementCompanyName =
      managementCompany.nameEn
        ? managementCompany.nameEn
        : "---";
    // const contactName = detail.managementCompanyPerson || "---";
    // const contactPhones = detail.managementCompanyContact || "---";
    const contactName = detail.managementCompanyPeople || "---";
    const contactPhones = detail.managementCompanyContacts || "---";

    let generalMapping = {
      [intl.formatMessage({
        id: "stock.usage"
      })]: { value: buildingUsage, xs: 6 },
      [intl.formatMessage({
        id: "search.form.grade"
      })]: { value: buildingGrade, xs: 6 },
      [intl.formatMessage({
        id: "building.mtr"
      })]: { value: isCloseToMTR, xs: 6 },
      [intl.formatMessage({
        id: "building.competition"
      })]: { value: completionDate, xs: 6 },
      [intl.formatMessage({
        id: "stock.mgtfee"
      })]: { value: managementFee, xs: 6 },
      [intl.formatMessage({
        id: "building.accharge"
      })]: { value: feeIncludeAC, xs: 6 },
      [intl.formatMessage({
        id: "building.developer"
      })]: { value: developer, xs: 12 },
      [intl.formatMessage({
        id: "building.mgtcompany"
      })]: { value: managementCompanyName, xs: 12 },
      [intl.formatMessage({
        id: "stock.contact"
      })]: { value: contactName, xs: 12 },
      [intl.formatMessage({
        id: "building.phone"
      })]: { value: contactPhones, xs: 12 }
    };

    return (
      <div className={classes.root}>
        <Grid container spacing={2}>
          {Object.keys(generalMapping).map((v, i) => (
            <Grid item xs={generalMapping[v].xs} key={v}>
              <FieldVal field={v} style={generalMapping[v].style}>
                {generalMapping[v].value}
              </FieldVal>
            </Grid>
          ))}
        </Grid>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(BasicInfo));
