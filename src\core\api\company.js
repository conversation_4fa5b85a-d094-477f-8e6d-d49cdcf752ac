import fetch from "node-fetch";
import _ from "lodash";

import { api } from "@/config";

function resJson(res, status, error, data) {
  res.json({
    status,
    errors: error ? [{ message: error }] : null,
    data,
  });
  res.end();
}

export const getCompanyInfo = async (query, variables, headers = {}) =>
  fetch(api.contact, {
    method: "POST",
    headers: {
      "content-type": "application/json",
      ...headers,
    },
    body: JSON.stringify({ query, variables }),
  }).then((res) => res.json());

export const listCompanies = async (req, res, next, getQuery) => {
  if (!req.user) {
    next();
  } else {
    try {
      const query = await getQuery("LIST_COMPANIES_QUERY");

      const resp = await getCompanyInfo(query, req.body.variables, {
        Authorization: req.headers["authorization"],
        "CAS-Authorization": req.headers["cas-authorization"],
      });
      if (!_.isEmpty(_.get(resp, "errors"))) {
        throw new Error(
          _.get(resp, "errors.0.message") || "Internal server error",
        );
      }
      resJson(res, 200, null, resp.data);
    } catch (e) {
      console.error(e);
      resJson(res, 300, e.message);
    }
  }
};

export const createCompany = async (req, res, next, getQuery) => {
  if (!req.user) {
    next();
  } else {
    try {
      const query = `
      mutation ($companyBookContactDetails : CompanyBookContactDetailsCreate!){
        createCompanyBookContact(companyBookContactDetails: $companyBookContactDetails){
          _id
        }
      }`

      const resp = await getCompanyInfo(query, req.body.variables, {
        Authorization: req.headers["authorization"],
        "CAS-Authorization": req.headers["cas-authorization"],
      });
      if (!_.isEmpty(_.get(resp, "errors"))) {
        throw new Error(
          _.get(resp, "errors.0.message") || "Internal server error",
        );
      }
      resJson(res, 200, null, resp.data);
    } catch (e) {
      console.error(e);
      resJson(res, 300, e.message);
    }
  }
};

export const listCompany = async (req, res, next, getQuery) => {
  if (!req.user) {
    next();
  } else {
    try {
      const query = await getQuery("LIST_COMPANY_DETAIL_QUERY");

      const resp = await getCompanyInfo(query, req.body.variables, {
        Authorization: req.headers["authorization"],
        "CAS-Authorization": req.headers["cas-authorization"],
      });
      console.log('[ resp ] >', resp)
      const company = _.get(resp, "data.companyBookContactDetails.0") || {};
      if (_.isEmpty(company)) {
        throw new Error("Company not found");
      } else {
        resJson(res, 200, null, company);
      }
    } catch (e) {
      console.error(e);
      resJson(res, 300, e.message);
    }
  }
};

export const querySourceOptions = async (req, res, next, getQuery) => {
  if (!req.user) {
    next();
  } else {
    try {
      const query = await getQuery("CONTACT_SOURCE_OPTIONS");

      const resp = await getCompanyInfo(query, req.body.variables, {
        Authorization: req.headers["authorization"],
        "CAS-Authorization": req.headers["cas-authorization"],
      });
      const sourceOptions = _.get(resp, "data.source") || {};
      if (_.isEmpty(sourceOptions)) {
        throw new Error("sourceOptions not found");
      } else {
        resJson(res, 200, null, sourceOptions);
      }
    } catch (e) {
      console.error(e);
      resJson(res, 300, e.message);
    }
  }
};

export const applyCompanySearch = async (req, res, next, getQuery) => {
  if (!req.user) {
    next();
  } else {
    try {
      const query = await getQuery("APPLY_COMPANY_SEARCH");

      const details = { ...req.body.details };
      if (
        !details.applicantUserName &&
        _.get(req, "user.login.info.user_id", "")
      ) {
        details.applicantUserName = req.user.login.info.user_id;
      }

      if (!details.applicantId && _.get(req, "user.login.info.emp_id", "")) {
        details.applicantId = req.user.login.info.emp_id;
      }

      const resp = await fetch(api.landSearchDetails, {
        method: "POST",
        headers: {
          "content-type": "application/json",
          Authorization: req.headers["authorization"],
          "CAS-Authorization": req.headers["cas-authorization"],
        },
        body: JSON.stringify({
          query,
          variables: {
            details,
          },
        }),
      }).then((response) => response.json());

      if (!_.isEmpty(_.get(resp, "errors"))) {
        resJson(res, 300, _.get(resp, "errors"));
      } else {
        resJson(res, 200, null, resp);
      }
    } catch (e) {
      console.error(e);
      resJson(res, 300, e);
    }
  }
};
