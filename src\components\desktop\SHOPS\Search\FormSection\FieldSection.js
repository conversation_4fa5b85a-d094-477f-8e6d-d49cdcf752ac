import React, { useState } from "react";
import { connect } from "react-redux";
import { Field, FieldArray, change } from "redux-form";
import { withStyles } from "@material-ui/core/styles";
import OutlinedInput from "@material-ui/core/OutlinedInput";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import TextInput from "../../../../common/TextInput";
import TextInputCombined from "../../../../common/TextInputCombined";
import ArrayPillCheckBox from "../../../../common/ArrayPillCheckBox";
import PillCheckBox from "../../../../common/PillCheckBox";
import PillButton from "../../../../common/PillButton";
import TextArrayInput from "../../../../common/TextArrayInput";
import SelectFieldArrayOutput from "../../../../common/SelectFieldArrayOutput";
import ReactSelectCreatable from "../../../../common/ReactSelectCreatable";
import ChipsCheckBox from "../../../../common/ChipsCheckBox";
import AutoCompleteSelect from "../../../../common/AutoCompleteSelect";
import Search from "../../../../common/Search";
import Switch from "../../../../common/Switch";
import Grid from "@material-ui/core/Grid";
import Collapse from "@material-ui/core/Collapse";
import DatePicker from "../../../../common/DatePicker";
import { minValue, maxValue, number } from "../../../../../core/formValidators";
import InputAdornment from "@material-ui/core/InputAdornment";
import { injectIntl, FormattedMessage } from "react-intl";
import { listBuildings } from "../../../../../actions/building";
import { listStreets } from "../../../../../actions/street";
import { listCompanies } from "../../../../../actions/stocklist";
import { getLangKey } from "../../../../../helper/generalHelper";
import { enableConsolidLandSearch } from "../../../../../config";

// We can inject some CSS into the DOM.
const styles = {
  expendbutton: {
    textTransform: "none",
    display: "flex",
    marginLeft: "auto",
    color: "#33CCCC",
  },
  divider: {
    textAlign: "center",
    margin: "auto 0px",
  },
  Headertitle: {
    color: "rgba(0, 0, 0, 0.54)",
    fontSize: "1em",
    fontWeight: "400",
  },
  DetailBoxSectionContent: {
    paddingLeft: 0,
    paddingRight: 0,
    paddingBottom: "3vh",
  },
  sticky: {
    position: "-webkit-sticky" /* Safari */,
    position: "sticky",
    top: "52px",
  },
  sectionTitleBtnContainer: {
    "& > button": {
      marginLeft: "1vw",
      fontSize: 12,
    },
  },
  fieldsdivider: {
    marginTop: 8,
    marginBottom: 10,
    borderRadius: 0,
    "& input": {
      textAlign: "center",
      transform: "scale(1.6)",
    },
    "& .MuiOutlinedInput-notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23)",
      borderLeft: 0,
      borderRight: 0,
    },
    "&:hover .MuiOutlinedInput-notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23)",
    },
    "& $focused $notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23) !important",
    },
  },
  fieldMargin: {
    marginTop: 8,
    marginBottom: 10,
  },
  paddingSection: {
    paddingBottom: "3vh",
  },
};

const boolranges = [
  {
    value: "",
    label: "All",
  },
  {
    value: true,
    label: "True",
  },
  {
    value: false,
    label: "False",
  },
];

function getstatusOptions(intl) {
  const statusOptions = [
    {
      value: "Sale",
      label: intl.formatMessage({
        id: "search.status.sale",
      }),
    },
    {
      value: "Lease",
      label: intl.formatMessage({
        id: "search.status.lease",
      }),
    },
    {
      value: "Sale+Lease",
      label: intl.formatMessage({
        id: "search.status.salesandlease",
      }),
    },
    {
      value: "Leased",
      label: intl.formatMessage({
        id: "search.status.leased",
      }),
    },
    {
      value: "Sold",
      label: intl.formatMessage({
        id: "search.status.sold",
      }),
    },
    {
      value: "Search",
      label: intl.formatMessage({
        id: "search.status.se",
      }),
    },
    {
      value: "Cancel",
      label: intl.formatMessage({
        id: "search.status.cancel",
      }),
    },
    {
      value: "History",
      label: intl.formatMessage({
        id: "search.status.history",
      }),
    },
    {
      value: "Pending",
      label: intl.formatMessage({
        id: "search.status.pending",
      }),
    },
    {
      value: "Don't Call",
      label: intl.formatMessage({
        id: "search.status.dontcall",
      }),
    },
    {
      value: "CU",
      label: intl.formatMessage({
        id: "search.status.cu",
      }),
    },
  ];
  return statusOptions;
}

function getpropertyconditionOptions(intl) {
  const miscOptions = [
    {
      value: "isConfirmorSales",
      label: intl.formatMessage({
        id: "search.form.confirmor",
      }),
    },
    {
      value: "isMortgagee",
      label: intl.formatMessage({
        id: "search.form.mortgagee",
      }),
    },
    {
      value: "isWithKey",
      label: intl.formatMessage({
        id: "search.form.withkey",
      }),
    },
    {
      value: "isShoppingMallStock",
      label: intl.formatMessage({
        id: "search.form.shoppingmallstock",
      }),
    },
    {
      value: "isSingleSideStock",
      label: intl.formatMessage({
        id: "search.form.singlesidestock",
      }),
    },
    {
      value: "isFrontAndRearPortion",
      label: intl.formatMessage({
        id: "search.form.frontandrearportion",
      }),
    },
    {
      value: "isSoleAgentForSale",
      label: intl.formatMessage({
        id: "search.form.soleagentsale",
      }),
    },
    {
      value: "isSoleAgentForLease",
      label: intl.formatMessage({
        id: "search.form.soleagentlease",
      }),
    },
    {
      value: "isSoleAgentExpired",
      label: intl.formatMessage({
        id: "search.form.soleagentexpired",
      }),
    },
    {
      value: "haveSurveyorProposal",
      label: intl.formatMessage({
        id: "search.form.proposal",
      }),
    },
    {
      value: "isPrivacy",
      label: intl.formatMessage({
        id: "search.form.privacylimit",
      }),
    },

    {
      value: "havePropertyAdvertisements",
      label: intl.formatMessage({
        id: "search.form.advsconsent",
      }),
    },
  ];
  return miscOptions;
}

function getmarketableMapping(intl) {
  const marketableMapping = [
    {
      value: "isMarketableForLease",
      label: intl.formatMessage({
        id: "search.marketable.lease",
      }),
    },
    {
      value: "isMarketableForSale",
      label: intl.formatMessage({
        id: "search.marketable.sale",
      }),
    },
    {
      value: "isMarketableForSaleOrLease",
      label: intl.formatMessage({
        id: "search.marketable.salelease",
      }),
    },
  ];
  return marketableMapping;
}

function getcockloftMapping(intl) {
  const marketableMapping = [
    {
      value: "haveCockloft",
      label: intl.formatMessage({
        id: "stock.cockloft.authorized",
      }),
    },
    {
      value: "haveLegalCockloft",
      label: intl.formatMessage({
        id: "stock.cockloft.selfbuilt",
      }),
    },
    {
      value: "haveCockloftOrLegalCockloft",
      label: intl.formatMessage({
        id: "stock.cockloft.authorizedandselfbuilt",
      }),
    },
  ];
  return marketableMapping;
}

const unitM = {
  endAdornment: (
    <InputAdornment position="end">
      <span>M</span>
    </InputAdornment>
  ),
};

const unitK = {
  endAdornment: (
    <InputAdornment position="end">
      <span>K</span>
    </InputAdornment>
  ),
};

const unitFormatter = (value, unit) => {
  if (!value || value == null) return "";

  let unitValue;
  if (unit == "K") {
    unitValue = 1000;
  } else if (unit == "M") {
    unitValue = 1000000;
  } else {
    unitValue = 1;
  }
  return value / unitValue;
};

const formatMaxLength = (value, length) => {
  if (!value) {
    return value;
  }

  if (value.length <= length) {
    return `${value.slice(0, length)}`;
  }
  return `${value.slice(0, length)}`;
};

const formatUnitandMaxLength = (value, unit) => {
  if (!value) {
    return value;
  }

  value = value.replace(/[^0-9.]/g, "");

  let unitValue;
  if (unit == "K") unitValue = 1000;
  if (unit == "M") unitValue = 1000000;

  let number = value * unitValue;
  const roundnumber = number.toFixed();
  // return the value after rounding
  return `${roundnumber.toString()}`;
};

function createMinvalue(min) {
  return minValue(min, <FormattedMessage id="search.form.invalidinput" />);
}
function createMaxvalue(max) {
  return maxValue(max, <FormattedMessage id="search.form.invalidinput" />);
}
const numberValidate = number(
  <FormattedMessage id="search.form.invalidinput" />
);
const minvaluezero = createMinvalue(0);
const maxvalue1 = createMaxvalue(200);
const maxvalue2 = createMaxvalue(999);
const maxvalue3 = createMaxvalue(9999);
const maxvalue4 = createMaxvalue(99999);
const maxvalue5 = createMaxvalue(9999999);
const maxvalue6 = createMaxvalue(99999999);
const maxvalue7 = createMaxvalue(999999999);
const maxvalue8 = createMaxvalue(9999999999);

function FieldSection(props) {
  const {
    classes,
    buildings,
    fieldhistory,
    streets,
    districts,
    listBuildings,
    listStreets,
    selectedData,
    setSelectedData,
    expanded,
    intl,
    changeForm,
    initialValues,
    companies,
    listCompanies,
  } = props;

  const stocktypes = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "Groud Shop",
      label: intl.formatMessage({
        id: "search.form.groundshop",
      }),
    },
    {
      value: "Whole Block",
      label: intl.formatMessage({
        id: "search.form.wholeblock",
      }),
    },
    {
      value: "Upstairs Shop",
      label: intl.formatMessage({
        id: "search.form.unstarisshop",
      }),
    },
    {
      value: "Basement",
      label: intl.formatMessage({
        id: "search.form.basement",
      }),
    },
    {
      value: "Carpark",
      label: intl.formatMessage({
        id: "search.form.carpark",
      }),
    },
    {
      value: "Site",
      label: intl.formatMessage({
        id: "search.form.site",
      }),
    },
    {
      value: "Cabinet",
      label: intl.formatMessage({
        id: "search.form.cabinet",
      }),
    },
    {
      value: "Office",
      label: intl.formatMessage({
        id: "common.office",
      }),
    },
    {
      value: "Trade and Industry",
      label: intl.formatMessage({
        id: "search.form.tradeandindustry",
      }),
    },
    // {
    //   value: "Shopping Mall Stock",
    //   label: intl.formatMessage({
    //     id: "search.form.shoppingmallstock",
    //   }),
    // },
    {
      value: "--",
      label: "--",
    },
  ];

  const possession = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "Vacant",
      label: intl.formatMessage({
        id: "search.form.vacant",
      }),
    },
    {
      value: "Immediate Vacancy",
      label: intl.formatMessage({
        id: "search.form.immediatevacant",
      }),
    },
    {
      value: "Nego. Vacant",
      label: intl.formatMessage({
        id: "search.form.negovacant",
      }),
    },
    {
      value: "Vacant Possession",
      label: intl.formatMessage({
        id: "search.form.vacantpossession",
      }),
    },
    {
      value: "Vacant Transaction",
      label: intl.formatMessage({
        id: "search.form.vacanttransaction",
      }),
    },
    {
      value: "Tenant Occupies",
      label: intl.formatMessage({
        id: "search.form.tenant",
      }),
    },
    {
      value: "Owner Occupies",
      label: intl.formatMessage({
        id: "search.form.occupied",
      }),
    },
    {
      value: "Sale With TA",
      label: intl.formatMessage({
        id: "search.form.salwwithta",
      }),
    },
    {
      value: "Incomplete",
      label: intl.formatMessage({
        id: "search.form.incomplete",
      }),
    },
    {
      value: "Sale & Leaseback",
      label: intl.formatMessage({
        id: "search.form.saleandleaseback",
      }),
    },
    {
      value: "N/A",
      label: intl.formatMessage({
        id: "common.na",
      }),
    },
  ];

  const parity = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "odd",
      label: intl.formatMessage({
        id: "search.form.odd",
      }),
    },
    {
      value: "even",
      label: intl.formatMessage({
        id: "search.form.even",
      }),
    },
  ];

  const boardstatus = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "已掛板",
      label: intl.formatMessage({
        id: "tips.stocktag.boardhanged",
      }),
    },
    {
      value: "未掛板",
      label: intl.formatMessage({
        id: "search.form.noboard",
      }),
    },
    {
      value: "不可掛板",
      label: intl.formatMessage({
        id: "search.form.canthang",
      }),
    },
    {
      value: "不適用",
      label: intl.formatMessage({
        id: "common.na",
      }),
    },
  ];

  const newsstatus = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "已貼海報",
      label: intl.formatMessage({
        id: "search.form.poster",
      }),
    },
    {
      value: "未登報紙",
      label: intl.formatMessage({
        id: "search.form.noadv",
      }),
    },
    {
      value: "不可貼海報",
      label: intl.formatMessage({
        id: "search.form.cantposter",
      }),
    },
    {
      value: "不可登報",
      label: intl.formatMessage({
        id: "search.form.cantadv",
      }),
    },
    {
      value: "不適用",
      label: intl.formatMessage({
        id: "common.na",
      }),
    },
  ];

  let priceType, rentType;

  if (initialValues.priceMinTotal || initialValues.priceMaxTotal)
    priceType = "Total";
  if (initialValues.priceMinAvg || initialValues.priceMaxAvg) priceType = "Avg";
  if (initialValues.rentMinTotal || initialValues.rentMaxTotal)
    rentType = "Avg";
  if (initialValues.rentMinAvg || initialValues.rentMaxAvg) rentType = "Avg";
  const [TogglePriceValue, setTogglePriceValue] = useState(
    priceType ? priceType : "Total"
  );
  const [ToggleRentValue, setToggleRentValue] = useState(
    rentType ? rentType : "Total"
  );

  const langkey = getLangKey(intl);

  const selectAllStatus = () => {
    const statusValue = getstatusOptions(intl).map((v) => v.value);
    changeForm("status", statusValue);
  };

  const deselectAllStatus = (fieldName) => {
    changeForm(fieldName, []);
  };

  const singleSelectAllOptions = (options) => {
    const miscOptions = options.map((v) => v.value);
    for (let key in miscOptions) {
      changeForm(miscOptions[key], true);
    }
  };

  const singledeSelectAllOptions = (options) => {
    const miscOptions = options.map((v) => v.value);
    for (let key in miscOptions) {
      changeForm(miscOptions[key], false);
    }
  };

  return (
    <div>
      {!expanded ? (
        <div className={classes.sticky}>
          <Field
            name="streets"
            margin="normal"
            label={intl.formatMessage({
              id: "search.form.street",
            })}
            fullWidth
            component={AutoCompleteSelect}
            optionsdata={streets}
            history={fieldhistory}
            apiaction={listStreets}
            selectedData={selectedData && selectedData.streets}
            setSelectedData={setSelectedData}
          />
        </div>
      ) : (
          <Field
            name="streets"
            margin="normal"
            label={intl.formatMessage({
              id: "search.form.street",
            })}
            fullWidth
            component={AutoCompleteSelect}
            optionsdata={streets}
            apiaction={listStreets}
            selectedData={selectedData && selectedData.streets}
            setSelectedData={setSelectedData}
            customInputProps={{
              className: classes.fieldMargin,
            }}
          />
        )}

      {/* <Field
          name="building"
          type="text"
          margin="normal"
          label="Building"
          component={TextInput}
          fullWidth
          variant="outlined"
        /> */}

      <Collapse in={expanded}>
        <div className={classes.paddingSection}>
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="streetsNoMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.form.streetnumber",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 4)}
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="streetsNoMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue3]}
                  parse={(value) => formatMaxLength(value, 4)}
                  compPosition={"right"}
                />
              </div>
            </Grid>
            <Grid item xs={12}>
              <Field
                name="parity"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.header.parity",
                })}
                ranges={parity}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                selectedData={selectedData && selectedData.parity}
                setSelectedData={setSelectedData}
              />
            </Grid>
          </Grid>

          <Field
            name="district"
            margin="normal"
            label={intl.formatMessage({
              id: "search.form.district",
            })}
            fullWidth
            component={Search}
            searchItems={districts}
            selectedData={selectedData && selectedData.district}
            setSelectedData={setSelectedData}
            customInputProps={{
              className: classes.fieldMargin,
            }}
          />

          <Field
            name="building"
            margin="normal"
            label={intl.formatMessage({
              id: "search.form.building",
            })}
            fullWidth
            component={AutoCompleteSelect}
            optionsdata={buildings}
            apiaction={listBuildings}
            selectedData={selectedData && selectedData.building}
            setSelectedData={setSelectedData}
            customInputProps={{
              className: classes.fieldMargin,
            }}
          />
        </div>

        {/* marketable */}
        <DetailBoxSection
          text={intl.formatMessage({
            id: "search.header.marketable",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
          customRight={
            <div className={classes.sectionTitleBtnContainer}>
              <PillButton
                onClick={() =>
                  singleSelectAllOptions(getmarketableMapping(intl))
                }
              >
                {intl.formatMessage({
                  id: "search.form.all",
                })}
              </PillButton>
              <PillButton
                onClick={() =>
                  singledeSelectAllOptions(getmarketableMapping(intl))
                }
              >
                {intl.formatMessage({
                  id: "search.form.none",
                })}
              </PillButton>
            </div>
          }
        >
          <Grid container spacing={1}>
            {getmarketableMapping(intl).map((v, i) => {
              return (
                <Grid key={i} item xs={4}>
                  <div>
                    <Field
                      name={v.value}
                      text={v.label}
                      component={PillCheckBox}
                    />
                  </div>
                </Grid>
              );
            })}
          </Grid>
        </DetailBoxSection>

        {/* status */}
        <DetailBoxSection
          text={intl.formatMessage({
            id: "search.header.status",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
          customRight={
            <div className={classes.sectionTitleBtnContainer}>
              <PillButton onClick={selectAllStatus}>
                {intl.formatMessage({
                  id: "search.form.all",
                })}
              </PillButton>
              <PillButton onClick={() => deselectAllStatus("status")}>
                {intl.formatMessage({
                  id: "search.form.none",
                })}
              </PillButton>
            </div>
          }
        >
          <FieldArray
            name="status"
            component={ArrayPillCheckBox}
            options={getstatusOptions(intl)}
          />
        </DetailBoxSection>

        {/* cockloft */}
        <DetailBoxSection
          text={intl.formatMessage({
            id: "stock.cockloft",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
          customRight={
            <div className={classes.sectionTitleBtnContainer}>
              <PillButton
                onClick={() => singleSelectAllOptions(getcockloftMapping(intl))}
              >
                {intl.formatMessage({
                  id: "search.form.all",
                })}
              </PillButton>
              <PillButton
                onClick={() =>
                  singledeSelectAllOptions(getcockloftMapping(intl))
                }
              >
                {intl.formatMessage({
                  id: "search.form.none",
                })}
              </PillButton>
            </div>
          }
        >
          <Grid container spacing={1}>
            {getcockloftMapping(intl).map((v, i) => {
              return (
                <Grid key={i} item xs={4}>
                  <div>
                    <Field
                      name={v.value}
                      text={v.label}
                      component={PillCheckBox}
                    />
                  </div>
                </Grid>
              );
            })}
          </Grid>
        </DetailBoxSection>

        <DetailBoxSection
          text={intl.formatMessage({
            id: "search.header.stock",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
        >
          {/* area */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="areaMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.area",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="areaMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue8]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>

          {/* area for shop */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="areaForShopMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "stock.shoparea",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="areaForShopMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue8]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>

          {/* price */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"priceMin" + TogglePriceValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  label={intl.formatMessage({
                    id: "search.common.price",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  validate={
                    TogglePriceValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue8]
                      : [minvaluezero]
                  }
                  InputProps={TogglePriceValue === "Total" ? unitM : null}
                  format={(value) => {
                    let formattedvalue;
                    if (TogglePriceValue === "Total") {
                      formattedvalue = unitFormatter(value, "M");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (TogglePriceValue === "Total") {
                      return formatUnitandMaxLength(value, "M");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"priceMax" + TogglePriceValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  validate={
                    TogglePriceValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue8]
                      : [minvaluezero, maxvalue8]
                  }
                  InputProps={TogglePriceValue == "Total" && unitM}
                  format={(value) => {
                    let formattedvalue;
                    if (TogglePriceValue === "Total") {
                      formattedvalue = unitFormatter(value, "M");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (TogglePriceValue === "Total") {
                      return formatUnitandMaxLength(value, "M");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                // rightcomp={
                //   <Switch
                //     value={TogglePriceValue}
                //     textL={intl.formatMessage({
                //       id: "search.common.pricetotal",
                //     })}
                //     textR={intl.formatMessage({
                //       id: "search.common.priceavg",
                //     })}
                //     valleft="Total"
                //     valright="Avg"
                //     onChange={onChangePriceTotalAvg}
                //   />
                // }
                />
              </div>
            </Grid>
          </Grid>

          {/* rent */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"rentMin" + ToggleRentValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  label={intl.formatMessage({
                    id: "search.common.rent",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  validate={
                    ToggleRentValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue6]
                      : [minvaluezero]
                  }
                  InputProps={ToggleRentValue === "Total" && unitK}
                  format={(value) => {
                    let formattedvalue;
                    if (ToggleRentValue === "Total") {
                      formattedvalue = unitFormatter(value, "K");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (ToggleRentValue === "Total") {
                      return formatUnitandMaxLength(value, "K");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"rentMax" + ToggleRentValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  validate={
                    ToggleRentValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue6]
                      : [minvaluezero, maxvalue8]
                  }
                  InputProps={ToggleRentValue === "Total" && unitK}
                  format={(value) => {
                    let formattedvalue;
                    if (ToggleRentValue === "Total") {
                      formattedvalue = unitFormatter(value, "K");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (ToggleRentValue === "Total") {
                      return formatUnitandMaxLength(value, "K");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                // rightcomp={
                //   <Switch
                //     value={ToggleRentValue}
                //     textL={intl.formatMessage({
                //       id: "search.common.renttotal",
                //     })}
                //     textR={intl.formatMessage({
                //       id: "search.common.rentavg",
                //     })}
                //     valleft="Total"
                //     valright="Avg"
                //     onChange={onChangeRentTotalAvg}
                //   />
                // }
                />
              </div>
            </Grid>
          </Grid>

          {/* yield */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="yieldMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "stock.yield",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="yieldMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue8]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>

          <Grid container>
            <Grid item xs={12}>
              <Field
                name="stockType"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.type",
                })}
                ranges={stocktypes}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                selectedData={selectedData && selectedData.stockType}
                setSelectedData={setSelectedData}
              />
            </Grid>
          </Grid>

          {/* board  */}
          <Grid container>
            <Grid item xs={12}>
              <Field
                name="boardStatus"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.board",
                })}
                ranges={boardstatus}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
              />
            </Grid>
          </Grid>

          {/* promote news  */}
          <Grid container>
            <Grid item xs={12}>
              <Field
                name="newsStatus"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.news",
                })}
                ranges={newsstatus}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
              />
            </Grid>
          </Grid>

          {/* possession */}
          <Grid container>
            <Grid item xs={12}>
              <Field
                name="possession"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.possession",
                })}
                ranges={possession}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
              />
            </Grid>
          </Grid>
        </DetailBoxSection>

        <DetailBoxSection
          text={intl.formatMessage({
            id: "search.header.miscellaneous",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
          customRight={
            <div className={classes.sectionTitleBtnContainer}>
              <PillButton
                onClick={() =>
                  singleSelectAllOptions(getpropertyconditionOptions(intl))
                }
              >
                {intl.formatMessage({
                  id: "search.form.all",
                })}
              </PillButton>
              <PillButton
                onClick={() =>
                  singledeSelectAllOptions(getpropertyconditionOptions(intl))
                }
              >
                {intl.formatMessage({
                  id: "search.form.none",
                })}
              </PillButton>
            </div>
          }
        >
          <div className={classes.paddingSection}>
            <Grid container spacing={1}>
              {getpropertyconditionOptions(intl).map((v, i) => {
                return (
                  <Grid key={i} item xs={4}>
                    <div>
                      <Field
                        name={v.value}
                        text={v.label}
                        component={PillCheckBox}
                      />
                    </div>
                  </Grid>
                );
              })}
            </Grid>
          </div>

          {/* create date */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="createDateMin"
                  type="date"
                  inputProps={{
                    placeholder: intl.formatMessage({
                      id: "search.form.common.min",
                    }),
                    // onFocus: dateonFocus,
                    // onBlur: () => dateonBlur("min"),
                  }}
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.createdate",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="createDateMax"
                  type="date"
                  inputProps={{
                    placeholder: intl.formatMessage({
                      id: "search.form.common.min",
                    }),
                    // onFocus: dateonFocus,
                    // onBlur: () => dateonBlur("min"),
                  }}
                  margin="normal"
                  className={classes.fieldMargin}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>

          {/* last update date */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="lastUpdateDateMin"
                  type="date"
                  inputProps={{
                    placeholder: intl.formatMessage({
                      id: "search.form.common.min",
                    }),
                  }}
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.lastupdatedate",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="lastUpdateDateMax"
                  type="date"
                  inputProps={{
                    placeholder: intl.formatMessage({
                      id: "search.form.common.min",
                    }),
                  }}
                  margin="normal"
                  className={classes.fieldMargin}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>

          {/* price change date */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="priceChangeDateMin"
                  type="date"
                  inputProps={{
                    placeholder: intl.formatMessage({
                      id: "search.form.common.min",
                    }),
                  }}
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.form.pricechange",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="priceChangeDateMax"
                  type="date"
                  inputProps={{
                    placeholder: intl.formatMessage({
                      id: "search.form.common.min",
                    }),
                  }}
                  margin="normal"
                  className={classes.fieldMargin}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>

          {/* tenancy expiry date */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="tenancyExpireDateMin"
                  type="date"
                  inputProps={{
                    placeholder: intl.formatMessage({
                      id: "search.form.common.min",
                    }),
                  }}
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "stock.tenancyperiod",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="tenancyExpireDateMax"
                  type="date"
                  inputProps={{
                    placeholder: intl.formatMessage({
                      id: "search.form.common.min",
                    }),
                  }}
                  margin="normal"
                  className={classes.fieldMargin}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>

          <Field
            name="unicornId"
            type="number"
            margin="normal"
            className={classes.fieldMargin}
            label={intl.formatMessage({
              id: "search.header.stockid",
            })}
            component={TextArrayInput}
            fullWidth
            validate={[numberValidate, minvaluezero, maxvalue5]}
            variant="outlined"
          />
        </DetailBoxSection>

        {enableConsolidLandSearch == "true" && <DetailBoxSection
          text={intl.formatMessage({
            id: "search.header.consolidate",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
        >
          <Grid container>
            <Grid item xs={6}>
              <Field
                name="isContact"
                label={intl.formatMessage(
                  { id: "search.form.owner" },
                )}
                component={ChipsCheckBox}
              />
            </Grid>
            <Grid item xs={6}>
              <Field
                name="isCurrent"
                label={intl.formatMessage(
                  { id: "stock.tenant" },
                  { status: intl.formatMessage({ id: "stock.currenttenancy" }) },
                )}
                component={ChipsCheckBox}
              />
            </Grid>
            <Grid item xs={6}>
              <Field
                name="isPrevious"
                label={intl.formatMessage(
                  { id: "stock.tenant" },
                  { status: intl.formatMessage({ id: "stock.previoustenancy" }) },
                )}
                component={ChipsCheckBox}
              />
            </Grid>
            <Grid item xs={6}>
              <Field
                name="isFormer"
                label={intl.formatMessage(
                  { id: "stock.tenant" },
                  { status: intl.formatMessage({ id: "stock.formertenancy" }) },
                )}
                component={ChipsCheckBox}
              />
            </Grid>
            <Grid item xs={6}>
              <Field
                name="isAdvance"
                label={intl.formatMessage(
                  { id: "stock.tenant" },
                  { status: intl.formatMessage({ id: "stock.advancetenancy" }) },
                )}
                component={ChipsCheckBox}
              />
            </Grid>

            <Grid item xs={12}>
              <Field
                name="person"
                margin="normal"
                label={intl.formatMessage({
                  id: "stock.contact",
                })}
                fullWidth
                component={ReactSelectCreatable}
                optionsdata={[]}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                selectedData={selectedData && selectedData.person}
                setSelectedData={setSelectedData}
              />
            </Grid>
            <Grid item xs={12}>
              <Field
                name="company"
                margin="normal"
                label={intl.formatMessage({
                  id: "search.form.company",
                })}
                fullWidth
                component={AutoCompleteSelect}
                optionsdata={companies}
                apiaction={listCompanies}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                selectedData={selectedData && selectedData.company}
                setSelectedData={setSelectedData}
                showZhEnLabel={true}
              />
            </Grid>
            <Grid item xs={12}>
              <Field
                name="phone"
                margin="normal"
                label={intl.formatMessage({
                  id: "building.phone",
                })}
                fullWidth
                component={ReactSelectCreatable}
                optionsdata={[]}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                selectedData={selectedData && selectedData.phone}
                setSelectedData={setSelectedData}
              />
            </Grid>
            <Grid item xs={12}>
              <Field
                name="email"
                margin="normal"
                label={intl.formatMessage({
                  id: "search.form.email",
                })}
                fullWidth
                component={ReactSelectCreatable}
                optionsdata={[]}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                selectedData={selectedData && selectedData.email}
                setSelectedData={setSelectedData}
              />
            </Grid>
          </Grid>
        </DetailBoxSection>}

        {props.children}
      </Collapse>
    </div>
  );
}

const mapStateToProps = (state) => ({
  buildings: state.building.buildings || [],
  fieldhistory: state.stocklist.fieldhistory || [],
  queryvariables: state.stocklist.queryvariables
    ? state.stocklist.queryvariables
    : {},
  listing: state.building.listing ? state.building.listing : false,
  listed: state.building.listed ? state.building.listed : false,
  streets: state.street.streets ? state.street.streets : [],
  districts: state.district.districts ? state.district.districts : [],
  companies: state.stocklist.companies ? state.stocklist.companies : [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    listBuildings: (graphqlvariable) => {
      dispatch(listBuildings(graphqlvariable));
    },
    listStreets: (graphqlvariable) => dispatch(listStreets(graphqlvariable)),
    listCompanies: (graphqlvariable) => dispatch(listCompanies(graphqlvariable)),
    changeForm: (field, val) => dispatch(change("searchForm", field, val)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(FieldSection)));
