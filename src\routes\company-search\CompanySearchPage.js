import React, { useEffect, useMemo } from "react";
import PropTypes from "prop-types";
import { FormattedMessage, injectIntl } from "react-intl";
import { connect } from "react-redux";
import { Box } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import history from "@/core/history";

import Layout from "@/components/Layout/Layout";
import CompanySearchForm from "@/components/CompanySearchForm";
import SortingBar from "@/components/common/SortingBar";
import { getSorters } from "@/constants/company";
import { listCompanies, updateSorter } from "@/actions/company";
import CompanyList from "@/components/CompanyList";
import BottomButtons from "@/components/common/BottomButtons";

const useStyles = makeStyles({
  sortingBar: {
    // position: "-webkit-sticky" /* Safari */,
    // position: "sticky",
    // top: 129,
    position: "fixed",
    width: "100%",
    zIndex: 1,
  },
});

function CompanySearchPage({
  queryFromUri,
  sorter,
  queryvariables,
  listedCompanies,
  updateSorter,
  queryCompanies,
  intl,
}) {
  const classes = useStyles();

  useEffect(() => {
    if (listedCompanies) {
      queryCompanies(queryvariables.offset > 0);
    } else {
      queryCompanies();
    }
  }, [sorter, queryvariables]);

  const sorterOptions = useMemo(() => getSorters(intl), []);
  return (
    <Layout
      header={<FormattedMessage id="home.companySearch" />}
      hideMoreAction
      isSticky
    >
      <Box position="relative">
        <CompanySearchForm />

        <SortingBar
          className={classes.sortingBar}
          options={sorterOptions}
          queryvariables={{ sorter }}
          updateQuery={updateSorter}
        />

        <CompanyList />

        <BottomButtons  buttons={[{
          label: intl.formatMessage({ id: "company.list.apply" }),
          onClick: () => {
            history.push(`/applyCompanySearch`)
          },
        }]}/>
      </Box>
    </Layout>
  );
}

CompanySearchPage.propTypes = {
  queryFromUri: PropTypes.object.isRequired,

  sorter: PropTypes.array.isRequired,
  queryvariables: PropTypes.object.isRequired,
  listedCompanies: PropTypes.bool.isRequired,
  updateSorter: PropTypes.func.isRequired,
  queryCompanies: PropTypes.func.isRequired,

  intl: PropTypes.object.isRequired,
};

const mapDispatchToProps = (dispatch) => ({
  updateSorter: ({ sorter }) => dispatch(updateSorter(sorter)),
  // updateQueryVariables: (variables) => ,
  queryCompanies: (fetchMore = false) => dispatch(listCompanies(fetchMore)),
});

const mapStateToProps = (state) => ({
  sorter: state.company.sorter,
  queryvariables: state.company.queryvariables,
  listedCompanies: state.company.listedCompanies,
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(CompanySearchPage));
