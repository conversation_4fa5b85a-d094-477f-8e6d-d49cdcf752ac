/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import Layout from "../../components/Layout";
import Tips from "./Tips";
import { FormattedMessage } from "react-intl";

const title = "Help";

async function action() {
  return {
    chunks: ["tips"],
    title,
    component: (
      <Layout
        title="tips"
        header={<FormattedMessage id="home.help" />}
        hideSearchIcon={true}
      >
        <Tips />
      </Layout>
    )
  };
}

export default action;
