
import React from "react";
import {
  Box,
  IconButton,
  makeStyles,
  Typography,
} from "@material-ui/core";
import ArrowBackIosIcon from "@material-ui/icons/ArrowBackIos";

const useStyles = makeStyles((theme) => ({
  root: {

  },
  header: {
    display: "flex",
    alignItems: "center",
    position: "relative",
    zIndex: 2,
    // padding: "16px 12px 36px 12px",
    padding: "16px 12px",
    height: "25px",
  },
  backButton: {
    color: "white",
    height: "25px",
  },
  headerTitle: {
    position: "absolute",
    transform: "translate(50%, 0)",
    letterSpacing: "0.4px",
    right: "50%",
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",

    "margin": "0 auto",
    "height": "25px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "20px",
    "lineHeight": "25px",
    "textAlign": "center",
    "color": "#FFFFFF",
    "flex": "none",
    "flexGrow": 0,
  },
}));

function Header({
  personalType,
}) {
  const classes = useStyles();

  const handleBack = React.useCallback(() => {
    window.history.back();
  }, []);

  {/* 头部 */ }
  return (
    <Box style={{ backgroundColor: "#392E06" }}>
      <Box className={classes.header}>
        <IconButton className={classes.backButton} size={"small"} onClick={handleBack}>
          <ArrowBackIosIcon fontSize={"small"} />
        </IconButton>

        <Typography variant="subtitle1" className={classes.headerTitle}>
          Mita Club 業績指標
        </Typography>
      </Box>

      {!personalType ? null : <Box style={{ position: "relative", backgroundColor: "inherit" }}>
        <div style={{
          width: "100%",
          height: "101px",
          backgroundColor: "inherit",
          position: "absolute",
          zIndex: 0,
          top: 0,
        }} />
        <div style={{ height: "8px", backgroundColor: "inherit" }} />
      </Box>}
    </Box>
  );
}

export default Header;
