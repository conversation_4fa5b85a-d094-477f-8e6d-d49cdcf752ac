import React from "react";
import PropTypes from "prop-types";
import { createStyles, withStyles } from "@material-ui/styles";
import { injectIntl } from "react-intl";
import squareSvg from "../../../files/icons/transparent-square.svg";
import MediumProperties from "./MediumProperties";
import VideoPopup from "./VideoPopup";
import { sbu } from "../../../config";
import MediaHandleResultDialog from "./MediaHandleResultDialog";

const styles = createStyles({
  image: {
    width: "100%",
    cursor: "pointer",
    backgroundSize: "cover",
    backgroundPosition: "center",
    backgroundRepeat: "no-repeat",
  },
  thumbnailContainer: {
    overflow: "hidden",
    position: "relative",
  },
  videoContainer: {
    width: "100%",
    position: "relative",
    paddingBottom: "100%", // 4:3 aspect ratio (can be adjusted to match your images)
    overflow: "hidden",
  },
  videoElement: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    objectFit: "cover",
    cursor: "pointer",
  }
});

const mediaTypeMapping = Object.freeze({
  video: "video",
  kol_video: "kolVideo",
});
class VideoThumbnail extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    video: PropTypes.object,
    deletable: PropTypes.bool,
    handleOpenDeleteMediaDialog: PropTypes.func,
    renderCustomButton: PropTypes.func,
    handleOpenPopup: PropTypes.func
  };

  constructor(props) {
    super(props);
    this.state = {
      popupOpen: false,
      thumbnailError: false,
    };
  }

  handlePopupOpen = () => {
    if (this.props.handleOpenPopup) {
      this.props.handleOpenPopup();
    }
    this.setState({ popupOpen: true });
  };

  handlePopupClose = () => {
    this.setState({ popupOpen: false });
  };

  handleThumbnailError = () => {
    this.setState({ thumbnailError: true });
  };

  render() {
    const {
      classes,
      video,
      deletable = false,
      handleOpenDeleteMediaDialog,
      intl,
      renderCustomButton,
    } = this.props;
    const {
      popupOpen,
      thumbnailError,
    } = this.state;

    return (
      <>
        <div className={classes.thumbnailContainer}>
          {video.youtubeId || video.youtubeMrId ? (
            <img
              className={classes.image}
              alt=""
              role="presentation"
              src={squareSvg}
              onClick={this.handlePopupOpen}
              style={{
                backgroundImage: `url(https://img.youtube.com/vi/${video.youtubeId || video.youtubeMrId}/0.jpg)`,
              }}
            />
          ) : (
            thumbnailError ? (
              <div className={classes.videoContainer}>
                <video className={classes.videoElement} preload="metadata" controlsList="nofullscreen" onClick={this.handlePopupOpen}>
                  <source src={`${video.mediumRoot}/mr_video.mp4#t=0.001`} type="video/mp4">
                  </source>
                </video>
              </div>
            ) : (
              <>
                <img
                  className={classes.image}
                  alt=""
                  role="presentation"
                  src={squareSvg}
                  onClick={this.handlePopupOpen}
                  onError={this.handleThumbnailError}
                  style={{
                    ...(video.thumbnail ? { backgroundImage: `url(${video.thumbnail})` } : { backgroundColor: '#AAA' }),
                  }}
                />
                {video.thumbnail && (
                  <img 
                    src={video.thumbnail} 
                    alt=""
                    style={{ display: 'none' }} 
                    onError={this.handleThumbnailError}
                  />
                )}
              </>
            )
          )}
          <MediumProperties medium={video} />
        </div>
        {popupOpen && <VideoPopup
          video={video}
          handlePopupClose={this.handlePopupClose}
          deletable={deletable}
          handleOpenDeleteMediaDialog={handleOpenDeleteMediaDialog}
          renderCustomButton={renderCustomButton}
        />}

        {video?.approval === "pending" &&
          <MediaHandleResultDialog
            media={video}
            mediaType={mediaTypeMapping[video.type]}
          />}
      </>
    );
  }
}
export default withStyles(styles)(injectIntl(VideoThumbnail));
