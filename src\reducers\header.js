import {
  UPDATE_HEADER,
  UPDATE_HEADER_TITLE,
  UPDATE_HEADER_SUBTITLE
} from "../constants/header";

import _ from "lodash";

const initialState = {
  title: 'MSearch 搵盤易',
  subTitle: '',
};

export default function header(state = initialState, action) {
  switch (action.type) {
    case UPDATE_HEADER:
      return {
        ...state,
        title: _.get(action, "payload.title", "") || "MSearch 搵盤易",
        subTitle: _.get(action, "payload.subTitle", "")
      };
    case UPDATE_HEADER_TITLE:
      return {
        ...state,
        title: _.get(action, "payload", "") || "MSearch 搵盤易",
      };
    case UPDATE_HEADER_SUBTITLE:
      return {
        ...state,
        subTitle: _.get(action, "payload", ""),
      };
    default:
      return state;
  }
}
