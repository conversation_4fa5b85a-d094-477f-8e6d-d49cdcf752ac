import React from "react";
import _ from "lodash";
import moment from "moment";
import history from "../core/history";
import querystring from "querystring";
import { sbu, mode } from "../config";
import fetch from "node-fetch";

function number_format(number, decimals, dec_point, thousands_sep) {
  // Strip all characters but numerical ones.
  number = (number + "").replace(/[^0-9+\-Ee.]/g, "");
  var n = !isFinite(+number) ? 0 : +number,
    prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
    sep = typeof thousands_sep === "undefined" ? "," : thousands_sep,
    dec = typeof dec_point === "undefined" ? "." : dec_point,
    s = "",
    toFixedFix = function (n, prec) {
      var k = Math.pow(10, prec);
      return "" + Math.round(n * k) / k;
    };
  // Fix for IE parseFloat(0.55).toFixed(0) = 0;
  s = (prec ? toFixedFix(n, prec) : "" + Math.round(n)).split(".");
  if (s[0].length > 3) {
    s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
  }
  if ((s[1] || "").length < prec) {
    s[1] = s[1] || "";
    s[1] += new Array(prec - s[1].length + 1).join("0");
  }
  return s.join(dec);
}

export function convertCurrency(num, lang = "english", { decimals = 2} = {}) {
  if (isNaN(parseFloat(num))) return num;
  num = parseFloat(num);
  let re = "";

  if (lang === "english") {
    let bi = 0;

    let mi = (num - bi * 1000000000) / 1000000;
    if (mi < 1) mi = 0;
    let ki = (num - mi * 1000000) / 1000;

    if (mi >= 1) re += number_format(parseFloat(mi), decimals) + "M";
    else if (ki >= 1) re += number_format(parseFloat(ki), decimals) + "K";
    else re = num.toString();
  } else {
    let hm;
    if (num >= 100000000) hm = Math.floor(num / 100000000);
    else hm = 0;

    let tk = (num - hm * 100000000) / 10000;
    tk = Math.round(tk);

    if (tk >= 10000) {
      tk -= 10000;
      hm++;
    }

    if (hm >= 1) re += number_format(hm) + "億";
    if (tk >= 1) re += number_format(tk) + "萬";
  }
  return re;
}

export function numberComma(num, decimals = 0) {
  if (isNaN(parseFloat(num))) return num;
  return number_format(parseFloat(num), decimals);
}

// ref: https://stackoverflow.com/questions/13627308/add-st-nd-rd-and-th-ordinal-suffix-to-a-number/13627586
export function ordinalSuffix(i) {
  let j = i % 10,
    k = i % 100;
  if (j == 1 && k != 11) {
    return i + "st";
  }
  if (j == 2 && k != 12) {
    return i + "nd";
  }
  if (j == 3 && k != 13) {
    return i + "rd";
  }
  return i + "th";
}

export function convertNewlineToBr(str) {
  if(str.indexOf('<a href=') > -1) {
    return (
      <div dangerouslySetInnerHTML= {{__html: str}}></div>
    )
  }
  let bits = str.split(/(?:\r\n|\r|\n)/g);

  return bits.map((v, i) => (
    <React.Fragment key={i}>
      {i > 0 && <br />}
      {v}
    </React.Fragment>
  ));
}

export function getSearchQueryString(
  query,
  selectedData,
  isAdvanced = true,
  anchorTo = "",
) {
  return (
    "param=" +
    encodeURIComponent(JSON.stringify(query)) +
    "&selectedData=" +
    encodeURIComponent(JSON.stringify(selectedData)) +
    "&isAdvanced=" +
    (isAdvanced ? "true" : "false") +
    "&anchorTo=" +
    anchorTo
  );
}

export const dateFormatter = inputDateString => {
  if(!inputDateString) return '---'
  if (/^\d{2}\/\d{2}\/\d{4}$/.test(inputDateString)) return inputDateString
  const output = moment(inputDateString).format('DD/MM/yyyy');
  if (output === "01/01/1970") return "";
  return output;
}

export function getSearchResultURL(
  query,
  selectedData,
  isAdvanced = true,
  anchorTo = "",
) {
  return (
    "/" +
    (isAdvanced ? "detailsearch" : "search") +
    "?" +
    getSearchQueryString(query, selectedData, isAdvanced, anchorTo)
  );
}

export function getTxResultURL(query, selectedData) {
  return (
    "/transaction" +
    "?param=" +
    encodeURIComponent(JSON.stringify(query)) +
    "&selectedData=" +
    encodeURIComponent(JSON.stringify(selectedData))
  );
}

let defaultStatus;
let defaultConsolidateType;
if (sbu === "COMM") {
  defaultStatus = ["Lease", "Sale", "Sale+Lease", "Surrend", "Tenanted"];
  defaultConsolidateType = {
    isVendor: true,
    isCurrent: true,
    isPrevious: true,
  };
} else if (sbu === "IND") {
  defaultStatus = ["Sale", "Lease", "Tenanted", "Sale+Lease"];
  defaultConsolidateType = {
    isVendor: true,
    isCurrent: true,
    isFormer: true,
  };
} else if (sbu === "SHOPS") {
  defaultStatus = ["Sale", "Lease", "Sale+Lease"];
  defaultConsolidateType = {
    isVendor: true,
    isCurrent: true,
    isPrevious: true,
    isFormer: true,
    isAdvance: true,
  };
}

export const consolidateType = defaultConsolidateType;

export const defaultTxQuery = {
  limit: 50,
  offset: 0,
  sorter: [{ field: "date", order: "DESC" }],
};

export const defaultSearchQuery = {
  limit: 50,
  offset: 0,
  status: defaultStatus,
  ...defaultConsolidateType,
  // isMarketableOrNew: true,
};

export function getDefaultSearchResultURL() {
  const selectedData = {};
  return getSearchResultURL(defaultSearchQuery, selectedData, true);
}

export function getDefaultTxResultURL() {
  const selectedData = {};
  return getTxResultURL(defaultTxQuery, selectedData);
}

export function goToSearchResult(query, selectedData, isAdvanced, anchorTo) {
  history.push(getSearchResultURL(query, selectedData, isAdvanced, anchorTo));
}

export function goToTxResult(query, selectedData) {
  history.push(getTxResultURL(query, selectedData));
}

export function goToDefaultSearchResult() {
  history.push(getDefaultSearchResultURL());
}

export function checkAndParseUrlParam(str) {
  let parsedjson = null;
  if (typeof str !== "undefined") {
    try {
      parsedjson = JSON.parse(str);
      if (typeof parsedjson !== "object") return false;
    } catch (e) {
      return false;
    }
  }
  return parsedjson;
}

export function getTransactionSearchResultURL(query, selectedData) {
  return (
    "/transaction" +
    "?param=" +
    encodeURIComponent(JSON.stringify(query)) +
    "&selectedData=" +
    encodeURIComponent(JSON.stringify(selectedData))
  );
}

export function getDefaultTransactionSearchResultURL() {
  const query = {
    limit: 25,
    offset: 0,
  };
  const selectedData = {};
  return getTransactionSearchResultURL(query, selectedData);
}

export function goToTransactionSearchResult(query, selectedData) {
  history.push(getTransactionSearchResultURL(query, selectedData));
}

export function goToDefaultTransactionSearchResult() {
  history.push(getDefaultTransactionSearchResultURL());
}

export function getLangKey({ locale }, fieldName = "name") {
  return locale === "zh" ? fieldName + "Zh" : fieldName + "En";
}

export function getReverseLangKey({ locale }, fieldName = "name") {
  return locale === "zh" ? fieldName + "En" : fieldName + "Zh";
}

export function goToProposalList() {
  history.push("/proposal/list");
}

export function getProposalTenancyDesc(record, lang) {
  let {
    isIncludeGovernmentRent,
    isIncludeRates,
    isIncludeAirConditioning,
    isIncludeManagementFee,
  } = record;
  if (sbu === "IND") {
    isIncludeGovernmentRent =
      (record.inclusive && record.inclusive.governmentRent) || false;
    isIncludeRates = (record.inclusive && record.inclusive.rates) || false;
    isIncludeAirConditioning =
      (record.inclusive && record.inclusive.airConditioningFee) || false;
    isIncludeManagementFee =
      (record.inclusive && record.inclusive.managementFee) || false;
  }

  if (lang === "zh") {
    if (
      isIncludeGovernmentRent &&
      isIncludeRates &&
      isIncludeAirConditioning &&
      isIncludeManagementFee
    )
      return "包括差餉，地租，冷氣費，管理費";

    let str = "不包括";
    if (!isIncludeGovernmentRent) str += "差餉，";
    if (!isIncludeRates) str += "地租，";
    if (!isIncludeAirConditioning) str += "冷氣費，";
    if (!isIncludeManagementFee) str += "管理費，";
    return str.substring(0, str.length - 1);
  } else {
    if (
      isIncludeGovernmentRent &&
      isIncludeRates &&
      isIncludeAirConditioning &&
      isIncludeManagementFee
    )
      return "Include G Rent, rates, A/C Fee, Management Fee";

    let str = "Not include ";
    if (!isIncludeGovernmentRent) str += "G Rent, ";
    if (!isIncludeRates) str += "rates, ";
    if (!isIncludeAirConditioning) str += "A/C Fee, ";
    if (!isIncludeManagementFee) str += "Management Fee, ";
    return str.substring(0, str.length - 2);
  }
}

export function staticMapUrl(lat, lng) {
  const icon =
    // "https://www.midlandici.com.hk/ics/property/img/pin_building.png";
    "https://file.midlandici.com/msearch/sales-kit/pin.png";
  const key = "AIzaSyClCwgxVpAgIwyhCo4H9dg64Bby2YzCE4w";
  const initialUrl = "https://maps.googleapis.com/maps/api/staticmap";
  const params = {
    center: `${lat},${lng}`,
    size: "640x358",
    scale: 2,
    markers: `icon:${icon}|scale:4|${lat},${lng}`,
    key: key,
    style: [
      "feature:all|element:labels.text.fill|color:0x736c68",
      "feature:landscape.man_made|element:geometry.fill|color:0xebebeb",
      "feature:landscape.natural|element:all|visibility:on|color:0xd4e4d3",
      "feature:landscape.natural|element:labels|color:0x95b7ab|weight:0.54",
      "feature:poi|element:geometry.fill|visibility:on|color:0xe1e0e0",
      "feature:poi.park|element:geometry.fill|color:0xd4e4d3",
      "feature:road|element:geometry.fill|color:0xf5f5f5",
      "feature:road|element:geometry.stroke|color:0xe7e6e5|gamma:0.65|lightness:0",
      "feature:transit|element:labels.text|visibility:off",
      "feature:water|element:all|color:0xa2c6db",
      "feature:water|element:labels|color:0x87aec5",
    ],
  };
  const parsedParams = querystring.stringify(params);
  return `${initialUrl}?${parsedParams}`;
}

export function paresFloor(floor, { locale }) {
  let f = floor.trim();
  while (f.startsWith("0")) f = f.substring(1); // remove leading zeros
  if (parseInt(f).toString() !== f) return floor;

  if (locale === "zh") return floor + "樓";
  else return "Floor " + floor;
}

// for COMM, IND
export function paresFloorUnit(floor, unit, intl) {
  const f = paresFloor(floor, intl);

  if (intl.locale === "zh") return f + ", " + (unit || "---") + "室";
  else return f + ", Unit " + (unit || "---");
}

// for SHOPS
export function paresFloorShopNo(floor, floorInChinese, unit, intl) {
  if (intl.locale === "zh")
    return (
      floorInChinese +
      ", " +
      (unit || "---") +
      intl.formatMessage({ id: "stock.numberofshop" })
    );
  else
    return (
      floor +
      ", " +
      intl.formatMessage({ id: "stock.numberofshop" }) +
      " " +
      (unit || "---")
    );
}

export function getDisplayStockId(stockId) {
  let prefix = "";
  let totalDigit = 8;
  if (sbu === "COMM") {
    prefix = "C";
    totalDigit = 7;
  } else if (sbu === "IND") {
    prefix = "I";
    totalDigit = 8;
  } else if (sbu === "SHOPS") {
    prefix = "S";
    totalDigit = 6;
  }

  return stockId && stockId.toString().length <= 6
    ? `${prefix}${_.padStart(stockId, totalDigit, "0")}`
    : "";
}

export function parsePeriod(min, max, intl) {
  return min && max
    ? min + " - " + max
    : min
    ? intl.formatMessage({ id: "common.from" }) + " " + min
    : max
    ? intl.formatMessage({ id: "common.to" }) + " " + max
    : "";
}

// dedup: whether show both en and zh or not when en = zh
export function parseNameWithBr(en, zh, intl, dedup = false) {
  return en && zh && (!dedup || en === zh) ? (
    intl.locale === "zh" ? (
      <>
        {zh}
        <br />
        {en}
      </>
    ) : (
      <>
        {en}
        <br />
        {zh}
      </>
    )
  ) : (
    en || zh || "---"
  );
}

export function parseNameWithDash(en, zh, intl, dedup = false) {
  return en && zh && (!dedup || en === zh)
    ? intl.locale === "zh"
      ? `${zh} - ${en}`
      : `${en} - ${zh}`
    : en || zh;
}

export function getHandsMapping(handsInfo) {
  let map = [];
  handsInfo &&
    handsInfo.forEach((v, i) => {
      if (i === 0 || handsInfo[i - 1].isDeal) {
        map.push([v.handsId]);
      } else {
        map[map.length - 1].push(v.handsId);
      }
    });
  if (map.length === 0) map.push([]);
  return map;
}

export const checkExpiredDate = (start, end) => {
  const now = moment();
  const startDay = moment(start);
  const endDay = moment(end).add(1, "days");
  if (!start) return now.isBefore(endDay);
  if (!end) return now.isAfter(startDay);
  return now.isAfter(startDay) && now.isBefore(endDay);
};

export const matchPreviousName = (name, previousNames) => {
  const regex = new RegExp(name && name.toLowerCase());
  for (let i = 0; i < previousNames.length; i++) {
    const nameEN = previousNames[i]?.nameEn ? previousNames[i].nameEn.toLowerCase() : "";
    const nameZh = previousNames[i]?.nameZh ? previousNames[i].nameZh.toLowerCase() : "";
    if (regex.test(nameEN) || regex.test(nameZh)) {
      return previousNames[i];
    }
  }
  return null;
};

export const generateGovMapImage = async (centerLon, centerLat, width = 400, height = 400, zoom = 19, markerScale = 0.3, language = 'tc') => {
    // 动态导入OpenLayers模块
  const [
    { default: Map },
    { default: View },
    { default: TileLayer },
    { default: VectorLayer },
    { default: XYZ },
    { default: VectorSource },
    { default: Feature },
    { default: Point },
    { fromLonLat },
    { Style, Icon }
  ] = await Promise.all([
    import('ol/Map'),
    import('ol/View'),
    import('ol/layer/Tile'),
    import('ol/layer/Vector'),
    import('ol/source/XYZ'),
    import('ol/source/Vector'),
    import('ol/Feature'),
    import('ol/geom/Point'),
    import('ol/proj'),
    import('ol/style')
  ]);

  return new Promise((resolve, reject) => {
    const basemapAPI = 'https://mapapi.geodata.gov.hk/gs/api/v1.0.0/xyz/basemap/wgs84/{z}/{x}/{y}.png';
    const labelAPI = `https://mapapi.geodata.gov.hk/gs/api/v1.0.0/xyz/label/hk/${language}/wgs84/{z}/{x}/{y}.png`;

    // 创建临时容器
    const mapContainer = document.createElement('div');
    mapContainer.style.width = `${width}px`;
    mapContainer.style.height = `${height}px`;
    mapContainer.style.visibility = 'hidden';
    document.body.appendChild(mapContainer);

    const map = new Map({
      controls: [],
      interactions: [],
      layers: [
        new TileLayer({
          source: new XYZ({
            url: basemapAPI,
            crossOrigin: 'anonymous'
          })
        }),
        new TileLayer({
          source: new XYZ({
            url: labelAPI,
            crossOrigin: 'anonymous'
          })
        }),
        new VectorLayer({
          source: new VectorSource({
            features: [new Feature({
              geometry: new Point(fromLonLat([centerLon, centerLat]))
            })]
          }),
          style: new Style({
            image: new Icon({
              src: "asset/getPinImg",
              scale: markerScale,
              anchor: [0.5, 1],
              crossOrigin: 'anonymous'
            })
          })
        }),
      ],
      target: mapContainer,
      view: new View({
        center: fromLonLat([centerLon, centerLat]),
        zoom: zoom,
        minZoom: 10,
        maxZoom: 20
      })
    });

    // 修改渲染完成的处理逻辑
    map.once('rendercomplete', () => {
      try {
        const canvases = mapContainer.querySelectorAll('canvas');

        const exportCanvas = document.createElement('canvas');
        exportCanvas.width = canvases[0].width;
        exportCanvas.height = canvases[0].height;
        const exportContext = exportCanvas.getContext('2d');

        canvases.forEach(canvas => {
          exportContext.drawImage(canvas, 0, 0);
        });
        // 确保使用正确的 MIME 类型和质量参数
        exportCanvas.toBlob(
          (blob) => {
            if (blob) {
              const url = URL.createObjectURL(blob);
              // 清理DOM
              map.setTarget(null);
              document.body.removeChild(mapContainer);
              resolve(url);
            } else {
              reject(new Error('Failed to create blob from canvas'));
            }
          },
          'image/png',
          1.0  // 设置质量参数为最高质量
        );
      } catch (error) {
        // 清理DOM
        map.setTarget(null);
        document.body.removeChild(mapContainer);
        reject(error);
      }
    });

    // 添加错误处理
    map.once('error', (error) => {
      // 清理DOM
      map.setTarget(null);
      document.body.removeChild(mapContainer);
      reject(error);
    });
  });
};

export const getCookieDomain = (domain) => {
  return mode === "production" ?
    (domain || "").replace(/(msi|msc|mss|)(-dev|-uat)?/g, "") :
    "";
}

export const compareDate = (startDate, endDate) => {
  const today = new Date();
  const start = new Date(startDate);
  const end = new Date(endDate);
  end.setDate(end.getDate() + 1);
  if (!startDate) return today <= end;
  if (!endDate) return start <= today;
  return start <= today && today <= end;
};
