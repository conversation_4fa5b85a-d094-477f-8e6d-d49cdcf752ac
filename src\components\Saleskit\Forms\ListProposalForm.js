import _, { set } from "lodash";
import React, { useState, useEffect } from "react";
import { change, getFormValues, reduxForm } from "redux-form";
import { injectIntl, FormattedMessage } from "react-intl";
import { makeStyles } from "@material-ui/core/styles";
import { FormatLineSpacing } from "@material-ui/icons";

import SectionBox from "../SectionBox";
import ProposalGeneralSection from "../ProposalGeneralSection";
import { connect } from "react-redux";
import { getDefaultRemarks } from "../helpers";
import StockListWrapper from "../DragDrop/StockListWrapper";
import CreateProposalDialog from "../../common/CreateProposalDialog";
import { addActivityLog } from "@/actions/log";

const useStyles = makeStyles(() => ({
  stockList: {
    background: "transparent",
  },
}));

let ListProposalForm = ({
  handleSubmit,
  initialize,
  initialValues,
  changeFieldValue,
  addActivityLog,
  FormComponent,
  formState,
  createDialogOpen,
  closeDialog,
  mode,
  intl,
}) => {
  const classes = useStyles();
  const [initialized, setInitialized] = useState(false);
  const [ordering, setOrdering] = useState(false);

  useEffect(() => {
    if (!_.isEmpty(initialValues)) {
      addActivityLog("proposal", "read", initialValues);
      initialize(initialValues);
      setInitialized(true);
    }
  }, [mode]);

  const handleTypeChange = (v) => {
    const { value } = v;

    if (!_.isEmpty(_.get(formState, "stocks"))) {
      const enableRent = value === "Lease" || value === "SaleAndLease";
      const enablePrice = value === "Sale" || value === "SaleAndLease";
      _.forEach(Object.keys(_.get(formState, "stocks")), (stockId) => {
        changeFieldValue(
          `stocks.${stockId}.stock.avgRent[0].isShow`,
          enableRent,
        );
        changeFieldValue(
          `stocks.${stockId}.stock.totalRent[0].isShow`,
          enableRent,
        );
        changeFieldValue(
          `stocks.${stockId}.stock.avgPrice[0].isShow`,
          enablePrice,
        );
        changeFieldValue(
          `stocks.${stockId}.stock.totalPrice[0].isShow`,
          enablePrice,
        );
        changeFieldValue(
          `stocks.${stockId}.stock.possession[0].isShow`,
          !(value === 'Lease'),
        );
        if (mode === "indv") {
          changeFieldValue(
            `stocks.${stockId}.stock.termRemarks`,
            getDefaultRemarks(value),
          );
        }
      });
      changeFieldValue("general.showPossession[0].isShow", !(value === 'Lease'));
      if (mode === "list") {
        changeFieldValue("general.termRemarks", getDefaultRemarks(value));
      }
    }
  };

  const handleOrderChange = (srcIdx, destIdx) => {
    const tmpOrderList = _.get(formState, "order", []);
    if (tmpOrderList.length === 0) return;
    const tmp = _.get(tmpOrderList, srcIdx);

    if (srcIdx > destIdx) {
      tmpOrderList.splice(destIdx, 0, tmp);
      tmpOrderList.splice(srcIdx + 1, 1);
    } else {
      tmpOrderList.splice(destIdx + 1, 0, tmp);
      tmpOrderList.splice(srcIdx, 1);
    }
    changeFieldValue("order", tmpOrderList);
  };

  const stockListActions = ordering
    ? [
        {
          label: <FormattedMessage id="proposal.form.orderingFinish" />,
          action: () => setOrdering(false),
          // icon: <FormatLineSpacing />,
        },
      ]
    : [
        {
          label: <FormattedMessage id="proposal.form.ordering" />,
          action: () => setOrdering(true),
          icon: <FormatLineSpacing />,
        },
      ];

  return initialized ? (
    <form onSubmit={handleSubmit}>
      <SectionBox
        header={intl.formatMessage({ id: "proposal.section.ppOptions" })}
      >
        <ProposalGeneralSection
          changeFieldValue={changeFieldValue}
          onTypeChange={handleTypeChange}
          isListProposal={mode === "list"}
        />
      </SectionBox>
      <SectionBox
        header={intl.formatMessage({ id: "proposal.section.stockList" })}
        className={classes.stockList}
        actions={stockListActions}
      >
        <StockListWrapper
          FormComponent={FormComponent}
          changeFieldValue={changeFieldValue}
          ordering={ordering}
          handleOrderChange={handleOrderChange}
          formState={formState}
          initialValues={initialValues}
          mode={mode}
        />
      </SectionBox>
      <CreateProposalDialog
        open={createDialogOpen && !ordering}
        close={closeDialog}
      />
    </form>
  ) : null;
};

ListProposalForm = reduxForm({
  form: "proposal",
})(ListProposalForm);

const mapStateToProps = (state) => ({
  formState: getFormValues("proposal")(state),
});

const mapDispatchToProps = (dispatch) => ({
  changeFieldValue: (...args) => dispatch(change("proposal", ...args)),
  addActivityLog: (...args) => dispatch(addActivityLog(...args)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(ListProposalForm));
