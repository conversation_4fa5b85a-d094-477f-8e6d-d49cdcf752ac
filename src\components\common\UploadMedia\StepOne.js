import React from "react";
import PropTypes from "prop-types";
import { Field } from "redux-form";
import { connect } from "react-redux";
import { required, imageFile, videoFile, pdfFile } from "../../../core/formValidators";
import { createStyles, withStyles } from "@material-ui/styles";
import { injectIntl } from "react-intl";
import Switch from "../Switch";
import MediaTypeSelect from "./MediaTypeSelect";
import DialogButton from "../DialogButton";
import FieldFileInput from "../FieldFileInput";
import Grid from "@material-ui/core/Grid";
// @ts-ignore
import moment from "moment";
import squareSvg from "../../../files/icons/transparent-square.svg";
import DarkBgCross from "../../common/DarkBgCross";
import ExifOrientationFix from "../../common/ExifOrientationFix";
import SelectField from "../SelectField";
import CustomAutocompleteField from "../CustomAutocompleteField";
import TextInput from "../TextInput";
import FieldBgmSelector from "../FieldBgmSelector";
import { media, sbu } from "../../../config";
import { PERMISSIONS } from "@/constants/auth";
import Document from "@/components/common/MediaMain/Document";
import Snackbar from "@material-ui/core/Snackbar";
import Alert from "@material-ui/lab/Alert";

import Dialog from "@material-ui/core/Dialog";
import DialogContent from "@material-ui/core/DialogContent";
import DialogActions from "@material-ui/core/DialogActions";
import Button from "@material-ui/core/Button";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import Checkbox from "@material-ui/core/Checkbox";
import IconButton from "@material-ui/core/IconButton";
import PlayArrowIcon from "@material-ui/icons/PlayArrow";
import PauseIcon from "@material-ui/icons/Pause";
import QueueIcon from '@material-ui/icons/Queue';
import AddIcon from '@material-ui/icons/Add';
import axios from "axios";
import _ from "lodash";
const { EDIT_MEDIA_OWNER } = PERMISSIONS;
const MAX_FILES = 20;

const styles = createStyles((theme) => ({
  root: {
    // padding: "3vh 0",
  },
  font18: {
    fontSize: "1.125em",
  },
  selectRow: {
    // marginBottom: "3vh",
  },
  switchRow: {
    lineHeight: "25px",
    display: "flex",
    justifyContent: "space-between",
    marginBottom: "2vh",
  },
  mediaTypeSelect: {
    marginBottom: "2vh",
  },
  uploadToSwitch: {
    display: "inline-block",
  },
  selectFileRow: {
    display: "flex",
    marginBottom: "1vh",
    // textAlign: "center",
    "& > *": {
      flex: 1,
    },
    "& > *:not(:last-child)": {
      marginRight: "3vw",
    },
  },
  image: {
    width: "100%",
    backgroundSize: "cover",
    backgroundPosition: "center",
    backgroundRepeat: "no-repeat",
    imageOrientation: "none", // Disable chrome's orientation fix. We use our own
  },
  video: {
    width: "100%",
    height: "100%",
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translateX(-50%) translateY(-50%)",
    objectFit: "cover",
  },
  previewBox: {
    maxHeight: "30vh",
    color: "#000",
    borderRadius: 4,
    padding: 4,
    marginBottom: "1vh",
    backgroundColor: "#005F5F",
    overflowY: "auto",
  },
  imageContainer: {
    height: "100%",
    position: "relative",
    overflow: "hidden",
  },
  removeImg: {
    position: "absolute",
    top: 0,
    right: 0,
  },
  checkbox: {
    "&.Mui-checked": {
      color: "#13CE66",
    },
  },
  addPhotoContainer: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    border: '2px dashed #00C974',
    borderRadius: '4px',
    backgroundColor: 'rgba(0, 201, 116, 0.1)',
    cursor: 'pointer',
    minHeight: '93px',
  },
  addIcon: {
    color: '#00C974',
    fontSize: '2rem',
  },
  addText: {
    color: '#00C974',
    marginTop: '5px',
    fontSize: '0.8rem',
  },
}));

/**
 * @param {{zh: string, en:string}} labelObj 
 * @param {string} value 
 * @param {Object} intl 
 */
const createHardCodeObj = (labelObj, value, intl) => ({
  label: labelObj?.[intl.locale],
  value,
});

function getphotoContentOptions(intl) {
  const photoContentOptions = [
    { value: "layout", label: "Building Outlook" },
    { value: "interior", label: "Interior" },
    { value: "plan", label: "Floor Plan" },
    { value: "lobby", label: "Lobby" },
    { value: "entrance", label: "Entrance" },
    { value: "lift", label: "Lift" },
    { value: "kol_video", label: intl.formatMessage({id: "stock.kol"}) },
    ...(sbu === "COMM"
      ? [
          { value: "view", label: "View" },
          { value: "decoration", label: "Decoration" },
        ]
      : []),
  ];
  return photoContentOptions;
}

export function getPhotoContentOptions(intl) {
  const arr = (sbu === "COMM") ? [
    { value: "Transaction book", en: "Transaction book", zh: "成交紀錄冊"},
    { value: "Cover", en: "Cover", zh: "封面相" },
    { value: "floorPlan", en: "Floor Plan", zh: "平面圖" },
    { value: "arcCert", en: "Architectural Cert.", zh: "建築師圖則" },
    { value: "plan", en: "Plan", zh: "圖則" },
    { value: "layout", en: "Layout Plan", zh: "間格圖" },
    { value: "interior", en: "Interior", zh: "室內" },
    { value: "lobby", en: "Lobby", zh: "大堂" },
    { value: "lift", en: "Lift", zh: "電梯" },
    { value: "entrance", en: "Entrance", zh: "入口" },
    { value: "positionPlan", en: "Location Plan", zh: "位置圖" },
    { value: "areaPlan", en: "Area Plan", zh: "面積圖" },
    { value: "buildingInfo", en: "Building Outlook", zh: "大廈外觀相" },
    { value: "gfPhoto", en: "GF Photo", zh: "地廠相" },
  ] : [
    { value: "Cover", en: "Cover", zh: "封面相" },
    { value: "layout", en: "Outlook", zh: "外觀" },
    { value: "interior", en: "Interior", zh: "室內" },
    { value: "plan", en: "Layout", zh: "圖則" },
    { value: "lobby", en: "Lobby", zh: "大堂" },
    { value: "lift", en: "Lift", zh: "電梯" },
    { value: "entrance", en: "Entrance", zh: "入口" },
    { value: "positionPlan", en: "Location Plan", zh: "位置圖" },
    { value: "floorPlan", en: "Floor Plan", zh: "平面圖" },
    { value: "areaPlan", en: "Area Plan", zh: "面積圖" },
    { value: "buildingInfo", en: "Building Info.", zh: "大廈資料" },
    { value: "gfPhoto", en: "GF Photo", zh: "地廠相" },
    { value: "arcCert", en: "Arc Cert", zh: "測師圖" },
  ];
  return arr.map((v) => createHardCodeObj(v, v.value, intl));
}

function getVRVideoContentOptions(intl) {
  return getPhotoContentOptions(intl);
}

const uploadToLabelMapping = Object.freeze({
  stock: Object.freeze({ zh: "樓盤", en: "Property" }),
  street: Object.freeze({ zh: "街道", en: "Street" }),
  building: Object.freeze({ zh: "大廈", en: "Building" }),
});

function getMediaUploadToOptions(intl) {
  const result = [{ value: "stock", label: uploadToLabelMapping["stock"][intl.locale] }];
  if (sbu === "SHOPS") {
    result.push({ value: "street", label: uploadToLabelMapping["street"][intl.locale] });
  } else {
    result.push({ value: "building", label: uploadToLabelMapping["building"][intl.locale] });
  }
  return result;
}

function getDocumentContentOptions(intl) {
  return [
    { value: "Surveyor PP", en: "Surveyor PP", zh: "測量部建議書"},
    { value: "Consumption", en: "Consumption", zh: "樓盤消耗表"},
    { value: "WB TX", en: "WB TX", zh: "全幢成交紀錄"},
    { value: "First Hand", en: "First Hand", zh: "一手文件"},
    { value: "PDF PP", en: "PDF PP", zh: "樓書"},
    { value: "Sheet", en: "Sheet", zh: "報表"},
    { value: "Sales Brochure", en: "Sales Brochure", zh: "售樓說明書"},
    { value: "Price List", en: "Price List", zh: "價單"},
    { value: "Sales Arrangement", en: "Sales Arrangement", zh: "銷售安排"},
    { value: "Transaction book", en: "Transaction book", zh: "成交紀錄冊"},
  ].map((v) => createHardCodeObj(v, v.value, intl));
}

class StepOne extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      uploadToOptions: getMediaUploadToOptions(this.props.intl),
      mediaContentOptions: [],
      contentName: "",
      videoErrors: {},
      selectedFileIndex: 0,
      // 添加URL缓存对象
      fileUrlCache: {},
      // 添加错误提示状态
      fileCountError: null,
      showErrorSnackbar: false
    };
    this.videoRefs = {};
    
    // 预先创建验证函数，避免每次渲染都创建新实例
    this.characteristicValidator = required(this.props.intl, "stock.kol.characteristicZh");
  };

  componentDidMount() {
    this.handleMediaTypeChangeEffect();
    
    // 设置employeeId的默认值
    const { employee, changeForm } = this.props;
    if (employee && employee.emp_id && changeForm) {
      changeForm("employeeId", employee.emp_id);
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.mediaType !== this.props.mediaType) {
      this.handleMediaTypeChangeEffect();
    }

    // 检测字段变化，并保存到文件属性中
    const fieldProps = ['description', 'characteristicZh', 'address', 'isShowContact', 'bgm'];
    const hasFieldChanged = fieldProps.some(prop => 
      this.props[prop] !== undefined && prevProps[prop] !== this.props[prop]
    );

    if (hasFieldChanged && this.props.files && this.props.files.length > 0) {
      this.saveFileProperties();
    }
    
    // 如果thumbnail变化了，保存到当前文件
    if (this.props.thumbnail !== prevProps.thumbnail) {
      this.saveFileProperties();
    }
    
    // 如果文件列表发生变化，则更新URL缓存
    if (prevProps.files !== this.props.files) {
      this.updateUrlCache(this.props.files);
    }
  }
  
  // 添加URL缓存管理方法
  updateUrlCache = (files) => {
    if (!files || !files.length) return;
    
    const { fileUrlCache } = this.state;
    const newCache = { ...fileUrlCache };
    
    // 为每个文件创建URL（如果缓存中不存在）
    files.forEach(file => {
      const fileId = this.getFileId(file);
      if (!newCache[fileId]) {
        newCache[fileId] = URL.createObjectURL(file);
      }
    });
    
    // 清理不再需要的URL
    const currentFileIds = files.map(file => this.getFileId(file));
    Object.keys(newCache).forEach(cachedId => {
      if (!currentFileIds.includes(cachedId)) {
        URL.revokeObjectURL(newCache[cachedId]);
        delete newCache[cachedId];
      }
    });
    
    this.setState({ fileUrlCache: newCache });
  };
  
  // 获取文件唯一标识符
  getFileId = (file) => {
    return `${file.name}-${file.size}-${file.lastModified}`;
  };
  
  // 获取文件URL（从缓存中）
  getFileUrl = (file) => {
    if (!file) return '';
    
    const fileId = this.getFileId(file);
    const { fileUrlCache } = this.state;
    
    if (!fileUrlCache[fileId]) {
      fileUrlCache[fileId] = URL.createObjectURL(file);
      this.setState({ fileUrlCache });
    }
    
    return fileUrlCache[fileId];
  };

  // 清理组件卸载时的URL对象
  componentWillUnmount() {
    // 释放所有创建的URL对象
    const { fileUrlCache } = this.state;
    Object.values(fileUrlCache).forEach(url => {
      URL.revokeObjectURL(url);
    });
  }

  handleMediaTypeChangeEffect () {
    const { mediaType } = this.props;
    const fn = {
      document: getDocumentContentOptions,
      photo: getPhotoContentOptions,
      video: getVRVideoContentOptions,
      kol_video: getVRVideoContentOptions,
    }[mediaType];
    const contentName = mediaType === "document" ? "documentContent" : "photoContent";
    this.setState({
      mediaContentOptions: fn(this.props.intl),
      contentName,
    });
  };

  removeOneFile = (index) => () => {
    const { files, mediaType, changeForm } = this.props;

    let newFiles = files.slice();
    newFiles.splice(index, 1);
    changeForm("files", newFiles);
    
    // 如果是视频类型，需要重新调整videoRefs对象
    if (['kol_video', 'video'].includes(mediaType)) {
      const newVideoRefs = {};
      const newVideoErrors = {};
      
      // 重新映射videoRefs和videoErrors，确保索引匹配
      Object.keys(this.videoRefs).forEach(idx => {
        const numIdx = parseInt(idx);
        if (numIdx < index) {
          newVideoRefs[numIdx] = this.videoRefs[idx];
          if (this.state.videoErrors[idx] !== undefined) {
            newVideoErrors[numIdx] = this.state.videoErrors[idx];
          }
        } else if (numIdx > index) {
          newVideoRefs[numIdx - 1] = this.videoRefs[idx];
          if (this.state.videoErrors[idx] !== undefined) {
            newVideoErrors[numIdx - 1] = this.state.videoErrors[idx];
          }
        }
      });
      
      this.videoRefs = newVideoRefs;
      this.setState({ videoErrors: newVideoErrors });
    }

    // 如果删除的是当前选中的文件，调整选中索引
    if (index === this.state.selectedFileIndex) {
      // 如果删除的是最后一个文件，选中前一个，否则保持当前索引
      const newSelectedIndex = index === newFiles.length ? Math.max(0, index - 1) : index;
      this.setState({ selectedFileIndex: newSelectedIndex });
      
      // 如果还有文件，加载选中文件的属性
      if (newFiles.length > 0) {
        this.loadFileProperties(newFiles[newSelectedIndex]);
      }
    } else if (index < this.state.selectedFileIndex) {
      // 如果删除的文件在当前选中文件之前，调整选中索引
      this.setState({ selectedFileIndex: this.state.selectedFileIndex - 1 });
    }
  };
  
  handleFileSelect = (files) => {
    // 为新添加的文件设置默认属性
    if (files && files.length > 0) {
      this.processFiles(files);
    }
  };

  processFiles = (files) => {
    const { mediaType, changeForm } = this.props;
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      if (!file.fileProperties) {
        file.fileProperties = {
          description: mediaType === 'kol_video' ?  this.props.description : file.name,
        };
        
        if (mediaType === 'video' || mediaType === 'kol_video') {
          file.fileProperties.thumbnail = null;
          file.fileProperties.isShowContact = mediaType === 'kol_video' ? true : false;
          file.fileProperties.bgm = "A Relax.mp3";
          file.fileProperties.characteristicZh = this.props.characteristicZh || "";
          file.fileProperties.address = this.props.address || "";
        }
      }
    }
  
    // 如果添加了新文件，设置选中最后一个文件
    if (this.props.files && files.length > this.props.files.length) {
      const newIndex = files.length - 1;
      this.setState({ selectedFileIndex: newIndex });
      
      // 加载新文件的属性
      this.loadFileProperties(files[newIndex]);
    }
  }

  // 添加选择文件的方法
  selectFile = (index) => () => {
    if (this.state.selectedFileIndex !== index) {
      // 先保存当前文件属性
      this.saveFileProperties();
      this.setState({ selectedFileIndex: index });
      
      const { files } = this.props;
      if (files && files.length > index) {
        this.loadFileProperties(files[index]);
      }
    }
  };

  // 加载文件属性到表单
  loadFileProperties = (file) => {
    const { changeForm } = this.props;
    
    if (file && file.fileProperties) {
      const props = file.fileProperties;
      
      // 加载文件属性到表单字段
      changeForm("description", props.description || file.name);
      
      // 仅对视频类型加载这些属性
      if (['kol_video', 'video'].includes(this.props.mediaType)) {
        changeForm("characteristicZh", props.characteristicZh || "");
        changeForm("address", props.address || "");
        changeForm("isShowContact", props.isShowContact);
        changeForm("bgm", props.bgm || "");
        if (props.thumbnail) {
          changeForm("thumbnail", [props.thumbnail]);
        } else {
          changeForm("thumbnail", null);
        }
      }
    }
  };

  // 保存表单字段到当前选中文件的属性中
  saveFileProperties = () => {
    const { files, changeForm, mediaType } = this.props;
    const { selectedFileIndex } = this.state;
    
    if (files && files.length > selectedFileIndex) {
      const file = files[selectedFileIndex];
      if (!file.fileProperties) {
        file.fileProperties = {};
      }
      
      // 保存表单字段到文件属性
      file.fileProperties.description = this.props.description || file.name;
      
      if (['kol_video', 'video'].includes(mediaType)) {
        file.fileProperties.characteristicZh = this.props.characteristicZh || "";
        file.fileProperties.address = this.props.address || "";
        file.fileProperties.isShowContact = this.props.isShowContact;
        file.fileProperties.bgm = this.props.bgm || "";
        file.fileProperties.thumbnail = this.props.thumbnail && this.props.thumbnail.length > 0 ? this.props.thumbnail[0] : null;
      }
      
      // 更新文件列表
      const newFiles = [...files];
      newFiles[selectedFileIndex] = file;
      changeForm("files", newFiles);
    }
  };

  handleVideoLoad = (index) => () => {
    const videoRef = this.videoRefs[index];
    if (videoRef && videoRef.videoWidth === 0) {
      console.warn("视频没有视频轨道或编码不受支持");
      const newVideoErrors = { ...this.state.videoErrors };
      newVideoErrors[index] = true;
      this.setState({ videoErrors: newVideoErrors });
    } else {
      const newVideoErrors = { ...this.state.videoErrors };
      newVideoErrors[index] = false;
      this.setState({ videoErrors: newVideoErrors });
    }
  };

  handleVideoError = (index) => (e) => {
    console.error("视频加载错误:", e);
    const newVideoErrors = { ...this.state.videoErrors };
    newVideoErrors[index] = true;
    this.setState({ videoErrors: newVideoErrors });
  };

  // 添加一个新方法来处理添加更多照片
  handleAddMorePhotos = (event) => {
    event.stopPropagation(); // 阻止事件冒泡，防止触发父元素的点击事件
    
    const { files } = this.props;
    
    // 如果当前文件数量已经达到上限，显示错误提示
    if (files && files.length >= MAX_FILES) {
      this.setState({ 
        fileCountError: this.props.intl.formatMessage(
          { id: "media.maxFiles" }, 
          { maxFiles: MAX_FILES }
        ),
        showErrorSnackbar: true
      });
      return;
    }
    
    // 创建一个隐藏的文件输入元素
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.multiple = true;
    fileInput.accept = media.acceptableFormatForKOLCoverPicture.split(",").map((v) => "." + v).join(",");
    
    // 监听文件选择变化
    fileInput.addEventListener('change', (e) => {
      if (e.target.files && e.target.files.length > 0) {
        const newFiles = Array.from(e.target.files);
        const { files, changeForm } = this.props;
        
        // 检查总文件数量是否超过限制
        if ((files.length + newFiles.length) > MAX_FILES) {
          const remainingSlots = MAX_FILES - files.length;
          
          this.setState({ 
            fileCountError: this.props.intl.formatMessage(
              { id: "media.maxFiles" }, 
              { maxFiles: MAX_FILES }
            ),
            showErrorSnackbar: true
          });
          
          // 如果还有剩余插槽，选择前N个文件
          if (remainingSlots > 0) {
            const limitedNewFiles = newFiles.slice(0, remainingSlots);
            const combinedFiles = [...files, ...limitedNewFiles];
            this.processFiles(combinedFiles);
            changeForm("files", combinedFiles);
          }
        } else {
          // 文件数量未超过限制，正常处理
          const combinedFiles = [...files, ...newFiles];
          this.processFiles(combinedFiles);
          changeForm("files", combinedFiles);
        }
      }
    });
    
    // 触发文件选择对话框
    fileInput.click();
  };
  
  // 添加关闭提示的方法
  handleCloseSnackbar = () => {
    this.setState({ showErrorSnackbar: false });
  };

  render() {
    const {
      classes,
      files,
      syncError,
      intl,
      permissions,
      validEmployees,
      employee,
      mediaType = "photo",
      accept,
      description,
      characteristicZh,
      address,
      isShowContact,
      bgm,
      thumbnail,
    } = this.props;
    const { selectedFileIndex, fileCountError, showErrorSnackbar } = this.state;

    const employeesOptions = validEmployees
      .map((employee) => ({
        value: employee["emp_id"],
        label: `${employee["dept_code"]} ${employee[`name_${intl.locale}`]}`,
      }));

    const acceptableUploadFormatForKOL = media.acceptableFormatForKOL
      .split(",")
      .map((v) => "." + v)
      .join(",");
    const acceptableUploadFormatForKOLCover = media.acceptableFormatForKOLCoverPicture
      .split(",")
      .map((v) => "." + v)
      .join(",");
    const acceptableUploadFormat = accept || (media.acceptableFormat
      .split(",")
      .map((v) => "." + v)
      .join(","));

    const canEditOwner = permissions[EDIT_MEDIA_OWNER]; // && _.get(employee, "auth.role").includes("MARKETING") && _.get(media, "approval") !== "approved";

    const renderFiles = (!syncError || !syncError.files) && files.length > 0 && (
      <div className={classes.previewBox}>
        <Grid container spacing={1}>
          {files.map((v, i) => (
            <Grid item xs={4} key={i}>
              <div className={classes.imageContainer} 
                   onClick={this.selectFile(i)} 
                   style={{
                     cursor: 'pointer',
                     outline: selectedFileIndex === i ? '2px solid #00C974' : 'none',
                     boxSizing: 'border-box'
                   }}>
                {mediaType === "document" && (
                  <Document file={v} record={null} disablePopup={true} />
                )}
                {v.type.split("/")[0] === "video" && (
                  <>
                    <img className={classes.image} alt="" src={squareSvg} />
                    <video
                      className={classes.video}
                      muted
                      autoPlay
                      loop
                      playsInline
                      ref={(ref) => this.videoRefs[i] = ref}
                      onError={this.handleVideoError(i)}
                      onLoadedMetadata={this.handleVideoLoad(i)}
                      key={`video-${i}-${v.name}`}
                    >
                      <source src={this.getFileUrl(v)} type={v.type} />
                    </video>
                    {this.state.videoErrors[i] && (
                      <div style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: 'rgba(0,0,0,0.7)',
                        color: 'white',
                        zIndex: 0,
                        textAlign: 'center',
                      }}>
                        <p>{intl.formatMessage({ id: "media.video.error" })}</p>
                      </div>
                    )}
                  </>
                )}
                {mediaType === "photo" && (
                  <ExifOrientationFix src={this.getFileUrl(v)}>
                    {(css) => (
                      <img
                        className={classes.image}
                        alt=""
                        src={squareSvg}
                        style={{
                          ...css,
                          backgroundImage: `url(${this.getFileUrl(v)})`,
                        }}
                      />
                    )}
                  </ExifOrientationFix>
                )}
                <DarkBgCross
                  className={classes.removeImg}
                  onClick={this.removeOneFile(i)}
                />
              </div>
            </Grid>
          ))}
          
          {/* 添加"Add Photo"按钮 */}
          {mediaType === "photo" && (
            <Grid item xs={4}>
              <div className={classes.addPhotoContainer} onClick={this.handleAddMorePhotos}>
                <AddIcon className={classes.addIcon} />
                <div className={classes.addText}>{intl.formatMessage({ id: "media.addPhoto" })}</div>
              </div>
            </Grid>
          )}
        </Grid>
        {files.length > 0 && (
          <div style={{ textAlign: 'center', marginTop: '10px', color: '#fff' }}>
            {intl.formatMessage({ id: "media.selectedFile" })}: {selectedFileIndex + 1} / {files.length} - {files[selectedFileIndex]?.name}
          </div>
        )}
      </div>
    )

    return (
      <div className={classes.root}>
        {/*<div className={classes.switchRow}>*/}
        {/*  <div className={classes.font18}>Upload media to…</div>*/}
        {/*  <Field name="uploadTo" component={Switch} textL="Building" textR="Unit" className={classes.uploadToSwitch} validate={required} />*/}
        {/*</div>*/}
        {/*<div className={classes.font18}>Please select media type:</div>*/}
        {/*<div className={classes.mediaTypeSelect}><Field name="type" component={MediaTypeSelect} validate={required} /></div>*/}

        <div className={classes.selectRow}>
          {mediaType !== 'kol_video' && (<>
            <Field
              name="uploadTo"
              component={CustomAutocompleteField}
              label={intl.formatMessage({ id: `media.${mediaType}.type` })}
              ranges={this.state.uploadToOptions}
              disableClearable={true}
            />
            {!['video', 'kol_video'].includes(mediaType) && (
            <Field
              name={this.state.contentName}
              component={CustomAutocompleteField}
              label={intl.formatMessage({
                id: `stock.${mediaType}content`,
              })}
              ranges={this.state.mediaContentOptions}
              disabled={mediaType === 'kol_video' ? true : false}
              disableClearable={true}
            />
            )}
          </>)}
          {/* {mediaType === 'kol_video' && (
            <Field
              name="photoContent"
              component={CustomAutocompleteField}
              label={intl.formatMessage({
                id: "stock.photocontent",
              })}
              ranges={getphotoContentOptions(intl)}
              disabled={true}
              disableClearable={true}
            />
          )} */}
          {canEditOwner && (
            <Field
              name="employeeId"
              component={CustomAutocompleteField}
              label={intl.formatMessage({
                id: "stock.owner",
              })}
              ranges={employeesOptions}
              disableClearable={true}
              disabled={!canEditOwner}
              defaultValue={employee?.emp_id} // 使用默认值
            />
          )}
        </div>

        {mediaType === 'kol_video' ? <p>{intl.formatMessage({id: "stock.updatedate"})}:<br />{moment().format("DD/MM/YYYY")}</p> : null}
        
        {files.length == 0 && (
          <div className={classes.selectFileRow}>
            <Field
              name="files"
              component={FieldFileInput}
              label={
                <DialogButton>
                  {intl.formatMessage({
                    id: `stock.select${mediaType === 'kol_video' ? 'kol' : mediaType}`,
                  })}
                </DialogButton>
              }
              accept={acceptableUploadFormat}
              multiple={mediaType === 'photo' ? true : false} // 除了photo，其他类型都不支持多文件上传
              validate={[mediaType === 'photo' ? imageFile : ['video', 'kol_video'].includes(mediaType) ? videoFile : mediaType === 'document' ? pdfFile : null]}
              onChange={this.handleFileSelect}
              maxFiles={mediaType === 'photo' ? MAX_FILES : null}
            />
          </div>
        )}
        
        {renderFiles}

        {/* {files.length > 0 && (<> */}
        {['kol_video', 'video'].includes(mediaType) ? (<>
          <div className={classes.selectFileRow}>
            <Field
              name="thumbnail"
              fileName={thumbnail && thumbnail.length > 0 ? thumbnail[0].name : null}
              component={FieldFileInput}
              label={
                <DialogButton>
                  {intl.formatMessage({
                    id: "stock.selectkolcoverpicture",
                  })}
                </DialogButton>
              }
              accept={acceptableUploadFormatForKOLCover}
              multiple={false}
              validate={[imageFile]}
            />
          </div>

          <div>
            <Field
              required
              name="address"
              component={TextInput}
              variant="outlined"
              margin="normal"
              label={intl.formatMessage({ id: "stock.applySearch.address" })} />
          </div>

          <div>
            <Field
              //required
              name="description"
              component={TextInput}
              variant="outlined"
              margin="normal"
              placeholder={intl.formatMessage({ id: "stock.kol.description" })}
              label={
                <div>
                  <span>{intl.formatMessage({ id: "stock.kol.description" })}</span>
                  <span style={{ color: "yellow", fontWeight:200, marginLeft: "5px" }}>{`${intl.locale === "zh" ? "將不會顯示於影片中" : "Will not be displayed in video"}`}</span>
                </div>
              } />
          </div>

          <div>
            <Field
              required
              name="characteristicZh"
              component={TextInput}
              variant="outlined"
              margin="normal"
              validate={[this.characteristicValidator]}
              label={intl.formatMessage({ id: "stock.kol.characteristicZh" })} />
          </div>

          {/* 背景音乐选择 - 使用新的FieldBgmSelector组件 */}
          <Field
            name="bgm"
            component={FieldBgmSelector}
            label={intl.formatMessage({ id: "stock.kol.bgm" })}
          />

          {/* 片尾加联络人资料 */}
          {mediaType === 'video' && (
          <div>
            <Field
              name="isShowContact"
              component={({ input }) => (
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={input.value || false}
                      onChange={input.onChange}
                      className={classes.checkbox}
                      disabled={mediaType === 'kol_video'}
                    />
                  }
                  label={intl.formatMessage({ id: "stock.kol.isShowContact" })}
                />
              )}
            />
          </div>
          )}

          <p dangerouslySetInnerHTML={{__html: intl.formatMessage({ id: `stock.${mediaType === 'kol_video' ? 'kol' : 'video'}.upload.restrictions` })}} />
        </>) : (
          <div>
            <Field
              //required
              name="description"
              component={TextInput}
              variant="outlined"
              margin="normal"
              placeholder={intl.formatMessage({ id: "stock.kol.description" })}
              label={intl.formatMessage({ id: "stock.kol.description" })} />
          </div>
        )}
        {/* </>)} */}
        
        {/* 添加错误提示弹窗 */}
        <Snackbar 
          open={showErrorSnackbar} 
          autoHideDuration={5000} 
          onClose={this.handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert onClose={this.handleCloseSnackbar} severity="error">
            {fileCountError}
          </Alert>
        </Snackbar>
      </div>
    );
  }
}

const mapStateToProps = state => ({
  permissions: state.employee.permissions,
  validEmployees: state.employee.validEmployees || [],
  employee: _.get(state, "employee.employees.0"),
  description: _.get(state, "form.uploadMediaForm.values.description"),
  characteristicZh: _.get(state, "form.uploadMediaForm.values.characteristicZh"),
  address: _.get(state, "form.uploadMediaForm.values.address"),
  isShowContact: _.get(state, "form.uploadMediaForm.values.isShowContact"),
  bgm: _.get(state, "form.uploadMediaForm.values.bgm"),
  thumbnail: _.get(state, "form.uploadMediaForm.values.thumbnail"),
});

const mapDispatchToProps = dispatch => ({
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(StepOne)));
