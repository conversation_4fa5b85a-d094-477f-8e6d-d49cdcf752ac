import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import SortingBar from "../../../common/SortingBar";
import { injectIntl } from "react-intl";

const styles = (theme) => ({
  sticky: {
    position: "fixed",
    width: "100%",
    zIndex: 500,
  },
});

class StockSortingBar extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    queryvariables: PropTypes.object,
    updateQuery: PropTypes.func,
    fetchData: PropTypes.func,
  };

  fetchData = (queryvariables) => {
    this.props.fetchData(queryvariables, false);
  };

  render() {
    const { classes, queryvariables, updateQuery, expanded, intl } = this.props;

    const sorterOptions = [
      {
        value: {
          asc: [
            { field: "streetsNo", order: "asc" },
            { field: "floor", order: "asc" },
            { field: "unit", order: "asc" },
          ],
          desc: [
            { field: "streetsNo", order: "asc" },
            { field: "floor", order: "asc" },
            { field: "unit", order: "desc" },
          ],
        },
        label: intl.formatMessage({
          id: "stock.shopnumber",
        }),
      },
      {
        value: "rentTotal",
        label: intl.formatMessage({
          id: "search.common.rent",
        }),
      },
      {
        value: "priceTotal",
        label: intl.formatMessage({
          id: "search.common.price",
        }),
      },
      {
        value: "area",
        label: intl.formatMessage({
          id: "search.common.area",
        }),
      },
    ];

    return (
      <div className={expanded ? null : classes.sticky}>
        <SortingBar
          options={sorterOptions}
          queryvariables={queryvariables}
          updateQuery={updateQuery}
          fetchData={this.fetchData}
        />
      </div>
    );
  }
}

const mapStateToProps = (state) => ({});

const mapDispatchToProps = (dispatch) => {
  return {

  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(StockSortingBar)));
