import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import { injectIntl } from "react-intl";
import ConsultantShareBox from "./ConsultantShareBox";

const styles = theme => ({
  root: {
    padding: "1vh 0",
  },
});

class ConsultantShare extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;

    let shares = detail.consultantShares || [];
    shares = shares.map(v => {
      return {
        employee: v.employee && v.employee.name_en ? v.employee.name_en : "---",
        dateTime: v.date || "---",
        remarks: v.remarks || "---",
      }
    });
    if (shares.length === 0) shares = [{}];

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.consultantshare"
          })}
        >
          {shares.map((v, i) => (
            <ConsultantShareBox
              {...v}
              key={i}
            />
          ))}
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(ConsultantShare));
