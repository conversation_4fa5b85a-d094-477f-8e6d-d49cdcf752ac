import React from "react";
import PropTypes from "prop-types";
import moment from "moment";
import { withStyles } from "@material-ui/styles";
import DetailBoxTitle from "../../../../common/DetailBoxTitle";
import {
  numberComma,
  get<PERSON>ang<PERSON>ey,
  convertNewlineToBr,
  paresFloorUnit,
} from "../../../../../helper/generalHelper";
import clsx from "clsx";
import { injectIntl } from "react-intl";
import FormButton from "../../../../common/FormButton";
import Link from "../../../../Link";
import PropertyTagBar from "../../../../common/PropertyTagBar";
import NormalAndBottomPrice from "../../../../common/NormalAndBottomPrice";
import InfoIcon from "@material-ui/icons/Info";
import Grid from "@material-ui/core/Grid";
import FieldVal from "../../../../common/FieldVal";
import FieldValHorizontal from "../../../../common/FieldValHorizontal";

const styles = (theme) => ({
  root: {
    // padding: "0 2vw"
  },
  textClass: {
    fontSize: "1.175em",
  },
  nameAndBtn: {
    display: "flex",
    alignItems: "center",
    "& > *:last-child": {
      flex: "0 0 auto",
    },
  },
  right: {
    textAlign: "right",
  },
  twoLevelItem: {
    marginBottom: "1vh",
    "& > *:not(:first-child)": {
      color: "rgba(255, 255, 255, 0.75)",
    },
    padding: "0 4px",
    borderRadius: 4,
    color: "#fff",
  },
  rentItem: {
    backgroundColor: "rgba(0, 197, 197, .75)",
  },
  priceItem: {
    backgroundColor: "rgba(232, 0, 0, .75)",
  },
  greyItem: {
    backgroundColor: "rgba(132, 132, 132, .1)",
  },
  rentItemNoValue: {
    backgroundColor: "rgba(140, 190, 190, 0.2)",
  },
  priceItemNoValue: {
    backgroundColor: "rgba(200, 170, 170, 0.2)",
  },
  sepText: {
    fontSize: "1.4em",
    textAlign: "right",
    "& > *:not(:last-child)": {
      marginRight: "5vw",
    },
  },
  btn: {
    height: 22,
    lineHeight: "22px",
    // fontSize: ".875em",
    padding: "0 10px",
    textTransform: "none",
    background: "#9932CC",
    minWidth: 0,
  },
  btnIcon: {
    width: 16,
    height: 16,
    marginLeft: 4,
  },
  link: {
    textDecoration: "none",
  },
  section: {
    display: 'flex',
    marginBottom: "2vh",
    justifyContent: "space-between",
  },
  areaSmallFont: {
    fontSize: ".75em",
  },
  area: {
    display: "inline-flex",
    flexDirection: "column",
    fontWeight: "bold"
  },
  lmrAlign: {
    paddingLeft: "2vw",
  },
  commentBox: {
    padding: "8px",
    marginLeft: "-8px",
    borderRadius: "4px",
    backgroundColor: "rgba(255, 255, 100, .4)",
    lineHeight: "1.5",
  },
  updateDateFields: {
    padding: "0 2vw",
    margin: "5vh 0 3vh",
    textAlign: "right",
  },
  text1: {
    width: "100%",
    display: 'flex',
    justifyContent: 'space-between',
  }
});

class StockDetail extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
    btnController: PropTypes.node
  };

  render() {
    const { classes, detail, intl, btnController } = this.props;
    const langKey = getLangKey(intl);

    const statusName =
    detail.status && detail.status[langKey]
      ? detail.status[langKey]
      : "---";
    const unit = detail.unit || "---";
    const floor = detail.floor || "---";
    const buildingName =
      detail.building && detail.building[langKey]
        ? detail.building[langKey]
        : "---";
    const streetNo =
      detail.street && detail.street.number ? detail.street.number + " " : "";
    const streetName =
      detail.street && detail.street.street && detail.street.street[langKey]
        ? detail.street.street[langKey]
        : "---";
    const district =
      detail.building &&
      detail.building.district &&
      detail.building.district[langKey]
        ? detail.building.district[langKey]
        : "---";
    const buildingId =
      detail.building && detail.building._id ? detail.building._id : null;
    const buildingunicornId =
      detail.building && detail.building.unicorn && detail.building.unicorn.id
        ? detail.building.unicorn.id
        : null;

    let sizeWithTypes = detail?.area?.sizes?.map(size => {
      if (size.value) {
        const type = size.type.toLowerCase();
        const message = intl.formatMessage({ id: `stock.area.${type}` });
        const formattedValue = numberComma(size.value);
        return intl.locale === "zh" ? `${message} ${formattedValue}` : `${formattedValue} ${message}`;
      }
      return null;
    }) ?? null;
    let sizeNet = null;
    if (
      detail.area &&
      detail.area.sizes &&
      detail.area.sizes.filter((v) => v.type === "NET").length > 0
    )
      sizeNet = numberComma(
        detail.area.sizes.filter((v) => v.type === "NET")[0].value
      );

    let efficiency =
      detail.area && detail.area.efficiency
        ? Math.trunc(detail.area.efficiency, 2) + "%"
        : null;

    sizeNet = sizeNet
      ? intl.locale == "zh"
        ? (sizeNet =
          intl.formatMessage({ id: "stock.area.netLong" }) + " " + sizeNet)
        : (sizeNet =
          sizeNet + " " + intl.formatMessage({ id: "stock.area.netLong" }))
      : null;

    efficiency = efficiency
      ? intl.locale == "zh"
        ? (efficiency =
            intl.formatMessage({ id: "stock.area.effcy" }) + " " + efficiency)
        : (efficiency =
            efficiency + " " + intl.formatMessage({ id: "stock.area.effcy" }))
      : null;

    const remarksQuick =
      detail.remarks && detail.remarks.internal
        ? convertNewlineToBr(detail.remarks.internal)
        : "---";
    const createDate =
      detail.recordOperation && detail.recordOperation.createDate
        ? detail.recordOperation.createDate
        : "---";
    const lastUpdateDate =
      detail.recordOperation && detail.recordOperation.lastUpdateDate
        ? detail.recordOperation.lastUpdateDate
        : "---";

    const status =
      detail && detail.status && detail.status.nameEn
        ? detail.status.nameEn
        : null;
    let priceContainer, leaseContainer;
    let askingPrice = detail.askingPrice;
    let askingRent = detail.askingRent;
    switch (status) {
      case "S": //出售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        askingRent = {};
        break;
      case "L": //出租
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "SL": //連約售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        askingRent = {};
        break;
      case "S+L": //租及售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Pending": //封盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "TEL": //電話盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "SE": //查冊盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Don't Call": //拒致電
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Sold": //已售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Leased": //已租
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Fail": //錯誤盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "History": //舊資料
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Cancel": //錯盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      default:
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
    }
    const hasPrice = askingPrice && (askingPrice.total || askingPrice.average);
    const hasRent = askingRent && (askingRent.total || askingRent.average);

    const buildingInfoBtn = buildingId && (
      <Link className={classes.link} to={"/building/" + buildingId}>
        <FormButton
          className={classes.btn}
          onClick={() => {
            window.dataLayer.push({ buildingId: buildingunicornId });
          }}
        >
          {intl.formatMessage({
            id: "stock.building",
          })}
          <InfoIcon className={classes.btnIcon} />
        </FormButton>
      </Link>
    );

    const now = Date.now();
    const tagCar = detail.stockType && detail.stockType.nameEn === "Carpark";
    const tagWww = !!detail.isWWW;
    const tagMtg = !!detail.mortgagee;
    const saleData =
      detail.propertyAdvertisements && detail.propertyAdvertisements.saleData
        ? detail.propertyAdvertisements.saleData
        : {};
    const rentData =
      detail.propertyAdvertisements && detail.propertyAdvertisements.rentData
        ? detail.propertyAdvertisements.rentData
        : {};
    const dataArr = [saleData, rentData];
    let tagEaa = false;
    for (let i = 0; i < dataArr.length; i++) {
      let minDate = dataArr[i].minDate
        ? moment(dataArr[i].minDate, "YYYY-MM-DD").format("x")
        : null;
      let maxDate = dataArr[i].maxDate
        ? moment(dataArr[i].maxDate, "YYYY-MM-DD").format("x")
        : null;

      if (minDate && maxDate) {
        tagEaa = minDate < now && now < maxDate;
      } else if (minDate) {
        tagEaa = minDate < now;
      } else if (maxDate) {
        tagEaa = now < maxDate;
      }
    }
    const createTimestamp =
      detail.recordOperation && detail.recordOperation.createDate
        ? moment(detail.recordOperation.createDate, "YYYY-MM-DD").format("x")
        : null;
    const tagNew =
      createTimestamp && createTimestamp > now - 15 * 24 * 60 * 60 * 1000;
    const tagSole = !!(
      detail.soleagent &&
      detail.soleagent.periodStart != null &&
      detail.soleagent.periodEnd != null
    );
    const tagEq = !!detail.saleEquity;
    const tagTerrace = !!(detail.area && detail.area.terrace);
    const tagRoof = !!(detail.area && detail.area.roof);
    const tagCockloft = !!(detail.area && detail.area.cockloft);

    const tagsOverride = {
      [intl.formatMessage({ id: "stock.tag.sole" })]: tagSole,
      [intl.formatMessage({ id: "stock.tag.carpark" })]: tagCar,
      [intl.formatMessage({ id: "stock.tag.www" })]: tagWww,
      [intl.formatMessage({ id: "stock.tag.mortgagee" })]: tagMtg,
      [intl.formatMessage({ id: "stock.tag.eaa" })]: tagEaa,
      [intl.formatMessage({ id: "stock.tag.new" })]: tagNew,
      [intl.formatMessage({ id: "stock.tag.equaity" })]: tagEq,
      [intl.formatMessage({ id: "stock.tag.terrace" })]: tagTerrace,
      [intl.formatMessage({ id: "stock.tag.roof" })]: tagRoof,
      [intl.formatMessage({ id: "stock.tag.cockloft" })]: tagCockloft,
    };

    return (
      <div className={classes.root}>
        <DetailBoxTitle
          text={
            <div className={classes.text1}>
              <div>{paresFloorUnit(floor, unit, intl)}</div>
              <div>{btnController}</div>
            </div>
          }
          text2={
            <div className={classes.nameAndBtn}>
              <div>{buildingName}</div>
            </div>
          }
          textClass={classes.textClass}
          subtitle2={
            <div className={classes.text1}>
              <div style={{lineHeight: '2em'}}>{streetNo + streetName + ", " + district}</div>
              <div style={{fontSize: "1.4em", fontWeight: 'bold'}}>{statusName}</div>
            </div>
            }
        >
          <div className={classes.section}>
            <div style={{paddingTop: "5px"}}>{buildingInfoBtn}</div>
            <div className={classes.sepText}>
              {sizeNet && (
                <span className={classes.areaSmallFont}>{sizeNet}</span>
              )}
              {efficiency && (
                <span className={classes.areaSmallFont}>
                  {/* {intl.formatMessage({
                    id: "stock.area.effcy",
                  })}{" "} */}
                  {efficiency}
                </span>
              )}
              {sizeWithTypes && (<span className={classes.area}>
                {sizeWithTypes.map(size => size && (<span>{size}</span>))}
              </span>)}
            </div>
          </div>
          <div className={classes.right}>
            <div
              className={clsx(
                classes.twoLevelItem,
                leaseContainer,
                !hasRent && classes.rentItemNoValue
              )}
            >
              <NormalAndBottomPrice
                data={askingRent}
                avgDecimal={2}
                label="Rent"
              />
            </div>

            <div
              className={clsx(
                classes.twoLevelItem,
                priceContainer,
                !hasPrice && classes.priceItemNoValue
              )}
            >
              <NormalAndBottomPrice data={askingPrice} label="Price" />
            </div>
          </div>

          <div className={classes.section}>
            <PropertyTagBar detail={detail} tagsOverride={tagsOverride} />
          </div>

          <Grid container spacing={2} className={classes.lmrAlign}>
            <Grid item xs={12}>
              <FieldVal
                field={intl.formatMessage({
                  id: "stock.comment",
                })}
              />
              <FieldVal className={classes.commentBox}>{remarksQuick}</FieldVal>
            </Grid>
          </Grid>

          <Grid container className={classes.updateDateFields}>
            <Grid item xs={12}>
              <FieldValHorizontal
                field={intl.formatMessage({ id: "stock.stockcreate" })}
              >
                {createDate}
              </FieldValHorizontal>
            </Grid>
            <Grid item xs={12}>
              <FieldValHorizontal
                field={intl.formatMessage({ id: "stock.stockupdate" })}
              >
                {lastUpdateDate}
              </FieldValHorizontal>
            </Grid>
          </Grid>
        </DetailBoxTitle>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(StockDetail));
