import {
  LIST_TEAMS_KOL_VIDEOS_START,
  LIST_MORE_TEAMS_KOL_VIDEOS_START,
  LIST_TEAMS_KOL_VIDEOS_SUCCESS,
  LIST_TEAMS_KOL_VIDEOS_NULL_SUCCESS,
  LIST_TEAMS_KOL_VIDEOS_ERROR,
  CLEAR_TEAMS_KOL_VIDEOS,
} from "../constants/kolVideo";
import config from "../config";
import { PERMISSIONS } from "@/constants/auth";
const { VIEW_ALL_TEAMS, VIEW_KOL } = PERMISSIONS;

export function listTeamsKolVideos(variables, isFetchingMore, selectedData) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    if (isFetchingMore) {
      dispatch({
        type: LIST_MORE_TEAMS_KOL_VIDEOS_START,
        payload: {
          variables,
          selectedData
        },
        checkrefreshToken: true
      });
    } else {
      dispatch({
        type: LIST_TEAMS_KOL_VIDEOS_START,
        payload: {
          variables,
          selectedData
        },
        checkrefreshToken: true
      });
    }

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      };

      // const loginEmployee = await graphqlRequest(
      //     api.employee,
      //     await getQuery("LIST_EMPLOYEES_QUERY"),
      //     { emp_id: [getState().auth.user.login.info.emp_id] },
      //     options,
      //   );
      const query = await getQuery("LIST_EMPLOYEES_QUERY");

      const loginEmployee = await universalRequest("/employee/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: { emp_id: [getState().auth.user.login.info.emp_id] }
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (loginEmployee.errors) {
        throw new Error(loginEmployee.errors[0].message);
      }

      const employee = loginEmployee?.data?.employees?.[0] || null;

      let temasEmployeeIds = null
      if (employee && employee?.belongingTeams?.length && getState().employee.permissions[VIEW_KOL]) {
        // const temasEmployees = await graphqlRequest(
        //   api.employee,
        //   await getQuery("LIST_TEAMS_EMPLOYEES_QUERY"),
        //   { dept_code: employee.belongingTeams },
        //   options,
        // );

        const query = await getQuery("LIST_TEAMS_EMPLOYEES_QUERY");

        const temasEmployees = await universalRequest("/employee/graphql", {
          method: "POST",
          body: JSON.stringify({
            query,
            variables: { dept_code: employee.belongingTeams }
          }),
          ...options
        }).catch((error) => {
          return {
            errors: error,
          };
        });

        if (temasEmployees.errors) {
          throw new Error(temasEmployees.errors[0].message);
        }

        temasEmployeeIds = temasEmployees.data.employees.map(obj => obj.emp_id) || [];
      }

      let kolVideos = null;

      if (getState().employee.permissions[VIEW_ALL_TEAMS]) {
        const query = await getQuery("LIST_TEAMS_KOL_VIDEO_QUERY");

        const resp = await universalRequest("/media/graphql", {
          method: "POST",
          body: JSON.stringify({
            query: query,
            variables: { approval: "pending" },
          }),
          ...options
        }).catch((error) => {
          return {
            errors: error,
          };
        });

        if (resp.errors) {
          throw new Error(resp.errors[0].message);
        }

        const { data } = resp;

        kolVideos = data || [];
      } else if (temasEmployeeIds?.length) {
        const query = await getQuery("LIST_TEAMS_KOL_VIDEO_QUERY");

        const resp = await universalRequest("/media/graphql", {
          method: "POST",
          body: JSON.stringify({
            query: query,
            variables: { approval: "pending", employees: temasEmployeeIds },
          }),
          ...options
        }).catch((error) => {
          return {
            errors: error,
          };
        });

        if (resp.errors) {
          throw new Error(resp.errors[0].message);
        }

        const { data } = resp;

        kolVideos = data || [];
      }

      if (kolVideos?.media && kolVideos?.media?.length > 0) {
        dispatch({
          type: LIST_TEAMS_KOL_VIDEOS_SUCCESS,
          payload: {
            kolVideos
          }
        });
      } else {
        dispatch({
          type: LIST_TEAMS_KOL_VIDEOS_NULL_SUCCESS,
          payload: {
            kolVideos
          }
        });
      }
    } catch (error) {
      dispatch({
        type: LIST_TEAMS_KOL_VIDEOS_ERROR,
        payload: {
          error
        }
      });
      //throw new Error(error);
    }
  };
}

export function clearTeamsKolVideos() {
  return async dispatch => {
    dispatch({
      type: CLEAR_TEAMS_KOL_VIDEOS
    });
  };
}

export function batchRemoveKolVideo(variables) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const { ids } = variables;

      const options = {
        headers: {
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      };

      const query = await getQuery("BATCH_REMOVE_KOL_VIDEO");

      const data = await universalRequest("/media/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: { ids: ids, bypassCode: config.mediaBypassCode },
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }
    } catch (error) {
      console.log(error);
      //throw new Error(error);
    }
  };
}

export function batchApprovedKolVideo(variables) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const { ids } = variables;

      const options = {
        headers: {
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      };

      const query = await getQuery("BATCH_APPROVED_KOL_VIDEO");

      const data = await universalRequest("/media/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: { 
            ids: ids,
            bypassCode: config.mediaBypassCode,
          },
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }
    } catch (error) {
      console.log(error);
      //throw new Error(error);
    }
  };
}
