import React, { useState, useEffect, useCallback } from "react";
import PropTypes from "prop-types";
import { createStyles, withStyles } from "@material-ui/styles";
import clsx from "clsx";
// import PersonIcon from "@material-ui/icons/Person";
import LanguageIcon from "@material-ui/icons/Language";
import PlayCircleOutlineIcon from '@material-ui/icons/PlayCircleOutline';
import DeleteRoundedIcon from '@material-ui/icons/DeleteRounded';
import { connect } from "react-redux";
import { 
  updateMedium, 
  deleteMedium, 
  clearProgressingMediumById,
  getMedium,
  getMediumByIds,
} from "@/actions/medium";
import { updateBuildingMediaByIndex, updateBuildingMediaList } from "@/actions/building";
import { updateStockMediaByIndex, updateStockMediaList } from "@/actions/stock";
import { updateStreetMediaByIndex, updateStreetMediaList } from "@/actions/street";
import { PERMISSIONS } from "../../../constants/auth";
import { injectIntl, useIntl } from "react-intl";
import _ from "lodash";
import SubmitDialog from "../../common/SubmitDialog";

const styles = createStyles({
  root: {
    width: "100%",
    height: "100%",
    display: "flex",
    position: "absolute",
    top: "0",
    left: "0",
    pointerEvents: "none",
    gap: "2px",
    padding: "2px",
  },
  tagContainer: {
    width: "100%",
    height: "fit-content",
    display: "flex",
    pointerEvents: "none",
    gap: "2px",
    padding: "2px",
    background: "rgba(0, 0, 0, 0.5)",
  },
  typeIcon: {
    width: "50%",
    height: "50%",
    color: "#FFF",
    position: "absolute",
    top: "45%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    display: "block",
  },
  tag: {
    width: "20%",
    height: "20%",
    color: "#FFF",
  },
  tagIcon: {
    width: "100%",
    height: "100%",
    display: "block",
  },
  newTag: {
    width: "24px",
    height: "24px",
    borderRadius: "4px",
    textAlign: "center",
    lineHeight: "24px",
  },
  name: {
    width: "100%",
    color: "#FFF",
    fontSize: "0.875em",
    padding: "0 3px",
    position: "absolute",
    bottom: "0",
    left: "0",
    // backgroundColor: "#3F3F3F",
    background: "rgba(0, 0, 0, 0.5)",
    boxSizing: "border-box",
    textOverflow: "ellipsis",
    overflow: "hidden",
    whiteSpace: "nowrap",
  },
  personal: {
    backgroundColor: "rgba(234, 66, 79, 0.7)", // "#EC1F26",
  },
  www: {
    backgroundColor: "#1378CE",
  },
  yellowButton: {
    textAlign: "center",
    backgroundColor: "#fee100",
    // marginLeft: "2px",
    borderRadius: "4px",
  },
  orangeButton: {
    color: "#FFF",
    textAlign: "center",
    backgroundColor: "#FC792F",
    // marginLeft: "2px",
    borderRadius: "4px",
  },
  autoWidth: {
    width: "auto",
    padding: "0 4px",
  },
  redButton: {
    color: "#FFF",
    backgroundColor: "#F12424",
  },
  greyButton: {
    color: "#808080",
    backgroundColor: "#EAEAEA",
  },
  languageIconContainer: {
    position: "relative",
  },
  languageIcon: {
    position: "absolute",
    left: 0,
  },
  deleteButton: {
    width: "24px",
    height: "24px",
    borderRadius: "4px",
    color: "#fff",
    backgroundColor: "#13CE66", // 绿色背景
    marginLeft: "auto", // 右对齐
    marginRight: "4px", // 
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    cursor: "pointer",
  },
});

const {
  SET_WWW_PHOTO,
  REMOVE_MEDIA,
} = PERMISSIONS;
const MediumProperties = (props) => {
  const {
    classes,
    medium,
    isApproveMediaPage,
    progressingMedia,
    buildingMedia = {},
    stockMedia = {},
    streetMedia = {},
    updateMedium,
    deleteMedium,
    clearProgressingMediumById,
    getMedium,
    getMediumByIds,
    updateBuildingMediaByIndex,
    updateStockMediaByIndex,
    updateStreetMediaByIndex,
    updateStockMediaList,
    updateBuildingMediaList,
    updateStreetMediaList,
    PERMISSION_SET_WWW_PHOTO,
    PERMISSION_REMOVE_MEDIA,
  } = props;
  const intl = useIntl();

  const [updatingMedia, setUpdatingMedia] = useState({});
  const [deletingMedia, setDeletingMedia] = useState({});
  const [dialogOpen, setDialogOpen] = useState(false);
  const [currentMedium, setCurrentMedium] = useState(null);
  const localMediaRef = React.useRef({
    stockMedia,
    streetMedia,
    buildingMedia,
    updateBuildingMediaByIndex,
    updateStockMediaByIndex,
    updateStreetMediaByIndex,
  });
  useEffect(() => {
    localMediaRef.current = {
      stockMedia,
      streetMedia,
      buildingMedia,
      updateBuildingMediaByIndex,
      updateStockMediaByIndex,
      updateStreetMediaByIndex,
    };
  }, [
    stockMedia,
    streetMedia,
    buildingMedia,
    updateBuildingMediaByIndex,
    updateStockMediaByIndex,
    updateStreetMediaByIndex,
  ]);

  // 查找媒体在各类数据中的索引
  const findMediaIndexById = useCallback((mediaData = {}, mediaId, mediaToUpdate = null) => {
    const result = { index: -1, mediaIndex: -1, media: {}, mediaType: "" };

    for (const key in mediaData) {
      if (!Array.isArray(mediaData[key])) continue;
      
      const mediaArray = mediaData[key];
      const foundIndex = mediaArray.findIndex(m => m.id === mediaId);

      if (foundIndex !== -1) {
        return {
          index: 0, // 数据结构已改变，index总是0
          mediaIndex: foundIndex,
          mediaType: key,
          media: mediaToUpdate ? _.omit(mediaToUpdate, ['id', 'startTime', 'state']) : {}
        };
      }
    }

    return result;
  }, []);

  // 根据媒体类型更新对应的媒体数据
  const updateMediaByType = useCallback((findResult, mediaData = null) => {
    if (findResult.mediaIndex === -1) return false;
    
    // 如果提供了mediaData，使用它来更新
    if (mediaData) {
      findResult.media = _.omit(mediaData, ['id', 'startTime', 'state']);
    }
    
    const { current } = localMediaRef;
    // 根据媒体类型调用对应的更新函数
    if (findResult.mediaType) {
      if (current.stockMedia && findResult.mediaType in current.stockMedia) {
        current.updateStockMediaByIndex(findResult);
        return true;
      }
      if (current.buildingMedia && findResult.mediaType in current.buildingMedia) {
        current.updateBuildingMediaByIndex(findResult);
        return true;
      }
      if (current.streetMedia && findResult.mediaType in current.streetMedia) {
        current.updateStreetMediaByIndex(findResult);
        return true;
      }
    }
    return false;
  }, []);

  const updateLocalMedia = useCallback(async (media) => {
    const {
      stockMedia,
      streetMedia,
      buildingMedia,
    } = localMediaRef.current;

    // 如果媒体有scoring属性且为true，则批量查询同类型媒体状态
    if (medium.scoring === true) {
      try {
        const batchMediaData = await fetchAndProcessBatchMedia(medium.id);
        if (batchMediaData) return; // 如果成功处理了批量媒体数据，直接返回
      } catch (error) {
        console.error(`处理批量媒体数据时出错: ${error}`);
      }
    }

    const mediaTypes = [
      { data: stockMedia },
      { data: buildingMedia },
      { data: streetMedia }
    ];

    for (const { data } of mediaTypes) {
      const findResult = findMediaIndexById(data, media.id, media);
      if (updateMediaByType(findResult)) return;
    }
  }, [fetchAndProcessBatchMedia]);

  // 获取同类型媒体的所有ID
  const getSameTypeMediaIds = useCallback((targetMediaId) => {
    const {
      stockMedia,
      buildingMedia,
      streetMedia,
    } = localMediaRef.current;

    const mediaTypes = [
      { data: stockMedia, type: 'stock' },
      { data: buildingMedia, type: 'building' },
      { data: streetMedia, type: 'street' }
    ];

    for (const { data, type } of mediaTypes) {
      for (const key in data) {
        if (Array.isArray(data[key])) {
          const mediaArray = data[key];
          const targetMedia = mediaArray.find(m => m.id === targetMediaId);
          if (targetMedia) {
            // 找到目标媒体，返回同类型的所有媒体ID
            return {
              mediaIds: mediaArray.map(m => m.id),
              mediaType: type,
              categoryKey: key
            };
          }
        }
      }
    }

    return { mediaIds: [], mediaType: '', categoryKey: '' };
  }, []);

  // 辅助函数：批量获取并处理媒体数据
  const fetchAndProcessBatchMedia = useCallback(async (targetMediaId) => {
    let retryCount = 0;
    const maxRetries = 10;
    let lastBatchMediaData = []; // 保存最后一次获取的批量媒体数据
    
    // 获取同类型媒体的所有ID
    const { mediaIds, mediaType, categoryKey } = getSameTypeMediaIds(targetMediaId);
    
    if (mediaIds.length === 0) {
      console.error(`无法找到媒体ID: ${targetMediaId}的同类型媒体`);
      return null;
    }

    console.log(`开始批量处理媒体，目标媒体ID: ${targetMediaId}, 同类型媒体数量: ${mediaIds.length}`);
    
    const fetchBatchMediaData = async (ids) => {
      return await getMediumByIds(ids);
    };
    
    const checkBatchMediaProcessing = async () => {
      if (retryCount >= maxRetries) {
        console.log(`批量媒体处理达到最大重试次数，目标媒体: ${targetMediaId}`);
        
        // 即使没有获得"done"状态，也使用最后一次查询结果进行更新
        if (lastBatchMediaData.length > 0) {
          updateBatchMediaInLocal(lastBatchMediaData, mediaType, categoryKey);
          return lastBatchMediaData;
        }
        
        return null;
      }
      
      try {
        const batchMediaData = await fetchBatchMediaData(mediaIds);
        if (!batchMediaData || batchMediaData.length === 0) {
          console.error(`无法获取批量媒体数据，媒体ID列表: ${mediaIds}`);
          return null;
        }
        
        // 保存最后一次获取的数据
        lastBatchMediaData = batchMediaData;
        
        // 检查目标媒体的处理状态
        const targetMediaData = batchMediaData.find(m => m.id === targetMediaId);
        if (targetMediaData && targetMediaData.sqsProcessing === "done") {
          // 目标媒体处理完成，更新所有同类型媒体
          updateBatchMediaInLocal(batchMediaData, mediaType, categoryKey);
          return batchMediaData;
        } else {
          // 处理尚未完成，继续重试
          retryCount++;
          return new Promise(resolve => {
            setTimeout(async () => {
              resolve(await checkBatchMediaProcessing());
            }, 300);
          });
        }
      } catch (error) {
        console.error(`检查批量媒体处理状态时出错: ${error}`);
        return null;
      }
    };
    
    // 开始检查批量媒体处理状态
    return await checkBatchMediaProcessing();
  }, [getSameTypeMediaIds, getMediumByIds]);

  // 批量更新本地媒体数据
  const updateBatchMediaInLocal = useCallback((batchMediaData, mediaType, categoryKey) => {
    const { current } = localMediaRef;
    
    // 根据媒体类型获取对应的媒体数据和更新函数
    let currentMediaData = null;
    let updateFunction = null;
    
    if (mediaType === 'stock' && current.stockMedia) {
      currentMediaData = current.stockMedia[categoryKey];
      updateFunction = current.updateStockMediaByIndex;
    } else if (mediaType === 'building' && current.buildingMedia) {
      currentMediaData = current.buildingMedia[categoryKey];
      updateFunction = current.updateBuildingMediaByIndex;
    } else if (mediaType === 'street' && current.streetMedia) {
      currentMediaData = current.streetMedia[categoryKey];
      updateFunction = current.updateStreetMediaByIndex;
    }
    
    if (!currentMediaData || !Array.isArray(currentMediaData) || !updateFunction) {
      console.error(`无法找到对应的媒体数据或更新函数，媒体类型: ${mediaType}, 分类: ${categoryKey}`);
      return;
    }
    
    // 批量更新每个媒体
    batchMediaData.forEach(updatedMedia => {
      const mediaIndex = currentMediaData.findIndex(m => m.id === updatedMedia.id);
      if (mediaIndex !== -1) {
        const findResult = {
          index: 0, // 数据结构已改变，index总是0
          mediaIndex: mediaIndex,
          mediaType: categoryKey,
          media: _.omit(updatedMedia, ['id', 'startTime', 'state'])
        };
        updateFunction(findResult);
      }
    });
    
    console.log(`批量更新完成，媒体类型: ${mediaType}, 分类: ${categoryKey}, 更新数量: ${batchMediaData.length}`);
  }, []);

  const updateWWWPhoto = useCallback((medium) => {
    const { id } = medium;
    // 更新状态，添加正在更新的媒体
    setUpdatingMedia(prevState => ({
      ...prevState,
      [id]: true
    }));

    updateMedium(medium);
  }, [updateMedium]);

  const handleClickOpenDialog = (medium) => {
    setCurrentMedium(medium);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setCurrentMedium(null);
  };

  const handleDelete = useCallback((medium) => {
    // 添加到正在删除的媒体状态中
    setDeletingMedia(prevState => {
      const newState = {
        ...prevState,
        [medium.id]: true
      };
      return newState;
    });
  
    deleteMedium(medium.id);
  }, [deleteMedium]);

  // 从本地媒体列表中移除已删除的媒体
  const removeLocalMedia = useCallback((mediaId) => {
    const {
      stockMedia,
      streetMedia,
      buildingMedia,
    } = localMediaRef.current;

    // 使用新的更新媒体列表函数
    const removeMediaById = (mediaData = {}, mediaId, mediaListType='stock') => {
      for (const key in mediaData) {
        if (Array.isArray(mediaData[key])) {
          const mediaArray = mediaData[key];
          const foundIndex = mediaArray.findIndex(m => m.id === mediaId);

          if (foundIndex !== -1) {
            // 创建不包含被删除媒体的新数组
            const updatedMedia = mediaArray.filter(m => m.id !== mediaId);
            
            // 返回一个函数，将在成功回调中调用
            return () => {
              // 根据媒体类型调用对应的新 action creator
              if (mediaListType === 'stock') {
                props.updateStockMediaList(key, updatedMedia);
                return true;
              } else if (mediaListType === 'building') {
                props.updateBuildingMediaList(key, updatedMedia);
                return true;
              } else if (mediaListType === 'street') {
                props.updateStreetMediaList(key, updatedMedia);
                return true;
              }
            };
          }
        }
      }
      return null;
    };

    // 保存更新函数以供后续使用
    const stockUpdate = removeMediaById(stockMedia, mediaId, 'stock');
    if (stockUpdate) return stockUpdate;
    
    const buildingUpdate = removeMediaById(buildingMedia, mediaId, 'building');
    if (buildingUpdate) return buildingUpdate;
    
    return removeMediaById(streetMedia, mediaId, 'street');
  }, [props]);

  // 存储当前被删除媒体的更新函数
  const [mediaUpdateCallback, setMediaUpdateCallback] = useState(null);

  useEffect(() => {
    // 检查所有正在更新的媒体
    Object.keys(updatingMedia).forEach(mediaId => {
      // 查找当前medium对应的progressingMedia
      const currentProgressingMedia = progressingMedia?.find(pm => pm.id === mediaId);

      // 如果状态为done或error，说明请求已完成
      if (currentProgressingMedia &&
        (currentProgressingMedia.state === "done" || currentProgressingMedia.state === "error")) {

        // 更新状态，移除已完成的媒体
        setUpdatingMedia(prevState => {
          const newUpdatingMedia = { ...prevState };
          delete newUpdatingMedia[mediaId];
          return newUpdatingMedia;
        });

        if (currentProgressingMedia.state === "done") {
          updateLocalMedia(currentProgressingMedia);
        }
      }
    });
    
    // 检查所有正在删除的媒体
    Object.keys(deletingMedia).forEach(mediaId => {
      // 查找当前medium对应的progressingMedia
      const currentProgressingMedia = progressingMedia?.find(pm => pm.id === mediaId);

      // 如果状态为done或error，说明请求已完成
      if (currentProgressingMedia &&
        (currentProgressingMedia.state === "done" || currentProgressingMedia.state === "error")) {

        // 更新状态，移除已完成的媒体
        setDeletingMedia(prevState => {
          const newDeletingMedia = { ...prevState };
          delete newDeletingMedia[mediaId];
          return newDeletingMedia;
        });

        if (currentProgressingMedia.state === "done") {
          // 删除成功，获取更新函数但不立即执行
          const updateFn = removeLocalMedia(mediaId);
          if (updateFn) {
            setMediaUpdateCallback(() => updateFn);
          }
        }
      }
    });
  }, [progressingMedia, updatingMedia, deletingMedia, updateLocalMedia, removeLocalMedia]);

  // 删除对话框的成功回调
  const handleDeleteSuccess = useCallback(() => {
    // 执行媒体列表更新
    if (mediaUpdateCallback) {
      mediaUpdateCallback();
      clearProgressingMediumById(currentMedium.id);
      setMediaUpdateCallback(null);
    }
  }, [mediaUpdateCallback]);

  if (isApproveMediaPage) {
    return null;
  }

  const { id, status, tags, approval, markByWWW, scoring, manualOffline } = medium;
  const isWWWPhoto = status === "valid"; //網標籤
  const isMainPhoto = (tags || []).includes("main"); //主標籤
  const isMarkByWWW = markByWWW; //計標籤

  const isPersonal = (medium.approval === "waiting" || medium.approval === "rejected");
  const isValid = medium.status === "valid";
  const isVideo = medium.type === "video";
  const isKolVideo = medium.type === "kol_video";
  const isDocument = medium.type === "document";

  const isPrivate = (medium.approval === "pending" || medium.approval === "rejected");
  const isNotApproved = (medium.approval === "waiting");

  // remove the file extension
  let bits = (medium.originalFilename || "").split(".");
  if (bits.length > 1) bits = bits.slice(0, bits.length - 1);
  let name = bits.join(".");
  if (medium.description) { // (isKolVideo || isDocument) {
    name = medium.description;
  }

  let globeButtonClass = "";
  let showGlobeButton = true;
  let updateData = {}
  let allowAction = true;
  if ((!scoring && status === "valid") || (scoring === true && status === "valid" && markByWWW === true && !manualOffline)) {
    globeButtonClass = classes.yellowButton;
    updateData = {
      status: "invalid",
      sqsProcessing: null,
    }
  } else if (scoring === true && status === "invalid" && !markByWWW && manualOffline === true) {
    globeButtonClass = classes.redButton;
    updateData = {
      manualOffline: false,
      sqsProcessing: null,
    }
  } else if ((!scoring && status === "invalid") || (scoring === true && status === "invalid" && !markByWWW && !manualOffline)) {
    globeButtonClass = classes.greyButton;
    if(scoring === true){
      allowAction = false;
    } else {
      updateData = {
        status: "valid",
      }
    }
  } else {
    showGlobeButton = false;
  }

  const deleteButton = () => (<>
    <div 
      className={classes.deleteButton} 
      onClick={() => handleClickOpenDialog(medium)} 
      style={{ pointerEvents: "auto" }}
    >
      <DeleteRoundedIcon style={{ width: 17, height: 17 }} />
    </div>
    <SubmitDialog
      dialogOpen={dialogOpen}
      handleCloseDialog={handleCloseDialog}
      submitting={currentMedium ? !!deletingMedia[currentMedium.id] : false}
      submitted={currentMedium && progressingMedia?.find(pm => pm.id === currentMedium.id)?.state === "done"}
      error={currentMedium && progressingMedia?.find(pm => pm.id === currentMedium.id)?.state === "error"}
      submit={() => {
        if (currentMedium) {
          handleDelete(currentMedium);
        }
      }}
      submitBtnText={intl?.formatMessage({ id: "common.ok" })}
      succMsg={intl?.formatMessage({ id: "media.delete.success" })}
      succCallback={handleDeleteSuccess}
    >
      <div style={{ textAlign: "center" }}>
        {intl?.formatMessage(
          { id: "media.delete.confirm" }, 
          { filename: currentMedium?.description || currentMedium?.originalFilename || "" }
        )}
      </div>
    </SubmitDialog>
  </>);
  if (isPrivate || isNotApproved) {
    return (
      <div className={classes.root}>
        {(isVideo || isKolVideo) && <PlayCircleOutlineIcon className={classes.typeIcon} />}
        <div className={classes.tagContainer}>
          {isPrivate && <div className={clsx(classes.greyButton, classes.autoWidth, classes.newTag)}>
            <span>私人</span>
          </div>}
          {isNotApproved && <div className={clsx(classes.redButton, classes.autoWidth, classes.newTag)}>
            <span>未批核</span>
          </div>}
          {(isPrivate || isNotApproved || PERMISSION_REMOVE_MEDIA) && deleteButton()}
        </div>
        {name && <div className={clsx(classes.name)}>
          {name}
        </div>}
      </div>
    );
  }

  return (
    <div className={classes.root}>
      {isVideo && <PlayCircleOutlineIcon className={classes.typeIcon} />}
      {isKolVideo && <PlayCircleOutlineIcon className={classes.typeIcon} />}

      {!isPersonal && <>
        <div className={classes.tagContainer}>
        {/*
        {isPersonal && <div className={`${classes.tag} ${classes.personal}`}>
          <PersonIcon className={classes.tagIcon} />
        </div>}
        {isWWWPhoto && <div className={`${classes.tag} ${classes.personal} ${classes.yellowButton}`}>網</div>}
        */}
        {showGlobeButton &&<div
          onClick={!PERMISSION_SET_WWW_PHOTO ? undefined : () => allowAction && updateWWWPhoto({id, ...updateData})}
          className={clsx(classes.newTag, globeButtonClass, classes.languageIconContainer)}
          style={{ pointerEvents: "auto" }}
        >
          <LanguageIcon className={classes.languageIcon} />
        </div>}
        {<div className={clsx(classes.newTag, isMainPhoto ? classes.yellowButton : classes.greyButton)} style={{ pointerEvents: "auto" }}>主</div>}
        {(scoring === true && !manualOffline) && <div className={`${classes.newTag} ${isMarkByWWW ? classes.orangeButton : classes.greyButton}`} style={{ pointerEvents: "auto" }}>計</div>}
        {(isPrivate || isNotApproved || PERMISSION_REMOVE_MEDIA) && deleteButton()}
        </div>

        {name && <div className={clsx(classes.name)}>
          {name}
        </div>}
      </>}
    </div>
  );
};

MediumProperties.propTypes = {
  classes: PropTypes.object.isRequired,
  medium: PropTypes.object.isRequired,
  progressingMedia: PropTypes.array,
  buildingMedia: PropTypes.object,
  stockMedia: PropTypes.object,
  streetMedia: PropTypes.object,
  updateBuildingMediaByIndex: PropTypes.func,
  updateStockMediaByIndex: PropTypes.func,
  updateStreetMediaByIndex: PropTypes.func,
  updateStockMediaList: PropTypes.func,
  updateBuildingMediaList: PropTypes.func,
  updateStreetMediaList: PropTypes.func,
  PERMISSION_SET_WWW_PHOTO: PropTypes.bool,
};

const mapDispatchToProps = dispatch => ({
  updateMedium: (medium) => dispatch(updateMedium(medium)),
  updateBuildingMediaByIndex: (index) => dispatch(updateBuildingMediaByIndex(index)),
  updateStockMediaByIndex: (index) => dispatch(updateStockMediaByIndex(index)),
  updateStreetMediaByIndex: (index) => dispatch(updateStreetMediaByIndex(index)),
  deleteMedium: (medium) => dispatch(deleteMedium(medium)),
  updateStockMediaList: (mediaType, mediaList) => dispatch(updateStockMediaList(mediaType, mediaList)),
  updateBuildingMediaList: (mediaType, mediaList) => dispatch(updateBuildingMediaList(mediaType, mediaList)),
  updateStreetMediaList: (mediaType, mediaList) => dispatch(updateStreetMediaList(mediaType, mediaList)),
  clearProgressingMediumById: (mediaId) => dispatch(clearProgressingMediumById(mediaId)),
  getMedium: (mediaId, customQuery) => dispatch(getMedium(mediaId, customQuery)),
  getMediumByIds: (mediaIds) => dispatch(getMediumByIds(mediaIds)),
  dispatch,
});

const hasPermissionSetWWWPhoto = (state = {}) => {
  const hasSetWWWPhoto = _.get(state, `employee.permissions.${SET_WWW_PHOTO}`) || false;
  if (hasSetWWWPhoto) { return hasSetWWWPhoto; }
  return _.findIndex(
    _.get(state, "wwwStock.usedCount.markWWWList", []),
    markWWW => markWWW.stockId === _.get(state, "stock.detail.0._id"),
  ) !== -1;
};

const hasPermissionRemoveMedia = (state = {}) => {
  const hasRemoveMedia = _.get(state, `employee.permissions.${REMOVE_MEDIA}`) || false;
  return hasRemoveMedia;
};

const mapStateToProps = (state) => ({
  isApproveMediaPage: state.medium.isApproveMediaPage,
  progressingMedia: state.medium.progressingMedia,

  stockMedia: _.get(state, "stock.media[0].data", {}),
  buildingMedia: _.get(state, "building.media[0].data", {}),
  streetMedia: _.get(state, "street.media[0].data", {}),
  PERMISSION_SET_WWW_PHOTO: hasPermissionSetWWWPhoto(state),
  PERMISSION_REMOVE_MEDIA: hasPermissionRemoveMedia(state),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(MediumProperties))); 
