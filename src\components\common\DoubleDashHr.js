import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    height: 1,
    borderTop: "1px dashed #C1C1C1",
    borderBottom: "1px dashed #C1C1C1"
  }
};

function DoubleDashHr(props) {
  const { classes, className, ...other } = props;

  return (
    <div className={`${classes.root} ${className}`} {...other} />
  );
}

DoubleDashHr.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string
};

export default withStyles(styles)(DoubleDashHr);
