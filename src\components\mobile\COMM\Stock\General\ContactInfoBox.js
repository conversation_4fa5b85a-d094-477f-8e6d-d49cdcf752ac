import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import _ from "lodash";
import { withStyles } from "@material-ui/core/styles";
import SearchIcon from "@material-ui/icons/Search";
import FieldValArrBox from "../../../../common/FieldValArrBox";
import Grid from "@material-ui/core/Grid";
import { injectIntl } from "react-intl";
import CallItem from "../../../../common/CallItem";
import FormButtonInline from "../../../../common/FormButtonInline";
import {
  goToSearchResult,
  consolidateType,
} from "../../../../../helper/generalHelper";
import { clearStock } from "../../../../../actions/stock";
import { clearStockList } from "../../../../../actions/stocklist";
import { enableConsolidLandSearch } from "../../../../../config";

// We can inject some CSS into the DOM.
const styles = {
  root: {},
  phonesContainer: {
    paddingTop: 8,
  },
  contact: {
    display: "flex",
    alignItems: "center",
    "& > *:nth-child(2)": {
      marginLeft: "2vw",
      flex: "0 0 auto",
    },
  },
};

function ContactInfoBox(props) {
  const {
    classes,
    company,
    companyId,
    contactName,
    contactTitle,
    phones = [],
    mongoId,
    stockId,
    clearStockDetail,
    clearStockList,
    deniedCalls,
    intl,
    ...others
  } = props;

  let contactNameFull =
    contactTitle && contactName
      ? contactTitle + " " + contactName
      : contactName || "---";
  if (contactName === "false") contactNameFull = "---";

  let CallItems = phones.length > 0 && (
    <Grid container spacing={1} className={classes.phonesContainer}>
      {phones.map((v, i) => (
        <Grid item xs={6} key={i}>
          <CallItem
            type={v.type}
            number={v.number}
            mongoId={mongoId}
            stockId={stockId}
            isDeniedCall={_.get(deniedCalls, v.number) || false}
          />
        </Grid>
      ))}
    </Grid>
  );

  const query = {
    company: [
      {
        value: companyId,
        label: company,
      },
    ]
      .filter((v) => v.value && v.label)
      .map((v) => v.value),
    person: [contactName].filter((v) => v && v !== "false"),
    phone: _.uniqBy(phones, "number").map((v) => v.number),
    limit: 50,
    offset: 0,
    ...consolidateType,
  };
  const selectedData = {
    company: [
      {
        value: companyId,
        label: company,
      },
    ].filter((v) => v.value && v.label),
    person: [contactName]
      .filter((v) => v && v !== "false")
      .map((v) => {
        return {
          value: v,
          label: v,
        };
      }),
    phone: _.uniqBy(phones, "number").map((v) => {
      return {
        value: v.number,
        label: v.number,
      };
    }),
  };
  const goToSearchContactResult = () => {
    // console.log(query)
    // console.log(selectedData)
    clearStockDetail();
    clearStockList(false);
    goToSearchResult(query, selectedData, true, "consolidate");
  };

  const consolidateSearchBtn = enableConsolidLandSearch == "true" &&
    (query.company.length > 0 ||
      query.person.length > 0 ||
      query.phone.length > 0) && (
      <FormButtonInline onClick={goToSearchContactResult} icon={<SearchIcon />}>
        {intl.formatMessage({
          id: "stock.consosearch",
        })}
      </FormButtonInline>
    );

  const items = [
    {
      field: intl.formatMessage({ id: "stock.vendor" }),
      val: company || "---",
    },
    {
      field: intl.formatMessage({ id: "stock.contact" }),
      val: (
        <div className={classes.contact}>
          <div>{contactNameFull}</div>
          {consolidateSearchBtn}
        </div>
      ),
      extra: CallItems,
    },
  ];

  return <FieldValArrBox className={classes.root} items={items} {...others} />;
}

ContactInfoBox.propTypes = {
  classes: PropTypes.object.isRequired,
  company: PropTypes.string,
  companyId: PropTypes.string,
  contactName: PropTypes.string,
  contactTitle: PropTypes.string,
  phones: PropTypes.arrayOf(PropTypes.object),
  mongoId: PropTypes.string,
  stockId: PropTypes.number,
  deniedCalls: PropTypes.object,
};

const mapDispatchToProps = (dispatch) => {
  return {
    clearStockDetail: () => dispatch(clearStock()),
    clearStockList: (...args) => dispatch(clearStockList(...args)),
  };
};

export default connect(
  null,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(ContactInfoBox)));
