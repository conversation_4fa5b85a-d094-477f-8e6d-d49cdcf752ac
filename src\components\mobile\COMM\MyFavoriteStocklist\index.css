/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

.root {
  padding: 1vw 3vw;
  background-color: #fff;
}

.bg {
  width: 100%;
  height: 100vh;
  background-image: url(../../../../files/bg/comm_watermark_result_list.png);
  background-repeat: no-repeat;
  background-position: center;
  background-color: #F5F5F5;
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
}

.card {
  padding: 1vw 2vw;
}

.count {
  text-align: center;
  padding: 1vw 3vw;
  background-color: #fff;
}

.container {
  margin: 0 auto;
  padding: 20px 0;
  max-width: var(--max-content-width);
}
