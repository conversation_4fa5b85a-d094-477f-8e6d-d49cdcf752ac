import { createMuiTheme } from "@material-ui/core/styles";

const style = (theme) => ({
  root: {
    paddingLeft: 20,
    paddingRight: 20,
  },

  container: {
    margin: "0 auto",
    padding: "0 0 40px",
    maxWidth: "var(--max-content-width)",
    paddingTop: 10,
  },

  sectionBox: {
    marginBottom: "5vh",
  },

  sectionTitle: {
    fontSize: "1.125em",
    fontWeight: 700,
    marginBottom: 10,
    color: "rgb(119, 119, 119)",
  },

  sectionTitleWithStrike: {
    fontSize: "1.125em",
    fontWeight: 700,
    marginBottom: 20,
    color: "rgb(119, 119, 119)",
    overflow: "hidden",
    whiteSpace: "nowrap",
    flex: 1,
    "& > div": {
      position: "relative",
      display: "inline-flex",
      alignItems: "center",
      "&::after": {
        content: '""',
        position: "absolute",
        top: "calc(50% - 1px)",
        width: 9999,
        height: 1,
        background: "#C1C1C1",
        left: "100%",
        marginLeft: "1vw",
      },
    },
  },

  descriptionRow: {
    display: "flex",
    margin: "10px auto",
  },

  propertyTag: {
    minWidth: 38,
    maxHeight: 20,
    color: "#000",
    fontSize: "0.8em",
    textAlign: "center",
    borderRadius: 4,
    padding: "1px 3px",
    backgroundColor: "#ffd905",
    display: "inline-block",
  },

  propertyTagRed: {
    minWidth: 38,
    maxHeight: 20,
    color: "#fff",
    fontSize: "0.8em",
    textAlign: "center",
    borderRadius: 4,
    padding: "1px 3px",
    backgroundColor: "#b00f15",
    display: "inline-block",
  },

  tagDescription: {
    paddingLeft: 20,
    fontSize: "1.125em",
  },

  font18: {
    fontSize: "1.125em",
  },

  orderlistText: {
    marginLeft: -20,
    "& li": {
      fontSize: "1.125em",
      margin: "10px auto",
    },
  },

  //   ol.orderlistText li,
  //   ul.orderlistText li {
  //     font-size: 1.125em
  //     margin: 10px auto
  //   },

  secondOrderContainer: {
    marginLeft: -40,
    "& li": {
      fontSize: "1em !important",
    },
  },

  //   .secondOrderContainer li {
  //     font-size: 1em !important
  //   }

  districtOrder: {
    fontSize: "1.125em",
    "& li": {
      margin: "10px auto",
    },
  },

  //   ol.districtOrder li {
  //     margin: 10px auto
  //   }

  paragraph: {
    margin: "10px auto",
  },

  imgBox: {
    display: "block",
    textAlign: "center",
  },

  increasing: {
    color: "#069f4d",
    transform: "rotate(-90deg)",
  },

  decreasing: {
    color: "#ec1f27",
    transform: "rotate(90deg)",
  },

  link: {
    textDecoration: "none",
  },

  usermanualbtn: {
    fontSize: "1em",
    fontWeight: 600,
    textTransform: "capitalize",
  },

  tutorialVideo: {
    marginBottom: "3vh",
  },

  btnRow: {
    textAlign: "left",
    marginBottom: "3vh",
  },

  searchicon: {
    width: 32,
    height: 36,
    backgroundColor: theme.base.backgroundColor,
    color: "#fff",
  },
});

export { style };
