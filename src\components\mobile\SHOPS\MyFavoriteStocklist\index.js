import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import withStyles from "isomorphic-style-loader/lib/withStyles";
import InfiniteScroll from "react-infinite-scroll-component";
import s from "./index.css";
import {
  listStockList,
  getUnlockCount,
  clearStockList,
} from "../../../../actions/stocklist";
import SearchResultCard from "../Search/SearchResultCard";
import LoadingOverlay from "../../../LoadingOverlay";
import config from "../../../../config";
import StockListCount from "../../../common/StockListCount";
import { clearStock, listmyFavorite } from "../../../../actions/stock";
import { injectIntl } from "react-intl";
import _ from "lodash";

class UnlockedStocklist extends React.Component {
  static propTypes = {
    stocks: PropTypes.array,
    unlockedStockIds: PropTypes.array,
    favoriteStockIds: PropTypes.array,
    listed: PropTypes.bool,
    listing: PropTypes.bool,
    count: PropTypes.number,
    getUnlockCount: PropTypes.func.isRequired,
    listStockList: PropTypes.func.isRequired,
    clearStockDetail: PropTypes.func.isRequired,
    clearStockList: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
  }

  firstFetch = (variables) => {
    let options = {
      isFetchingMore: false,
      isUnlockPage: true,
      intl: this.props.intl,
    };
    this.props.listStockList(variables, options);
  };

  checkQueryAndFetch = () => {
    let query;
    console.log(this.props.favoriteStockIds);
    if (
      Array.isArray(this.props.favoriteStockIds) &&
      this.props.favoriteStockIds.length > 0
    ) {
      query = {
        stockIds: this.props.favoriteStockIds,
        limit: this.props.maxQuota, //config.generalDailyQuota,
        offset: 0,
      };
      this.firstFetch(query);
    }
  };

  componentDidMount() {
    this.props.clearStockList(true);
    this.props.listmyFavorite();
    if (this.props.stock && this.props.stock._id && this.props.listed) {
      this.props.clearStockDetail();
    } else {
      this.props.getUnlockCount();
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.favoriteStockIds !== this.props.favoriteStockIds) {
      this.checkQueryAndFetch();
    }
  }

  fetchMoreData = () => {
    const { stocks, favoriteStockIds, maxQuota, intl } = this.props;
    const query = {
      stockIds: favoriteStockIds,
      limit: maxQuota,
      offset: stocks.length,
    };
    const options = {
      isFetchingMore: true,
      isUnlockPage: true,
      intl,
    };
    this.props.listStockList(query, options);
  };

  render() {
    const { listed, listing, stocks, unlockedStockIds, favoriteStockIds } =
      this.props;

    const count =
      Array.isArray(favoriteStockIds) && favoriteStockIds.length > 0
        ? favoriteStockIds.length
        : 0;

    return (
      <div>
        <div className={s.bg} />
        <StockListCount stocksCount={count} />
        {listed ? (
          <div className={s.card}>
            <InfiniteScroll
              dataLength={stocks.length}
              next={this.fetchMoreData}
              hasMore={stocks.length < count}
              style={{ overflowY: "hidden" }}
              endMessage={<p></p>}
            >
              {stocks.map((stock, index) => {
                let isUnlocked =
                  unlockedStockIds.indexOf(stock._id) < 0 ? false : true;
                return (
                  <div key={index}>
                    <SearchResultCard result={stock} isUnlocked={isUnlocked} />
                  </div>
                );
              })}
            </InfiniteScroll>
          </div>
        ) : null}

        {listing && <LoadingOverlay />}
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  user: state.auth.user ? state.auth.user : [],
  stocks: state.stocklist.unlockedStocks ? state.stocklist.unlockedStocks : [],
  unlockedStockIds: state.stocklist.unlockedStockIds
    ? state.stocklist.unlockedStockIds
    : [],
  favoriteStockIds: state.stock.favoriteStockIds
    ? state.stock.favoriteStockIds
    : [],
  listed: state.stocklist.unlockedListed
    ? state.stocklist.unlockedListed
    : false,
  listing: state.stocklist.unlockedListing
    ? state.stocklist.unlockedListing
    : false,
  stock: state.stock.detail ? state.stock.detail : null,
  maxQuota: !_.isEmpty(state.employee.permissions)
    ? !_.isNil(
        state.employee.permissions[
          config.permissions.PERMISSION_VIEW_STOCK_QUOTA
        ],
      )
      ? state.employee.permissions[
          config.permissions.PERMISSION_VIEW_STOCK_QUOTA
        ]
      : 0
    : config.generalDailyQuota,
});

const mapDispatchToProps = (dispatch) => {
  return {
    listStockList: (...args) => dispatch(listStockList(...args)),
    clearStockList: (...args) => dispatch(clearStockList(...args)),
    getUnlockCount: () => dispatch(getUnlockCount()),
    clearStockDetail: () => dispatch(clearStock()),
    listmyFavorite: () => dispatch(listmyFavorite()),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(s)(injectIntl(UnlockedStocklist)));
