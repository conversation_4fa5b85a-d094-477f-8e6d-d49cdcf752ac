import React from "react";
import PropTypes from "prop-types";
import { createStyles, withStyles } from "@material-ui/styles";
import { injectIntl } from "react-intl";
import DeleteOutlineIcon from "@material-ui/icons/DeleteOutline";
import { sbu } from "../../../config";
import Popup from "./Popup";
import FormButton from "../FormButton";
import moment from "moment";
import ReactPlayer from 'react-player';
import { LightboxButtonInjector } from "./Lightbox";
import { connect } from "react-redux";
import { updateMedium } from "@/actions/medium";
import { dateFormatter } from "@/helper/generalHelper";
import _ from "lodash";

const styles = createStyles({
  popup: {
    width: "100%",
  },
  youtubeVideoContainer: {
    position: "relative",
    paddingBottom: "56.25%" /* 16:9 */,
    paddingTop: "2vh",
    height: 0,
    "& > iframe": {
      position: "absolute",
      top: 0,
      left: 0,
      width: "100%",
      height: "100%",
    },
  },
  externalVideoContainer: {
    position: "relative",
    paddingBottom: "56.25%" /* 16:9 */,
    backgroundColor: "rgba(0, 0, 0, .2)"
  },
  externalPlayer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: "100% !important",
    height: "100% !important",
  },
  videoContainer: {
    position: "relative",
    backgroundColor: "rgba(0, 0, 0, .2)"
  },
  processingOverlay: {
    width: "100%",
    height: "100%",
    color: "#FFF",
    fontSize: "1.25em",
    textAlign: "center",
    position: "absolute",
    top: 0,
    left: 0,
  },
  video: {
    width: "100%",
  },
  caption: {
    wordBreak: "break-word",
  },
  videoBtnRow: {
    marginTop: "3vh",
    display: "flex",
    flexWrap: "wrap",
    gap: "2vw",
    justifyContent: "center",
    // "& > *:not(:last-child)": {
    //   paddingRight: "2vw",
    // },
  },
  link: {
    textDecoration: "none",
    display: "flex",
    gap: "1rem",
  },
  deleteIcon: {
    width: 30,
    height: 30,
    color: "#FFF",
  },
  downloadButton: {
    height: "48px",
    width: "150px",
    lineHeight: "1.25",
  },
  downloadButton2: {
    height: "60px",
    width: "126px",
    lineHeight: "1.25",
    minWidth: "126px",
  },
  customButtonContainer: {
    position: "absolute",
    width: "60%",
    padding: "0 20%",
    // bottom: "-40px",
    top: "calc(100% + 20px)",
    left: 0,
    display: 'flex',
    justifyContent: 'center',
    zIndex: 1001,
    "& button": {
      paddingTop: "20px",
      paddingBottom: "20px",
    },
  },
});

class VideoPopup extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    video: PropTypes.object,
    handlePopupClose: PropTypes.func.isRequired,
    deletable: PropTypes.bool,
    handleOpenDeleteMediaDialog: PropTypes.func,
    renderCustomButton: PropTypes.func,
  };

  constructor(props) {
    super(props);
    this.handleShareToPublic = this.handleShareToPublic.bind(this);
    this.state = {
      isInvalidUrl: false,
    };
  }

  handleShareToPublic() {
    const { updateMedium, video } = this.props;
    if (!video?.id) return;
    updateMedium({
      id: video.id,
      approval: "waiting",
    });
  };

   // Determine if Vimeo player should be used
  isExternalVideo = (props) => {
    const { video } = props;
    return video.vimeoId && !this.state.isInvalidUrl;
  };

  renderVideoContent = (props) => {
    const { video, videoFilename, classes } = props;

    if (this.isExternalVideo(props)) {
      const url = video.vimeoId ? `https://vimeo.com/${video.vimeoId}` : "";

      return (
        <ReactPlayer
          url={url}
          controls
          className={classes.externalPlayer}
          playsinline
          onError={(e) => {
            console.error('video error:', e);
            this.setState({ isInvalidUrl: true });
          }}
        />
      );
    }

    return (
      <video className={classes.video} controls controlsList={video.approval !== "approved" ? 'nodownload' : ''}>
        <source src={`${video.mediumRoot}/${videoFilename}#t=0.001`} type="video/mp4" />
      </video>
    );
  };

  render() {
    const {
      classes,
      video,
      handlePopupClose,
      deletable,
      handleOpenDeleteMediaDialog,
      intl,
      renderCustomButton,
    } = this.props;

    const videoFilename = sbu === "SHOPS" ? "original.mp4" : "mr_video.mp4";
    const isPersonal = video.approval !== "approved";

    const sanitizedFilename = () => {
      const sanitizeRegex = /[^\u4e00-\u9fa5a-zA-Z0-9.-]/g;
      const fileExtension = video.originalFilename.split('.').pop() || '';
      const baseName = (video.description || video.originalFilename).replace(`.${fileExtension}`, '');
      return `${baseName}.${fileExtension}`.replace(sanitizeRegex, '_');
    }

    return (
      <Popup
        className={classes.popup}
        onCloseRequest={handlePopupClose}
        caption={
          this.props.isApproveMediaPage ? <>
            {(video.buildingName && video.propertyRefId) && (
              <div className={classes.caption}>
                {"樓盤: "}
                {video.buildingName} ({video.propertyRefId})
              </div>
            )}
            {(video.createdDate && video.employee) && (
              <div className={classes.caption}>
                {"擁有人: "}
                {dateFormatter(video.createdDate || "")} {video.employee?.branchId} {intl.locale === 'zh' ? video.employee?.cName : video.employee?.eName}
              </div>
            )}
            {(video.type) && (
              <div className={classes.caption}>
                {"多媒體類型: "}
                {video.type === "kol_video" ? "KOL" : "VIDEO"}
              </div>
            )}
            {(video.originalFilename) && (
              <div className={classes.caption}>
                {"名稱: "}
                {video.originalFilename}
              </div>
            )}
          </> : <>
            {video.propertyRefId && (
              <div className={classes.caption}>
                {"樓盤ID"}
                {": "}
                {video.propertyRefId}
              </div>
            )}
            {video.buildingId && (
              <div className={classes.caption}>
                {"大廈ID"}
                {": "}
                {video.buildingId}
              </div>
            )}
            <div className={classes.caption}>
              {intl.formatMessage({ id: "stock.photo.name" })}
              {": "}
              {video.originalFilename}
            </div>
            <div className={classes.caption}>
              {"上傳者"}
              {": "}
              {video?.operator?.eName || ""}
            </div>
            <div className={classes.caption}>
              {"上傳時間"}
              {": "}
              {moment(video.createdDate).format("HH:mm:ss YYYY-MM-DD")}
            </div>
          </>
        }
        toolbarButtons={
          deletable && isPersonal
            ? [
              <DeleteOutlineIcon
                className={classes.deleteIcon}
                onClick={() => handleOpenDeleteMediaDialog(video.id)}
              />
            ]
            : []
        }
      >
        {/* {video.youtubeId || video.youtubeMrId ? (
          <div className={classes.youtubeVideoContainer}>
            <iframe
              src={
                "https://www.youtube.com/embed/" +
                (video.youtubeId || video.youtubeMrId) +
                "?rel=0&wmode=transparent"
              }
              frameBorder="0"
              allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          </div>
        ) : ( */}
          <div className={this.isExternalVideo(this.props) ? classes.externalVideoContainer : classes.videoContainer}>
            {this.renderVideoContent({...this.props, videoFilename})}
            {video.processing !== "done" && <div className={classes.processingOverlay}>
              {intl.formatMessage({ id: "stock.video.processing" })}
            </div>}

            {typeof renderCustomButton !== "function" ? null : <div className={classes.customButtonContainer}>
              {renderCustomButton(video)}
            </div>}
          </div>
        {/* )} */}
        {video.approval !== "waiting" && (["kol_video", "video"].includes(video.type)) &&
          <div className={classes.videoBtnRow}>
            {video.approval === "approved" && <>
              {video.youtubeId || video.youtubeMrId ? (
                <a className={classes.link} href={`https://www.youtube.com/embed/${video.youtubeId || video.youtubeMrId}?rel=0&wmode=transparent`}>
                  <FormButton className={(video.youtubeId || video.youtubeMrId) ? classes.downloadButton2 : classes.downloadButton}>Open Youtube</FormButton>
                </a>
              ) : null}
              <a className={classes.link} href={`/asset/video?mediumRoot=${encodeURIComponent(video.mediumRoot)}&filename=${encodeURIComponent(videoFilename)}`} download={`${sanitizedFilename()}`}>
                <FormButton className={(video.youtubeId || video.youtubeMrId) ? classes.downloadButton2 : classes.downloadButton}>Download Video</FormButton>
              </a>
              <a className={classes.link} href={`/asset/video?mediumRoot=${encodeURIComponent(video.mediumRoot)}&filename=${encodeURIComponent(video.filename)}`} download={`${sanitizedFilename()}`}>
                <FormButton className={(video.youtubeId || video.youtubeMrId) ? classes.downloadButton2 : classes.downloadButton}>Download Video Without Watermark</FormButton>
              </a>
            </>}
            {(video.approval === "pending" || video.approval === "rejected") &&
              <FormButton className={classes.downloadButton} onClick={this.handleShareToPublic}>
                {intl.formatMessage({ id: "media.update.shareToPublic" })}
              </FormButton>
            }
          </div>
        }
      </Popup>
    );
  }
}

const mapStateToProps = state => ({
  // ref: https://stackoverflow.com/questions/48819138/how-do-you-get-syncerrors-out-of-state-using-redux-form-selectors
  isApproveMediaPage: state.medium.isApproveMediaPage,
});

/** @param {React.Dispatch<any>} dispatch */
const mapDispatchToProps = (dispatch) => ({
  updateMedium: (...args) => dispatch(updateMedium(...args)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(VideoPopup)));
