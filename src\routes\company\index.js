import React from "react";

const title = "Company";

async function action({ store, params }) {
  const { auth } = store.getState();
  if (!auth.user || !auth.user.authorized) {
    return { redirect: "/login" };
  }

  const CompanyPage = await require.ensure(
    [],
    (require) => require("./CompanyPage").default,
    "companyPage",
  );

  return {
    chunks: ["company"],
    title,
    component: <CompanyPage id={params.id} />,
  };
}

export default action;
