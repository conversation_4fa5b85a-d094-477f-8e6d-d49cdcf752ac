import {
  LIST_TX_REPORTS_START,
  LIST_TX_REPORTS_SUCCESS,
  LIST_TX_REPORTS_NULL_SUCCESS,
  LIST_TX_REPORTS_ERROR,
  CLEAR_TX_REPORTS
} from "../constants/listTxReport";

import _ from "lodash";

export function listTxReports() {
  return async (
    dispatch,
    getState,
    { delay, universalRequest }
  ) => {
    dispatch({
      type: LIST_TX_REPORTS_START,
      checkrefreshToken: true
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          "Authorization": getState().auth.user.oauth,
          "CAS-Authorization":  localStorage.getItem('casAccessToken')
        }//getState().auth.user.casAccessToken }
      };

      const query = `query {
        largeAmountTxReports {
          date
          reportName
          reportNameZh
          downloadLink
        }
      }`;;

      const resp = await universalRequest("/transaction/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (resp.errors) {
        throw new Error(resp.errors[0].message);
      }

      const { data } = resp;

      if (data.largeAmountTxReports && data.largeAmountTxReports.length > 0) {
        dispatch({
          type: LIST_TX_REPORTS_SUCCESS,
          payload: {
            data
          }
        });
      } else {
        dispatch({
          type: LIST_TX_REPORTS_NULL_SUCCESS,
          payload: {
            data
          }
        });
      }
    } catch (error) {
      dispatch({
        type: LIST_TX_REPORTS_ERROR,
        payload: {
          error
        }
      });
      // throw new Error(error);
    }
  };
}

export function clearTxReports() {
  return async dispatch => {
    dispatch({
      type: CLEAR_TX_REPORTS
    });
  };
}
