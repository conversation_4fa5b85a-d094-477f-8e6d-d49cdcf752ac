import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import FieldVal from "../../../../common/FieldVal";
import { getLangKey, numberComma } from "../../../../../helper/generalHelper";
import { injectIntl } from "react-intl";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import PropertyTag from "../../../../common/PropertyTag";
import _ from "lodash";

const styles = theme => ({
  root: {
    padding: "1vh 0",
  },
  tag: {
    fontSize: "0.8em",
    textAlign: "center",
    borderRadius: 4,
    padding: "1px",
    display: "inline-block",
    margin: 2,
    padding: "0px 10px",
  },
  lmrAlign: {
    paddingLeft: "2vw"
  }
});

class BasicProperty extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langKey = getLangKey(intl);
    const inspectionLangKey = intl.locale === "zh" ? "inspectionZh" : "inspection";
    const possessionLangKey = intl.locale === "zh" ? "possessionZh" : "possession";

    const unitViewName =
      detail.unitViews && detail.unitViews.length > 0
        ? detail.unitViews.map(unitView => _.get(unitView, langKey, '')).join(', ')
        : "---";
    const decoName =
      detail.decorations && detail.decorations.length > 0
        ? detail.decorations.map(decoration => _.get(decoration, langKey, '')).join(', ')
        : "---";
    const possession = detail[possessionLangKey] || "---";
    const availability = detail.availability || "---";
    const managementFee = detail.managementFee
      ? "$" + detail.managementFee + "/ft"
      : "---";
    const isIncludeAirConditioning = detail.isIncludeAirConditioning
    const photoUrl = detail.photoUrl || "---";
    const videoUrl = detail.videoUrl || "---";
    const vrUrl = detail.vrUrl || "---";
      // ? " A/C"
      // : "";
    const isIncludeManagementFee = detail.isIncludeManagementFee
      ? " Inclusive"
      : "";
    const keyNumber = detail.keyNumber || "---";
    const rates = detail.rates
      ? "$" + numberComma(detail.rates) + "/Qtr"
      : "---";
    const isIncludeRates = detail.isIncludeRates ? " Inclusive" : "";
    const governmentRent = detail.governmentRent
      ? "$" + numberComma(detail.governmentRent) + "/Qtr"
      : "---";
    const isIncludeGovernmentRent = detail.isIncludeGovernmentRent
      ? " Inclusive"
      : "";
    const completionDate = detail.completionDate || "---";
    const inspection = detail[inspectionLangKey] || "---";

    const sbutypeHeader = intl.formatMessage({
      id: "stock.sbutype",
    });
    const possessionHeader = intl.formatMessage({
      id: "stock.possession"
    });
    const viewHeader = intl.formatMessage({
      id: "stock.views"
    });
    const mgtfeeHeader = intl.formatMessage({
      id: "stock.mgtfee"
    });
    const ACHeader = intl.formatMessage({
      id: "proposal.form.mgtFeeOpenAC"
    });
    const PhotoUrlHeader = intl.formatMessage({
      id: "stock.photo"
    });
    const VideoUrlHeader = intl.formatMessage({
      id: "stock.video"
    });
    const VRUrlHeader = intl.formatMessage({
      id: "stock.vr"
    });
    const availabilityHeader = intl.formatMessage({
      id: "stock.availability"
    });
    const decorationHeader = intl.formatMessage({
      id: "stock.decorations"
    });
    const keynumpwHeader = intl.formatMessage({
      id: "stock.keynumpw"
    });
    const ratesHeader = intl.formatMessage({
      id: "stock.rates"
    });
    const grentHeader = intl.formatMessage({
      id: "stock.grent"
    });
    const completionHeader = intl.formatMessage({
      id: "stock.completion"
    });
    const inspectionHeader = intl.formatMessage({
      id: "stock.inspection"
    });

    let generalMapping = {
      [sbutypeHeader]: {
        value: intl.formatMessage({
          id: "common.commercial",
        }),
        xs: 6,
      },
      [possessionHeader]: { value: possession, xs: 6 },
      [availabilityHeader]: { value: availability, xs: 6 },
      [keynumpwHeader]: { value: keyNumber, xs: 6 },
      [inspectionHeader]: { value: inspection, xs: 6 },
      [completionHeader]: { value: completionDate, xs: 6 },
      [decorationHeader]: { value: decoName, xs: 6 },
      [viewHeader]: { value: unitViewName, xs: 6 },
      [mgtfeeHeader]: {
        value:
          managementFee + isIncludeManagementFee,
        xs: 6
      },
      [ACHeader]: { value: isIncludeAirConditioning, xs: 6 },
      [ratesHeader]: { value: rates + isIncludeRates, xs: 6 },
      [grentHeader]: { value: governmentRent + isIncludeGovernmentRent, xs: 6 },
      [PhotoUrlHeader]: { value: photoUrl, xs: 12 },
      [VideoUrlHeader]: { value: videoUrl, xs: 12 },
      [VRUrlHeader]: { value: vrUrl, xs: 12 },
    };

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.unit",
          })}
        >
          <Grid container spacing={2} className={classes.lmrAlign}>
            {Object.keys(generalMapping).map((v, i) => (
              <Grid item xs={generalMapping[v].xs} key={v}>
                {v === ACHeader &&
                  <div className={classes.tag} style={generalMapping[v].value ? {backgroundColor: "#FFD905", color: "#000"} : {backgroundColor: "#E3E3E3", color: "#888"}}>
                    {v}
                  </div>
                }
                {v !== ACHeader &&
                <FieldVal field={v}>
                  {/* <div
                    className={
                      generalMapping[v].className
                        ? generalMapping[v].className
                        : null
                    }
                  > */}
                  {(v === PhotoUrlHeader || v === VideoUrlHeader || v === VRUrlHeader) && generalMapping[v].value !== "---" ?
                    <a href={generalMapping[v].value} target="_blank" rel="noopener noreferrer">
                      {generalMapping[v].value}
                    </a>
                  : generalMapping[v].value}
                  {/* </div> */}
                </FieldVal>
                }
              </Grid>
            ))}
          </Grid>
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(BasicProperty));
