import React, { useState } from "react";
import { connect } from "react-redux";
import { Field, FieldArray, change } from "redux-form";
import { withStyles } from "@material-ui/core/styles";
import OutlinedInput from "@material-ui/core/OutlinedInput";
import Paper from "@material-ui/core/Paper";
import TextField from "@material-ui/core/TextField";

import DetailBoxSection from "../../../../common/DetailBoxSection";
import TextInput from "../../../../common/TextInput";
import TextInputCombined from "../../../../common/TextInputCombined";
import ArrayPillCheckBox from "../../../../common/ArrayPillCheckBox";
import PillCheckBox from "../../../../common/PillCheckBox";
import PillButton from "../../../../common/PillButton";
import TextArrayInput from "../../../../common/TextArrayInput";
import SelectFieldArrayOutput from "../../../../common/SelectFieldArrayOutput";
import ReactSelectCreatable from "../../../../common/ReactSelectCreatable";
import ChipsCheckBox from "../../../../common/ChipsCheckBox";
import AutoCompleteSelect from "../../../../common/AutoCompleteSelect";
import Search from "../../../../common/Search";
import Switch from "../../../../common/Switch";
import MuiSelectArrayOutput from "../../../../common/MuiSelectArrayOutput";
import InputWithCheckBoxCustom from "../../../../common/InputWithCheckBoxCustom";

import Grid from "@material-ui/core/Grid";
import Collapse from "@material-ui/core/Collapse";
import DatePicker from "../../../../common/DatePicker";
import { minValue, maxValue, number } from "../../../../../core/formValidators";
import InputAdornment from "@material-ui/core/InputAdornment";
import { injectIntl, FormattedMessage } from "react-intl";
import { listBuildings } from "../../../../../actions/building";
import { listStreets } from "../../../../../actions/street";
import { listCompanies } from "../../../../../actions/stocklist";
import { getLangKey } from "../../../../../helper/generalHelper";
import { enableConsolidLandSearch } from "../../../../../config";

// We can inject some CSS into the DOM.
const styles = {
  expendbutton: {
    textTransform: "none",
    display: "flex",
    marginLeft: "auto",
    color: "#33CCCC",
  },
  divider: {
    textAlign: "center",
    margin: "auto 0px",
  },
  Headertitle: {
    color: "rgba(0, 0, 0, 0.54)",
    fontSize: "1em",
    fontWeight: "400",
  },
  DetailBoxSectionContent: {
    paddingLeft: 0,
    paddingRight: 0,
    paddingBottom: "3vh",
  },
  sticky: {
    position: "-webkit-sticky" /* Safari */,
    position: "sticky",
    top: "52px",
  },
  sectionTitleBtnContainer: {
    "& > button": {
      marginLeft: "1vw",
      fontSize: 12,
    },
  },
  fieldsdivider: {
    marginTop: 8,
    marginBottom: 10,
    borderRadius: 0,
    "& input": {
      textAlign: "center",
      transform: "scale(1.6)",
    },
    "& .MuiOutlinedInput-notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23)",
      borderLeft: 0,
      borderRight: 0,
    },
    "&:hover .MuiOutlinedInput-notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23)",
    },
    "& $focused $notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23) !important",
    },
  },
  fieldMargin: {
    marginTop: 8,
    marginBottom: 10,
  },
  paddingSection: {
    paddingBottom: "3vh",
  },
  paper: {
    padding: "18px",
  },
};

const boolranges = [
  {
    value: "",
    label: "All",
  },
  {
    value: true,
    label: "True",
  },
  {
    value: false,
    label: "False",
  },
];

function getstatusOptions(intl) {
  const statusOptions = [
    {
      value: "Lease",
      label: intl.formatMessage({
        id: "search.status.lease",
      }),
    },
    {
      value: "Sale",
      label: intl.formatMessage({
        id: "search.status.sale",
      }),
    },
    {
      value: "Sale+Lease",
      label: intl.formatMessage({
        id: "search.status.salesandlease",
      }),
    },
    {
      value: "Surrend",
      label: intl.formatMessage({
        id: "search.status.surrend",
      }),
    },
    {
      value: "Tenanted",
      label: intl.formatMessage({
        id: "search.status.tenanted",
      }),
    },
    {
      value: "Cancel",
      label: intl.formatMessage({
        id: "search.status.cancel",
      }),
    },
    {
      value: "Cancel*",
      label: intl.formatMessage({
        id: "search.status.cancelspecial",
      }),
    },
    {
      value: "Follow Up",
      label: intl.formatMessage({
        id: "search.status.followup",
      }),
    },
    {
      value: "Leased",
      label: intl.formatMessage({
        id: "search.status.leased",
      }),
    },
    {
      value: "Pending",
      label: intl.formatMessage({
        id: "search.status.pending",
      }),
    },
    {
      value: "Reference",
      label: intl.formatMessage({
        id: "search.status.reference",
      }),
    },
    {
      value: "Reserved",
      label: intl.formatMessage({
        id: "search.status.reserved",
      }),
    },
    {
      value: "Sold",
      label: intl.formatMessage({
        id: "search.status.sold",
      }),
    },
    {
      value: "Search",
      label: intl.formatMessage({
        id: "search.status.search",
      }),
    },
    {
      value: "Selfuse",
      label: intl.formatMessage({
        id: "search.status.selfuse",
      }),
    },
  ];
  return statusOptions;
}

function getpropertyconditionOptions(intl) {
  const miscOptions = [
    {
      value: "isSoleAgent",
      label: intl.formatMessage({
        id: "stock.tag.sole",
      }),
    },
    {
      value: "isSaleEquity",
      label: intl.formatMessage({
        id: "search.form.saleequity",
      }),
    },
    {
      value: "isWWW",
      label: intl.formatMessage({
        id: "stock.tag.www",
      }),
    },
    {
      value: "isRaisedFloor",
      label: intl.formatMessage({
        id: "search.form.raisedfloor",
      }),
    },
    {
      value: "isNonWWW",
      label: intl.formatMessage({
        id: "search.form.isnonwww",
      }),
    },
    {
      value: "isWithKey",
      label: intl.formatMessage({
        id: "search.form.withkey",
      }),
    },
    {
      value: "isNonCentralAC",
      label: intl.formatMessage({
        id: "search.form.isnoncentralac",
      }),
    },
    {
      value: "isCarPark",
      label: intl.formatMessage({
        id: "tips.stocktag.carpark",
      }),
    },
    {
      value: "isPrivacy",
      label: intl.formatMessage({
        id: "search.form.privacy",
      }),
    },
    {
      value: "isMortgagee",
      label: intl.formatMessage({
        id: "tips.stocktag.mortgagee",
      }),
    },
    {
      value: "isInvestment",
      label: intl.formatMessage({
        id: "search.form.investment",
      }),
    },
    {
      value: "havePhoto",
      label: intl.formatMessage({
        id: "stock.photo",
      }),
    },
    {
      value: "haveVideo",
      label: intl.formatMessage({
        id: "stock.video",
      }),
    },
  ];
  return miscOptions;
}

function getmarketableMapping(intl) {
  const marketableMapping = {
    [intl.formatMessage({
      id: "search.header.marketable",
    })]: "isMarketable",
    [intl.formatMessage({
      id: "search.new",
    })]: "isNew",
    [intl.formatMessage({
      id: "search.mktornew",
    })]: "isMarketableOrNew",
  };
  return marketableMapping;
}

const unitM = {
  endAdornment: (
    <InputAdornment position="end">
      <span>M</span>
    </InputAdornment>
  ),
};

const unitK = {
  endAdornment: (
    <InputAdornment position="end">
      <span>K</span>
    </InputAdornment>
  ),
};

const unitFormatter = (value, unit) => {
  if (!value || value == null) return "";

  let unitValue;
  if (unit == "K") {
    unitValue = 1000;
  } else if (unit == "M") {
    unitValue = 1000000;
  } else {
    unitValue = 1;
  }
  return value / unitValue;
};

const formatMaxLength = (value, length) => {
  if (!value) {
    return value;
  }

  if (value.length <= length) {
    return `${value.slice(0, length)}`;
  }
  return `${value.slice(0, length)}`;
};

const formatUnitandMaxLength = (value, unit) => {
  if (!value) {
    return value;
  }

  value = value.replace(/[^0-9.]/g, "");

  let unitValue;
  if (unit == "K") unitValue = 1000;
  if (unit == "M") unitValue = 1000000;

  let number = value * unitValue;
  const roundnumber = number.toFixed();
  // return the value after rounding
  return `${roundnumber.toString()}`;
};

function createMinvalue(min) {
  return minValue(min, <FormattedMessage id="search.form.invalidinput" />);
}
function createMaxvalue(max) {
  return maxValue(max, <FormattedMessage id="search.form.invalidinput" />);
}
const numberValidate = number(
  <FormattedMessage id="search.form.invalidinput" />,
);
const minvaluezero = createMinvalue(0);
const maxvalue1 = createMaxvalue(200);
const maxvalue2 = createMaxvalue(999);
const maxvalue3 = createMaxvalue(9999);
const maxvalue4 = createMaxvalue(99999);
const maxvalue5 = createMaxvalue(9999999);
const maxvalue6 = createMaxvalue(99999999);
const maxvalue7 = createMaxvalue(999999999);
const maxvalue8 = createMaxvalue(9999999999);

function FieldSection(props) {
  const {
    classes,
    buildings,
    fieldhistory,
    streets,
    districts,
    listBuildings,
    listStreets,
    decoration,
    unitview,
    selectedData,
    setSelectedData,
    expanded,
    intl,
    changeForm,
    initialValues,
    companies,
    listCompanies,
  } = props;

  const grades = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "A",
      label: "A",
    },
    {
      value: "B",
      label: "B",
    },
    {
      value: "C",
      label: "C",
    },
  ];

  const usages = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "Comm+Res",
      label: intl.formatMessage({
        id: "search.form.commres",
      }),
    },
    {
      value: "Comm.(Rev.Ind.Bldg.)",
      label: intl.formatMessage({
        id: "search.form.comm.revindbuilding",
      }),
    },
    {
      value: "Commercial",
      label: intl.formatMessage({
        id: "common.commercial",
      }),
    },
    {
      value: "Godown",
      label: intl.formatMessage({
        id: "search.form.godown",
      }),
    },
    {
      value: "Hotel",
      label: intl.formatMessage({
        id: "search.form.hotel",
      }),
    },
    {
      value: "I/O",
      label: intl.formatMessage({
        id: "search.form.io",
      }),
    },
    {
      value: "I/O(*)",
      label: intl.formatMessage({
        id: "search.form.iospecial",
      }),
    },
    {
      value: "Industrial",
      label: intl.formatMessage({
        id: "search.form.industrial",
      }),
    },
    {
      value: "Industrial(*)",
      label: intl.formatMessage({
        id: "search.form.industrialspecial",
      }),
    },
    {
      value: "Residential",
      label: intl.formatMessage({
        id: "search.form.residential",
      }),
    },
    {
      value: "Shop",
      label: intl.formatMessage({
        id: "common.shop",
      }),
    },
    {
      value: "Site",
      label: intl.formatMessage({
        id: "search.form.site",
      }),
    },
  ];

  const buildingOwners = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: true,
      label: intl.formatMessage({
        id: "search.form.single",
      }),
    },
    {
      value: false,
      label: intl.formatMessage({
        id: "search.form.multiple",
      }),
    },
  ];

  const inspection = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "Authorized Key",
      label: intl.formatMessage({
        id: "search.form.authorizedkey",
      }),
    },
    {
      value: "Call in Advance",
      label: intl.formatMessage({
        id: "search.form.callinadvance",
      }),
    },
    {
      value: "Direct to Mgt. Office",
      label: intl.formatMessage({
        id: "search.form.directtomgtoffice",
      }),
    },
    {
      value: "Direct to Site",
      label: intl.formatMessage({
        id: "search.form.directtosite",
      }),
    },
    {
      value: "Direct to Unit",
      label: intl.formatMessage({
        id: "search.form.directtounit",
      }),
    },
    {
      value: "Keys with Agreement",
      label: intl.formatMessage({
        id: "search.form.keyswithagreement",
      }),
    },
    {
      value: "Special Key",
      label: intl.formatMessage({
        id: "search.form.specialkey",
      }),
    },
    {
      value: "N/A",
      label: intl.formatMessage({
        id: "common.na",
      }),
    },
  ];

  const possession = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "Vacant",
      label: intl.formatMessage({
        id: "search.form.vacant",
      }),
    },
    {
      value: "Future Vacancy",
      label: intl.formatMessage({
        id: "search.form.futurevacancy",
      }),
    },
    {
      value: "Replacement",
      label: intl.formatMessage({
        id: "search.form.replacement",
      }),
    },
    {
      value: "Owner Occupies",
      label: intl.formatMessage({
        id: "search.form.owneroccupies",
      }),
    },
    {
      value: "Tenant Occupies",
      label: intl.formatMessage({
        id: "search.form.tenantoccupies",
      }),
    },
    {
      value: "Sale With TA",
      label: intl.formatMessage({
        id: "search.form.salewithta",
      }),
    },
    {
      value: "Sale W",
      label: intl.formatMessage({
        id: "search.form.salew",
      }),
    },
    {
      value: "N/A",
      label: intl.formatMessage({
        id: "common.na",
      }),
    },
  ];

  let priceType, rentType;

  if (initialValues.priceMinTotal || initialValues.priceMaxTotal)
    priceType = "Total";
  if (initialValues.priceMinAvg || initialValues.priceMaxAvg) priceType = "Avg";
  if (initialValues.rentMinTotal || initialValues.rentMaxTotal)
    rentType = "Avg";
  if (initialValues.rentMinAvg || initialValues.rentMaxAvg) rentType = "Avg";
  const [TogglePriceValue, setTogglePriceValue] = useState(
    priceType ? priceType : "Total",
  );
  const [ToggleRentValue, setToggleRentValue] = useState(
    rentType ? rentType : "Total",
  );

  const langkey = getLangKey(intl);
  const viewselection = (unitview || []).map((item) => ({
    value: item["_id"],
    label: item[langkey] !== "" ? item[langkey] : item["nameEn"],
  }));
  viewselection.unshift({
    value: "",
    label: intl.formatMessage({
      id: "search.form.all",
    }),
  });

  const decorationselection = (decoration || []).map((item) => ({
    value: item["_id"],
    label: item[langkey] !== "" ? item[langkey] : item["nameEn"],
  }));
  decorationselection.unshift({
    value: "",
    label: intl.formatMessage({
      id: "search.form.all",
    }),
  });

  const deselectAllStatus = () => {
    changeForm("status", []);
  };

  const onChangePriceTotalAvg = () => {
    if (TogglePriceValue === "Total") {
      changeForm("priceMinTotal", "");
      changeForm("priceMaxTotal", "");
      setTogglePriceValue("Avg");
    } else if (TogglePriceValue === "Avg") {
      changeForm("priceMinAvg", "");
      changeForm("priceMaxAvg", "");
      setTogglePriceValue("Total");
    }
  };

  const onChangeRentTotalAvg = () => {
    if (ToggleRentValue === "Total") {
      changeForm("rentMinTotal", "");
      changeForm("rentMaxTotal", "");
      setToggleRentValue("Avg");
    } else if (ToggleRentValue === "Avg") {
      changeForm("rentMinAvg", "");
      changeForm("rentMaxAvg", "");
      setToggleRentValue("Total");
    }
  };

  const selectAllStatus = () => {
    const statusValue = getstatusOptions(intl).map((v) => v.value);
    changeForm("status", statusValue);
  };

  const deselectAllMisc = () => {
    const miscOptions = getpropertyconditionOptions(intl).map((v) => v.value);
    for (let key in miscOptions) {
      changeForm(miscOptions[key], false);
    }
  };

  const selectAllMisc = () => {
    const miscOptions = getpropertyconditionOptions(intl).map((v) => v.value);
    for (let key in miscOptions) {
      changeForm(miscOptions[key], true);
    }
  };

  return (
    <div>
      <Grid container spacing={2}>
        {/* start of 1st column */}
        <Grid item xs={4}>
          <Grid container spacing={4} direction="column">
            <Grid item={6}>
              <Paper className={classes.paper}>
                <div>
                  <span>Stock Search</span>
                  <Field
                    name="building"
                    margin="normal"
                    label={intl.formatMessage({
                      id: "search.form.building",
                    })}
                    fullWidth
                    component={AutoCompleteSelect}
                    optionsdata={buildings}
                    apiaction={listBuildings}
                    selectedData={selectedData && selectedData.building}
                    setSelectedData={setSelectedData}
                    customInputProps={{
                      className: classes.fieldMargin,
                    }}
                  />

                  <Field
                    name="district"
                    margin="normal"
                    label={intl.formatMessage({
                      id: "search.form.district",
                    })}
                    fullWidth
                    component={Search}
                    searchItems={districts}
                    selectedData={selectedData && selectedData.district}
                    setSelectedData={setSelectedData}
                    customInputProps={{
                      className: classes.fieldMargin,
                    }}
                  />

                  <Field
                    name="street"
                    margin="normal"
                    label={intl.formatMessage({
                      id: "search.form.street",
                    })}
                    fullWidth
                    component={AutoCompleteSelect}
                    optionsdata={streets}
                    apiaction={listStreets}
                    selectedData={selectedData && selectedData.street}
                    setSelectedData={setSelectedData}
                    customInputProps={{
                      className: classes.fieldMargin,
                    }}
                  />

                  <Grid container>
                    <Grid item xs={5}>
                      <div>
                        <Field
                          name="streetNoMin"
                          type="number"
                          margin="normal"
                          className={classes.fieldMargin}
                          label={intl.formatMessage({
                            id: "search.form.streetnumber",
                          })}
                          placeholder={intl.formatMessage({
                            id: "search.form.common.min",
                          })}
                          component={TextInputCombined}
                          fullWidth
                          variant="outlined"
                          validate={[minvaluezero]}
                          parse={(value) => formatMaxLength(value, 4)}
                          compPosition={"left"}
                        />
                      </div>
                    </Grid>
                    <Grid item xs={2}>
                      <OutlinedInput
                        disabled
                        readOnly={true}
                        defaultValue="|"
                        variant="outlined"
                        className={classes.fieldsdivider}
                      />
                    </Grid>
                    <Grid item xs={5}>
                      <div>
                        <Field
                          name="streetNoMax"
                          type="number"
                          margin="normal"
                          className={classes.fieldMargin}
                          placeholder={intl.formatMessage({
                            id: "search.form.common.max",
                          })}
                          component={TextInputCombined}
                          fullWidth
                          variant="outlined"
                          validate={[minvaluezero, maxvalue3]}
                          parse={(value) => formatMaxLength(value, 4)}
                          compPosition={"right"}
                        />
                      </div>
                    </Grid>
                  </Grid>
                </div>
              </Paper>
            </Grid>

            <Grid item={6}>
              <Paper className={classes.paper}>
                <div>
                  <span>Status</span>
                  <div>
                    {/* <TextField
                      required
                      id="standard-required"
                      label="Required"
                      defaultValue="Hello World"
                    /> */}
                    <FieldArray
                      name="status"
                      component={ArrayPillCheckBox}
                      options={getstatusOptions(intl)}
                    />
                  </div>
                </div>
              </Paper>
            </Grid>

            <Grid item={6}>
              <Paper className={classes.paper}>
                <div>
                  <span>Date</span>
                  <div>
                    <TextField
                      required
                      id="standard-required"
                      label="Required"
                      defaultValue="Hello World"
                    />
                  </div>
                </div>
              </Paper>
            </Grid>
          </Grid>
        </Grid>
        {/* end of 1st column */}

        {/* start of 2nd column */}
        <Grid item xs={4}>
          <Grid container direction="column">
            <Grid item={6}>
              <Paper className={classes.paper}>
                <div>
                  <span>Building</span>
                  <div>
                    <TextField
                      required
                      id="standard-required"
                      label="Required"
                      defaultValue="Hello World"
                    />
                  </div>
                </div>
              </Paper>
            </Grid>

            <Grid item={6}>
              <Paper className={classes.paper}>
                <div>
                  <span>Stock</span>
                  <div>
                    <TextField
                      required
                      id="standard-required"
                      label="Required"
                      defaultValue="Hello World"
                    />
                  </div>
                </div>
              </Paper>
            </Grid>

            <Grid item={6}>
              <Paper className={classes.paper}>
                <div>
                  <span>Stock Nature</span>
                  <div>
                    <TextField
                      required
                      id="standard-required"
                      label="Required"
                      defaultValue="Hello World"
                    />
                  </div>
                </div>
              </Paper>
            </Grid>
          </Grid>
        </Grid>
        {/* end of 2nd column */}

        <Grid item xs={4}>
          <Paper className={classes.paper}>Marketable</Paper>
        </Grid>
      </Grid>
    </div>
  );
}

const mapStateToProps = (state) => ({
  buildings: state.building.buildings || [],
  fieldhistory: state.stocklist.fieldhistory || [],
  queryvariables: state.stocklist.queryvariables
    ? state.stocklist.queryvariables
    : {},
  listing: state.building.listing ? state.building.listing : false,
  listed: state.building.listed ? state.building.listed : false,
  streets: state.street.streets ? state.street.streets : [],
  districts: state.district.districts ? state.district.districts : [],
  decoration: state.stocklist.decoration ? state.stocklist.decoration : [],
  unitview: state.building.unitview ? state.building.unitview : [],
  companies: state.stocklist.companies ? state.stocklist.companies : [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    listBuildings: (graphqlvariable) => {
      dispatch(listBuildings(graphqlvariable));
    },
    listStreets: (graphqlvariable) => dispatch(listStreets(graphqlvariable)),
    listCompanies: (graphqlvariable) =>
      dispatch(listCompanies(graphqlvariable)),
    changeForm: (field, val) => dispatch(change("searchForm", field, val)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(FieldSection)));
