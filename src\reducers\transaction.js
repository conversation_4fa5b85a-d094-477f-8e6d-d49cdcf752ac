import {
  LIST_TRANSACTIONS_START,
  LIST_MORE_TRANSACTIONS_START,
  LIST_TRANSACTIONS_SUCCESS,
  LIST_TRANSACTIONS_NULL_SUCCESS,
  LIST_TRANSACTIONS_ERROR,
  CLEAR_TRANSACTIONS
} from "../constants/transaction";

const initialState = {
  listed: false,
  listing: false,
  transactions: [],
};

export default function transaction(state = initialState, action) {
  switch (action.type) {
    case LIST_TRANSACTIONS_START:
      return {
        ...state,
        listed: false,
        listing: true,
        transactions: [],
        hasMore: true,
        queryvariables: action.payload.variables,
        selectedData: action.payload.selectedData
      };
    case LIST_MORE_TRANSACTIONS_START:
      return {
        ...state,
        listing: true,
        hasMore: true,
        queryvariables: action.payload.variables,
        selectedData: action.payload.selectedData
      };
    case LIST_TRANSACTIONS_SUCCESS:
      return {
        ...state,
        listed: true,
        listing: false,
        hasMore: true,
        transactions: state.transactions.concat(action.payload.data.transactions || [])
      };
    case LIST_TRANSACTIONS_NULL_SUCCESS:
      return {
        ...state,
        listing: false,
        listed: true,
        hasMore: false,
        transactions: state.transactions.concat(action.payload.data.transactions || [])
      };
    case LIST_TRANSACTIONS_ERROR:
      return {
        ...state,
        listed: false,
        listing: false,
        error: action.payload.error
      };
    case CLEAR_TRANSACTIONS:
      return {
        ...state,
        listed: false,
        listing: false,
        transactions: [],
        queryvariables: null,
        error: null
      };
    default:
      return state;
  }
}
