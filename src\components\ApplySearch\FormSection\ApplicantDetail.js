import React from "react";
import PropTypes from "prop-types";
import _ from "lodash";
import { Grid } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import { Field } from "redux-form";
import { connect } from "react-redux";
import { injectIntl } from "react-intl";

import TextInput from "../../common/TextInput";
import ReadOnlyText from "../../common/ReadOnlyText";

const useStyles = makeStyles({
  wrapper: {
    margin: "0 -8px 5px",
  },
});

function ApplicantDetail({ employee, intl }) {
  const classes = useStyles();

  return (
    <Grid item container spacing={2} className={classes.wrapper}>
      <Grid item xs={6}>
        <ReadOnlyText
          label={intl.formatMessage({ id: "stock.applySearch.applicant" })}
          value={`${_.get(employee, "dept_code") || ""} ${_.get(
            employee,
            "nickname",
          )} ${_.get(employee, "surname_en")}`}
        />
      </Grid>
      <Grid item xs={6}>
        <Field
          required
          name="contactPhone"
          component={TextInput}
          label={intl.formatMessage({ id: "stock.applySearch.contactPhone" })}
        />
      </Grid>
    </Grid>
  );
}

ApplicantDetail.propTypes = {
  employee: PropTypes.object.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  employee: _.get(state, "employee.employees.0"),
});

export default connect(mapStateToProps)(injectIntl(ApplicantDetail));
