import fetch from "node-fetch";

import { proposalBypassCode, api, sbu } from "../../config";
import { getEmployeeInfo } from "./proposal";

function resJson(res, status, error, data) {
  res.json({
    status,
    errors: error ? [{ message: error }] : null,
    data,
  });
}

export const createListProposal = async (req, res, next, getQuery) => {
  if (!req.user) {
    next();
  } else {
    let listProposal = req.body.listProposal;
    let userInfo =
      req.user && req.user.login && req.user.login.info
        ? req.user.login.info
        : {};

    try {
      let employee = await getEmployeeInfo(getQuery, userInfo.emp_id);

      listProposal.salesman = {
        id: employee.emp_id,
        nameZh: employee.name_long_zh,
        nameEn: employee.name_long_en,
        titleNameZh: employee.title_zh,
        titleNameEn: employee.title_en,
        licence: employee.licence,
        tele: employee.phone,
        email: employee.email,
        mitaclubed: employee.mitaclubed,
        company: employee.company,
        ics: sbu,
        empType: employee.emp_type,
        wechatQr: employee.wechat_qr,
        isManager: employee.is_manager,
        nickname: employee.nickname,
        department: {
          code: employee.dept_code,
          tele:
            employee.dept && employee.dept.phone ? employee.dept.phone : null,
          email:
            employee.dept && employee.dept.email ? employee.dept.email : null,
          nameZh:
            employee.dept && employee.dept.name_zh
              ? employee.dept.name_zh
              : null,
          nameEn:
            employee.dept && employee.dept.name_en
              ? employee.dept.name_en
              : null,
          addrZh:
            employee.dept && employee.dept.addr_full_zh
              ? employee.dept.addr_full_zh
              : null,
          addrEn:
            employee.dept && employee.dept.addr_full_en
              ? employee.dept.addr_full_en
              : null,
          compLicenceNo:
            employee.dept && employee.dept.comp_licence_no
              ? employee.dept.comp_licence_no
              : null,
        },
      };
    } catch (e) {
      console.log(e);
      resJson(res, 300, "Failed to fetch employee info");
      return;
    }

    const query = await getQuery("CREATE_LIST_PROPOSAL_QUERY");
    const variables = {
      listProposal,
      bypassCode: proposalBypassCode,
    };

    const body = {
      query,
      variables,
    };

    try {
      const resp = await fetch(`${api.proposal}/graphql`, {
        method: "POST",
        headers: {
          "content-type": "application/json",
        },
        body: JSON.stringify(body),
      });

      if (resp.status !== 200 && resp.status !== 400)
        throw new Error(resp.statusText);

      const { data, errors } = await resp.json();

      if (errors) throw new Error(errors[0].message);

      console.log(data);
      resJson(res, 200, null, data);
    } catch (e) {
      console.log(e);
      resJson(res, 300, e.message);
    }
  }
};
