import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import DetailBoxTitle from "../../../../common/DetailBoxTitle";
import FormButton from "../../../../common/FormButton";
import { FormattedMessage } from "react-intl";
import { goToSearchResult, consolidateType } from "../../../../../helper/generalHelper";
import { clearStock } from "../../../../../actions/stock";
import { clearStockList } from "../../../../../actions/stocklist";

const styles = theme => ({
  root: {
    padding: "0 2vw"
  },
  textClass: {
    fontSize: "1.175em"
  },
  btnRow: {
    display: "flex",
    alignItems: "center",
    flexWrap: "wrap",
    marginBottom: "1vh"
  },
  btn: {
    height: 24,
    marginRight: "2vw",
    textTransform: "none"
  },
});

class BuildingDetail extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
    clearStockDetail: PropTypes.func.isRequired,
    clearStockList: PropTypes.func.isRequired,
  };

  render() {
    const { classes, detail } = this.props;

    const nameEn = detail.nameEn || "---";
    const nameZh = detail.nameZh || "---";
    const streetNo =
      detail.streets && detail.streets[0] && detail.streets[0].streetNo
        ? detail.streets[0].streetNo + " "
        : "";
    const streetName =
      detail.streets && detail.streets[0] && detail.streets[0].nameEn
        ? detail.streets[0].nameEn
        : "---";
    const district =
      detail.districtDetail && detail.districtDetail.nameEn
        ? detail.districtDetail.nameEn
        : "---";

    const _id = detail.id;
    const query = {
      building: [_id],
      street: [],
      district: [],
      limit: 50,
      offset: 0,
      ...consolidateType
    };
    const selectedData = {
      building: [
        {
          value: _id,
          label: detail.nameEn
        }
      ]
    };
    const goToSearchBuildingResult = () => {
      this.props.clearStockDetail();
      this.props.clearStockList(false);
      goToSearchResult(query, selectedData, true);
    };

    return (
      <div className={classes.root}>
        <DetailBoxTitle
          subtitle2={streetNo + streetName + ", " + district}
          text={nameEn}
          text2={nameZh}
          textClass={classes.textClass}
        >
          <div className={classes.btnRow}>
            {_id && detail.nameEn && (
              <FormButton className={classes.btn} onClick={goToSearchBuildingResult}>
                <FormattedMessage id="building.searchstock" />
              </FormButton>
            )}
          </div>
        </DetailBoxTitle>
      </div>
    );
  }
}

const mapDispatchToProps = dispatch => {
  return {
    clearStockDetail: () => dispatch(clearStock()),
    clearStockList: (...args) => dispatch(clearStockList(...args)),
  };
};

export default connect(
  null,
  mapDispatchToProps
)(withStyles(styles)(BuildingDetail));
