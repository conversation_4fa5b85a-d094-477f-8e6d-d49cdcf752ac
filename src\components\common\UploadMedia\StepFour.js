import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import { FormattedMessage, injectIntl } from "react-intl";

const styles = (theme) => ({
  root: {
    fontSize: "1.125em",
    textAlign: "center",
    padding: "5vh 0",
  },
  error: {
    color: "#F44336",
    fontSize: "0.667em",
  },
});

class StepFour extends React.Component {
  render() {
    const { classes, progressingMedia, isKol, intl } = this.props;
    const hasErrors = progressingMedia.filter((v) => v.state === "error").length > 0;
    const firstMedia = progressingMedia[0] || {};
    const { type, approval } = firstMedia;
    
    // 根据type确定媒体类型的本地化ID
    const getMediaTypeId = (mediaType) => {
      switch(mediaType) {
        case 'photo': return {zh: '相片', en: 'Photo'};
        case 'document': return {zh: '文檔', en: 'Document'};
        case 'video': return {zh: '影片', en: 'Video'};
        case 'kol_video': return {zh: 'KOL 影片', en: 'KOL Video'};
        default: return {zh: '多媒體', en: 'Media'};
      }
    };
    
    // 获取上传成功消息ID
    const getSuccessMessageId = () => {
      if (approval === "waiting") {
        return intl.locale === "zh"
          ? `成功上載${getMediaTypeId(type).zh}，等待批核`
          : `${getMediaTypeId(type).en} uploaded successfully, waiting for approval`;
      } else if (approval === "approved") {
        return intl.locale === "zh"
          ? `${getMediaTypeId(type).zh}已自動通過批核`
          : `${getMediaTypeId(type).en} was approved automatically`;
      } else {
        return intl.locale === "zh"
          ? `成功上載${getMediaTypeId(type).zh}`
          : `${getMediaTypeId(type).en} uploaded successfully`;
      }
    };

    return (
      <div className={classes.root}>
        {hasErrors ? (
          !isKol && <FormattedMessage id="stock.photo.uploadfail" />
        ) : (
          <div>{getSuccessMessageId()}</div>
        )}
        <div className={!isKol ? classes.error: ''}>
          {progressingMedia.map((v, i) => {
            let errorMsg;
            const message = v.message && v.message.message.split("|");
            if (message) {
              switch (message[0]) {
                case "Only image file is acceptable":
                  errorMsg = intl.formatMessage({
                    id: "stock.photo.uploadformatmsg",
                  });
                  break;
                case "KOL Video file does not meet the requirements":
                  errorMsg = intl.locale === "zh" ? message[1] : message[2];
                  break;
                case "Thumbnail Image file does not meet the requirements":
                  errorMsg = intl.locale === "zh" ? message[1] : message[2];
                  break;
                case "Cannot find thumbnail file":
                  errorMsg = intl.formatMessage({
                    id: "stock.kol.thumbnail.upload.null",
                  });
                  break;
                default:
                  errorMsg = intl.formatMessage({
                    id: "stock.photo.uploadfailmsg",
                  });
                  break;
              }
            } else {
              errorMsg = intl.formatMessage({
                id: "stock.photo.uploadfailmsg",
              });
            }

            return (
              v.state === "error" && (
                !isKol ? <div key={v.startTime}>{v.filename + ": " + errorMsg}</div> : <div key={v.startTime} dangerouslySetInnerHTML={{__html: errorMsg}}></div>
              )
            );
          })}
        </div>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(StepFour));
