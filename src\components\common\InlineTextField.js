import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import InlineTextInput from './InlineTextInput';

// We can inject some CSS into the DOM.
const styles = {
  root: {

  },
  label: {
    lineHeight: 1.3,
    color: "#777",
    fontSize: "0.875em"
  },
};

function InlineTextField(props) {
  const {
    classes,
    className,
    label,
    customLabelProps = {},
    ...others
  } = props;

  return (
    <div className={clsx(classes.root, className)}>
      <label
        {...customLabelProps}
        className={clsx(classes.label, customLabelProps.className)}
      >
        {label}
      </label>
      <InlineTextInput {...others} />
    </div>
  );
}

InlineTextField.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  label: PropTypes.string,
  customLabelProps: PropTypes.object,
};

export default withStyles(styles)(InlineTextField);
