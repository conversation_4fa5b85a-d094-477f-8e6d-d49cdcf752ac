/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import fs from "fs";
import path from "path";
import webpack from "webpack";
import WebpackAssetsManifest from "webpack-assets-manifest";
import nodeExternals from "webpack-node-externals";
import { BundleAnalyzerPlugin } from "webpack-bundle-analyzer";
import overrideRules from "./lib/overrideRules";
import pkg from "../package.json";
import Dotenv from "dotenv-webpack";

const ROOT_DIR = path.resolve(__dirname, "..");
const resolvePath = (...args) => path.resolve(ROOT_DIR, ...args);
const SRC_DIR = resolvePath("src");
const BUILD_DIR = resolvePath("build");

const isDebug = !process.argv.includes("--release");
const isVerbose = process.argv.includes("--verbose");
const isAnalyze =
  process.argv.includes("--analyze") || process.argv.includes("--analyse");

const sbu = process.env.SBU;
const mediaBypassCode = process.env.MEDIA_BYPASS_CODE;
const apiDistrict = process.env.API_DISTRICT;
const apiStreet = process.env.API_STREET;
const apiBuilding = process.env.API_BUILDING;
const apiStock = process.env.API_STOCK;
const apiTransaction = process.env.API_TRANSACTION;
const apiEmployee = process.env.API_EMPLOYEE;
const apiEmployeeInternal = process.env.API_EMPLOYEE_INTERNAL;
const apiSupplement = process.env.API_SUPPLEMENT;
const apiSearch = process.env.API_SEARCH;
const apiWww = process.env.API_WWW;
const apiContact = process.env.API_CONTACT;
const apiMedia = process.env.API_MEDIA;
const apiFirstHand = process.env.FIRST_HAND_STOCK_API;
const requestTimeout = process.env.REQUEST_TIMEOUT;
const uploadRequestTimeout = process.env.UPLOAD_REQUEST_TIMEOUT;
const API_CLOUDWATCH_LOG_URL = process.env.API_CLOUDWATCH_LOG_URL;
const LOG_GROUP = process.env.LOG_GROUP;
const REFRESH_TOKEN_TIMEOUT = process.env.REFRESH_TOKEN_TIMEOUT;
const RECAPTCHA_SITE_KEY = process.env.RECAPTCHA_SITE_KEY;
const locale = process.env.locale;
const resendpinCounter = process.env.RESENDPIN_COUNTER;
const enableTranslation = process.env.ENABLE_TRANSLATION;
const enableRecaptcha = process.env.ENABLE_RECAPTCHA;
const unlockquota = process.env.UNLOCK_QUOTA;
const proposalquota = process.env.PROPOSAL_QUOTA;
const apiProposal = process.env.API_PROPOSAL;
const landsearch = process.env.API_LANDSEARCH;
const landsearchfilepath = process.env.API_LANDSEARCH_FILEPATH;
const enableConsolidLandSearch = process.env.ENABLE_CONSOLID_LANDSEARCH;
const enableDesktopView = process.env.ENABLE_DESKTOPVIEW;
const enableBatchTwo = process.env.ENABLE_BATCH_TWO;
const enableWWWScore = process.env.ENABLE_WWW_SCORE;
const SSO = process.env.SSO;
const CAS_ACCESS_TOKEN_HOST = process.env.CAS_ACCESS_TOKEN_HOST;
const CAS_REFRESH_TOKEN_HOST = process.env.CAS_REFRESH_TOKEN_HOST;
const CAS_CLIENT_SERVICE = process.env.CAS_CLIENT_SERVICE;
const CAS_PROFILE_HOST = process.env.CAS_PROFILE_HOST;
const CAS_LOGOUT_HOST = process.env.CAS_LOGOUT_HOST;
const CAS_AUTH_CODE_HOST = process.env.CAS_AUTH_CODE_HOST;
const CAS_CLIENT_ID = process.env.CAS_CLIENT_ID;
const CAS_CLIENT_SECRET = process.env.CAS_CLIENT_SECRET;
const apiLandSearchDetails = process.env.API_LANDSEARCH_DETAILS;
const GATEWAY = process.env.GATEWAY;
const PERMISSION_VIEW_STOCK_QUOTA = process.env.PERMISSION_VIEW_STOCK_QUOTA;
const WB_TYPE_ID = process.env.WB_TYPE_ID;
const productionHost = process.env.PRODUCTION_HOST;
const publicHost = process.env.PUBLIC_HOST;

const reScript = /\.(js|jsx|mjs)$/;
const reStyle = /\.(css|less|styl|scss|sass|sss)$/;
const reImage = /\.(bmp|gif|jpg|jpeg|png|svg)$/;
const staticAssetName = isDebug
  ? "[path][name].[ext]?[hash:8]"
  : "[hash:8].[ext]";

// CSS Nano options http://cssnano.co/
const minimizeCssOptions = {
  discardComments: { removeAll: true },
};

//
// Common configuration chunk to be used for both
// client-side (client.js) and server-side (server.js) bundles
// -----------------------------------------------------------------------------
const config = {
  context: ROOT_DIR,

  mode: isDebug ? "development" : "production",

  output: {
    path: resolvePath(BUILD_DIR, "public/assets"),
    publicPath: "/assets/",
    pathinfo: isVerbose,
    filename: isDebug ? "[name].js" : "[name].[chunkhash:8].js",
    chunkFilename: isDebug
      ? "[name].chunk.js"
      : "[name].[chunkhash:8].chunk.js",
    // Point sourcemap entries to original disk location (format as URL on Windows)
    devtoolModuleFilenameTemplate: (info) =>
      path.resolve(info.absoluteResourcePath).replace(/\\/g, "/"),
  },

  resolve: {
    // Allow absolute paths in imports, e.g. import Button from 'components/Button'
    // Keep in sync with .flowconfig and .eslintrc
    modules: ["node_modules"],
    alias: {
      "@": SRC_DIR,
    },
  },

  module: {
    // Make missing exports an error instead of warning
    strictExportPresence: true,

    rules: [
      // Rules for JS / JSX
      {
        test: reScript,
        include: [SRC_DIR, resolvePath("tools")],
        loader: "babel-loader",
        options: {
          // https://github.com/babel/babel-loader#options
          cacheDirectory: isDebug,

          // https://babeljs.io/docs/usage/options/
          babelrc: false,
          configFile: false,
          presets: [
            // A Babel preset that can automatically determine the Babel plugins and polyfills
            // https://github.com/babel/babel-preset-env
            [
              "@babel/preset-env",
              {
                targets: {
                  browsers: pkg.browserslist,
                },
                forceAllTransforms: !isDebug, // for UglifyJS
                modules: false,
                useBuiltIns: false,
                debug: false,
              },
            ],
            // Flow
            // https://github.com/babel/babel/tree/master/packages/babel-preset-flow
            "@babel/preset-flow",
            // JSX
            // https://github.com/babel/babel/tree/master/packages/babel-preset-react
            ["@babel/preset-react", { development: isDebug }],
          ],
          plugins: [
            // Experimental ECMAScript proposals
            "@babel/plugin-proposal-class-properties",
            "@babel/plugin-syntax-dynamic-import",
            // Treat React JSX elements as value types and hoist them to the highest scope
            // https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-constant-elements
            ...(isDebug ? [] : ["@babel/transform-react-constant-elements"]),
            // Replaces the React.createElement function with one that is more optimized for production
            // https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-react-inline-elements
            ...(isDebug ? [] : ["@babel/transform-react-inline-elements"]),
          ],
          env: {
            production: {
              plugins: [
                // Remove unnecessary React propTypes from the production build
                // https://github.com/oliviertassinari/babel-plugin-transform-react-remove-prop-types
                ...(isDebug ? [] : ["transform-react-remove-prop-types"]),
              ],
            },
          },
        },
      },

      // Rules for Style Sheets
      {
        test: reStyle,
        rules: [
          // Convert CSS into JS module
          {
            issuer: { not: [reStyle] },
            use: "isomorphic-style-loader",
          },

          // Process external/third-party styles
          {
            exclude: SRC_DIR,
            loader: "css-loader",
            options: {
              sourceMap: isDebug,
              minimize: isDebug ? false : minimizeCssOptions,
            },
          },

          // Process internal/project styles (from src folder)
          {
            include: SRC_DIR,
            loader: "css-loader",
            options: {
              // CSS Loader https://github.com/webpack/css-loader
              importLoaders: 1,
              sourceMap: isDebug,
              // CSS Modules https://github.com/css-modules/css-modules
              modules: true,
              localIdentName: isDebug
                ? "[name]-[local]-[hash:base64:5]"
                : "[hash:base64:5]",
              // CSS Nano http://cssnano.co/
              minimize: isDebug ? false : minimizeCssOptions,
            },
          },

          // Apply PostCSS plugins including autoprefixer
          {
            loader: "postcss-loader",
            options: {
              config: {
                path: "./tools/postcss.config.js",
              },
            },
          },

          // Compile Less to CSS
          // https://github.com/webpack-contrib/less-loader
          // Install dependencies before uncommenting: yarn add --dev less-loader less
          // {
          //   test: /\.less$/,
          //   loader: 'less-loader',
          // },

          // Compile Sass to CSS
          // https://github.com/webpack-contrib/sass-loader
          // Install dependencies before uncommenting: yarn add --dev sass-loader node-sass
          // {
          //   test: /\.(scss|sass)$/,
          //   loader: 'sass-loader',
          // },
        ],
      },

      // Rules for images
      {
        test: reImage,
        oneOf: [
          // Inline lightweight images into CSS
          {
            issuer: reStyle,
            oneOf: [
              // Inline lightweight SVGs as UTF-8 encoded DataUrl string
              {
                test: /\.svg$/,
                loader: "svg-url-loader",
                options: {
                  name: staticAssetName,
                  limit: 4096, // 4kb
                },
              },

              // Inline lightweight images as Base64 encoded DataUrl string
              {
                loader: "url-loader",
                options: {
                  name: staticAssetName,
                  limit: 4096, // 4kb
                },
              },
            ],
          },

          // Or return public URL to image resource
          {
            loader: "file-loader",
            options: {
              name: staticAssetName,
            },
          },
        ],
      },

      // Convert plain text into JS module
      {
        test: /\.txt$/,
        loader: "raw-loader",
      },

      // Convert Markdown into HTML
      {
        test: /\.md$/,
        loader: path.resolve(__dirname, "./lib/markdown-loader.js"),
      },

      // Return public URL for all assets unless explicitly excluded
      // DO NOT FORGET to update `exclude` list when you adding a new loader
      {
        exclude: [reScript, reStyle, reImage, /\.json$/, /\.txt$/, /\.md$/],
        loader: "file-loader",
        options: {
          name: staticAssetName,
        },
      },

      // Exclude dev modules from production build
      ...(isDebug
        ? []
        : [
            {
              test: resolvePath(
                "node_modules/react-deep-force-update/lib/index.js",
              ),
              loader: "null-loader",
            },
          ]),
    ],
  },

  // Don't attempt to continue if there are any errors.
  bail: !isDebug,

  cache: isDebug,

  // Specify what bundle information gets displayed
  // https://webpack.js.org/configuration/stats/
  stats: {
    cached: isVerbose,
    cachedAssets: isVerbose,
    chunks: isVerbose,
    chunkModules: isVerbose,
    colors: true,
    hash: isVerbose,
    modules: isVerbose,
    reasons: isDebug,
    timings: true,
    version: isVerbose,
  },

  // Choose a developer tool to enhance debugging
  // https://webpack.js.org/configuration/devtool/#devtool
  devtool: isDebug ? "cheap-module-source-map" : false,
};

//
// Configuration for the client-side bundle (client.js)
// -----------------------------------------------------------------------------

const clientConfig = {
  ...config,

  name: "client",
  target: "web",

  entry: {
    client: ["@babel/polyfill", "./src/client.js"],
  },

  plugins: [
    // Define free variables
    // https://webpack.js.org/plugins/define-plugin/
    new Dotenv(),
    new webpack.DefinePlugin({
      "process.env.BROWSER": true,
      "process.env.SBU": JSON.stringify(sbu),
      "process.env.MEDIA_BYPASS_CODE": JSON.stringify(mediaBypassCode),
      "process.env.API_DISTRICT": JSON.stringify(apiDistrict),
      "process.env.API_STREET": JSON.stringify(apiStreet),
      "process.env.API_BUILDING": JSON.stringify(apiBuilding),
      "process.env.API_STOCK": JSON.stringify(apiStock),
      "process.env.API_TRANSACTION": JSON.stringify(apiTransaction),
      "process.env.API_EMPLOYEE": JSON.stringify(apiEmployee),
      "process.env.API_EMPLOYEE_INTERNAL": JSON.stringify(apiEmployeeInternal),
      "process.env.API_SUPPLEMENT": JSON.stringify(apiSupplement),
      "process.env.API_SEARCH": JSON.stringify(apiSearch),
      "process.env.API_WWW": JSON.stringify(apiWww),
      "process.env.API_CONTACT": JSON.stringify(apiContact),
      "process.env.API_MEDIA": JSON.stringify(apiMedia),
      "process.env.FIRST_HAND_STOCK_API": JSON.stringify(apiFirstHand),
      "process.env.REQUEST_TIMEOUT": JSON.stringify(requestTimeout),
      "process.env.UPLOAD_REQUEST_TIMEOUT":
        JSON.stringify(uploadRequestTimeout),
      "process.env.API_CLOUDWATCH_LOG_URL": JSON.stringify(
        API_CLOUDWATCH_LOG_URL,
      ),
      "process.env.LOG_GROUP": JSON.stringify(LOG_GROUP),
      "process.env.REFRESH_TOKEN_TIMEOUT": JSON.stringify(
        REFRESH_TOKEN_TIMEOUT,
      ),
      "process.env.RECAPTCHA_SITE_KEY": JSON.stringify(RECAPTCHA_SITE_KEY),
      "process.env.locale": JSON.stringify(locale),
      "process.env.RESENDPIN_COUNTER": JSON.stringify(resendpinCounter),
      "process.env.ENABLE_TRANSLATION": JSON.stringify(enableTranslation),
      "process.env.ENABLE_RECAPTCHA": JSON.stringify(enableRecaptcha),
      "process.env.UNLOCK_QUOTA": JSON.stringify(unlockquota),
      "process.env.PROPOSAL_QUOTA": JSON.stringify(proposalquota),
      "process.env.API_LANDSEARCH": JSON.stringify(landsearch),
      "process.env.API_LANDSEARCH_FILEPATH": JSON.stringify(landsearchfilepath),
      "process.env.ENABLE_CONSOLID_LANDSEARCH": JSON.stringify(
        enableConsolidLandSearch,
      ),
      "process.env.ENABLE_DESKTOPVIEW": JSON.stringify(enableDesktopView),
      "process.env.ENABLE_BATCH_TWO": JSON.stringify(enableBatchTwo),
      "process.env.ENABLE_WWW_SCORE": JSON.stringify(enableWWWScore),
      "process.env.CAS_CLIENT_SERVICE": JSON.stringify(CAS_CLIENT_SERVICE),
      "process.env.SSO": JSON.stringify(
        SSO,
      ),
      "process.env.CAS_ACCESS_TOKEN_HOST": JSON.stringify(
        CAS_ACCESS_TOKEN_HOST,
      ),
      "process.env.CAS_REFRESH_TOKEN_HOST": JSON.stringify(
        CAS_REFRESH_TOKEN_HOST,
      ),
      "process.env.CAS_PROFILE_HOST": JSON.stringify(CAS_PROFILE_HOST),
      "process.env.CAS_LOGOUT_HOST": JSON.stringify(CAS_LOGOUT_HOST),
      "process.env.CAS_AUTH_CODE_HOST": JSON.stringify(CAS_AUTH_CODE_HOST),
      "process.env.CAS_CLIENT_ID": JSON.stringify(CAS_CLIENT_ID),
      "process.env.CAS_CLIENT_SECRET": JSON.stringify(CAS_CLIENT_SECRET),
      "process.env.PERMISSION_VIEW_STOCK_QUOTA": JSON.stringify(
        PERMISSION_VIEW_STOCK_QUOTA,
      ),
      "process.env.WB_TYPE_ID": JSON.stringify(WB_TYPE_ID),
      "process.env.GATEWAY": JSON.stringify(GATEWAY),
      "process.env.PRODUCTION_HOST": JSON.stringify(productionHost),
      "process.env.PUBLIC_HOST": JSON.stringify(publicHost),
      "process.env.API_LANDSEARCH_DETAILS":
        JSON.stringify(apiLandSearchDetails),
      "process.env.API_PROPOSAL": JSON.stringify(apiProposal),

      __DEV__: isDebug,
    }),

    // Emit a file with assets paths
    // https://github.com/webdeveric/webpack-assets-manifest#options
    new WebpackAssetsManifest({
      output: `${BUILD_DIR}/asset-manifest.json`,
      publicPath: true,
      writeToDisk: true,
      customize: ({ key, value }) => {
        // You can prevent adding items to the manifest by returning false.
        if (key.toLowerCase().endsWith(".map")) return false;
        return { key, value };
      },
      done: (manifest, stats) => {
        // Write chunk-manifest.json.json
        const chunkFileName = `${BUILD_DIR}/chunk-manifest.json`;
        try {
          const fileFilter = (file) => !file.endsWith(".map");
          const addPath = (file) => manifest.getPublicPath(file);
          const chunkFiles = stats.compilation.chunkGroups.reduce((acc, c) => {
            acc[c.name] = [
              ...(acc[c.name] || []),
              ...c.chunks.reduce(
                (files, cc) => [
                  ...files,
                  ...cc.files.filter(fileFilter).map(addPath),
                ],
                [],
              ),
            ];
            return acc;
          }, Object.create(null));
          fs.writeFileSync(chunkFileName, JSON.stringify(chunkFiles, null, 2));
        } catch (err) {
          console.error(`ERROR: Cannot write ${chunkFileName}: `, err);
          if (!isDebug) process.exit(1);
        }
      },
    }),

    ...(isDebug
      ? []
      : [
          // Webpack Bundle Analyzer
          // https://github.com/th0r/webpack-bundle-analyzer
          ...(isAnalyze ? [new BundleAnalyzerPlugin()] : []),
        ]),
  ],

  // Move modules that occur in multiple entry chunks to a new entry chunk (the commons chunk).
  optimization: {
    splitChunks: {
      cacheGroups: {
        commons: {
          chunks: "initial",
          test: /[\\/]node_modules[\\/]/,
          name: "vendors",
        },
      },
    },
  },

  // Some libraries import Node modules but don't use them in the browser.
  // Tell Webpack to provide empty mocks for them so importing them works.
  // https://webpack.js.org/configuration/node/
  // https://github.com/webpack/node-libs-browser/tree/master/mock
  node: {
    fs: "empty",
    net: "empty",
    tls: "empty",
  },
};

//
// Configuration for the server-side bundle (server.js)
// -----------------------------------------------------------------------------

const serverConfig = {
  ...config,

  name: "server",
  target: "node",

  entry: {
    server: ["@babel/polyfill", "./src/server.js"],
  },

  output: {
    ...config.output,
    path: BUILD_DIR,
    filename: "[name].js",
    chunkFilename: "chunks/[name].js",
    libraryTarget: "commonjs2",
  },

  // Webpack mutates resolve object, so clone it to avoid issues
  // https://github.com/webpack/webpack/issues/4817
  resolve: {
    ...config.resolve,
  },

  module: {
    ...config.module,

    rules: overrideRules(config.module.rules, (rule) => {
      // Override babel-preset-env configuration for Node.js
      if (rule.loader === "babel-loader") {
        return {
          ...rule,
          options: {
            ...rule.options,
            presets: rule.options.presets.map((preset) =>
              preset[0] !== "@babel/preset-env"
                ? preset
                : [
                    "@babel/preset-env",
                    {
                      targets: {
                        node: pkg.engines.node.match(/(\d+\.?)+/)[0],
                      },
                      modules: false,
                      useBuiltIns: false,
                      debug: false,
                    },
                  ],
            ),
          },
        };
      }

      // Override paths to static assets
      if (
        rule.loader === "file-loader" ||
        rule.loader === "url-loader" ||
        rule.loader === "svg-url-loader"
      ) {
        return {
          ...rule,
          options: {
            ...rule.options,
            emitFile: false,
          },
        };
      }

      return rule;
    }),
  },

  externals: [
    "./chunk-manifest.json",
    "./asset-manifest.json",
    nodeExternals({
      whitelist: [reStyle, reImage],
    }),
  ],

  plugins: [
    // Define free variables
    // https://webpack.js.org/plugins/define-plugin/
    new webpack.DefinePlugin({
      "process.env.NODE_ENV": isDebug ? '"development"' : '"production"',
      "process.env.BROWSER": false,
      __DEV__: isDebug,
    }),

    // Adds a banner to the top of each generated chunk
    // https://webpack.js.org/plugins/banner-plugin/
    new webpack.BannerPlugin({
      banner: 'require("source-map-support").install();',
      raw: true,
      entryOnly: false,
    }),
  ],

  // Do not replace node globals with polyfills
  // https://webpack.js.org/configuration/node/
  node: {
    console: false,
    global: false,
    process: false,
    Buffer: false,
    __filename: false,
    __dirname: false,
  },
};

export default [clientConfig, serverConfig];
