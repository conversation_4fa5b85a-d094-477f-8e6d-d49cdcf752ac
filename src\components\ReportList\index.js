import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import List from "./List";
import { listTxReports } from '@/actions/listTxReport';

const styles = () => ({
  root: {
    padding: "1vh 2vw",
  },
  notFound: {
    margin: "1em 0",
    textAlign: "center",
    fontWeight: "bold",
  },
});

class ReportList extends React.Component {
  static propTypes = {
    listTxReports: PropTypes.func.isRequired,
  };

  componentDidMount() {
    this.props.listTxReports();
  }

  render() {
    const { classes } = this.props;

    return (
      <div className={classes.root}>
        <List />
      </div>
    );
  }
}

const mapDispatchToProps = dispatch => {
  return {
    listTxReports: () => dispatch(listTxReports()),
  };
};

export default connect(null, mapDispatchToProps)(withStyles(styles)(ReportList));
