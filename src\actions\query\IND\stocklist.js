export const LIST_STOCKLIST_QUERY = `query ($unicornId: [String], $status: [String], $includeId: [ID], $sbu: [String], $decoration: [ID], $unitView: [ID], $buildingGrade: [String], $hasCompany: Boolean $buildingUsage: [String], $stockIds: [ID], $sorter: [stockSorter], $buildingSourcesId: [ID], $district: [ID], $street: [ID], $streetNoMin: Int, $streetNoMax: Int, $areaMin: Int, $areaMax: Int, $searchAreaMin: Int, $searchAreaMax: Int, $priceMinAvg: Float, $priceMaxAvg: Float, $rentMinAvg: Float, $rentMaxAvg: Float, $priceMinTotal: Float, $priceMaxTotal: Float, $rentMinTotal: Float, $rentMaxTotal: Float, $isCarPark: Boolean, $underNego: Boolean, $haveSurveyorProposal: Boolean, $pdfPP: Boolean, $haveVR: Boolean, $haveStockPhoto: Boolean, $haveStockVideo: Boolean, $haveBuildingPhoto: Boolean, $haveBuildingVideo: Boolean, $haveLandSearchDoc: Boolean, $isWithKey: Boolean, $isMortgagee: Boolean, $isFacingLift: Boolean, $possession: [String], $limit: Int, $offset: Int, $ageMin: Int, $ageMax: Int, $floorMin: Int, $floorMax: Int, $floorLeft: String, $floorRight: String, $isNew: Boolean, $isMarketable: Boolean, $isWWW: Boolean, $isSoleAgent: Boolean, $havePhoto: Boolean, $haveVideo: Boolean, $createDateMin: String, $createDateMax: String, $lastUpdateDateMin: String, $lastUpdateDateMax: String, $tenancyExpireDateMin: String, $tenancyExpireDateMax: String, $havePropertyAdvertisements: Boolean, $confirmorStatus: Boolean, $isSaleEquity: Boolean, $withPassCodes: Boolean, $stockType: [String], $ownerType: [String], $isParentStock: Boolean, $isChildStock: Boolean, $isVendor: Boolean, $isCurrent: Boolean, $isFormer: Boolean, $contactsCompany: [ID], $contactsPerson: [String], $contactsEmail: [String]) {
  stocksCount(unicornId: $unicornId, status: $status, includeId: $includeId, sbu: $sbu, decoration: $decoration, unitView: $unitView, buildingGrade: $buildingGrade, hasCompany: $hasCompany, buildingUsage: $buildingUsage, _id: $stockIds, buildingSourcesId: $buildingSourcesId, district: $district, street: $street, streetNoMin: $streetNoMin, streetNoMax: $streetNoMax, areaMin: $areaMin, areaMax: $areaMax, searchAreaMin: $searchAreaMin, searchAreaMax: $searchAreaMax, priceMinAvg: $priceMinAvg, priceMaxAvg: $priceMaxAvg, rentMinAvg: $rentMinAvg, rentMaxAvg: $rentMaxAvg, priceMinTotal: $priceMinTotal, priceMaxTotal: $priceMaxTotal, rentMinTotal: $rentMinTotal, rentMaxTotal: $rentMaxTotal, isCarPark: $isCarPark, underNego: $underNego, haveSurveyorProposal: $haveSurveyorProposal, pdfPP: $pdfPP, haveVR: $haveVR, haveStockVideo: $haveStockVideo, haveBuildingVideo: $haveBuildingVideo, haveStockPhoto: $haveStockPhoto, haveBuildingPhoto: $haveBuildingPhoto, havePropertyAdvertisements: $havePropertyAdvertisements, haveLandSearchDoc: $haveLandSearchDoc, isWithKey: $isWithKey, isMortgagee: $isMortgagee, isFacingLift: $isFacingLift, possession: $possession, ageMin: $ageMin, ageMax: $ageMax, floorMin: $floorMin, floorMax: $floorMax, floorLeft: $floorLeft, floorRight: $floorRight, isNew: $isNew, isMarketable: $isMarketable, isSoleAgent: $isSoleAgent, isWWW: $isWWW, havePhoto: $havePhoto, haveVideo: $haveVideo, createDateMin: $createDateMin, createDateMax: $createDateMax, lastUpdateDateMin: $lastUpdateDateMin, lastUpdateDateMax: $lastUpdateDateMax, tenancyExpireDateMin: $tenancyExpireDateMin, tenancyExpireDateMax: $tenancyExpireDateMax, confirmorStatus: $confirmorStatus, isSaleEquity: $isSaleEquity, withPassCodes: $withPassCodes, stockType: $stockType, ownerType: $ownerType, isParentStock: $isParentStock, isChildStock: $isChildStock, isCurrVendor: $isVendor, isCurrent: $isCurrent, isFormer: $isFormer, contactsCompany: $contactsCompany, contactsPerson: $contactsPerson, contactsEmail: $contactsEmail)
  stocks(unicornId: $unicornId, status: $status, includeId: $includeId, sbu: $sbu, decoration: $decoration, unitView: $unitView, buildingGrade: $buildingGrade, hasCompany: $hasCompany, buildingUsage: $buildingUsage, _id: $stockIds, sort: $sorter, buildingSourcesId: $buildingSourcesId, district: $district, street: $street, streetNoMin: $streetNoMin, streetNoMax: $streetNoMax, areaMin: $areaMin, areaMax: $areaMax, searchAreaMin: $searchAreaMin, searchAreaMax: $searchAreaMax, priceMinAvg: $priceMinAvg, priceMaxAvg: $priceMaxAvg, rentMinAvg: $rentMinAvg, rentMaxAvg: $rentMaxAvg, priceMinTotal: $priceMinTotal, priceMaxTotal: $priceMaxTotal, rentMinTotal: $rentMinTotal, rentMaxTotal: $rentMaxTotal, isCarPark: $isCarPark, underNego: $underNego, haveSurveyorProposal: $haveSurveyorProposal, pdfPP: $pdfPP, haveVR: $haveVR, haveStockVideo: $haveStockVideo, haveBuildingVideo: $haveBuildingVideo, haveStockPhoto: $haveStockPhoto, haveBuildingPhoto: $haveBuildingPhoto, haveLandSearchDoc: $haveLandSearchDoc, isWithKey: $isWithKey, isMortgagee: $isMortgagee, isFacingLift: $isFacingLift, possession: $possession, limit: $limit, offset: $offset, ageMin: $ageMin, ageMax: $ageMax, floorMin: $floorMin, floorMax: $floorMax, floorLeft: $floorLeft, floorRight: $floorRight, isNew: $isNew, isMarketable: $isMarketable, isSoleAgent: $isSoleAgent, isWWW: $isWWW, havePhoto: $havePhoto, haveVideo: $haveVideo createDateMin: $createDateMin, createDateMax: $createDateMax, lastUpdateDateMin: $lastUpdateDateMin, lastUpdateDateMax: $lastUpdateDateMax, tenancyExpireDateMin: $tenancyExpireDateMin, tenancyExpireDateMax: $tenancyExpireDateMax, havePropertyAdvertisements: $havePropertyAdvertisements, confirmorStatus: $confirmorStatus, isSaleEquity: $isSaleEquity, withPassCodes: $withPassCodes, stockType: $stockType, ownerType: $ownerType, isParentStock: $isParentStock, isChildStock: $isChildStock, isCurrVendor: $isVendor, isCurrent: $isCurrent, isFormer: $isFormer, contactsCompany: $contactsCompany, contactsPerson: $contactsPerson, contactsEmail: $contactsEmail) {
    unit
    building {
      nameEn
      nameZh
    }
    district {
      abbr
      nameZh
    }
    askingRent {
      average
      total
      trend
    }
    askingPrice {
      average
      total
      trend
    }
    area
    _id
    sbu
    status
    statusZh
    floor {
      input
    }
    mortgagee
    isCarPark
    isSoleAgent
    isNew
    isSaleEquity
    unicorn {
      stock
    }
    haveTerrace
    haveRoof
    haveCockloft
    isMarketable
    isWWW
    tenancyCurrentTenant
    tenancyCurrentTenantZh
    tenancyExpireDate
    searchArea
    searchAreaType
    underNego
    haveSurveyorProposal
    pdfPP
    isWithKey
    withPassCodes
    isParentStock
    isChildStock
    haveVR
    haveStockVideo
    haveBuildingVideo
    haveStockPhoto
    haveBuildingPhoto
    havePropertyAdvertisements
    haveLandSearchDoc
    wwwScore {
      eaaOwner
      soleagent
      stockInformation
      newStock
      video
      kolVideo
      photo
    }
    wwwScoreTotal
    isOnline
    wwwAgentInfos {
      wwwBy
      wwwByName
      wwwByDept
      wwwDate
      wwwDesc
      scoreTotal
      score {
        eaaOwner
        soleAgent
        photo
        video
        kolVideo
      }
      eaaOwner
      soleAgent
      video
      videoId
      kolVideo
      kolVideoId
      photo
      photoIds
    }
    wwwChannelFull {
      eaaOwner
      soleagent
      video
      kolVideo
      photo
    }
  }
}
`;

export const LIST_DECORATIONS_QUERY = `
{
  decorations {
    _id
    nameEn
    nameZh
  }
}
`;

export const LIST_POSSESSIONS_QUERY = `
{
  possessions(sort: [{ field: _id, order: ASC }]) {
    _id
    nameEn
    nameZh
  }
}
`;

export const LIST_USAGES_QUERY = `
{
  usages {
    _id
    nameEn
    nameZh
  }
}
`;

export const LIST_STOCKTYPES_QUERY = `
{
  stockTypes(sort: [{ field: _id, order: ASC }]) {
    _id
    nameEn
    nameZh
  }
}
`;

export const LIST_CURRENTSTATES_QUERY = `
{
  currentState(sort: [{ field: _id, order: ASC }]) {
    _id
    nameEn
    nameZh
  }
}
`;
