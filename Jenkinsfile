pipeline {
    agent {
        label BUILD_LABEL
    }

    stages {
        stage('Git Pull') {
            steps {                
                sh '''
                    cd $SOURCE_PATH
                    git pull --rebase
                '''
            }
        }

        stage('Build') {
            steps {
                sh 'cat $SOURCE_PATH/docker-compose.yml | envsubst > $SOURCE_PATH/tmp.yml'
                sh '''
                    cd $SOURCE_PATH
                    docker-compose -f $SOURCE_PATH/tmp.yml build
                '''
            }
        }

        stage('Deploy') {
            steps {
                script {                    
                    try {
                        sh 'docker-compose -f $SOURCE_PATH/tmp.yml down'
                    } catch  (exc) {}

                    sh '''
                        cd $SOURCE_PATH
                        docker-compose -f tmp.yml up -d
                        rm tmp.yml
                    '''
                }
            }
        }
    }   
}