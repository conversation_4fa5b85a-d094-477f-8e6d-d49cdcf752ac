import {
  LIST_MESSAGES_START,
  LIST_MORE_MESSAGES_START,
  LIST_MESSAGES_SUCCESS,
  LIST_MESSAGES_NULL_SUCCESS,
  LIST_MESSAGES_PRIVATE_START,
  LIST_MORE_MESSAGES_PRIVATE_START,
  LIST_MESSAGES_PRIVATE_SUCCESS,
  LIST_MESSAGES_PRIVATE_NULL_SUCCESS,
  LIST_MESSAGES_PRIVATE_ERROR,
  CLEAR_MESSAGES_PRIVATE,
  LIST_MESSAGES_ERROR,
  CLEAR_MESSAGES
} from "../constants/messageCenter";

const initialState = {
  listed: false,
  listing: false,
  messages: [],
};

export default function messageCenter(state = initialState, action) {
  switch (action.type) {
    case LIST_MESSAGES_START:
      return {
        ...state,
        listed: false,
        listing: true,
        messages: [],
        hasMore: true,
        queryvariables: action.payload.variables,
        privateListed: false,
        privateListing: true,
        privateMessages: [],
        privateHasMore: true,
        privateQueryvariables: action.payload.variables
      };
    case LIST_MORE_MESSAGES_START:
      return {
        ...state,
        listing: true,
        hasMore: true,
        queryvariables: action.payload.variables
      };
    case LIST_MESSAGES_SUCCESS:
      return {
        ...state,
        listed: true,
        listing: false,
        hasMore: true,
        messages: state.messages.concat(action.payload.data.messageCenter || [])
      };
    case LIST_MESSAGES_NULL_SUCCESS:
      return {
        ...state,
        listing: false,
        listed: true,
        hasMore: false,
        messages: state.messages.concat(action.payload.data.messageCenter || [])
      };
    case LIST_MESSAGES_ERROR:
      return {
        ...state,
        listed: false,
        listing: false,
        error: action.payload.error
      };
    case CLEAR_MESSAGES:
      return {
        ...state,
        listed: false,
        listing: false,
        messages: [],
        queryvariables: null,
        error: null
      };
    case LIST_MESSAGES_PRIVATE_START:
      return {
        ...state,
        privateListed: false,
        privateListing: true,
        privateMessages: [],
        privateHasMore: true,
        privateQueryvariables: action.payload.variables
      };
    case LIST_MORE_MESSAGES_PRIVATE_START:
      return {
        ...state,
        privateListing: true,
        privateHasMore: true,
        privateQueryvariables: action.payload.variables
      };
    case LIST_MESSAGES_PRIVATE_SUCCESS:
      return {
        ...state,
        privateListed: true,
        privateListing: false,
        privateHasMore: true,
        privateMessages: state.privateMessages.concat(action.payload.data.privateMessage || [])
      };
    case LIST_MESSAGES_PRIVATE_NULL_SUCCESS:
      return {
        ...state,
        privateListing: false,
        privateListed: true,
        privateHasMore: false,
        privateMessages: state.privateMessages.concat(action.payload.data.privateMessage || [])
      };
    case LIST_MESSAGES_PRIVATE_ERROR:
      return {
        ...state,
        privateListed: false,
        privateListing: false,
        privateError: action.payload.error
      };
    case CLEAR_MESSAGES_PRIVATE:
      return {
        ...state,
        privateListed: false,
        privateListing: false,
        privateError: action.payload.error
      };
    default:
      return state;
  }
}
