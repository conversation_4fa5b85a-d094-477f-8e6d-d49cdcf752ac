import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import FieldValArrBox from "../../../../common/FieldValArrBox";
import { injectIntl } from "react-intl";

// We can inject some CSS into the DOM.
const styles = {
  priceBox: {
    backgroundColor: "rgba(232, 0, 0, .08)"
  },
  rentBox: {
    backgroundColor: "rgba(0, 197, 197, .1)"
  }
};

function EaaRecordBox(props) {
  const {
    classes,
    type,
    price = "---",
    date = "---",
    consultant = "---",
    intl,
    ...others
  } = props;

  let typeField = type === "rent"
    ? intl.formatMessage({ id: "search.common.rent" })
    : intl.formatMessage({ id: "search.common.price" });

  const items = [
    {
      field: typeField,
      val: price,
    },
    {
      field: intl.formatMessage({ id: "stock.effectivedate" }),
      val: date,
    },
    {
      field: intl.formatMessage({ id: "stock.handledby" }),
      val: consultant,
    },
  ];

  return (
    <FieldValArrBox
      className={type === "rent" ? classes.rentBox : classes.priceBox}
      items={items}
      {...others}
    />
  );
}

EaaRecordBox.propTypes = {
  classes: PropTypes.object.isRequired,
  type: PropTypes.string.isRequired,
  price: PropTypes.node,
  date: PropTypes.node,
  consultant: PropTypes.string,
};

export default withStyles(styles)(injectIntl(EaaRecordBox));
