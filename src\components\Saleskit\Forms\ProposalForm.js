import React, { useEffect, useState } from "react";
import { change, reduxForm, getFormValues } from "redux-form";
import { withStyles } from "@material-ui/core/styles";
import _ from "lodash";
import { connect, useSelector } from "react-redux";
import { injectIntl } from "react-intl";

import ProposalGeneralSection from "../ProposalGeneralSection";
import SectionBox from "../SectionBox";
import { getDefaultRemarks } from "../helpers";
import CreateProposalDialog from "../../common/CreateProposalDialog";
import { addActivityLog } from "@/actions/log";

const styles = (theme) => ({
  root: {},
  searchbutton: {
    margin: "1vw",
    textTransform: "none",
    fontSize: "1.125em",
    height: "42px",
  },
  clearbutton: {
    background: "#6be6a1",
  },
  buttoncontainer: {
    display: "flex",
    justifyContent: "space-evenly",
    alignItems: "center",
    zIndex: "999",
    position: "fixed",
    left: "50vw",
    bottom: "2vh",
    transform: "translateX(-50%)",
  },
});

const validate = (values) => {
  const errors = {};
  // if (values.dateMin && values.dateMax) {
  //   if (values.dateMin > values.dateMax) {P
  //     errors.dateMin = errors.dateMax = "Input format is invalid";
  //   }
  // }
  return errors;
};

let ProposalForm = ({
  classes,
  handleSubmit,
  initialize,
  initialValues,
  changeFieldValue,
  addActivityLog,
  stockId,
  buildingId,
  streetId,
  FormComponent,
  formState,
  stockMedia,
  buildingMedia,
  streetMedia,
  createDialogOpen,
  closeDialog,
  intl,
}) => {
  const stock = useSelector((state) => _.head(state.stock.detail));
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    if (!_.isEmpty(initialValues)) {
      addActivityLog("proposal", "read", initialValues);
      initialize(initialValues);
      setInitialized(true);
    }
  }, []);

  const handleTypeChange = (v) => {
    const { value } = v;

    const enableRent = value === "Lease" || value === "SaleAndLease";
    const enablePrice = value === "Sale" || value === "SaleAndLease";
    changeFieldValue("stock.totalPrice[0].isShow", enablePrice);
    changeFieldValue("stock.totalRent[0].isShow", enableRent);
    changeFieldValue("stock.avgPrice[0].isShow", enablePrice);
    changeFieldValue("stock.avgRent[0].isShow", enableRent);

    changeFieldValue("general.termRemarks", getDefaultRemarks(value));
  };

  const stockMediaData = stockId
    ? _.get(
        _.find(stockMedia, (m) => m.id === stockId.toString()),
        "data",
        {},
      )
    : {};
  const buildingMediaData = buildingId
    ? _.get(
        _.find(buildingMedia, (m) => m.id === buildingId.toString()),
        "data",
        {},
      )
    : {};

  const streetMediaData = streetId
    ? _.get(
        _.find(streetMedia, (m) => m.id === streetId.toString()),
        "data",
        {},
      )
    : {};

  return initialized ? (
    <form onSubmit={handleSubmit} className={classes.root}>
      <SectionBox
        header={intl.formatMessage({ id: "proposal.section.ppOptions" })}
      >
        <ProposalGeneralSection
          onTypeChange={handleTypeChange}
          changeFieldValue={changeFieldValue}
        />
      </SectionBox>
      <SectionBox header={intl.formatMessage({ id: "proposal.section.stock" })}>
        <FormComponent
          type={_.get(formState, "general.type")}
          changeFieldValue={changeFieldValue}
          formState={formState}
          mediaData={{
            stock: stockMediaData,
            building: buildingMediaData,
            street: streetMediaData,
          }}
          stockDetail={stock}
          initialValues={initialValues}
        />
      </SectionBox>
      <CreateProposalDialog open={createDialogOpen} close={closeDialog} />
    </form>
  ) : null;
};

ProposalForm = reduxForm({
  form: "proposal",
  validate,
})(ProposalForm);

const mapStateToProps = (state) => ({
  formState: getFormValues("proposal")(state),
  stockMedia: _.get(state, "stock.media", []),
  buildingMedia: _.get(state, "building.media", []),
  streetMedia: _.get(state, "street.media", []),
});

const mapDispatchToProps = (dispatch) => ({
  changeFieldValue: (key, value) => dispatch(change("proposal", key, value)),
  addActivityLog: (...args) => dispatch(addActivityLog(...args)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(ProposalForm)));
