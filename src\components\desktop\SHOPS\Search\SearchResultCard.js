import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import moment from "moment";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import UnlockDialogItem from "../../../common/UnlockDialogItem";
import DetailBoxTitle from "../../../common/DetailBoxTitle";
import DetailBoxSection from "../../../common/DetailBoxSection";
import VaryingVal from "../../../common/VaryingVal";
import GridSection from "../../../common/GridSection";
import { unlockStock } from "../../../../actions/stocklist";
import PropertyTagBar from "../../../common/PropertyTagBar";
import FavoriteButton from "../../../common/FavoriteButton";
import SearchCardTenantBar from "../../../common/SearchCardTenantBar";
import { numberComma, convertCurrency, get<PERSON><PERSON><PERSON><PERSON>, pares<PERSON><PERSON>or<PERSON>hopNo } from "../../../../helper/generalHelper";
import history from "../../../../core/history";
import clsx from "clsx";
import { injectIntl } from "react-intl";
import { formValueSelector } from "redux-form";
import { enableConsolidLandSearch,  permissions, generalDailyQuota } from "../../../../config";
import _ from "lodash";

const styles = (theme) => ({
  unlockedroot: {
    padding: "1vw 2vw",
    backgroundColor: "rgba(255, 255, 200, .6)",
    borderTop: "0px solid #fff",
    boxShadow: "none",
  },
  root: {
    padding: "1vw 2vw",
    backgroundColor: "rgba(255, 255, 255, .6)",
    borderTop: "0px solid #fff",
    boxShadow: "none",
  },
  priceRow: {
    display: "flex",
    justifyContent: "space-between",
    textAlign: "right",
    padding: "1vh 0 0.5vh 0",
  },
  commonItem: {
    display: "flex",
    fontSize: "1.5em",
    padding: "0 4px",
    borderRadius: 4,
    color: "#fff",
  },
  rentItem: {
    backgroundColor: "rgba(0, 197, 197, .75)",
  },
  priceItem: {
    backgroundColor: "rgba(232, 0, 0, .75)",
  },
  greyItem: {
    backgroundColor: "rgba(132, 132, 132, .1)",
  },
  rentItemNoValue: {
    backgroundColor: "rgba(140, 190, 190, 0.2)",
  },
  priceItemNoValue: {
    backgroundColor: "rgba(200, 170, 170, 0.2)",
  },
  fieldRow: {
    display: "flex",
    justifyContent: "space-between",
    marginTop: "-1vh",
  },
  fieldItem: {
    fontSize: "1.175em",
  },
  districtCode: {
    fontSize: "0.8em",
    fontWeight: 700,
    alignSelf: "center",
  },
  status: {
    fontWeight: 700,
  },
  link: {
    textDecoration: "none",
    color: "#000",
  },
  DetailBoxSectionContent: {
    padding: 0,
  },
  totalItem: {
    display: "block",
    width: "100%",
  },
  subtotalItem: {
    fontSize: "0.75em",
  },
  tagContainer: {
    padding: "0",
  },
  myfavoriteIcon: {
    padding: "0 1vw"
  }
});

class SearchResultCard extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    result: PropTypes.object,
    count: PropTypes.number,
    max: PropTypes.number,
    unlockStock: PropTypes.func.isRequired,
  };
  constructor() {
    super();
    this.state = {
      dialogOpen: false,
    };
  }

  handleClickOpenDialog = () => {
    this.setState({ dialogOpen: true });
  };

  handleCloseDialog = () => {
    this.setState({ dialogOpen: false });
  };

  goStockDetail = () => {
    history.push("/stock/" + this.props.result._id);
  };

  render() {
    const {
      classes,
      result,
      unlockStock,
      count,
      max,
      isUnlocked,
      unlockfinished,
      formStreets,
      formStreetsNoMax,
      formStreetsNoMin,
      favoriteStockIds,
      intl,
    } = this.props;
    const langKey = getLangKey(intl);
    const statusLangKey = intl.locale === "zh" ? "statusZh" : "status";
    const currentTenantLangKey = intl.locale === "zh" ? "tenancyCurrentTenantZh" : "tenancyCurrentTenant";

    const id = result._id ? result._id : "---";
    const unicornid =
      result.unicorn && result.unicorn.stock ? result.unicorn.stock : "---";

    // use the matchedStreets from backend if provided
    const backendMatchedStreet = result.matchedStreets && result.matchedStreets[0] ? result.matchedStreets[0] : null;
    // calculate matched street if backend did not provide
    let streets = result.streets || [];
    streets = streets.filter(v => {
      if (formStreets && formStreets.length > 0) {
        if (formStreets.indexOf(v._id) < 0) return false;
      }
      if (!isNaN(parseInt(formStreetsNoMax)) && formStreetsNoMax >= 0) {
        if (v.numberInNum > formStreetsNoMax) return false;
      }
      if (!isNaN(parseInt(formStreetsNoMin)) && formStreetsNoMin >= 0) {
        if (v.numberInNum < formStreetsNoMin) return false;
      }
      return true;
    });
    const matchedStreet = streets[0] || null;
    // use the main street as fallback
    const firstStreet = result.streets && result.streets[0] ? result.streets[0] : null;
    const street = backendMatchedStreet || matchedStreet || firstStreet;

    const streetnameEn = street && street.nameEn ? street.nameEn : "---";
    const streetnameZh = street && street.nameZh ? street.nameZh : "---";
    const streetnumber = street && street.number ? street.number : "---";

    const districtabbr =
      result.district && result.district.abbr ? result.district.abbr : "---";
    const districtZh =
      result.district && result.district.nameZh
        ? result.district.nameZh
        : "---";
    const streetRowEn = districtabbr + ", " + streetnameEn + " " + streetnumber;
    const streetRowZh = districtZh + ", " + streetnameZh + " " + streetnumber + " " + intl.formatMessage({ id: "stock.number" })
    const titleDown = intl.locale === "zh" ? streetRowZh : streetRowEn;

    const status =
      result.status && result.statusLangKey ? result.statusLangKey : "---";
    let statusDisplay = result[statusLangKey] ? result[statusLangKey] : "---";

    let size = result.area ? numberComma(result.area) : null
    let sizeforShop = result.areaForShop ? numberComma(result.areaForShop) : null;
    let sizeDisplay = size ? sizeforShop ? `${sizeforShop} / ${size}` : `${size}` : "---";


    let askingRent = result.askingRent ? result.askingRent.average : 0;
    let askingTotalRent = result.askingRent ? result.askingRent.total : 0;
    const askingRentType =
      result.askingRent && result.askingRent.trend
        ? result.askingRent.trend
        : null;
    let askingPrice = result.askingPrice ? result.askingPrice.average + " " : 0;
    let askingTotalPrice = result.askingPrice
      ? result.askingPrice.total
      : 0;
    const askingPriceType =
      result.askingPrice && result.askingPrice.trend
        ? result.askingPrice.trend
        : null;

    const floor =
      result.floor && result.floor.input ? result.floor.input : "---";
    const floorInChinese =
      result.floor && result.floor.inputInChinese ? result.floor.inputInChinese : "---";
    const unit = result.unit || "---";

    const buildingName =
      result.building && result.building[langKey]
        ? result.building[langKey]
        : "---";
    const currentTenant = result[currentTenantLangKey] ? result[currentTenantLangKey] : "---";
    const tenancyExpireDate = result.tenancyExpireDate ? moment(result.tenancyExpireDate, "x").format("YYYY-MM-DD") : "---";

    let priceContainer, leaseContainer;
    switch (status) {
      case "Sale": //出售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        askingRent = 0;
        askingTotalRent = 0;
        break;
      case "Lease": //出租
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Sale+Lease": //連約售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        askingRent = 0;
        askingTotalRent = 0;
        break;
      case "S+L": //租及售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Pending": //封盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Search": //查冊盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Don't Call": //拒致電
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Sold": //已售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Leased": //已租
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Fail": //錯誤盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "History": //舊資料
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Cancel": //錯盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "CU": //未定
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      default:
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
    }

    const tagMtg = !!result.mortgagee;
    const tagEaa = !!result.havePropertyAdvertisements;
    const tagSB = !!result.isSoleAgentForSale;
    const tagSR = !!result.isSoleAgentForLease;
    const tagMLL = !!result.isShoppingMallStock;
    const tagSSS = !!result.isSingleSideStock;
    const tagFnR = !!result.isFrontAndRearPortion;
    const tagKey = !!result.isWithKey;
    const tagPD = !!result.isWithKey; // TODO: field is deleted (TBC)
    const tagREP = !!result.isHandOver;
    const tagBD = result.boardStatus && result.boardStatus === "已掛板";
    const tagWWW = !!result.isWWW;
    const tagVideo = !!result.haveVideo;
    const tagsOverride = {
      [intl.formatMessage({ id: "stock.tag.sole" })]: null,
      [intl.formatMessage({ id: "stock.tag.carpark" })]: null,
      [intl.formatMessage({ id: "stock.tag.new" })]: null,
      [intl.formatMessage({ id: "stock.tag.equaity" })]: null,
      [intl.formatMessage({ id: "stock.tag.soleagentrent" })]: tagSR,
      [intl.formatMessage({ id: "stock.tag.soleagentbuy" })]: tagSB,
      [intl.formatMessage({ id: "stock.tag.mortgagee" })]: tagMtg,
      [intl.formatMessage({ id: "stock.tag.eaa" })]: tagEaa,
      [intl.formatMessage({ id: "stock.tag.shoppingmall" })]: tagMLL,
      [intl.formatMessage({ id: "stock.tag.singleside" })]: tagSSS,
      [intl.formatMessage({ id: "stock.tag.frontandrear" })]: tagFnR,
      [intl.formatMessage({ id: "stock.tag.key" })]: tagKey,
      // [intl.formatMessage({ id: "stock.tag.pending" })]: tagPD,
      [intl.formatMessage({ id: "stock.tag.replacement" })]: tagREP,
      [intl.formatMessage({ id: "stock.tag.www" })]: tagWWW,
      [intl.formatMessage({ id: "stock.video" })]: tagVideo,
    };

    return (
      <div>
        <div
          onClick={isUnlocked ? this.goStockDetail : this.handleClickOpenDialog}
          className={classes.link}
        >
          <GridSection
            className={isUnlocked ? classes.unlockedroot : classes.root}
          >
            <DetailBoxTitle
              text={paresFloorShopNo(floor, floorInChinese, unit, intl)}
              subtitle={titleDown}
              rightIcon={
                <span className={classes.status}>{statusDisplay}
                  <FavoriteButton favoriteStockIds={favoriteStockIds}
                    mongoid={id} stockid={unicornid} className={classes.myfavoriteIcon} />
                </span>
              }
            >
              <DetailBoxTitle
                subtitle={buildingName}
                rightIcon={
                  <span className={classes.fieldItem}>
                    {sizeDisplay}
                  </span>
                }
              />
              {enableConsolidLandSearch === "true" ? (
                <SearchCardTenantBar date={tenancyExpireDate}>{currentTenant}</SearchCardTenantBar>
              ) : (
                <div>{intl.formatMessage({ id: "stock.currenttenant" })}{": "}{currentTenant}</div>
              )}
              {/* { floor }{" "}
              {intl.formatMessage({ id: "search.common.floor" })}{", "}{unit} */}
              <DetailBoxSection
                contentClass={classes.DetailBoxSectionContent}
                noStrike={true}
              >
                <Grid container spacing={1} className={classes.priceRow}>
                  <Grid item xs={6}>
                    <VaryingVal
                      className={clsx(
                        classes.commonItem,
                        priceContainer,
                        askingPrice <= 0 && askingTotalPrice <= 0 && classes.priceItemNoValue
                      )}
                      type={askingPriceType}
                    >
                      <span>
                        {askingTotalPrice > 0 ? (
                          <span className={classes.totalItem}>
                            ${convertCurrency(askingTotalPrice)}
                          </span>
                        ) : (
                            <span className={classes.totalItem}>---</span>
                          )}
                        {askingPrice > 0
                          ? <span className={classes.subtotalItem}>@${numberComma(askingPrice)}</span>
                          : "---"}
                      </span>
                    </VaryingVal>
                  </Grid>

                  <Grid item xs={6}>
                    <VaryingVal
                      className={clsx(
                        classes.commonItem,
                        leaseContainer,
                        askingRent <= 0 && askingTotalRent <= 0 && classes.rentItemNoValue
                      )}
                      type={askingRentType}
                    // label={"Rent"}
                    >
                      <span>
                        {askingTotalRent > 0 ? (
                          <span className={classes.totalItem}>
                            ${convertCurrency(askingTotalRent)}
                          </span>
                        ) : (
                            <span className={classes.totalItem}>---</span>
                          )}
                        {askingRent > 0 ?
                          <span className={classes.subtotalItem}>@${numberComma(askingRent)}</span>
                          : "---"}
                      </span>
                    </VaryingVal>
                  </Grid>
                </Grid>
                <div className={classes.tagContainer}>
                  <PropertyTagBar detail={result} tagsOverride={tagsOverride} />
                </div>
              </DetailBoxSection>
            </DetailBoxTitle>
          </GridSection>
        </div>

        <UnlockDialogItem
          dialogOpen={this.state.dialogOpen}
          handleCloseDialog={this.handleCloseDialog}
          unlockStock={unlockStock}
          stockid={id}
          stockunicornid={unicornid}
          count={count}
          max={max}
          readytoRedirect={unlockfinished}
        />
      </div >
    );
  }
}

const selector = formValueSelector("searchForm");
const mapStateToProps = (state) => ({
  count: state.stocklist.count ? state.stocklist.count : 0,
  max: !_.isEmpty(state.employee.permissions)? 
  (!_.isNil(state.employee.permissions[permissions.PERMISSION_VIEW_STOCK_QUOTA])? 
    state.employee.permissions[permissions.PERMISSION_VIEW_STOCK_QUOTA] : 0)
 : generalDailyQuota,
  unlockfinished: state.stocklist.unlockfinished
    ? state.stocklist.unlockfinished
    : false,
  formStreets: selector(state, "streets"),
  formStreetsNoMax: selector(state, "streetsNoMax"),
  formStreetsNoMin: selector(state, "streetsNoMin"),
  favoriteStockIds: state.stock.favoriteStockIds ? state.stock.favoriteStockIds : [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    unlockStock: (id) => dispatch(unlockStock(id)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(SearchResultCard)));
