FROM node:16.13.1-alpine as module-install

WORKDIR /app

COPY package.json /app/package.json
COPY yarn.lock /app/yarn.lock

COPY package.json /app/
RUN yarn install

###### Stop running steps after this stage in development
###### Specify --target=serve when building the image
FROM node:16.13.1-alpine as serve

WORKDIR /home/<USER>

USER node

COPY --from=module-install --chown=node:node /app/node_modules/ /home/<USER>/node_modules

# ENV HOME=/home/<USER>

# Copy application files
COPY --chown=node:node . /home/<USER>

# RUN chown node:node entrypoint.sh

ARG MODE
ENV NODE_ENV=${MODE}
