/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import { checkAndParseUrlParam } from "../../helper/generalHelper";

const title = "ApproveMedia";

async function action({ store, params, query }) {
  const { auth } = store.getState();
  if (!auth.user) {
    return { redirect: "/login" };
  } else if (auth.user.authorized == false) {
    return { redirect: "/login" };
  }

  const ApproveMedia = await require.ensure(
    [],
    require => require("./ApproveMedia").default,
    "approveMedia"
  );

  return {
    chunks: ["approveMedia"],
    title,
    component: (
      <ApproveMedia />
    )
  };
}

export default action; 