import React from 'react';
import PdfJs from 'pdfjs-dist';
import PropTypes from 'prop-types';

PdfJs.GlobalWorkerOptions.workerSrc = '//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.1.266/pdf.worker.js';

class PdfImg extends React.Component {
  static defaultProps = {
    page: '1',
  }

  static propTypes = {
    page: PropTypes.string,
    src: PropTypes.string,
    /** @type PropTypes.Requireable<File> */
    file: PropTypes.any,
  };

  componentDidMount() {
    this._renderPdf();
  }

  componentDidUpdate(prevProps) {
    if (
      (prevProps.src !== this.props.src || prevProps.file !== this.props.file) ||
      (prevProps.page !== this.props.page)
    ) {
      this._cleanupResources();
      this._renderPdf();
    }
  }

  componentWillUnmount() {
    this._cleanupResources();
  }

  constructor(props) {
    super(props);
    this.state = {
      imgSrc: '',
    };
    this.currentPdf = null;
    this.currentCanvas = null;
    this.renderTask = null;
  }

  render() {
    const { /* className, */ src, page, ...rest } = this.props;
    const { imgSrc } = this.state;
    return (
      <div
        {...rest}
        ref={(container) => this.container = container}>
        <img src={imgSrc} alt={`Page ${page}`} />
      </div>
    )
  }

  async _renderPdf() {
    const { src: pdfUrl, file, page: pageNumber } = this.props;

    let loadingTask;
    if (pdfUrl) {
      loadingTask = PdfJs.getDocument(pdfUrl);
    } else if (file) {
      const arrayBuffer = await file.arrayBuffer();
      loadingTask = PdfJs.getDocument({ data: arrayBuffer });
    } else {
      return;
    }

    if (!this.container) { return; }
    const { width, height } = this.container.getBoundingClientRect();
    loadingTask
      .promise.then((pdf) => {
        this.currentPdf = pdf;
        return this.currentPdf.getPage(parseInt(pageNumber));
      }).catch((error) => {
        console.error('Error rendering PDF:', error);
      })
      .then((page) => {
        const naturalViewport = page.getViewport(1);
        const scale = width / naturalViewport.width;
        const viewport = page.getViewport(scale);

        this.currentCanvas = document.createElement('canvas');
        const canvasContext = this.currentCanvas.getContext('2d');
        this.currentCanvas.height = viewport.height;
        this.currentCanvas.width = viewport.width;

        this.renderTask = page.render({ canvasContext, viewport });
        return this.renderTask.promise;
      })
      .then(() => {
        const imgSrc = this.currentCanvas.toDataURL();
        this.setState({ imgSrc });
      });
  }

  _cleanupResources = () => {
    // Cancel any ongoing render task
    if (this.renderTask) {
      this.renderTask.cancel();
      this.renderTask = null;
    }

    // Destroy the PDF instance
    if (this.currentPdf) {
      this.currentPdf.destroy();
      this.currentPdf = null;
    }

    // Clear the canvas reference (no need to remove from DOM)
    if (this.currentCanvas) {
      this.currentCanvas = null;
    }

    // Clear the data URL from state for garbage collection
    if (this.state.imgSrc) {
      this.setState({ imgSrc: '' });
    }
  };
}

export default PdfImg;
