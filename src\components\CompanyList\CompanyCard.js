import React from "react";
import { makeStyles } from "@material-ui/core/styles";
import PropTypes from "prop-types";
import _ from "lodash";
import { Grid } from "@material-ui/core";
import { FormattedMessage } from "react-intl";
import moment from "moment";

import history from "@/core/history";

const useStyles = makeStyles({
  wrapper: {
    backgroundColor: "rgba(255, 255, 255, .6)",
    height: 120,
    margin: "5px 0px 7px",
    padding: "1vw 2vw",
    fontSize: 16,
  },
  status: {
    fontWeight: 600,
    fontSize: 15,
    whiteSpace: "nowrap",
  },
});

function CompanyCard({ company }) {
  const classes = useStyles();

  return (
    <Grid
      container
      direction="column"
      className={classes.wrapper}
      justify="space-between"
      onClick={() => history.push(`/company/${_.get(company, "_id")}`)}
    >
      <Grid item container justify="space-between" wrap="nowrap" spacing={2}>
        <Grid item>
          {_.get(company, "companyNameZh") && (
            <Grid item>{_.get(company, "companyNameZh")}</Grid>
          )}

          {_.get(company, "companyNameEn") && (
            <Grid item>{_.get(company, "companyNameEn")}</Grid>
          )}
        </Grid>
        {/* <Grid item className={classes.status}>
          暫沒更新
        </Grid> */}
      </Grid>
      <Grid
        item
        container
        justify="space-between"
        wrap="nowrap"
        spacing={2}
        style={{ lineHeight: 1.2 }}
      >
        <Grid item style={{ width: "100%" }}>
          {_.get(company, "rightPerson")}
        </Grid>
        <Grid item style={{ whiteSpace: "nowrap", textAlign: "end" }}>
          <div>
            <FormattedMessage
              id="company.card.createDate"
              values={{
                date: _.get(company, "createDate")
                  ? moment(
                      _.get(company, "createDate"),
                    ).format("DD/MM/YYYY")
                  : "",
              }}
            />
          </div>
          <div>
            <FormattedMessage
              id="company.card.updateDate"
              values={{
                date: _.get(company, "updateDate")
                  ? moment(
                      _.get(company, "updateDate"),
                    ).format("DD/MM/YYYY")
                  : "",
              }}
            />
          </div>
        </Grid>
      </Grid>
    </Grid>
  );
}

CompanyCard.propTypes = {
  company: PropTypes.object.isRequired,
};

export default CompanyCard;
