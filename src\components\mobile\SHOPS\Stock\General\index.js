import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import DetailTabPanelFrame from "../../../../common/DetailTabPanelFrame";
import StockMain from "./StockMain";
import BasicProperty from "./BasicProperty";
import Remarks from "./Remarks";
import ContactInfo from "./ContactInfo";
import EaaRecord from "./EaaRecord";
import Tenancy from "./Tenancy";
import ExtraCharge from "./ExtraCharge";
import Promote from "./Promote";
import ConsultantShare from "./ConsultantShare";
import LandSearch from "./LandSearch";
import {enableConsolidLandSearch, enableWWWScore} from "../../../../../config";
import _ from "lodash";
import WWWStocks from "@/components/mobile/SHOPS/Stock/General/WWWStocks";

const styles = (theme) => ({
  mainBox: {
    display: "flex"
  }
});

function General({
  classes,
  listing,
  listed,
  currentStock,
  detail,
  handController,
  btnController
}) {
  const [appIsMounted, setAppIsMounted] = useState(false);

  useEffect(() => {
    requestAnimationFrame(() => {
      setAppIsMounted(true);
    });
  }, []);

  const stock = _.find(detail, (stock) => stock._id === currentStock);

  const hasData = !!stock && Object.keys(stock).length > 0;

  return (
    <DetailTabPanelFrame
      hasData={hasData}
      listing={listing}
      listed={listed}
      notFoundText="Stock not found"
    >
      {handController}
      <StockMain detail={stock} btnController={btnController} />
      <BasicProperty detail={stock} />
      {/* only load transaction graph in client side */}
      {appIsMounted &&
        React.createElement(require("./Transaction").default, {
          detail: stock,
        })}
      {enableConsolidLandSearch == "true" && <LandSearch detail={stock} />}
      <Tenancy detail={stock} />
      <Promote detail={stock} />
      <ExtraCharge detail={stock} />
      <ContactInfo detail={stock} />
      <EaaRecord detail={stock} />
      {enableWWWScore && <WWWStocks stockId={_.get(stock, '_id')} />}
      <Remarks detail={stock} />
      <ConsultantShare detail={stock} />
    </DetailTabPanelFrame>
  );
}

General.propTypes = {
  classes: PropTypes.object.isRequired,
  listing: PropTypes.bool,
  currentStock: PropTypes.string,
  detail: PropTypes.array,
  handController: PropTypes.node,
  btnController: PropTypes.node,
};

const mapStateToProps = (state) => ({
  detail: state.stock.detail ? state.stock.detail : [],
  currentStock: state.stock.currentDetail ? state.stock.currentDetail : "",
  listed: state.stock.listed ? state.stock.listed : false,
  listing: state.stock.listing ? state.stock.listing : false,
});

export default connect(mapStateToProps)(withStyles(styles)(General));
