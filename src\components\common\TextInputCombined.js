import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import TextField from "@material-ui/core/TextField";
import Switch from "./Switch";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    "& .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline": {
      // borderRadius: 0,
    },
    "&:hover .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline": {
      borderColor: "rgba(0, 0, 0, 0.23)",
    },
    "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23)",
    },
  },
  notchedOutline: {
    borderWidth: "1px",
    borderColor: "green !important",
  },
  leftnotchedOutline: {
    borderRight: 0,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
  },
  rightnotchedOutline: {
    borderLeft: 0,
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    "& legend": {
      textAlign: "right",
      visibility: "visible",
      transform: "translate(8px, -8px) scale(0.9)",
      pointerEvents: "visible",
    },
  },
  labelRoot: {
    fontSize: 30,
  },
  legendRight: {
    textAlign: "right",
  },
  leftInputLabel: {},
  rightInputLabel: {
    visibility: "hidden",
  },
};

function TextInputCombined(props) {
  const {
    classes,
    className,
    label,
    input,
    meta: { touched, invalid, error },
    compPosition,
    rightcomp,
    InputProps,
    disabled,
    ...custom
  } = props;

  return (
    <TextField
      className={clsx(classes.root, className)}
      label={compPosition === "right" ? rightcomp : label}
      placeholder={label}
      error={touched && invalid}
      helperText={touched && error}
      InputLabelProps={{
        shrink: true,
        classes: {
          root:
            compPosition == "left"
              ? classes.leftInputLabel
              : classes.rightInputLabel,
        },
      }}
      InputProps={{
        ...InputProps,
        classes: {
          notchedOutline:
            compPosition == "left"
              ? classes.leftnotchedOutline
              : classes.rightnotchedOutline,
        },
        disabled,
      }}
      {...input}
      {...custom}
    />
  );
}

export default withStyles(styles)(TextInputCombined);
