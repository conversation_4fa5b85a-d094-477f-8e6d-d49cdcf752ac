import fetch from "node-fetch";
import _ from "lodash";

import { api, sbu } from "@/config";

export const video = async (req, res, next, getQuery) => {
  if (!req.user) {
    next();
  } else {
    try {
      // Fetch the image from the S3 URL
      const s3Url = req.query.mediumRoot + "/" + req.query.filename;
      const s3Response = await fetch(s3Url);

      // Return the image data to the frontend
      res.setHeader("Content-Type", s3Response.headers.get("Content-Type"));
      res.setHeader("Access-Control-Allow-Origin", "*");
      res.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
      res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
      res.setHeader("Pragma", "no-cache");
      res.setHeader("Expires", "0");
      s3Response.body.pipe(res);
    } catch (e) {
      console.error(e);
      res.send(e);
    }
  }
};
