import { sbu } from "../../../config";

export default function createGetQuery() {
  // import query files from the corresponding SBU folder
  const promises = [
    import("./" + sbu + "/stock"),
    import("./" + sbu + "/employee"),
    import("./" + sbu + "/proposal"),
    import("./" + sbu + "/listProposal"),
    import("./" + sbu + "/applySearch"),
    import("./" + sbu + "/company"),
  ];

  const promise = Promise.all(promises);

  return async function getQuery(key) {
    let query = null;
    const modules = await promise;
    modules.forEach((v) => {
      if (v[key]) query = v[key];
    });
    return query;
  };
}
