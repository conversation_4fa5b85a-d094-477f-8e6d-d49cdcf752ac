/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React, { useState, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { createStyles, withStyles } from '@material-ui/styles';
import Layout from '../../components/Layout';
import { FormattedMessage, injectIntl } from 'react-intl';
import CircularProgress from '@material-ui/core/CircularProgress';
import Typography from '@material-ui/core/Typography';
import Button from '@material-ui/core/Button';
import Snackbar from '@material-ui/core/Snackbar';
import { listApproveMedia, updateMedium, clearApproveMedia } from '../../actions/medium';
import _ from 'lodash';
import CheckIcon from '@material-ui/icons/Check';
import CloseIcon from '@material-ui/icons/Close';

import MediaView from '../../components/Media/MediaView';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import CustomizedDialog from '@/components/common/Dialog';
import clsx from 'clsx';

const styles = createStyles(theme => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    fontSize: '1rem',
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    fontWeight: 400,
    lineHeight: '1.5',
    letterSpacing: '0.00938em',
  },
  loading: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: 'calc(100vh - 100px)',
    gap: '16px',
  },
  emptyList: {
    textAlign: 'center',
    marginTop: '30px',
    color: '#999',
  },
  approveButton: {
    backgroundColor: '#37CC71',
    color: 'white',
    padding: '8px 16px',
    borderRadius: '4px',
    fontWeight: 'bold',
    fontSize: '14px',
    textTransform: 'none',
    flex: '1',
    '&:hover': {
      backgroundColor: '#388e3c',
    },
    height: '24px',
    minWidth: '50px',
  },
  rejectButton: {
    backgroundColor: '#BE2129',
    color: 'white',
    padding: '8px 16px',
    borderRadius: '4px',
    fontWeight: 'bold',
    fontSize: '14px',
    textTransform: 'none',
    flex: '1',
    '&:hover': {
      backgroundColor: '#d32f2f',
    },
    height: '24px',
    minWidth: '50px',
  },
  approvalButtonsContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%',
    // marginTop: '8px',
    gap: '6px',
  },
  contentContainer: {
    padding: '1vh 2vw',
  },
  buttonIcon: {
    marginRight: '4px',
  },
  centerText: {
    textAlign: 'center',
  },
  popupContainer: {
    width: "60vw",
    zIndex: 1001,

    "& button": {
      paddingTop: "20px",
      paddingBottom: "20px",
    },
  },
}));

const ApproveMedia = ({
  classes, 
  approveMediaList = [], 
  listedApproveMedia, 
  listingApproveMedia, 
  listApproveMedia, 
  updateMedium, 
  clearApproveMedia, 
  intl,
  dispatch,
  headerRef
}) => {
  // 状态管理
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: ''
  });
  
  // 确认弹窗状态
  const [dialogState, setDialogState] = React.useState({
    open: false,
    type: null, // 'approved' 或 'rejected'
    media: null,
    processing: false
  });
  
  const [resDialogState, setResDialogState] = React.useState({
    open: false,
    type: null, // 'approved' 或 'rejected'
  });

  // 页面加载时获取待审核的媒体列表
  useEffect(() => {
    listApproveMedia({ approval: 'waiting' });

    if (headerRef?.current) {
      headerRef.current.changeTitle(
        intl.formatMessage({ id: 'home.approve.media' })
      );
    }

    // 组件卸载时清除媒体列表数据
    return () => clearApproveMedia();
  }, [listApproveMedia, clearApproveMedia, intl, headerRef]);

  // 处理媒体审批状态更新
  const handleMediaStatusUpdate = (media, approval) => {
    setDialogState(prev => ({ ...prev, processing: true }));
    
    // 更新媒体状态
    updateMedium({
      id: media.id,
      approval
    })
    .then(() => {
      // 更新成功后再更新本地Redux状态
      const updatedApproveMediaList = approveMediaList.filter(item => item.id !== media.id);
      dispatch({
        type: 'LIST_APPROVE_MEDIA_SUCCESS',
        payload: {
          approveMediaList: updatedApproveMediaList
        }
      });

      // 显示操作成功的通知
      const messageId = approval === 'approved' 
        ? 'stock.approveMedia.approveSuccess'
        : 'stock.approveMedia.rejectSuccess';
      
      setSnackbar({
        open: true,
        message: intl.formatMessage({ id: messageId })
      });
    })
    .catch((error) => {
      // 显示操作失败的通知
      const messageId = approval === 'approved' 
        ? 'stock.approveMedia.approveFailed'
        : 'stock.approveMedia.rejectFailed';
      
      setSnackbar({
        open: true,
        message: intl.formatMessage({ id: messageId }, { error: error?.message || '' })
      });
    })
    .finally(() => {
      handleCloseDialog();
      setResDialogState({
        type: approval === 'approved' ? "approved" : "rejected",
        open: true,
      });
    });
  };

  // 关闭通知
  const handleCloseSnackbar = () => setSnackbar(prev => ({ ...prev, open: false }));
  
  // 打开确认对话框
  const handleOpenDialog = (media, type) => {
    setDialogState({
      open: true,
      type,
      media,
      processing: false
    });
  };
  
  // 关闭确认对话框
  const handleCloseDialog = () => {
    setDialogState({
      open: false,
      type: null,
      media: null,
      processing: false
    });
  };

  const handleCloseResDialog = () => {
    setResDialogState({
      type: null,
      open: false,
    });
  };
  
  // 确认操作
  const handleConfirmAction = () => {
    const { type, media } = dialogState;
    if (media && type) {
      const approval = type === 'approved' ? 'approved' : 'rejected';
      handleMediaStatusUpdate(media, approval);
    }
  };

  const renderCustomButton = useMemo(() => (media, isPopup = true) => {
    if (!media || media.approval !== 'waiting') return null;

    return (
      <div className={clsx(classes.approvalButtonsContainer, isPopup ? classes.popupContainer : "")}>
        <Button 
          className={classes.rejectButton}
          onClick={(e) => {
            e.stopPropagation();
            handleOpenDialog(media, 'rejected');
          }}
          variant="contained"
          fullWidth
        >
          <CloseIcon className={classes.buttonIcon} />
        </Button>
        <Button 
          className={classes.approveButton}
          onClick={(e) => {
            e.stopPropagation();
            handleOpenDialog(media, 'approved');
          }}
          variant="contained"
          fullWidth
        >
          <CheckIcon className={classes.buttonIcon} />
        </Button>
      </div>
    );
  }, [classes]);

  // 使用useMemo计算分组媒体数据，避免不必要的重新计算
  const groupedApproveMediaList = useMemo(() => {
    if (!approveMediaList?.length) return { stock: [], building: [], street: [] };
    
    return approveMediaList.reduce((acc, media) => {
      if (media.stockId) {
        acc.stock.push(media);
      } else if (media.buildingId) {
        acc.building.push(media);
      } else if (media.streetId) {
        acc.street.push(media);
      }
      return acc;
    }, { stock: [], building: [], street: [] });
  }, [approveMediaList]);
  
  // 准备各类媒体数据
  const prepareApproveMediaData = (approveMediaData) => {
    if (!approveMediaData?.length) return {};
    
    const groupedApproveMediaList = {
      photo: [],
      video: [],
      document: [],
      kolVideo: [],
    };
    
    approveMediaData.forEach(media => {
      const mediaWithButtons = {
        ...media,
        renderCustomButtons: () => renderCustomButton(media, false),
      };
      
      switch (media.type) {
        case 'photo':
          groupedApproveMediaList.photo.push(mediaWithButtons);
          break;
        case 'video':
          groupedApproveMediaList.video.push(mediaWithButtons);
          break;
        case 'kol_video':
          groupedApproveMediaList.kolVideo.push(mediaWithButtons);
          break;
        case 'document':
          groupedApproveMediaList.document.push(mediaWithButtons);
          break;
        default:
          break;
      }
    });
    
    return groupedApproveMediaList;
  };

  // 使用useMemo预处理各来源的媒体数据
  const stockMedia = useMemo(() => prepareApproveMediaData(groupedApproveMediaList.stock), [groupedApproveMediaList.stock]);
  const buildingMedia = useMemo(() => prepareApproveMediaData(groupedApproveMediaList.building), [groupedApproveMediaList.building]);
  const streetMedia = useMemo(() => prepareApproveMediaData(groupedApproveMediaList.street), [groupedApproveMediaList.street]);

  // 检查是否有数据
  const hasStockMedia = !_.isEmpty(stockMedia) && Object.values(stockMedia).some(arr => arr.length > 0);
  const hasBuildingMedia = !_.isEmpty(buildingMedia) && Object.values(buildingMedia).some(arr => arr.length > 0);
  const hasStreetMedia = !_.isEmpty(streetMedia) && Object.values(streetMedia).some(arr => arr.length > 0);
  const hasData = hasStockMedia || hasBuildingMedia || hasStreetMedia;

  // 组合所有媒体类型
  const groupSections = ['document', 'kol_video', 'video', 'photo'];

  return (
    <div>
      <div style={{
        position: 'fixed',
        width: '100%',
        height: '100vh',
        zIndex: -1,
        left: 0,
        top: 0,
        backgroundColor: '#FFF',
      }}></div>
      <Layout
        header={<FormattedMessage id="home.approve.media" />}
        hideSearchIcon={true}
      >
        {/* <DetailTabPanelFrame
          hasData={hasData}
          listing={listingMedia}
          listed={listedMedia}
          notFoundText="Building photo not found"
        >
          <MediaMain
            media={buildingMedia}
            mediaType="building"
            defaultExpand={true}
            isGroup
            groupSections={groupSections}
          />
        </DetailTabPanelFrame> */}
        <div className={classes.root}>

          {listingApproveMedia && (
            <div className={classes.loading}>
              <CircularProgress size={40} />
            </div>
          )}

          {!hasData && listedApproveMedia && (
            <Typography variant="body1" className={classes.emptyList}>
              <FormattedMessage id="stock.approveMedia.noMediaToApprove"/>
            </Typography>
          )}

          {listedApproveMedia && hasData && (
            <div className={classes.contentContainer}>
              <MediaView
                stockMedia={stockMedia}
                buildingMedia={buildingMedia}
                streetMedia={streetMedia}
                groupSections={groupSections}
                isGroup={false}
                hideTypeSelect
                viewType="list"
                renderCustomButton={renderCustomButton}
              />
            </div>
          )}
        </div>
      </Layout>
      
      <Snackbar
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        open={snackbar.open}
        autoHideDuration={2000}
        onClose={handleCloseSnackbar}
        message={snackbar.message}
      />
      
      {/* 确认对话框 */}
      <ConfirmDialog
        open={dialogState.open}
        handleClose={handleCloseDialog}
        onOkCallback={handleConfirmAction}
        buttonMainProps={{ disabled: dialogState.processing }}
        okMsg={intl.formatMessage({ id: "common.confirm" })}
        cancelMsg={intl.formatMessage({ id: "common.cancel2" })}
      >
        <div className={classes.centerText}>
          {dialogState.type === 'approved' && intl.formatMessage({ id: "media.message.confirmApprove" })}
          {dialogState.type === 'rejected' && intl.formatMessage({ id: "media.message.confirmReject" })}
        </div>
        {dialogState.processing && (
          <div className={classes.centerText} style={{ marginTop: '16px' }}>
            <CircularProgress size={24} />
          </div>
        )}
      </ConfirmDialog>

      <CustomizedDialog
        open={resDialogState.open}
        handleClose={handleCloseResDialog}
      >
        <div className={classes.centerText}>
          {resDialogState.type === "approved" && intl.formatMessage({ id: "media.message.approve.ok" })}
          {resDialogState.type === "rejected" && intl.formatMessage({ id: "media.message.reject.ok" })}
        </div>
      </CustomizedDialog>
    </div>
  );
};

ApproveMedia.propTypes = {
  classes: PropTypes.object.isRequired,
  approveMediaList: PropTypes.array,
  listedApproveMedia: PropTypes.bool,
  listingApproveMedia: PropTypes.bool,
  listApproveMedia: PropTypes.func.isRequired,
  updateMedium: PropTypes.func.isRequired,
  clearApproveMedia: PropTypes.func.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapStateToProps = state => ({
  approveMediaList: state.medium.approveMediaList || [],
  listedApproveMedia: state.medium.listedApproveMedia,
  listingApproveMedia: state.medium.listingApproveMedia,
});

const mapDispatchToProps = dispatch => ({
  listApproveMedia: (variables) => dispatch(listApproveMedia(variables)),
  updateMedium: (medium) => dispatch(updateMedium(medium)),
  clearApproveMedia: () => dispatch(clearApproveMedia()),
  dispatch,
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(ApproveMedia))); 