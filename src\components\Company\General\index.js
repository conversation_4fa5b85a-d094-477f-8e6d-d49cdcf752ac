import React, { useEffect } from "react";
import { Box, Grid } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import _ from "lodash";
import { connect, useSelector } from "react-redux";
import PropTypes from "prop-types";
import { injectIntl, FormattedMessage } from "react-intl";
import {dateFormatter} from '../../../helper/generalHelper'

import DetailTabPanelFrame from "../../common/DetailTabPanelFrame";
import Search from "./Search";
import Vendor from "./Vendor";
import { listCompanySearchDocs, querySourceOptions } from "@/actions/company";
import { queryBusinessOptions } from '@/actions/stock'
import { COMPANY_TYPES } from "@/constants/company";
import ReadOnlyText from "../../common/ReadOnlyText";
import IconChipButton from "../../common/IconChipButton";
import { listDistricts } from "../../../actions/district";

const useStyles = makeStyles({
  remarks: {
    "&>div": {
      background: 'rgba(255,254,201)',
      padding: 4,
      borderRadius: 4
    }
  }
});
function General({
  company,
  listingCompany,
  listedCompany,
  listCompanySearches,
  querySourceOptions,
  queryBusinessOptions,
  queryDistricts,
  intl,
}) {
  const sourceOptions = useSelector((state)=> state.company.sourceOptions)
  const businessOptions = useSelector((state)=> state.stock.businessOptions)
  const districts = useSelector((state)=> state.district.districts)
  const langKey = intl.local === 'zh' ? "nameZh" : "nameEn"
  const classes = useStyles()

  useEffect(() => {
    const companyRegNo = _.get(company, "companyRegistrationNumber");
    const companyNameZh = _.get(company, "companyNameZh");
    const companyNameEn = _.get(company, "companyNameEn");

    if (companyRegNo || companyNameEn || companyNameZh) {
      listCompanySearches(companyRegNo, companyNameEn || companyNameZh);
    }
  }, [company]);

  useEffect(()=>{
    if(!sourceOptions.length) {
      querySourceOptions()
    }
    if(!businessOptions.length) {
      queryBusinessOptions()
    }
    if(!districts){
      queryDistricts()
    }
  }, [])

  const displayUpdateMessage = (fieldName) => {
    const stringBlocks = [];

    if (fieldName === `createBy`) {
      if (
        !_.isNil(
          _.get(
            company,
            "createDate",
          ),
        )
      )
        stringBlocks.push(
          `${dateFormatter(
            _.get(
              company,
              "createDate",
            ),
          )}`,
        );

      if (
        !_.isNil(
          _.get(
            company,
            "createBy.name_en",
          ),
        )
      )
        stringBlocks.push(
          `${_.get(
            company,
            "createBy.name_en",
          ) ?? ""}`,
        );
    } else {
      if (
        !_.isNil(
          _.get(
            company,
            "updateDate",
          ),
        )
      )
        stringBlocks.push(
          `${dateFormatter(
            _.get(
              company,
              "updateDate",
            ),
          )}`,
        );

      if (
        !_.isNil(
          _.get(
            company,
            "updateBy.name_en",
          ),
        )
      )
        stringBlocks.push(
          `${_.get(
            company,
            "updateBy.name_en",
          ) ?? ""}`,
        );
    }

    return stringBlocks.join(" ");
  }

  const getDisplayLabel = (arr, id, langKey) => {
    const targetObj = _.find(arr, {
      _id: id,
    });

    return _.get(targetObj, langKey, "---");
  }

  const displayDistrictStr = (districtIds) => {
    let strArr = []
    districtIds.forEach(i => {
      const districtItem = _.find(districts, {_id: i})
      if(districtItem){
        strArr.push(districtItem[langKey])
      }
    })
    return strArr.join(',') || '---'
  }

  return (
    <DetailTabPanelFrame
      hasData={!_.isEmpty(company)}
      listed={listedCompany}
      listing={listingCompany}
      notFoundText="Company not found."
    >
      <Box padding="3px">
        <Grid container spacing={1} style={{ margin: "5px -4px 0" }}>
          <Grid item xs={6}>
            <ReadOnlyText
              label={intl.formatMessage({ id: "company.detail.clientId" })}
              value={_.get(company, "clientId") || "---"}
            />
          </Grid>
          <Grid item xs={6}>
            <ReadOnlyText
                label={intl.formatMessage({ id: "company.detail.source" })}
                value={getDisplayLabel(sourceOptions, _.get(company, 'source'), langKey)}
              />
          </Grid>
          <Grid container spacing={1} style={{ margin: "0 -4px 0 0" }}>
            <Grid item xs={6}>
              <ReadOnlyText
                label={intl.formatMessage({ id: "company.detail.createdBy" })}
                value={displayUpdateMessage('createBy') || '---'}
              />
            </Grid>
            <Grid item xs={6}>
              <ReadOnlyText
                label={intl.formatMessage({ id: "company.detail.updatedBy" })}
                value={displayUpdateMessage('updateBy') || '---'}
              />
            </Grid>
          </Grid>
          <Grid item xs={12}>
            <ReadOnlyText
              label={intl.formatMessage({ id: "company.detail.nextUpdate" })}
              value={dateFormatter(_.get(company, "nextUpdate")) || "---"}
            />
          </Grid>
        </Grid>
        <Grid container spacing={1}>
          <Grid item xs={12}>
            <ReadOnlyText
              multiline
              label={intl.formatMessage({
                id: "company.detail.companyNameZh",
              })}
              value={_.get(company, "companyNameZh") || "---"}
            />
          </Grid>
          <Grid item xs={12}>
            <ReadOnlyText
              multiline
              label={intl.formatMessage({
                id: "company.detail.companyNameEn",
              })}
              value={_.get(company, "companyNameEn") || "---"}
            />
          </Grid>
          <Grid container spacing={1} style={{ margin: "0 -4px 0 0" }}>
            <div style={{width: '100%', padding: '4px', height: 16}}>
              <div style={{fontSize: '1rem', color: 'rgba(0, 0, 0, 0.54)', transform: 'translate(0, 1.5px) scale(0.75)', transformOrigin: 'top left'}}>
                <FormattedMessage id="company.detail.companyPhoneNumber" />
              </div>
            </div>
            {
              _.size(_.get(company, "companyContact")) 
                ? _.map(_.get(company, 'companyContact'), (ele, index) => {
                  return (
                    <Grid key={'phone-'+index} item xs={6}>
                      <IconChipButton
                        editable={false}
                        title={''}
                        phoneType={_.get(ele, `phoneType`)}
                        phoneNum={_.get(ele, `phoneNo`)}
                        transparentbgcolor
                      />
                    </Grid>)
                })
                : '---'
            }
          </Grid>
          <Grid item xs={12}>
            <ReadOnlyText
              multiline
              label={intl.formatMessage({
                id: "company.detail.holdingCompanyZh",
              })}
              value={_.get(company, "holdingCompanyNameZh") || "---"}
            />
          </Grid>
          <Grid item xs={12}>
            <ReadOnlyText
              multiline
              label={intl.formatMessage({
                id: "company.detail.holdingCompanyEn",
              })}
              value={_.get(company, "holdingCompanyNameEn") || "---"}
            />
          </Grid>
          <Grid container spacing={1} style={{ margin: "0 -4px 0 0" }}>
            <Grid item xs={6}>
              <ReadOnlyText
                label={intl.formatMessage({ id: "company.detail.business" })}
                value={getDisplayLabel(businessOptions, _.get(company, "business"), langKey)}
              />
            </Grid>
            <Grid item xs={6}>
              <ReadOnlyText
                label={intl.formatMessage({ id: "company.detail.BR" })}
                value={_.get(company, "brNo") || "---"}
              />
            </Grid>
          </Grid>
          <Grid container spacing={1} style={{ margin: "0 -4px 0 0" }}>
            <Grid item xs={6}>
              <ReadOnlyText
                multiline
                label={intl.formatMessage({ id: "company.detail.district" })}
                value={displayDistrictStr(_.get(company, "district") || [])}
              />
            </Grid>
            <Grid item xs={6}>
              <ReadOnlyText
                label={intl.formatMessage({ id: "company.detail.URL" })}
                value={_.get(company, "url") || "---"}
              />
            </Grid>
          </Grid>
          <Grid item xs={12}>
            <ReadOnlyText
              multiline
              label={intl.formatMessage({
                id: "company.detail.companyAddressZh",
              })}
              value={_.get(company, "addressZh") || "---"}
            />
          </Grid>
          <Grid item xs={12}>
            <ReadOnlyText
              multiline
              label={intl.formatMessage({
                id: "company.detail.companyAddressEn",
              })}
              value={_.get(company, "addressEn") || "---"}
            />
          </Grid>
          <Grid item xs={12}>
            <ReadOnlyText
              multiline
              label={intl.formatMessage({
                id: "company.detail.remarks",
              })}
              value={_.get(company, "remarks") || "---"}
              className={classes.remarks}
            />
          </Grid>
        </Grid>
        </Box>

        {/* <Box margin="15px 0">
          <Grid container spacing={1}>
            <Grid item xs={12}>
              <ReadOnlyText
                label={intl.formatMessage({ id: "company.detail.rightPerson" })}
                value={_.get(company, "rightPerson") || "---"}
              />
            </Grid>
            {renderDirectors()}
            <Grid item xs={12}>
              <ReadOnlyText
                label={intl.formatMessage({
                  id: "company.detail.companyPhoneNumber",
                })}
                value={_.get(company, "phone") || "---"}
              />
            </Grid>
          </Grid>
        </Box> */}

        <Vendor contactsPerson={_.get(company, 'contactsPerson')} />
        <Search />
    </DetailTabPanelFrame>
  );
}

General.defaultProps = {
  company: null,
};

General.propTypes = {
  company: PropTypes.object,
  listingCompany: PropTypes.bool.isRequired,
  listedCompany: PropTypes.bool.isRequired,

  listCompanySearches: PropTypes.func.isRequired,

  intl: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  company: state.company.company,
  listingCompay: state.company.listingCompany,
  listedCompany: state.company.listedCompany,
  districts: state.district.districts
});

const mapDispatchToProps = (dispatch) => ({
  listCompanySearches: (regNo, companyName) =>
    dispatch(listCompanySearchDocs(regNo, companyName)),
  querySourceOptions: () => dispatch(querySourceOptions()),
  queryBusinessOptions: () => dispatch(queryBusinessOptions()),
  queryDistricts: () => dispatch(listDistricts())
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(General));
