import React, { useMemo } from "react";
import { Grid } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import { injectIntl } from "react-intl";
import PropTypes from "prop-types";
import { Field } from "redux-form";

import { OCCUPATION_PERMIT_TYPES } from "@/constants/landsearch";
import UnderlineSingleSelect from "@/components/common/UnderlineSingleSelect";
import StockInfo from "./StockInfo";

const useStyles = makeStyles({
  section: {
    margin: "5px 0",
  },
  multiInputContainer: {
    marginLeft: "-8px !important",
    marginRight: "-8px !important",
  },
  remarks: {
    color: "#555555",
    fontSize: "13px",
  },
});

function OccupationPermit({ intl }) {
  const classes = useStyles();

  const opTypes = useMemo(() =>
    OCCUPATION_PERMIT_TYPES.map((item) => ({
      value: item.value,
      label: item[intl.locale],
    })),
  );

  return (
    <Grid item container direction="column" className={classes.section}>
      <Grid item className={classes.section}>
        <Field
          name="occupationPermitType"
          label={intl.formatMessage({ id: "stock.applySearch.opType" })}
          component={UnderlineSingleSelect}
          type="select-multiple"
          options={opTypes}
        />
      </Grid>

      <Grid item container direction="column" className={classes.section}>
        <StockInfo />
      </Grid>
    </Grid>
  );
}

OccupationPermit.propTypes = {
  intl: PropTypes.object.isRequired,
};

export default injectIntl(OccupationPermit);
