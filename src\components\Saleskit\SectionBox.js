import React from "react";
import { Box, Grid, Button } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import PropTypes from "prop-types";
import clsx from "clsx";

const useStyles = makeStyles(() => ({
  header: {
    fontWeight: 600,
    fontSize: 14,
    marginBottom: 5,
    "& > span": {
      fontSize: 14,
    },
  },
  actionBtn: {
    padding: 0,
    "& .MuiButton-endIcon": {
      // marginLeft: 4,
    },
    "& .MuiButton-label": { textTransform: "none" },
  },
}));

function SectionBox({ className, header, actions, children }) {
  const classes = useStyles();
  return (
    <Box className={className}>
      <Grid container wrap="nowrap" justify="space-between">
        <Grid item>
          <span className={classes.header}>{header}</span>
        </Grid>
        <Grid item>
          {actions.map(
            ({ label, action, icon, className: actionClassName }) => (
              <Grid item key={label}>
                <Button
                  className={clsx(classes.actionBtn, actionClassName)}
                  onClick={action}
                  endIcon={icon}
                >
                  {label}
                </Button>
              </Grid>
            ),
          )}
        </Grid>
      </Grid>
      {children}
    </Box>
  );
}

SectionBox.defaultProps = {
  actions: [],
};

SectionBox.propTypes = {
  className: PropTypes.string,
  header: PropTypes.string.isRequired,
  actions: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.node.isRequired,
      action: PropTypes.func.isRequired,
      icon: PropTypes.node,
      className: PropTypes.string,
    }),
  ),
  children: PropTypes.node.isRequired,
};

export default SectionBox;
