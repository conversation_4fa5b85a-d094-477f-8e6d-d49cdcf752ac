import React from "react";
import { withStyles } from "@material-ui/core/styles";
import phoneSvg from "../../files/icons/phone.svg";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    width: 34,
    height: 34,
    background: "#56F371",
    borderRadius: "100%",
    position: "relative",
    cursor: "pointer",
  },
  icon: {
    position: "absolute",
    top: 6,
    left: 8,
    filter: "invert(100%)",
  }
};

function CallIcon(props) {
  const { classes, className, ...other } = props;

  return (
    <div className={`${classes.root} ${className}`} {...other}>
      <img className={classes.icon} src={phoneSvg} />
    </div>
  );
}

export default withStyles(styles)(CallIcon);
