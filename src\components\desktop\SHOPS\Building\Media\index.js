import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import DetailTabPanelFrame from "../../../../common/DetailTabPanelFrame";
import MediaMain from "../../../../common/MediaMain";
import { listBuildingMedia } from "../../../../../actions/building";
import { injectIntl } from "react-intl";

const styles = theme => ({});

class Media extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    media: PropTypes.object,
    listingMedia: PropTypes.bool,
    detail: PropTypes.object,
    listed: PropTypes.bool,
    userInfo: PropTypes.object
  };

  constructor(props) {
    super(props);
    this.state = {
      requestMedia: true
    };
  }

  componentDidUpdate() {
    const { detail, listed, listBuildingMedia, userInfo } = this.props;

    if (
      this.state.requestMedia &&
      listed &&
      detail &&
      detail.unicorn &&
      Number.isInteger(detail.unicorn.id)
    ) {
      this.setState({ requestMedia: false });
      let variables = {
        sid: detail.unicorn.id.toString(),
        empId: userInfo.emp_id
      };
      listBuildingMedia(variables);
    }
  }

  render() {
    const { classes, media, listingMedia, listedMedia, intl } = this.props;

    const hasData = Object.keys(media).length > 0;

    return (
      <DetailTabPanelFrame
        hasData={hasData}
        listing={listingMedia}
        listed={listedMedia}
        notFoundText="Building photo not found"
      >
        <MediaMain
          media={media}
          mediaType="building"
        />
      </DetailTabPanelFrame>
    );
  }
}

const mapStateToProps = state => ({
  userInfo:
    state.auth &&
    state.auth.user &&
    state.auth.user.login &&
    state.auth.user.login.info
      ? state.auth.user.login.info
      : {},
  media: state.building.media ? state.building.media : {},
  listedMedia: state.building.listedMedia ? state.building.listedMedia : false,
  listingMedia: state.building.listingMedia
    ? state.building.listingMedia
    : false,
  detail: state.building.detail ? state.building.detail : {},
  listed: state.building.listedDetail ? state.building.listedDetail : false
});

const mapDispatchToProps = dispatch => {
  return {
    listBuildingMedia: (...args) => dispatch(listBuildingMedia(...args))
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(Media)));
