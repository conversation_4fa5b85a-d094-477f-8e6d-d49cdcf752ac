import {
  CLEAR_APPLY_SEARCH_STATE,
  CREATE_APPLY_SEARCH,
  CREATE_APPLY_SEARCH_ERROR,
  CREATE_APPLY_SEARCH_SUCCESS,
  LIST_DD_TYPES,
  LIST_DD_TYPES_ERROR,
  LIST_DD_TYPES_SUCCESS,
} from "@/constants/landsearch";

const initialState = {
  ddTypes: [],
  listingDDTypes: false,
  listedDDTypes: false,
  listDDTypesError: null,

  applying: false,
  applyOk: false,
  applyError: [],
};

export default function landsearch(state = initialState, action) {
  switch (action.type) {
    case LIST_DD_TYPES:
      return {
        ...state,
        ddTypes: [],
        listingDDTypes: true,
        listedDDTypes: false,
      };
    case LIST_DD_TYPES_SUCCESS:
      return {
        ...state,
        ddTypes: action.payload.ddTypes,
        listingDDTypes: false,
        listedDDTypes: true,
      };
    case LIST_DD_TYPES_ERROR:
      return {
        ...state,
        listingDDTypes: false,
        listedDDTypes: true,
        listDDTypesError: action.payload.error,
      };

    case CREATE_APPLY_SEARCH:
      return {
        ...state,
        applying: true,
        applyOk: false,
        applyError: [],
      };

    case CREATE_APPLY_SEARCH_SUCCESS:
      return {
        ...state,
        applying: false,
        applyOk: true,
      };

    case CREATE_APPLY_SEARCH_ERROR:
      return {
        ...state,
        applying: false,
        applyError: action.payload.errors,
      };

    case CLEAR_APPLY_SEARCH_STATE:
      return { ...state, applying: false, applyOk: false, applyError: [] };

    default:
      return state;
  }
}
