import React, { useMemo } from "react";
import { injectIntl } from "react-intl";
import { makeStyles } from "@material-ui/core/styles";
import PropTypes from "prop-types";
import { Field } from "redux-form";
import { Grid, Typography } from "@material-ui/core";
import _ from "lodash";
import { connect } from "react-redux";

import {
  FILM_TYPES,
  governmentLeaseRemark,
  LANDSEARCH_ITEMS,
} from "@/constants/landsearch";
import UnderlineSingleSelect from "@/components/common/UnderlineSingleSelect";
import TextInput from "@/components/common/TextInput";
import SelectMultiInput from "@/components/common/SelectMultiInput";

const useStyles = makeStyles({
  section: {
    margin: "5px 0",
  },
  remarks: {
    color: "#555555",
    fontSize: "13px",
  },
});
function GovernmentLease({ ddTypes, intl }) {
  const classes = useStyles();

  const landSearchItems = useMemo(
    () =>
      LANDSEARCH_ITEMS.map((item) => ({
        value: item.value,
        label: item[intl.locale],
      })),
    [intl.locale],
  );

  const filmTypes = useMemo(
    () =>
      FILM_TYPES.map((item) => ({
        value: item.value,
        label: item[intl.locale],
      })),
    [intl.locale],
  );

  return (
    <Grid item container direction="column" className={classes.section}>
      <Grid item className={classes.section}>
        <Field
          name="item"
          label={intl.formatMessage({ id: "stock.applySearch.landSearchItem" })}
          component={UnderlineSingleSelect}
          type="select-multiple"
          options={landSearchItems}
        />
      </Grid>

      <SelectMultiInput
        required
        label={intl.formatMessage({ id: "stock.applySearch.ddType" })}
        selectName="ddType"
        selectXs={5}
        selectPosition="left"
        options={_.map(ddTypes, (ddType) => ({
          label: ddType.key,
          value: ddType.key,
        }))}
        spacing={2}
        InputComponent={UnderlineSingleSelect}
        inputProps={{
          name: "ddType",
          type: "select-multiple",
          options: _.map(ddTypes, (ddType) => ({
            label: ddType.value,
            value: ddType.key,
          })),
        }}
      />

      <Grid item className={classes.section}>
        <Field
          required
          name="ddNumber"
          component={TextInput}
          label={intl.formatMessage({ id: "stock.applySearch.ddNumber" })}
          showPlaceholder={false}
        />
      </Grid>

      <Grid item className={classes.section}>
        <Field
          name="filmType"
          label={intl.formatMessage({ id: "stock.applySearch.filmType" })}
          component={UnderlineSingleSelect}
          type="select-multiple"
          options={filmTypes}
        />
      </Grid>

      <Grid item style={{ marginTop: 15 }}>
        {governmentLeaseRemark.map((statement, index) => (
          <Typography
            key={`govLeaseRemark-${index}`}
            component="div"
            className={classes.remarks}
          >
            {statement}
          </Typography>
        ))}
      </Grid>
    </Grid>
  );
}

GovernmentLease.propTypes = {
  ddTypes: PropTypes.array.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  ddTypes: _.get(state, "landsearch.ddTypes") || [],
});

export default connect(mapStateToProps)(injectIntl(GovernmentLease));
