import React from "react";
import { makeStyles } from "@material-ui/core/styles";
import Checkbox from "@material-ui/core/Checkbox";
import FormControlLabel from "@material-ui/core/FormControlLabel";

// We can inject some CSS into the DOM.
const styles = makeStyles(theme => ({
  checkbox: {
    '&.Mui-checked:not(.Mui-disabled)': {
      color: "#13CE66",
    },
  },
  checked: {}
}));

function ChipsCheckBox(props) {
  const classes = styles();
  const {
    className,
    label,
    input,
    disabled,
    meta: { touched, invalid, error },
    ...custom
  } = props;

  return (
    <FormControlLabel
      control={
        <Checkbox
          classes={{
            root: classes.checkbox,
            checked: classes.checked
          }}
          checked={!!input.value}
          onChange={input.onChange}
          disabled={disabled}
        />
      }
      label={label}
      {...custom}
    />
  );
}

export default ChipsCheckBox;
