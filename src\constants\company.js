import { sbu } from "@/config";

export const UPDATE_QUERY_VARIABLES = "COMPANY/UPDATE_QUERY_VARIABLES";
export const UPDATE_SORTER = "COMPANY/UPDATE_SORTER";
export const UPDATE_CRITERIA = "COMPANY/UPDATE_CRITERIA";

export const QUERY_COMPANY_NAME_SUCCESS = "COMPANY/QUERY_COMPANY_NAME_SUCCESS";
export const LIST_COMPANIES = "COMPANY/LIST_COMPANIES";
export const LIST_COMPANIES_SUCCESS = "COMPANY/LIST_COMPANIES_SUCCESS";
export const LIST_MORE_COMPANIES_SUCCESS =
  "COMPANY/LIST_MORE_COMPANIES_SUCCESS";
export const LIST_COMPANIES_ERROR = "COMPANY/LIST_COMPANIES_ERROR";

export const QUERY_COMPANY_BY_REGNO = "COMPANY/QUERY_COMPANY_BY_REGNO";
export const QUERY_COMPANY_BY_REGNO_SUCCESS =
  "COMPANY/QUERY_COMPANY_BY_REGNO_SUCCESS";
export const QUERY_SOURCE_OPTIONS_SUCCESS =
"COMPANY/QUERY_SOURCE_OPTIONS_SUCCESS";
export const QUERY_COMPANY_BY_REGNO_ERROR =
  "COMPANY/QUERY_COMPANY_BY_REGNO_ERROR";
export const LIST_COMPANY_SEARCHES_SUCCESS =
  "COMPANY/LIST_COMPANY_SEARCHES_SUCCESS";

export const APPLY_COMPANY_SEARCH = "COMPANY/APPLY_COMPANY_SEARCH";
export const APPLY_COMPANY_SEARCH_SUCCESS =
  "COMPANY/APPLY_COMPANY_SEARCH_SUCCESS";
export const APPLY_COMPANY_SEARCH_ERROR = "COMPANY/APPLY_COMPANY_SEARCH_ERROR";
export const CLEAR_APPLY_COMPANY_SEARCH = "COMPANY/CLEAR_APPLY_COMPANY_SEARCH";

export const CLEAR_COMPANY = "COMPANY/CLEAR_COMPANY";

export const getSorters = (intl) => [
  {
    value: "companyNameZh",
    label: intl.formatMessage({
      id: "company.sorter.companyNameZh",
    }),
  },
  {
    value: "companyNameEn",
    label: intl.formatMessage({
      id: "company.sorter.companyNameEn",
    }),
  },
  ...(sbu === "SHOPS"
    ? [
        {
          value: "rightPerson",
          label: intl.formatMessage({
            id: "company.sorter.rightPerson",
          }),
        },
      ]
    : []),
  {
    value: "updateDate",
    label: intl.formatMessage({
      id: "company.sorter.updateDate",
    }),
  },
];

export const COMPANY_TYPES = {
  CO: "有限公司",
  UO: "無限公司",
};
