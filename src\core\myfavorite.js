import fetch from "node-fetch";
import mongodb from "../data/mongodb";
import config from "../config";

export const addtoMyFavorite = async (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    const body = {
      emp_id: req.body.emp_id,
      stockid: req.body.stockid,
      mongoid: req.body.mongoid,
      sbu: config.sbu,
    };

    try {
      const resp = await fetch(`${config.api.icimsgeneral}/addMyFavorite`, {
        method: "POST",
        body: JSON.stringify(body),
        headers: {
          "content-type": "application/json",
        },
      });
      const data = await resp.json();
      res.send(data);
    } catch (e) {
      console.log(e);
      return {};
    }
  }
};

// https://msdc-uat.midlandici.com.hk/msearch-api/api/listMyFavorite
// /addMyFavorite
export const listMyFavorite = async (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    const body = {
      emp_id: req.body.emp_id,
      sbu: config.sbu,
    };

    try {
      const resp = await fetch(`${config.api.icimsgeneral}/listMyFavorite`, {
        method: "POST",
        body: JSON.stringify(body),
        headers: {
          "content-type": "application/json",
        },
      });
      const data = await resp.json();
      res.send(data);
    } catch (e) {
      console.log(e);
      return {};
    }
  }
};
