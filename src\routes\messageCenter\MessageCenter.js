/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import withStyles from "isomorphic-style-loader/lib/withStyles";
import MessageCenterDetail from "../../components/MessageCenter";
import PrivateMessageIndex from "../../components/MessageCenter/PrivateMessageIndex";
import s from "./MessageCenter.css";
import {
  clearMessages,
  clearPrivateMessages,
  listMessages
} from "../../actions/messageCenter";
import AppBar from "@material-ui/core/AppBar";
import Tabs from "@material-ui/core/Tabs";
import Tab from "@material-ui/core/Tab";
import { FormattedMessage, injectIntl } from "react-intl";
import Typography from "@material-ui/core/Typography";
import {
  MuiThemeProvider,
  createMuiTheme,
  createStyles,
} from "@material-ui/core/styles";

function TabPanel(props) {
  const { children, value, index, ...other } = props;
  const [loaded, setLoaded] = React.useState(false);

  if (!loaded && value === index) setLoaded(true);

  return (
    <Typography
      component="div"
      role="tabpanel"
      hidden={value !== index}
      id={`full-width-tabpanel-${index}`}
      aria-labelledby={`full-width-tab-${index}`}
      {...other}
    >
      {loaded ? <>{children}</> : null}
    </Typography>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.any.isRequired,
  value: PropTypes.any.isRequired,
};

function a11yProps(index) {
  return {
    id: `full-width-tab-${index}`,
    "aria-controls": `full-width-tabpanel-${index}`
  };
}


const theme = createMuiTheme({
  overrides: {
    MuiTabs: {
      root: {
        minHeight: 36,
      },
      indicator: {
        height: 0,
        backgroundColor: "rgba(101,102,101,1)",
      },
    },
    MuiTab: {
      root: {
        flex: "1 0 auto",
        minHeight: 36,
        fontSize: "0.875em",
        lineHeight: 1.2,
        textTransform: "none",
        padding: "6px 8px",
        backgroundColor: "rgba(101,102,101,1)",
        "&$selected": {
          backgroundColor: "#FFF",
          color: "rgba(0, 0, 0, .6)",
        },
      },
      textColorInherit: {
        color: "#FFF",
        opacity: 1,
      },
      wrapper: {
        "&& > *:first-child": {
          marginBottom: 0,
        },
      },
    },
  },
});

class MessageCenter extends React.Component {
  static propTypes = {
    listMessages: PropTypes.func.isRequired,
    clearMessages: PropTypes.func.isRequired
  };

  constructor(props) {
    super(props);
    this.state = {
      tab: 0
    }
  }

  componentDidMount() {
    const variables = {
      limit: 50,
      offset: 0,
      isDeleted: false,
      sorter: [
        { field: "createDateTime", order: "DESC" },
      ]
    };
    this.props.listMessages(variables);
  }

  componentWillUnmount() {
    this.props.clearMessages();
    this.props.clearPrivateMessages();
  }

  handleChange = (event, value) => {
    this.setState({ tab: value });
    const variables = {
      limit: 50,
      offset: 0,
      isDeleted: false,
      sorter: value === 1 ? [
        { field: "recordOperationCreateDateTime", order: "DESC" },
      ] : [
        { field: "createDateTime", order: "DESC" },
      ]
    };
    
    this.props.listMessages(variables, false, value === 1 ? "private" : "public");
  };

  render() {
    return (
      <div className={s.root}>
        <div className={s.div}>
          <div>
            <MuiThemeProvider theme={theme}>
              <AppBar position="static" className={s.bar}>
                <Tabs
                  value={this.state.tab}
                  onChange={this.handleChange}
                  variant="fullWidth"
                  aria-label="full width tabs example"
                  centered
                >
                  <Tab 
                    label={<FormattedMessage id="publicmessage.public" />}
                    {...a11yProps(0)}
                  />
                  <Tab 
                    label={<FormattedMessage id="publicmessage.private" />}
                    {...a11yProps(1)}
                  />
                </Tabs>
              </AppBar>
            </MuiThemeProvider>
            
          <TabPanel value={this.state.tab} index={0}>
            <MessageCenterDetail />
          </TabPanel>
          <TabPanel value={this.state.tab} index={1}>
            <PrivateMessageIndex />
          </TabPanel>
          </div>
        </div>
      </div>
    );
  }
}

const mapDispatchToProps = dispatch => {
  return {
    listMessages: (...args) => dispatch(listMessages(...args)),
    clearMessages: () => dispatch(clearMessages()),
    clearPrivateMessages: () => dispatch(clearPrivateMessages())
  };
};

export default connect(
  null,
  mapDispatchToProps
)(withStyles(s)(MessageCenter));
