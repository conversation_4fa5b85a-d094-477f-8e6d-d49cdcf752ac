import _ from "lodash";
import { reset } from "redux-form";
import moment from 'moment';
import { sbu } from "@/config";
import { addActivityLog } from "./log";

import {
  CLEAR_COMPANY,
  LIST_COMPANIES,
  LIST_COMPANIES_ERROR,
  LIST_COMPANIES_SUCCESS,
  LIST_MORE_COMPANIES_SUCCESS,
  QUERY_COMPANY_BY_REGNO,
  QUERY_COMPANY_BY_REGNO_ERROR,
  QUERY_COMPANY_BY_REGNO_SUCCESS,
  QUERY_SOURCE_OPTIONS_SUCCESS,
  LIST_COMPANY_SEARCHES_SUCCESS,
  UPDATE_QUERY_VARIABLES,
  UPDATE_SORTER,
  CLEAR_APPLY_COMPANY_SEARCH,
  APPLY_COMPANY_SEARCH,
  APPLY_COMPANY_SEARCH_ERROR,
  APPLY_COMPANY_SEARCH_SUCCESS,
  QUERY_COMPANY_NAME_SUCCESS,
  UPDATE_CRITERIA,
} from "@/constants/company";

export const updateSorter = (newSorter) => ({
  type: UPDATE_SORTER,
  payload: {
    sorter: newSorter,
  },
});

export const updateQueryVariables = (
  key = "",
  value = "",
  queryvariables = null,
) => ({
  type: UPDATE_QUERY_VARIABLES,
  payload: {
    queryvariables,
    key,
    value,
  },
});

export const updateCriteria = (key, value) => ({
  type: UPDATE_CRITERIA,
  payload: {
    key,
    value,
  },
});

export const clearCompany = () => ({
  type: CLEAR_COMPANY,
});

export const listCompanies =
  (fetchMore) =>
  async (dispatch, getState, { universalRequest }) => {
    const state = await getState();
    const { queryvariables, sorter } = state.company;

    try {
      dispatch({
        type: LIST_COMPANIES,
        payload: { fetchMore },
      });

      dispatch(addActivityLog("company.search", "read", { ...queryvariables, sort: sorter }));

      const res = await universalRequest("/listCompanies", {
        method: "POST",
        headers: {
          "content-type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
        body: JSON.stringify({
          variables: { ...queryvariables, sort: sorter },
        }),
      });

      if (!_.isEmpty(res.errors)) {
        dispatch({
          type: LIST_COMPANIES_ERROR,
          payload: {
            error: res.errors,
          },
        });
      } else if (fetchMore) {
        dispatch({
          type: LIST_MORE_COMPANIES_SUCCESS,
          payload: { ...res.data },
        });
      } else {
        dispatch({
          type: LIST_COMPANIES_SUCCESS,
          payload: { ...res.data },
        });
      }
    } catch (e) {
      console.error(e);
      dispatch({
        type: LIST_COMPANIES_ERROR,
        payload: {
          error: e,
        },
      });
    }
  };

export const createCompany = (companyBookContactDetails) =>
  async (dispatch, getState, { universalRequest }) => {
    try {
      const state = await getState();
      const userInfo = _.get(state, "auth.user.login.info");
      const res = await universalRequest("/createCompany", {
        method: "POST",
        headers: {
          "content-type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
        body: JSON.stringify({
          variables: {
            companyBookContactDetails: {
              ...companyBookContactDetails,
              createBy: _.get(userInfo, "emp_id") || "",
              createDate: moment().format('YYYY-MM-DD')
            }
          }
        }),
      });

      if (!_.isEmpty(res.errors)) {
        dispatch({
          type: APPLY_COMPANY_SEARCH_ERROR,
          payload: {
            error: res.errors,
          },
        });
      } else {
        return res.data
      }
    } catch (e) {
      console.error(e);
    }
  };

// for company search auto-complete
export const queryCompanyName =
  (variables) =>
  async (dispatch, getState, { universalRequest }) => {
    try {
      const res = await universalRequest("/listCompanies", {
        method: "POST",
        headers: {
          "content-type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
        body: JSON.stringify({
          variables: {
            limit: 5,
            offset: 0,
            companyName: variables.name,
            sort: [
              { field: "companyNameEn", order: "ASC" },
              { field: "companyNameZh", order: "ASC" },
            ],
          },
        }),
      });

      if (_.isEmpty(res.errors)) {
        dispatch({
          type: QUERY_COMPANY_NAME_SUCCESS,
          payload: {
            names: _.map(_.get(res, "data.companyBook", []), (name) => ({
              _id: name.companyNameEn || name.companyNameZh,
              nameZh: name.companyNameZh,
              nameEn: name.companyNameEn,
            })),
          },
        });
      }
    } catch (e) {
      console.error(e);
    }
  };

export const queryCompany =
  (id) =>
  async (dispatch, getState, { universalRequest }) => {
    try {
      dispatch({
        type: QUERY_COMPANY_BY_REGNO,
      });

      const res = await universalRequest("/listCompany", {
        method: "POST",
        headers: {
          "content-type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
        body: JSON.stringify({
          variables: { id },
        }),
      });

      dispatch(addActivityLog("company", "read", { ...res }));

      if (!_.isEmpty(res.errors)) {
        dispatch({
          type: QUERY_COMPANY_BY_REGNO_ERROR,
          payload: {
            error: res.errors,
          },
        });
      } else {
        dispatch({
          type: QUERY_COMPANY_BY_REGNO_SUCCESS,
          payload: {
            company: res.data,
          },
        });
      }
    } catch (e) {
      console.error(e);
      dispatch({
        type: QUERY_COMPANY_BY_REGNO_ERROR,
        payload: {
          error: e,
        },
      });
    }
  };

export const querySourceOptions = (params = {}) => async (dispatch, getState, { universalRequest }) => {
  try {
    const res = await universalRequest("/querySourceOptions", {
      method: "POST",
      headers: {
        "content-type": "application/json",
        Authorization: getState().auth.user.oauth,
        "CAS-Authorization": localStorage.getItem("casAccessToken"),
      },
      body: JSON.stringify({
        variables: params,
      }),
    });

    if (!_.isEmpty(res.errors)) {
      dispatch({
        type: QUERY_COMPANY_BY_REGNO_ERROR,
        payload: {
          error: res.errors,
        },
      });
    } else {
      dispatch({
        type: QUERY_SOURCE_OPTIONS_SUCCESS,
        payload: {
          sourceOptions: res.data,
        },
      });
    }
  } catch (e) {
    console.error(e);
    dispatch({
      type: QUERY_COMPANY_BY_REGNO_ERROR,
      payload: {
        error: e,
      },
    });
  }
};

export const listCompanySearchDocs =
  (regNo, companyName) =>
  async (dispatch, getState, { universalRequest }) => {
    const res = await universalRequest("/listCompanySearch", {
      method: "POST",
      headers: {
        "content-type": "application/json",
        Authorization: getState().auth.user.oauth,
        "CAS-Authorization": localStorage.getItem("casAccessToken"),
      },
      body: JSON.stringify({
        ...(regNo ? { companyRegistrationNumber: regNo } : {}),
        ...(companyName ? { companyName } : {}),
      }),
    });
    if (!_.isEmpty(_.get(res, "data"))) {
      dispatch({
        type: LIST_COMPANY_SEARCHES_SUCCESS,
        payload: {
          searchDocs: _.get(res, "data"),
        },
      });
    }
  };

export const applyCompanySearch =
  (variables) =>
  async (dispatch, getState, { universalRequest }) => {
    const state = await getState();
    const userInfo = _.get(state, "auth.user.login.info");

    dispatch({
      type: APPLY_COMPANY_SEARCH,
    });

    try {
      const res = await universalRequest("/applyCompanySearch", {
        method: "POST",
        headers: {
          "content-type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
        body: JSON.stringify({
          details: {
            ...variables,
            applicantId: _.get(userInfo, "emp_id") || "",
            applicantUserName: _.get(userInfo, "user_id") || "",
          },
        }),
      });

      if (res.status !== 200 || !_.isNil(_.get(res, "errors.0"))) {
        dispatch({
          type: APPLY_COMPANY_SEARCH_ERROR,
          payload: {
            errors: res.errors[0].message,
          },
        });
      } else {
        dispatch({
          type: APPLY_COMPANY_SEARCH_SUCCESS,
        });
      }
    } catch (e) {
      console.error(e);
      dispatch({
        type: APPLY_COMPANY_SEARCH_ERROR,
        payload: {
          errors: e,
        },
      });
    }
  };

export const clearApplyCompanySearch = () => (dispatch) => {
  dispatch(reset("applyCompanySearch"));
  dispatch({
    type: CLEAR_APPLY_COMPANY_SEARCH,
  });
};
