import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import FavoriteIcon from "@material-ui/icons/Favorite";
import FavoriteBorderIcon from "@material-ui/icons/FavoriteBorder";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import ExpandLessIcon from "@material-ui/icons/ExpandLess";

// We can inject some CSS into the DOM.
const styles = {
  titleRow: {
    display: "flex",
    alignItems: "center",
  },
  title: {
    fontSize: "1.175em",
    flex: 1,
  },
  subtitle: {
    display: "flex",
    alignItems: "center",
  },
  fav: {
    color: "#DD5151",
    cursor: "pointer",
  },
  notFav: {
    color: "#707070",
    cursor: "pointer",
  },
  expand: {
    cursor: "pointer",
  },
  content: {
    // paddingTop: "1vh"
  },
};

class DetailBoxTitle extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isExpanding: props.isExpanding || false,
      dialogOpen: false,
    };
  }

  handleClick = (event, value) => {
    this.setState({ isExpanding: !this.state.isExpanding });
  };

  handleUnlockDialog = () => {
    this.setState({ dialogOpen: true });
  };

  handleUnlockClose = () => {
    this.setState({ dialogOpen: false });
  };

  render() {
    const {
      classes,
      children,
      text,
      text2,
      textClass,
      subtitle,
      subtitle2,
      icon,
      rightIcon,
      fav,
      expandable,
      locked,
      apiaction,
      ...other
    } = this.props;

    return (
      <div>
        <div className={classes.subtitle}>
          <div className={classes.title}>{subtitle}</div>
          {rightIcon}
        </div>
        <div className={classes.titleRow}>
          {icon}
          <div className={`${classes.title} ${textClass}`}>{text}</div>
          {fav === true && <FavoriteIcon className={classes.fav} />}
          {fav === false && <FavoriteBorderIcon className={classes.notFav} />}
          {expandable === true && this.state.isExpanding === false && (
            <ExpandMoreIcon
              className={classes.expand}
              onClick={this.handleClick}
            />
          )}

          {expandable === true && this.state.isExpanding === true && (
            <ExpandLessIcon
              className={classes.expand}
              onClick={this.handleClick}
            />
          )}
          {/* {locked === true && (
            <UnlockDialogItem
              className={classes.expand}
              apiaction={apiaction}
            />
          )} */}
        </div>
        <div className={`${classes.title} ${textClass}`}>{text2}</div>
        <div className={classes.subtitle}>{subtitle2}</div>
        {!(expandable === true && this.state.isExpanding === false) && (
          <div className={classes.content}>{children}</div>
        )}
      </div>
    );
  }
}

DetailBoxTitle.propTypes = {
  children: PropTypes.node,
  classes: PropTypes.object.isRequired,
  text: PropTypes.string,
  text2: PropTypes.node,
  subtitle2: PropTypes.node,
  icon: PropTypes.node,
  rightIcon: PropTypes.node,
  fav: PropTypes.bool,
  expandable: PropTypes.bool,
};

export default withStyles(styles)(DetailBoxTitle);
