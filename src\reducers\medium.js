import {
  CLEAR_PROGRESSING_MEDIUM,
  CLEAR_PROGRESSING_MEDIUM_BY_ID,
  CREATE_MEDIUM_START,
  CREATE_MEDIUM_SUCCESS,
  CREATE_MEDIUM_ERROR,
  UPDATE_MEDIUM_START,
  UPDATE_MEDIUM_SUCCESS,
  UPDATE_MEDIUM_ERROR,
  DELETE_MEDIUM_START,
  DELETE_MEDIUM_SUCCESS,
  DELETE_MEDIUM_ERROR,
  LIST_APPROVE_MEDIA_START,
  LIST_APPROVE_MEDIA_SUCCESS,
  LIST_APPROVE_MEDIA_ERROR,
  CLEAR_APPROVE_MEDIA
} from "../constants/medium";

const initialState = {
  progressingMedia: [],
  updated: false,
  approveMediaList: [],
  isApproveMediaPage: false,
  listedApproveMedia: false,
  listingApproveMedia: false
};

export default function medium(state = initialState, action) {
  const { progressingMedia } = state;
  switch (action.type) {
    case CLEAR_PROGRESSING_MEDIUM:
      return {
        state,
        progressingMedia: []
      };
    case CLEAR_PROGRESSING_MEDIUM_BY_ID:
      return {
        ...state,
        progressingMedia: progressingMedia.filter((media) => media.id !== action.payload.id),
      };
    case CREATE_MEDIUM_START:
      // const { progressingMedia } = state;
      progressingMedia.splice(0, 0, { ...action.payload, state: "creating" });
      return {
        ...state,
        creatingMedium: true,
        createdMedium: false,
        progressingMedia: Array.from(progressingMedia)
      };
    case CREATE_MEDIUM_SUCCESS:
      return {
        ...state,
        creatingMedium: false,
        createdMedium: true,
        progressingMedia: state.progressingMedia.map(pm =>
          pm.startTime === action.payload.startTime
            ? {
                ...pm,
                ...action.payload.medium,
                state: "done"
              }
            : pm
        )
      };
    case CREATE_MEDIUM_ERROR:
      return {
        ...state,
        creatingMedium: false,
        createdMedium: false,
        progressingMedia: state.progressingMedia.map(pm =>
          pm.startTime === action.payload.startTime
            ? {
                ...pm,
                state: "error",
                message: action.payload.error
              }
            : pm
        )
      };
    case UPDATE_MEDIUM_START:
      const oldProgressingMediaIndex = state.progressingMedia.findIndex(
        pm => pm.id === action.payload.id
      );
      if (oldProgressingMediaIndex !== -1) {
        progressingMedia[oldProgressingMediaIndex] = { ...action.payload, state: "updating" }
      } else {
        progressingMedia.splice(0, 0, { ...action.payload, state: "updating" });
      }
      return {
        ...state,
        updatingMedium: true,
        updatedMedium: false,
        progressingMedia: Array.from(progressingMedia)
      };
    case UPDATE_MEDIUM_SUCCESS:
      return {
        ...state,
        updatingMedium: false,
        updatedMedium: true,
        progressingMedia: state.progressingMedia.map(pm =>
          pm.id === action.payload.id
            ? {
                ...pm,
                ...action.payload.medium,
                state: "done"
              }
            : pm
        )
      };
    case UPDATE_MEDIUM_ERROR:
      return {
        ...state,
        updatingMedium: false,
        updatedMedium: false,
        progressingMedia: state.progressingMedia.map(pm =>
          pm.id === action.payload.id
            ? {
                ...pm,
                state: "error",
                message: action.payload.error
              }
            : pm
        )
      };
    case DELETE_MEDIUM_START:
      const deletingMediaIndex = state.progressingMedia.findIndex(
        pm => pm.id === action.payload.id
      );
      if (deletingMediaIndex !== -1) {
        progressingMedia[deletingMediaIndex] = { 
          ...action.payload, 
          state: "deleting",
          operation: action.payload.operation
        }
      } else {
        progressingMedia.splice(0, 0, { 
          ...action.payload, 
          state: "deleting",
          operation: action.payload.operation 
        });
      }
      return {
        ...state,
        deletingMedium: true,
        deletedMedium: false,
        progressingMedia: Array.from(progressingMedia)
      };
    case DELETE_MEDIUM_SUCCESS:
      return {
        ...state,
        deletingMedium: false,
        deletedMedium: true,
        progressingMedia: state.progressingMedia.map(pm =>
          pm.id === action.payload.medium.removeMediumBypass.id
            ? {
                ...pm,
                state: "done",
                operation: action.payload.operation
              }
            : pm
        )
      };
    case DELETE_MEDIUM_ERROR:
      return {
        ...state,
        deletingMedium: false,
        deletedMedium: false,
        progressingMedia: state.progressingMedia.map(pm =>
          pm.id === action.payload.id
            ? {
                ...pm,
                state: "error",
                message: action.payload.error.message,
                operation: action.payload.operation
              }
            : pm
        ),
        error: action.payload.error.message
      };
    case LIST_APPROVE_MEDIA_START:
      return {
        ...state,
        isApproveMediaPage: true,
        listedApproveMedia: false,
        listingApproveMedia: true,
        approveMediaList: []
      };
    case LIST_APPROVE_MEDIA_SUCCESS:
      return {
        ...state,
        listedApproveMedia: true,
        listingApproveMedia: false,
        approveMediaList: action.payload.approveMediaList
      };
    case LIST_APPROVE_MEDIA_ERROR:
      return {
        ...state,
        listedApproveMedia: false,
        listingApproveMedia: false,
        error: action.payload.error
      };
    case CLEAR_APPROVE_MEDIA:
      return {
        ...state,
        isApproveMediaPage: false,
        listedApproveMedia: false,
        listingApproveMedia: false,
        approveMediaList: []
      };
    default:
      return state;
  }
}
