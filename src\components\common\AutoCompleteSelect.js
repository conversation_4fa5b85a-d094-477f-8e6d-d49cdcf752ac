import React, { useMemo, useRef,useCallback } from "react";
import debounce from "lodash/debounce";
import _ from "lodash";
import PropTypes from "prop-types";
import TextField from "@material-ui/core/TextField";
import { makeStyles } from "@material-ui/core/styles";
import Select from "react-select";
import Paper from "@material-ui/core/Paper";
import { injectIntl } from "react-intl";
import { getLangKey, matchPreviousName, parseNameWithDash } from "../../helper/generalHelper";
import { sbu } from "../../config";

const useStyles = makeStyles((theme) => ({
  root: {
    // marginLeft: theme.spacing(1),
    // marginRight: theme.spacing(1)
    // width: '100%',
  },
  input: {
    display: "flex",
    // padding: "2px 0 2px 14px",
    height: "auto",
    cursor: "pointer",
  },
  valueContainer: {
    display: "flex",
    flexWrap: "wrap",
    flex: 1,
    alignItems: "center",
    overflow: "hidden",
  },
  paper: {
    position: "absolute",
    marginTop: theme.spacing(1),
    left: 0,
    right: 0,
    zIndex: 999,
  },
}));

function Control(props) {
  const {
    children,
    innerProps,
    innerRef,
    selectProps: { classes, TextFieldProps },
  } = props;

  return (
    <TextField
      fullWidth
      InputProps={{
        inputComponent,
        inputProps: {
          className: classes.input,
          ref: innerRef,
          children,
          disabled: TextFieldProps.disabled,
          onCompositionStart: TextFieldProps.onCompositionStart,
          onCompositionUpdate: TextFieldProps.onCompositionUpdate,
          onCompositionEnd: TextFieldProps.onCompositionEnd,
          ...innerProps,
        },
      }}
      {...TextFieldProps}
    />
  );
}

function Menu(props) {
  return (
    <Paper
      square
      className={props.selectProps.classes.paper}
      {...props.innerProps}
    >
      {props.children}
    </Paper>
  );
}

function ValueContainer(props) {
  return (
    <div className={props.selectProps.classes.valueContainer}>
      {props.children}
    </div>
  );
}

function inputComponent({ inputRef, ...props }) {
  return <div ref={inputRef} {...props} />;
}

const components = {
  Control,
  Menu,
  ValueContainer,
};

function AutoCompleteSelect(props, context) {
  const {
    input,
    optionsdata,
    apiaction,
    selectedData,
    setSelectedData,
    customInputProps,
    showZhEnLabel = false,
    disabled,
    intl,
    limit = 5,
  } = props;
  const { value, name, onChange, onBlur } = input;
  const { touched, invalid, error } = props.meta;
  const [single, setSingle] = React.useState(selectedData || []);
  const [inputLang, setInputLang] = React.useState("nameEn");
  const [inputValue, setInputValue] = React.useState("");
  const onComposition = useRef(false); // indicate state during typing composition
  const classes = useStyles();

  const langkey = getLangKey(intl);
  const [hasInput, sethasInput] = React.useState(false);
  let searchHistory = props.history ? props.history : [];

  React.useEffect(() => {
    setSingle(selectedData);
  }, [selectedData]);

  const getoptions = () => {
    const data =
      searchHistory.length > 0 && !hasInput ? searchHistory : optionsdata;

    const options = data.map((item) => {
      if (name === "buildingSourcesId" && inputValue) {
        // check building input match current name or not
        const regex = new RegExp(inputValue && inputValue.toLowerCase());
        const currentNameEN = _.get(item, "nameEn") ? _.get(item, "nameEn").toLowerCase() : "";
        const currentNameZh = _.get(item, "nameZh") ? _.get(item, "nameZh").toLowerCase() : "";
        const isMatchCurrentNames = inputValue && (regex.test(currentNameEN) || regex.test(currentNameZh));
        // check building input match previous name or not
        let matchedPreviousName = matchPreviousName(inputValue, item.previousNames?.length ? item.previousNames : [])
        if(sbu === 'COMM') {
          if(isMatchCurrentNames) {
            if(item.previousNames && item.previousNames.length > 0) {
              return {
                value: item["_id"] || item["id"],
                label: `(${_.get(item, 'districtDetail.abbr')})${showZhEnLabel
                  ? parseNameWithDash(currentNameEN, currentNameZh, intl)
                  : _.get(item, langkey)} - ${_.get(_.last(item.previousNames), langkey)}`
              }
            } else {
              console.log('[ item,  ] >', item, )
              return {
                value: item["_id"] || item["id"],
                label: `(${_.get(item, 'districtDetail.abbr')})${showZhEnLabel
                  ? parseNameWithDash(currentNameEN, currentNameZh, intl)
                  : _.get(item, langkey)}`
              }
            }
          } else if (matchedPreviousName) {
            return {
              value: item["_id"] || item["id"],
              label: `(${_.get(item, 'districtDetail.abbr')})${showZhEnLabel
                ? parseNameWithDash(matchedPreviousName.nameEn, matchedPreviousName.nameZh, intl)
                : matchedPreviousName[langkey]} - ${_.get(item, langkey)}${intl.locale === "zh" ? "(舊)" : "(Old)"}`,
            }
          }
      
          if(item.previousNames && item.previousNames.length > 0) {
            return {
              value: item["_id"] || item["id"],
              label: `${showZhEnLabel
                ? parseNameWithDash(item.nameEn, item.nameZh, intl)
                : item[langkey]} - ${_.get(_.last(item.previousNames), langkey)}`,
            }
          }
        }
        if (isMatchCurrentNames) {
          return {
            value: item["_id"] || item["id"],
            label: `${showZhEnLabel
              ? parseNameWithDash(currentNameEN, currentNameZh, intl)
              : _.get(item, langkey)}`
          }
        }
        if (matchedPreviousName) {
          return {
            value: item["_id"] || item["id"],
            label: `${intl.locale === "zh" ? "(舊)" : "(Old)"} ${showZhEnLabel
              ? parseNameWithDash(matchedPreviousName.nameEn, matchedPreviousName.nameZh, intl)
              : matchedPreviousName[langkey]}`,
          }
        }
      }
      return {
        value: item["_id"] || item["id"],
        label: showZhEnLabel
          ? parseNameWithDash(item.nameEn, item.nameZh, intl)
          : item[langkey],
      }
    });

    if (!(options && options.length > 0)) {
      const graphqlvariable = {
        name: "",
        limit,
        offset: 0,
        sorter: [{ field: "nameEn", order: "ASC" }],
      };
      apiaction(graphqlvariable);
    }
    return options;
  };

  // const debouncedFetchOption = debounce((newValue) => {
  //   const graphqlvariable = {
  //     name: newValue.trim(),
  //     limit,
  //     offset: 0,
  //     sorter: [{ field: inputLang, order: "ASC" }],
  //   };
  //   apiaction(graphqlvariable);
  // }, 5000);

  const debouncedFetchOption = useCallback(  
    _.debounce((newValue) => {
      const graphqlvariable = {
        name: newValue.trim(),
        limit,
        offset: 0,
        sorter: [{ field: inputLang, order: "ASC" }],
      };
      apiaction(graphqlvariable);
    }, 1000),  
    []
  )


  function handleInputChange(newValue) {
    const isEng = newValue.toLowerCase().match(/[a-z]/i) || newValue == "";

    setInputLang(isEng ? "nameEn" : "nameZh");
    setInputValue(newValue);

    if (newValue === "") {
      sethasInput(false);
    } else {
      sethasInput(true);
      /**
       * Desktop
       *    typing chinese -> trigger oncomposition event
       *
       * IOS
       *    typing chinese
       *        through keyboard -> trigger oncomposition event
       *        through voice -> normal input
       *
       * Android
       *    typing chinese
       *        through keyboard -> normal input
       *        through voice -> trigger oncomposition event
       */
      // only stop fetching api during typing composition
      if (isEng || !onComposition.current) {
        debouncedFetchOption(newValue);
      }
    }
    return newValue;
  }

  function handleClick(newValue) {
    setSingle(newValue);
    if (setSelectedData) setSelectedData(name, newValue);
    if (newValue && newValue.length > 0) {
      const arr = [];
      newValue.map((item) => {
        arr.push(item.value);
      });
      onChange(arr);
    } else {
      onChange([]);
    }
  }

  const selectStyles = {
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected ? "#33CCCC" : "#fff",
      ":active": {
        backgroundColor: state.isSelected ? "#33CCCC" : "#33CCCC",
      },
    }),
    placeholder: (styles, { isDisabled }) => ({
      ...styles,
      color: isDisabled ? "rgba(0, 0, 0, 0.38)" : "hsl(0,0%,20%)",
    }),
  };

  const noOptionsMessage = (inputValue) => {
    if (!inputValue) {
      return "It's null";
    }
    return "No results found";
  };

  const handleComposition = (e) => {
    if (e.type === "compositionend") {
      onComposition.current = false;

      handleInputChange(e.target.value);
    } else {
      onComposition.current = true;
    }
  };

  const options = useMemo(
    () => getoptions(),
    [optionsdata, searchHistory, inputLang],
  );

  return (
    <div className={classes.root}>
      <Select
        isMulti
        // loadOptions={loadOptions}
        defaultOptions
        options={options}
        filterOption={null}
        onChange={handleClick}
        onInputChange={handleInputChange}
        classes={classes}
        styles={selectStyles}
        noOptionsMessage={noOptionsMessage}
        maxMenuHeight={210}
        TextFieldProps={{
          label: props.label,
          error: touched && invalid,
          helperText: touched && error,
          InputLabelProps: {
            htmlFor: name || null,
            shrink: true,
          },
          margin: "dense",
          variant: context.browserDetect === "mobile" ? "outlined" : "standard",
          onCompositionStart: handleComposition,
          onCompositionUpdate: handleComposition,
          onCompositionEnd: handleComposition,
          ...customInputProps,
        }}
        placeholder={
          props.placeholder ||
          intl.formatMessage({
            id: "search.form.select",
          })
        }
        components={components}
        value={value !== "" ? single : null}
        isClearable
        isDisabled={disabled}
        instanceId={name}
        onBlur={() => onBlur()}
      />
    </div>
  );
}

AutoCompleteSelect.contextTypes = {
  browserDetect: PropTypes.string,
};

export default injectIntl(AutoCompleteSelect);
