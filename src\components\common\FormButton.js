import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { createStyles, withStyles } from "@material-ui/core/styles";
import Button from "@material-ui/core/Button";

// We can inject some CSS into the DOM.
const styles = createStyles({
  root: {
    background: "#13CE66",
    color: "#fff",
    borderRadius: 4,
    border: 0,
    height: 36,
    minWidth: 150,
    boxShadow: "0px 1px 3px #00000033",
    padding: "0px",
    "&:hover": {
      background: "#fff",
      color: "#13CE66"
    },
    textTransform: "none",
    "& span": { pointerEvents: "none" }
  }
});

function FormButton(props) {
  const { classes, children, className, ...other } = props;

  return (
    <Button className={clsx(classes.root, className)} {...other}>
      {children || "class names"}
    </Button>
  );
}

FormButton.propTypes = {
  children: PropTypes.node,
  onClick: PropTypes.func,
  classes: PropTypes.object.isRequired,
  className: PropTypes.string
};

export default withStyles(styles)(FormButton);
