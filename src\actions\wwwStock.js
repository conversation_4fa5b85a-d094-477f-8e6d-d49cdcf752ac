import U<PERSON><PERSON> from "uuid/v1";

import {
  CLEAR_LIST_WWW_STOCK,
  LIST_WWW_STOCK_RESULT_ERROR,
  LIST_WWW_STOCK_RESULT_START,
  LIST_WWW_STOCK_RESULT_SUCCESS,
  LIST_WWW_STOCK_RESULT_NULL_SUCCESS,
  LIST_MORE_WWW_STOCK_RESULT_START,
  SET_CURRENT_WWW_STOCK_ERROR,
  SET_CURRENT_WWW_STOCK_SUCCESS,
  SET_CURRENT_WWW_STOCK_START,
  SET_SEARCH_WWW_STOCK_RESULT_ANCHOR,
  <PERSON><PERSON><PERSON>_SEARCH_WWW_STOCK_RESULT_ANCHOR,
  LIST_WWW_STOCK_DESC_ERROR,
  LIST_WWW_STOCK_DESC_NULL_SUCCESS,
  LIST_WWW_STOCK_DESC_SUCCESS,
  LIST_WWW_STOCK_DESC_START,
  UPDATE_WWW_STOCK_DESC_ERROR,
  UPDATE_WWW_STOCK_DESC_SUCCESS,
  UPDATE_WWW_STOCK_DESC_START,
  UPDATE_WWW_STOCK_ERROR,
  UPDATE_WWW_STOCK_SUCCESS,
  UPDATE_WWW_STOCK_START,

  WWW_USED_COUNT_QUERY_START,
  WWW_USED_COUNT_QUERY_SUCCESS,
  WWW_USED_COUNT_QUERY_ERROR,
} from "@/constants/wwwStock";

export function clearWWWStockList() {
  return async (dispatch, getState, {getQuery, delay, universalRequest},) => {
    dispatch({
      type: CLEAR_LIST_WWW_STOCK,
    });
  }
}

export function fetchWWWStockList(variables, {isFetchingMore, intl}) {
  return async (dispatch, getState, {getQuery, delay, universalRequest},) => {

    const uuid = UUID();
    if (isFetchingMore) {
      dispatch({
        type: LIST_MORE_WWW_STOCK_RESULT_START,
        payload: {
          variables,
          uuid,
        },
        checkrefreshToken: true,
      });
    } else {

      dispatch({
        type: LIST_WWW_STOCK_RESULT_START,
        payload: {
          variables,
          uuid,
        },
        checkrefreshToken: true,
      });
    }

    try {
      const {refreshing} = getState().auth;
      refreshing && (await delay(1500));
      // await delay(2000);

      const token = getState().auth.user.oauth;
      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: token,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("LIST_WWW_STOCK_QUERY");
      // console.log("query:", query)

      const resp = await universalRequest("/www/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: variables
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });
      if (resp.errors) {
        throw new Error(resp.errors[0].message);
      }

      const {data} = resp;

      if (data.wwwStockList && data.wwwStockListCount) {
        dispatch({
          type: LIST_WWW_STOCK_RESULT_SUCCESS,
          payload: {
            data,
            uuid,
          },
        });
      } else {

        dispatch({
          type: LIST_WWW_STOCK_RESULT_NULL_SUCCESS,
          payload: {
            data,
            uuid,
          },
        });
      }
    } catch (error) {
      let errorMsg;
      if (error.message === "Request timeout.") {
        errorMsg = intl.formatMessage({
          id: "common.timeout.error",
        });
      } else {
        errorMsg = intl.formatMessage({
          id: "common.disconnect.error",
        });
      }

      dispatch({
        type: LIST_WWW_STOCK_RESULT_ERROR,
        payload: {
          error: {
            message: errorMsg,
          },
        },
      });

      // throw new Error(error);
    }
  };
}


export function fetchCurrentWWWStock(stockId, { intl}) {
  return async (dispatch, getState, {getQuery, delay, universalRequest},) => {
    const uuid = UUID();
    dispatch({
      type: SET_CURRENT_WWW_STOCK_START,
      payload: {
        variables: {stockId, limit:1},
        uuid,
      },
      checkrefreshToken: true,
    });

    try {
      const {refreshing} = getState().auth;
      refreshing && (await delay(1500));
      // await delay(2000);

      const token = getState().auth.user.oauth;
      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: token,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
      };

      const query = await getQuery("LIST_WWW_STOCK_QUERY");

      const resp = await universalRequest("/www/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables:  {stockId, limit:1},
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });
      if (resp.errors) {
        throw new Error(resp.errors[0].message);
      }

      const {data} = resp;

      dispatch({
        type: SET_CURRENT_WWW_STOCK_SUCCESS,
        payload: {
          data,
          uuid,
        },
      });
    } catch (error) {
      let errorMsg;
      if (error.message === "Request timeout.") {
        errorMsg = intl.formatMessage({
          id: "common.timeout.error",
        });
      } else {
        errorMsg = intl.formatMessage({
          id: "common.disconnect.error",
        });
      }

      dispatch({
        type: SET_CURRENT_WWW_STOCK_ERROR,
        payload: {
          error: {
            message: errorMsg,
          },
        },
      });

      // throw new Error(error);
    }
  };
}

export function setAnchor(px) {
  return async (dispatch) => {
    dispatch({
      type: SET_SEARCH_WWW_STOCK_RESULT_ANCHOR,
      payload: {
        px,
      },
    });
  };
}

export function clearAnchor() {
  return async (dispatch) => {
    dispatch({
      type: CLEAR_SEARCH_WWW_STOCK_RESULT_ANCHOR,
    });
  };
}

export function fetchWWWStockDesc(variables, { intl}) {
  return async (dispatch, getState, {getQuery, delay, universalRequest},) => {
    dispatch({
      type: LIST_WWW_STOCK_DESC_START,
      payload: {
        variables,
      },
      checkrefreshToken: true,
    });

    try {
      const {refreshing} = getState().auth;
      refreshing && (await delay(1500));
      // await delay(2000);

      const token = getState().auth.user.oauth;
      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: token,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("DESC_WWW_STOCK_QUERY");
      // console.log("query:", query)

      const resp = await universalRequest("/www/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: variables
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });
      if (resp.errors) {
        throw new Error(resp.errors[0].message);
      }

      const {data} = resp;

      if (data.wwwStockDesc) {
        dispatch({
          type: LIST_WWW_STOCK_DESC_SUCCESS,
          payload: {
            data,
          },
        });
      } else {

        dispatch({
          type: LIST_WWW_STOCK_DESC_NULL_SUCCESS,
          payload: {
            data,
          },
        });
      }
    } catch (error) {
      let errorMsg;
      if (error.message === "Request timeout.") {
        errorMsg = intl.formatMessage({
          id: "common.timeout.error",
        });
      } else {
        errorMsg = intl.formatMessage({
          id: "common.disconnect.error",
        });
      }

      dispatch({
        type: LIST_WWW_STOCK_DESC_ERROR,
        payload: {
          error: {
            message: errorMsg,
          },
        },
      });

      // throw new Error(error);
    }
  };
}


export function updateWWWStock(variables, { intl}) {
  return async (dispatch, getState, {getQuery, delay, universalRequest},) => {
    const uuid = UUID();
    dispatch({
      type: UPDATE_WWW_STOCK_START,
      payload: {
        variables,
        uuid,
      },
      checkrefreshToken: true,
    });

    try {
      const {refreshing} = getState().auth;
      refreshing && (await delay(1500));
      // await delay(2000);

      const token = getState().auth.user.oauth;
      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: token,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("UPDATE_WWW_STOCK_QUERY");
      // console.log("query:", query)

      const resp = await universalRequest("/www/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: variables
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });
      if (resp.errors) {
        throw new Error(resp.errors[0].message);
      }

      const {data} = resp;

      dispatch({
        type: UPDATE_WWW_STOCK_SUCCESS,
        payload: {
          data,
          uuid,
        },
      });
    } catch (error) {
      let errorMsg;
      if (error.message === "Request timeout.") {
        errorMsg = intl.formatMessage({
          id: "common.timeout.error",
        });
      } else {
        errorMsg = intl.formatMessage({
          id: "common.disconnect.error",
        });
      }

      dispatch({
        type: UPDATE_WWW_STOCK_ERROR,
        payload: {
          error: {
            message: errorMsg,
          },
        },
      });

      // throw new Error(error);
    }
  };
}

export function updateWWWStockDesc(variables, { intl}) {
  return async (dispatch, getState, {getQuery, delay, universalRequest},) => {
    const uuid = UUID();
    dispatch({
      type: UPDATE_WWW_STOCK_DESC_START,
      payload: {
        variables,
        uuid,
      },
      checkrefreshToken: true,
    });

    try {
      const {refreshing} = getState().auth;
      refreshing && (await delay(1500));
      // await delay(2000);

      const token = getState().auth.user.oauth;
      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: token,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("UPDATE_WWW_STOCK_DESC_QUERY");
      // console.log("query:", query)

      const resp = await universalRequest("/www/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: variables
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });
      if (resp.errors) {
        throw new Error(resp.errors[0].message);
      }

      const {data} = resp;

      dispatch({
        type: UPDATE_WWW_STOCK_DESC_SUCCESS,
        payload: {
          data,
          uuid,
        },
      });
    } catch (error) {
      let errorMsg;
      if (error.message === "Request timeout.") {
        errorMsg = intl.formatMessage({
          id: "common.timeout.error",
        });
      } else {
        errorMsg = intl.formatMessage({
          id: "common.disconnect.error",
        });
      }

      dispatch({
        type: UPDATE_WWW_STOCK_DESC_ERROR,
        payload: {
          error: {
            message: errorMsg,
          },
        },
      });

      // throw new Error(error);
    }
  };
}

export function queryWWWUsedCount(variables, { intl }) {
  return async (dispatch, getState, {getQuery, delay, universalRequest},) => {
    dispatch({
      type: WWW_USED_COUNT_QUERY_START,
      payload: {
        variables,
      },
      checkrefreshToken: true,
    });

    try {
      const {refreshing} = getState().auth;
      refreshing && (await delay(1500));
      // await delay(2000);

      const token = getState().auth.user.oauth;
      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: token,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("WWW_USED_COUNT_QUERY");
      // console.log("query:", query)

      const resp = await universalRequest("/www/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: variables
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });
      if (resp.errors) {
        throw new Error(resp.errors[0].message);
      }

      const { data } = resp;

      dispatch({
        type: WWW_USED_COUNT_QUERY_SUCCESS,
        payload: {
          data,
        }
      });
    } catch (error) {
      let errorMsg;
      if (error.message === "Request timeout.") {
        errorMsg = intl.formatMessage({
          id: "common.timeout.error",
        });
      } else {
        errorMsg = intl.formatMessage({
          id: "common.disconnect.error",
        });
      }

      dispatch({
        type: WWW_USED_COUNT_QUERY_ERROR,
        payload: {
          error: {
            message: errorMsg,
          },
        },
      });
    }
  };
}
