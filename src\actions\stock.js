/* eslint-disable import/prefer-default-export */
import _ from "lodash";
import moment from "moment";
import { sbu } from "../config";

import {
  LIST_STOCK_START,
  LIST_STOCK_SUCCESS,
  LIST_STOCK_ERROR,
  LIST_STOCKMEDIA_START,
  LIST_STOCKMEDIA_SUCCESS,
  LIST_STOCKMEDIA_ERROR,
  LIST_MYFAVORITE_SUCCESS,
  LIST_MYFAVORITE_ERROR,
  LIST_LANDSEARCH_SUCCESS,
  LIST_LANDSEARCH_ERROR,
  LIST_LANDSEARCH_PDF_SUCCESS,
  LIST_LANDSEARCH_PDF_ERROR,
  TOGGLE_MYFAVORITE_SUCCESS,
  TOGG<PERSON>_MYFAVORITE_ERROR,
  C<PERSON><PERSON>_STOCK,
  CHANGE_CURRENT_STOCK,
  QUERY_UNIT_VIEWS_SUCCESS,
  QUERY_UNIT_VIEWS_ERROR,
  QUERY_BUSINESS_SUCCESS,
  QUERY_DECORATIONS_SUCCESS,
  QUERY_DECORATIONS_ERROR,
  QUERY_POSSESSIONS_SUCCESS,
  QUERY_POSSESSIONS_ERROR,
  QUERY_CURRENT_STATES_SUCCESS,
  QUERY_CURRENT_STATES_ERROR,
  TOGGLE_MARK_STOCK_SUCCESS,
  TOGGLE_MARK_STOCK_ERROR,
  LIST_MARK_STOCK_SUCCESS,
  LIST_MARK_STOCK_ERROR,
  REMOVE_MARK_STOCK_SUCCESS,
  REMOVE_MARK_STOCK_ERROR,
  UPDATE_STOCK_MEDIA_BY_INDEX,
  UPDATE_STOCK_MEDIA_LIST,
  LIST_MARK_WWW_QUERY_SUCCESS,
  LIST_MARK_WWW_QUERY_ERROR,
} from "../constants/stock";
import {
  QUERY_PROPOSAL_RES_ERROR,
  QUERY_PROPOSAL_RES_START,
  QUERY_PROPOSAL_RES_SUCCESS,
  FETCH_RECREATE_PP,
  FETCH_RECREATE_PP_SUCCESS,
  SET_IS_RECREATE_PP,
} from "../constants/proposal";
import { addActivityLog } from "./log";

export function listStockDetail(variables, hash = null, isListProposal = '0') {
  return async (
    dispatch,
    getState,
    { fetch, universalRequest, api, getQuery, delay },
  ) => {
    dispatch({
      type: LIST_STOCK_START,
      payload: variables,
    });

    try {
      const { data, errors } = await universalRequest("/listStockDetail", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ variables }),
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      const query = await getQuery("WWW_DETAILS_QUERY");
      const promises = variables._id.map(id =>
        universalRequest("/www/graphql", {
          method: "POST",
          body: JSON.stringify({
            query: query,
            variables: { stock: id }
          }),
          headers: {
            "Content-Type": "application/json",
            Authorization: getState().auth.user.oauth,
            "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
          },
        }).then(res => {
          if (!res.errors) {
            return _.get(res, "data.details.0", null);
          }
          return null;
        }).catch(() => null)
      );

      const results = await Promise.all(promises);
      const www_details = results.filter(Boolean)

      if(data && data.data && data.data.stocks && data.data.stocks.length > 0) {
        data.data.stocks = data.data.stocks.map(stock => {
          const wwwDetail = www_details.find(detail => detail.stock === stock._id);
          if(wwwDetail) {
            return {
              ...stock,
              wwwDetail
            };
          }
          return stock;
        });
      }

      if (errors && errors[0])
        throw new Error("Server Error: " + errors[0].message);
      if (data && data.errors && data.errors[0])
        throw new Error("Stock Graphql Error: " + data.errors[0].message);

      await dispatch({ type: SET_IS_RECREATE_PP, payload: !!hash });
      if (hash) {
        await dispatch({ type: FETCH_RECREATE_PP });

        const { data, errors } = await universalRequest("/getReCreatePP", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            hash: hash,
            isListProposal: isListProposal,
          }),
        }).catch((error) => {
          return {
            errors: error,
          };
        });

        if (errors && errors[0])
          throw new Error("Server Error: " + errors[0].message);
        if (data && data.errors && data.errors[0])
          throw new Error("Stock Graphql Error: " + data.errors[0].message);

        dispatch({
          type: FETCH_RECREATE_PP_SUCCESS,
          payload: isListProposal === '1' ? data.listProposal : data.proposal,
        });
      }

      dispatch({
        type: LIST_STOCK_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_STOCK_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };
}

export function listStockMedia(variables, waitFor) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_STOCKMEDIA_START,
      checkrefreshToken: true,
    });

    dispatch(addActivityLog("media.search", "read", { ...variables, mediaType: "stock" }));

    if (waitFor && waitFor > 0) {
      const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
      await sleep(waitFor);
    }

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      };

      const query = await getQuery("LIST_STOCK_MEDIA_QUERY");

      const { sid, empId } = variables;
      const promises = sid.map(id =>
        universalRequest("/media/graphql", {
          method: "POST",
          body: JSON.stringify({
            query,
            variables: { sid: id, empId },
          }),
          ...options
        }).then(res => {
          if (!res.errors) {
            return { id, data: res.data.stock };
          }
          return null;
        }).catch(() => null)
      );
      const results = await Promise.all(promises);
      const data = results.filter(Boolean);

      dispatch({
        type: LIST_STOCKMEDIA_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_STOCKMEDIA_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };
}

export function updateStockMediaByIndex({ index = -1, mediaIndex = -1, mediaType = "photo", media }) {
  return async (
    dispatch,
  ) => {
    dispatch({
      type: UPDATE_STOCK_MEDIA_BY_INDEX,
      payload: {
        index,
        mediaIndex,
        mediaType,
        media,
      },
    });
  };
}

export function updateStockMediaList(mediaType, mediaList) {
  return {
    type: UPDATE_STOCK_MEDIA_LIST,
    payload: {
      mediaType,
      mediaList
    }
  };
}

export function clearStock() {
  return async (dispatch) => {
    dispatch({
      type: CLEAR_STOCK,
    });
  };
}

export function toggleMarkStock(stockid) {
  return async (dispatch, getState, { universalRequest }) => {
    const { emp_id } = getState().auth.user.login.info;

    try {
      const data = await universalRequest("/addMarkStock", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ emp_id, mongoid: "stock.mongoid", stockid }),
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: TOGGLE_MARK_STOCK_SUCCESS,
        payload: {
          data,
          stockid,
        },
      });
    } catch (error) {
      console.log(error);
      dispatch({
        type: TOGGLE_MARK_STOCK_ERROR,
        payload: { error: error.message },
      });
      //throw error;
    }
    await dispatch(listMarkStock());
  };
}

export function listMarkStock() {
  return async (dispatch, getState, { universalRequest }) => {
    const { emp_id } = getState().auth.user.login.info;
    const { stocks } = getState().stocklist;
    try {
      const data = await universalRequest("/listMarkStock", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ emp_id }),
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        console.log("Errors: " + data.errors[0].message);
        throw new Error(data.errors[0].message);
      }

      // 创建ID到索引的映射，避免重复查找
      const stockIndexMap = {};
      stocks.forEach((stock, index) => {
        if (stock._id) {
          stockIndexMap[stock._id] = index;
        }
      });

      const markStockIds = data.markStockIds.sort((a, b) => {
        const indexA = stockIndexMap[a] !== undefined ? stockIndexMap[a] : Number.MAX_SAFE_INTEGER;
        const indexB = stockIndexMap[b] !== undefined ? stockIndexMap[b] : Number.MAX_SAFE_INTEGER;
        return indexA - indexB;
      });
      data.markStockIds = markStockIds;

      dispatch({
        type: LIST_MARK_STOCK_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_MARK_STOCK_ERROR,
        payload: { error: error.message },
      });
      //throw error;
    }
  };
}

export function removeMarkStocks() {
  return async (dispatch, getState, { universalRequest }) => {
    const { emp_id } = getState().auth.user.login.info;
    try {
      const data = await universalRequest("removeMarkStock", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ emp_id }),
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        console.log("Errors: ", data.errors[0].message);
        throw new Error(data.errors[0].message);
      }

      // clear the search result to prevent infinite scroll
      // to fetch more data when refreshing
      // dispatch({
      //   type: CLEAR_SEARCHRESULT,
      // });
      dispatch({
        type: REMOVE_MARK_STOCK_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: REMOVE_MARK_STOCK_ERROR,
        payload: { error: error.message },
      });
    }
    await dispatch(listMarkStock());
  };
}

export function togglemyFavoriteapi({ mongoid, stockid }) {
  return async (dispatch, getState, { universalRequest }) => {
    const { emp_id } = getState().auth.user.login.info;

    try {
      const data = await universalRequest("/addMyFavorite", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ emp_id, mongoid, stockid }),
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: TOGGLE_MYFAVORITE_SUCCESS,
        payload: {
          data,
          mongoid,
        },
      });
    } catch (error) {
      console.log(error);
      dispatch({
        type: TOGGLE_MYFAVORITE_ERROR,
        payload: { error: error.message },
      });
      //throw error;
    }
  };
}

export function listmyFavorite() {
  return async (dispatch, getState, { universalRequest }) => {
    const { emp_id } = getState().auth.user.login.info;
    try {
      const data = await universalRequest("/listMyFavorite", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ emp_id }),
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        console.log("Errors: " + data.errors[0].message);
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_MYFAVORITE_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_MYFAVORITE_ERROR,
        payload: { error: error.message },
      });
      //throw error;
    }
  };
}

export function listLandSearch(variables) {
  return async (dispatch, getState, { universalRequest }) => {
    const today = moment();
    try {
      const data = await universalRequest("/listlandSearch", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          stock_id: variables,
          sbu
        }),
      }).catch((error) => {
        throw error;
      });

      if (data.errors) {
        throw data.errors[0];
      }

      dispatch({
        type: LIST_LANDSEARCH_SUCCESS,
        payload: {
          payload: variables,
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_LANDSEARCH_ERROR,
        payload: { error: _.get(error, "message", "List landsearch error.") },
      });
      //throw error;
    }
  };
}

export function listLandSearchPdf(variables) {
  return async (dispatch, getState, { universalRequest }) => {
    const { list, unicorn_id, landSearchRefNo } = variables
    try {
      const data = await universalRequest("/listlandSearchPdf", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ sbu, list, unicorn_id, landSearchRefNo }),
      }).catch((error) => {
        throw error;
      });

      if (data.errors) {
        throw data.errors[0];
      }

      dispatch({
        type: LIST_LANDSEARCH_PDF_SUCCESS,
        payload: {
          payload: variables,
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_LANDSEARCH_PDF_ERROR,
        payload: { error: _.get(error, "message", "List landsearch error.") },
      });
      //throw error;
    }
  };
}

export function changeCurrentStock(current) {
  return (dispatch) => {
    dispatch({
      type: CHANGE_CURRENT_STOCK,
      payload: { current },
    });
  };
}

export function queryProposalRes() {
  return async (dispatch, ...args) => {
    const promises = [
      queryUnitViews()(dispatch, ...args),
      queryDecorations()(dispatch, ...args),
      queryPossessions()(dispatch, ...args),
      queryCurrentstates()(dispatch, ...args)
    ];

    dispatch({ type: QUERY_PROPOSAL_RES_START });

    await Promise.all(promises)
      .then(() => {
        dispatch({ type: QUERY_PROPOSAL_RES_SUCCESS });
      })
      .catch(() => dispatch({ type: QUERY_PROPOSAL_RES_ERROR }));
  };
}

export function queryUnitViews() {
  return async (dispatch, getState, { graphqlRequest, api, getQuery, universalRequest }) => {
    const options = {
      headers: {
        "Content-Type": "application/json",
        Authorization: getState().auth.user.oauth,
        "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
      },
    };

    try {
      const query = await getQuery("QUERY_UNIT_VIEWS");

      const data = await universalRequest("/stock/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: {}
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: QUERY_UNIT_VIEWS_SUCCESS,
        payload: data,
      });
    } catch (e) {
      dispatch({
        type: QUERY_UNIT_VIEWS_ERROR,
        payload: { error: e.message },
      });
      throw e;
    }
  };
}

export function queryBusinessOptions(params) {
  return async (dispatch, getState, { graphqlRequest, api, getQuery, universalRequest }) => {
    const options = {
      headers: {
        "Content-Type": "application/json",
        Authorization: getState().auth.user.oauth,
        "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
      },
    };

    try {
      const query = await getQuery("QUERY_BUSINESS_OPTIONS");

      const data = await universalRequest("/stock/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: params
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: QUERY_BUSINESS_SUCCESS,
        payload: data,
      });
    } catch (e) {
      dispatch({
        type: QUERY_UNIT_VIEWS_ERROR,
        payload: { error: e.message },
      });
      throw e;
    }
  };
}

export function queryDecorations() {
  return async (dispatch, getState, { graphqlRequest, api, getQuery, universalRequest }) => {
    const options = {
      headers: {
        "Content-Type": "application/json",
        Authorization: getState().auth.user.oauth,
        "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
      },
    };

    try {
      const query = await getQuery("QUERY_DECORATIONS");

      const data = await universalRequest("/stock/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: {}
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: QUERY_DECORATIONS_SUCCESS,
        payload: data,
      });
    } catch (e) {
      dispatch({
        type: QUERY_DECORATIONS_ERROR,
        payload: { error: e.message },
      });
      throw e;
    }
  };
}

export function queryPossessions() {
  return async (dispatch, getState, { graphqlRequest, api, getQuery, universalRequest }) => {
    const options = {
      headers: {
        "Content-Type": "application/json",
        Authorization: getState().auth.user.oauth,
        "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
      },
    };

    try {
      const query = await getQuery("QUERY_POSSESSIONS");

      const data = await universalRequest("/stock/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: {}
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: QUERY_POSSESSIONS_SUCCESS,
        payload: data,
      });
    } catch (e) {
      dispatch({
        type: QUERY_POSSESSIONS_ERROR,
        payload: { error: e.message },
      });
      throw e;
    }
  };
}

export function queryCurrentstates() {
  return async (dispatch, getState, { graphqlRequest, api, getQuery, universalRequest }) => {
    const options = {
      headers: {
        "Content-Type": "application/json",
        Authorization: getState().auth.user.oauth,
        "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
      },
    };

    try {
      const query = await getQuery("QUERY_CURRENT_STATES");

      const data = await universalRequest("/stock/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: {}
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: QUERY_CURRENT_STATES_SUCCESS,
        payload: data,
      });
    } catch (e) {
      dispatch({
        type: QUERY_CURRENT_STATES_ERROR,
        payload: { error: e.message },
      });
      throw e;
    }
  };
}

export function getDeniedCalls(phoneNos) {
  return async (dispatch, getState, { universalRequest }) => {
    try {
      const phones = _.reduce(
        _.flatten(phoneNos),
        (numList, item) => {
          if (!item || !item.number) return numList;

          // Format the number by removing non-digit and leading 852 if exists
          let parsedNum = item.number.replace(/\D/g, "").trim();
          if (_.startsWith(parsedNum, "852"))
            parsedNum = parsedNum.substring(3);

          // Keep original number for return value
          return parsedNum.length === 8
            ? [...numList, { number: item.number, parsedNum }]
            : numList;
        },
        [],
      );

      // Request using parsed number
      const data = await universalRequest("/getDeniedCalls", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ phones: _.map(phones, "parsedNum") }),
      });

      // The result will be in { number: isDeniedCall } format
      return _.reduce(
        phones,
        (result, phone) => ({
          ...result,
          [phone?.number]: _.get(data, `data.${phone.parsedNum}`, false),
        }),
        {},
      );
    } catch (e) {
      console.error(e);
    }
  };
}

export function getStockAiTextUrl(params) {
  return async (dispatch, getState, { graphqlRequest, api, getQuery, universalRequest }) => {
    const options = {
      headers: {
        "Content-Type": "application/json",
        Authorization: getState().auth.user.oauth,
        "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
      },
    };

    try {
      const query = await getQuery("STOCK_AI_TEXT_QUERY");

      const data = await universalRequest("/stock/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: params
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      return data.data
    } catch (e) {
      throw e;
    }
  };
}

export function queryMarkWWWList(variables, waitFor) {
  return async (dispatch, getState, { graphqlRequest, api, getQuery, universalRequest }) => {
    const options = {
      headers: {
        "Content-Type": "application/json",
        Authorization: getState().auth.user.oauth,
        "CAS-Authorization": localStorage.getItem("casAccessToken"),
      },
    };

    try {
      const query = await getQuery("LIST_MARK_WWW_QUERY");

      const { stockId } = variables;
      const res = await universalRequest("/www/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: { stockId: _.isArray(stockId) ? stockId : [stockId] }
        }),
        ...options,
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (res.errors) {
        throw new Error(res.errors[0].message);
      }

      dispatch({
        type: LIST_MARK_WWW_QUERY_SUCCESS,
        payload: res.data.markWWWList,
      });
    } catch (error) {
      dispatch({
        type: LIST_MARK_WWW_QUERY_ERROR,
        payload: {
          error,
        },
      });
    }
  };
}
