import { MongoClient } from "mongodb";
import { databaseUrl, databaseName, sessionDatabaseName } from "../config";

class MongoDB {
  constructor() {
    this.db = null;
    this.sessionDb = null;
  }

  connect() {
    return new Promise((resolve, reject) => {
      MongoClient.connect(databaseUrl, (error, db) => {
        if (error) {
          reject(error);
        } else {
          this.db = db.db(databaseName)
          this.sessionDb = db.db(sessionDatabaseName);
          resolve([this.db, this.sessionDb]);
        }
      });
    });
  }
}

const mongodb = new MongoDB();

export default mongodb;
