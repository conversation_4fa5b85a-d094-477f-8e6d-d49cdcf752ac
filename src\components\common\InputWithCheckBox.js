import React from "react";
import { Field } from "redux-form";
import { FormattedMessage } from "react-intl";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import ShowHiddenCheckBox from "./ShowHiddenCheckBox";

export const minValue = (min, msg) => (field) =>
  field && field.value && field.value < min ? msg : undefined;

export const maxValue = (max, msg) => (field) =>
  field && field.value && field.value > max ? msg : undefined;

export const number = (msg) => (field) =>
  field && field.value && isNaN(Number(field.value)) ? msg : undefined;

function createMinvalue(min) {
  return minValue(min, <FormattedMessage id="search.form.invalidinput" />);
}
function createMaxvalue(max) {
  return maxValue(max, <FormattedMessage id="search.form.invalidinput" />);
}

const numberValidate = number(
  <FormattedMessage id="search.form.invalidinput" />,
);

const styles = {
  greyCheckbox: {
    width: 18,
    height: 18,
    borderRadius: 3,
    margin: 3,
    background: "rgba(206,217,224,.5)",
  },
};

// ref: https://gist.github.com/kitze/23d82bb9eb0baabfd03a6a720b1d637f
const ConditionalWrap = ({ condition, wrap, children }) =>
  condition ? wrap(children) : children;

function InputWithCheckBox(props) {
  const {
    classes,
    fields,
    label,
    changeFieldValue,
    renderComponent,
    min,
    max,
    checkboxProps,
    checkboxInFront,
    checkboxXs = 4,
    aligntoLabel,
    fakeCheckbox,
    xs,
    allowWrap = true,
    ...custom
  } = props;

  const [validators, setValidators] = React.useState([]);

  React.useEffect(() => {
    const validationArray = [];
    if (min || min === 0) validationArray.push(createMinvalue(min));
    if (max || max === 0) validationArray.push(createMaxvalue(max));
    if (custom.type === "number") validationArray.push(numberValidate);

    setValidators(validationArray);
  }, []);

  return fields.map((item, index) => (
    <ConditionalWrap
      condition={xs}
      wrap={(children) => (
        <Grid item xs={xs}>
          {children}
        </Grid>
      )}
      key={index}
    >
      <Grid container wrap={allowWrap ? "wrap" : "nowrap"}>
        {fakeCheckbox && <div className={classes.greyCheckbox} />}
        {!fakeCheckbox && checkboxInFront && (
          <Field
            name={item}
            component={ShowHiddenCheckBox}
            changeFieldValue={changeFieldValue}
            aligntoLabel={aligntoLabel}
            {...checkboxProps}
          />
        )}
        <Grid item xs={12 - checkboxXs} style={{ margin: "auto" }}>
          {(label === "Rent" || label === "Price") ? (
          <>
          <div style={{ display: "flex", alignItems: "center" }}>
          {item.toLowerCase().includes("total") && (<div style={{minWidth: "12px", maxWidth: "12px"}}>$</div>)}
          {item.toLowerCase().includes("avg") && (<div style={{minWidth: "22px", maxWidth: "22px"}}>@</div>)}
          <div style={{flexGrow: 1}}>
          <Field
            name={item}
            validate={validators}
            label={label + (fields.length > 1 ? " " + (index + 1) : "")}
            component={renderComponent}
            aligntoLabel={aligntoLabel}
            fullWidth
            {...custom}
            label={item}
          />
          </div>
          {item.toLowerCase().includes("rent") && item.toLowerCase().includes("total") && (<div style={{minWidth: "16px", maxWidth: "16px", textAlign: "left"}}>K</div>)}
          {item.toLowerCase().includes("price") && item.toLowerCase().includes("total") && (<div style={{minWidth: "16px", maxWidth: "16px", textAlign: "left"}}>M</div>)}
          </div>
          </>
          ):(
          <Field
            name={item}
            validate={validators}
            label={label + (fields.length > 1 ? " " + (index + 1) : "")}
            component={renderComponent}
            aligntoLabel={aligntoLabel}
            fullWidth
            {...custom}
          />
          )}
        </Grid>
        {!fakeCheckbox && !checkboxInFront && (
          <Grid item xs={checkboxXs}>
            <Field
              name={item}
              component={ShowHiddenCheckBox}
              changeFieldValue={changeFieldValue}
            />
          </Grid>
        )}
      </Grid>
    </ConditionalWrap>
  ));
}

export default withStyles(styles)(InputWithCheckBox);
