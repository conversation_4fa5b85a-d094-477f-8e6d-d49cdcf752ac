import React from "react";
import clsx from "clsx";
import { ButtonGroup } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import PropTypes from "prop-types";

import FormButton from "./FormButton";

const useStyles = makeStyles(() => ({
  wrapper: {
    width: "100%",
    zIndex: "999",
    position: "fixed",
    left: 0,
    bottom: 0,
    bottom: "env(safe-area-inset-bottom)",
  },
  fixBtn: {
    textTransform: "none",
    fontSize: "1.125em",
    height: "60px",
    minWidth: "auto",
    borderRadius: 0,
    lineHeight: "1em",
    "&:hover": {
      backgroundColor: "#13CE66",
      color: "#ffffff",
    },
  },
}));

function BottomButtons({ buttons }) {
  const classes = useStyles();

  if (!buttons || buttons.length === 0) return null;
  return (
    <div className={classes.wrapper}>
      <ButtonGroup fullWidth>
        {buttons.map(({ label, onClick, className, ...otherProps }) => (
          <FormButton
            key={label}
            onClick={onClick}
            className={clsx(classes.fixBtn, className)}
            {...otherProps}
          >
            {label}
          </FormButton>
        ))}
      </ButtonGroup>
    </div>
  );
}

BottomButtons.propTypes = {
  buttons: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.oneOfType([PropTypes.string, PropTypes.node]).isRequired,
      onClick: PropTypes.func.isRequired,
      className: PropTypes.string,
    }),
  ).isRequired,
};

export default BottomButtons;
