import React, { useState, useMemo, useRef } from "react";
import PropTypes from "prop-types";
import { injectIntl } from "react-intl";
import { connect } from "react-redux";
import _ from "lodash";
import { withStyles } from "@material-ui/styles";

import DialogFrame from "./DialogFrame";
import Dialog from "./Dialog";
import LoadingOverlay from "../LoadingOverlay";

const styles = theme => ({
});

function PerviewProposalDialog({
  open,
  perviewUrl,
  close,
  intl,
  classes,
}) {
  const [isLoading, setIsLoading] = useState(true);
  const handleLoad = () => {
    setIsLoading(false);
  }
  const handleError = (e) => {
    console.log('load error: ', e);
  }
  return (
    <Dialog open={open} handleClose={() => {close();setIsLoading(true);}}>
      {isLoading && <LoadingOverlay />}
      <iframe
        src={perviewUrl}
        frameBorder="0"
        scrolling="auto"
        style={{
          height: "70vh",
          width: "100%",
        }}
        onLoad={handleLoad} 
        onError={handleError}
      />
    </Dialog>
  )
}

PerviewProposalDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  perviewUrl: PropTypes.string,
  close: PropTypes.func.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
});

const mapDispatchToProps = (dispatch) => ({
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(PerviewProposalDialog)));
