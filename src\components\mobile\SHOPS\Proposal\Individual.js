import React, { useEffect, useMemo } from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import _ from "lodash";
import { injectIntl } from "react-intl";

import {
  clearCreateProposal,
  createProposal,
} from "../../../../actions/proposal";
import {
  getDefaultRemarks,
  langKey,
  findZhEnFromOptions,
  parseMedia,
  parseTenancyRecords,
  getFullAddress,
  getNotNullIsShow,
  mergeTenants,
} from "../../../Saleskit/helpers";
import FieldSection from "./FormSection/FieldSection";
import ProposalForm from "../../../Saleskit/Forms/ProposalForm";
import { sbu } from "../../../../config";

function Individual({
  detail,
  currentStock,
  media,
  buildingMedia,
  streetMedia,
  stockPossessions,
  stockCurrentStates,
  reCreatePPStocks,
  createDialogOpen,
  closeDialog,
  createProposal,
  clearCreateProposal,
  intl,
}) {
  useEffect(() => () => clearCreateProposal(), []);

  const stock = useMemo(
    () => _.find(detail, (s) => s._id === currentStock),
    [detail, currentStock],
  );

  const initializeFormState = () => {
    // avoid initiialize form if stock is still undefined
    if (!stock || !media || !buildingMedia || !streetMedia) return {};
    const [ppHistoryContent] = reCreatePPStocks;

    // determine stock type
    let type = _.get(ppHistoryContent, "type") || "SaleAndLease";
    // switch (stock?.status?.nameEn) {
    //   case "Lease":
    //     type = "Lease";
    //     break;
    //   case "Sale+Lease":
    //     type = "SaleAndLease";
    //     break;
    //   default:
    // }

    const stockMediaData = _.find(
      media,
      (m) => m.id === _.get(stock, "unicorn.id").toString(),
    );
    const buildingMediaData = _.find(
      buildingMedia,
      (m) => m.id === _.get(stock, "building.unicorn.id", "").toString(),
    );
    const streetMediaData = _.find(
      streetMedia,
      (m) => m.id === _.get(stock, "street.street.unicorn.id", "").toString(),
    );

    const mainPhoto = []
      .concat(
        _.get(stockMediaData, "data.photo", [])
      )
      .find((m) => m?.tags?.indexOf("main") >= 0);

    const termRemarks = _.get(ppHistoryContent, "termRemarks") || getDefaultRemarks(type);

    const possibleFeeType = ["/SqFt", "/Qtr", "/Month", "/SY"];
    const possiblePaidBy = ["Paid By Tenant", "Paid By Landlord"];

    let areaItems = [];
    let netAreaTotal = 0;
    if (_.get(ppHistoryContent, "areas")) {
      areaItems.push(..._.filter(
        _.get(ppHistoryContent, "areas", []),
        v => v.areaNameEn !== "Total")
      );
    } else {
      const areas = _.get(stock, "area.areas") || [];
      areas.forEach((v) => {
        if (areaItems.filter((type) => type.areaName === v.areaName).length === 0) {
          areaItems.push({
            areaName: v.areaName,
            areaNameEn: v.areaNameEn,
            gross: 0,
            grossIsShow: false,
            grossVerified: false,
            net: 0,
            netIsShow: false,
            netVerified: false,
          });
        }

        let areaItem = areaItems.filter(
          (type) => type.areaName === v.areaName,
        )[0];
        let areaType = v.type.toLowerCase();
        if (areaType === "gross") {
          areaItem.gross = v.value;
          areaItem.grossIsShow = v.value && parseFloat(v.value) > 0;
          areaItem.grossVerified = v.verified;
          // area of 閣樓, 自建樓 is unselectable
          if (areaItem.areaName === "閣樓" || areaItem.areaName === "自建閣") {
            areaItem.grossIsShow = false;
          }
        }
        if (areaType === "net") {
          areaItem.net = v.value;
          areaItem.netIsShow = false; // all net area default unselect
          areaItem.netVerified = v.verified;
          netAreaTotal += v.value;
        }
      });
    }

    /** `AWS-3829` disable checkbox when the `usage` is `Mix (with residential)` OR `Domestic Use` */
    const residentialRegex = /Mix\s*\(with residential\)|Domestic Use/;
    const isResidential = residentialRegex.test(_.get(stock, "usage.nameEn") || "");
    if (isResidential) {
      areaItems.forEach((area) => {
        area.grossIsShow = false;
        area.netIsShow = false;
      });
    }

    const managementFeeUnit = _.get(ppHistoryContent, "managementFee.unit") ||
    (_.indexOf(possibleFeeType, _.get(stock, "managementFee.type")) >
      -1
      ? _.get(stock, "managementFee.type", "")
      : "/SqFt");

    const totalMonthlyMgtFee = managementFeeUnit === "/SqFt" ? parseFloat(_.get(stock, "managementFee.number", 0)) * parseFloat(_.get(stock, "area.total", 0)) : 0;
    
    const form = {
      stock: {
        stockId: _.get(stock, "unicorn.id"),
        // stockTypeId: _.get(stock, "stockTypeId", ""),
        streetType: 0,
        avgPrice: [
          {
            value: _.get(ppHistoryContent, "avgPrice.value") || _.get(stock, "askingPrice.average", 0),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "avgPrice.isShow"), type === "Sale" || type === "SaleAndLease"),
          },
        ],
        totalPrice: [
          {
            value: _.get(ppHistoryContent, "totalPrice.value") || _.get(stock, "askingPrice.total", 0),
            // Proposal type is Sale by default
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "totalPrice.isShow", (type === "Sale" || type === "SaleAndLease"))),
            isNego: getNotNullIsShow(_.get(ppHistoryContent, "totalPrice.isNego", false)),
          },
        ],
        suggestedAvgPrice: [
          {
            value: _.get(ppHistoryContent, "suggestedAvgPrice.value") ||
              _.get(
                _.find(
                  _.get(stock, "askingPrice.details", []),
                  (v) => v.type === "average_bottom",
                ),
                "value",
              ) || 0,
            isShow: _.get(ppHistoryContent, "suggestedAvgPrice.isShow") || false,
          },
        ],
        suggestedTotalPrice: [
          {
            value: _.get(ppHistoryContent, "suggestedTotalPrice.value") ||
              _.get(
                _.find(
                  _.get(stock, "askingPrice.details"),
                  (v) => v.type === "total_bottom",
                ),
                "value",
              ) || 0,
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "suggestedAvgPrice.isShow"),
              (type === "Sale" || type === "SaleAndLease") &&
              !!_.get(
                _.find(
                  _.get(stock, "askingPrice.details"),
                  (v) => v.type === "total_bottom",
                ),
                "value",
              )),
          },
        ],
        avgRent: [
          {
            value: _.get(ppHistoryContent, "avgRent.value") || _.get(stock, "askingRent.average", 0),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "avgRent.isShow"), (type === "Lease" || type === "SaleAndLease")),
          },
        ],
        totalRent: [
          {
            value: _.get(ppHistoryContent, "totalRent.value") || _.get(stock, "askingRent.total", 0),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "totalRent.isShow"), (type === "Lease" || type === "SaleAndLease")),
            isNego: _.get(ppHistoryContent, "totalRent.isNego") || false,
          },
        ],
        suggestedAvgRent: [
          {
            value: _.get(ppHistoryContent, "suggestedAvgRent.value") ||
              _.get(
                _.find(
                  _.get(stock, "askingRent.details"),
                  (v) => v.type === "average_bottom",
                ),
                "value",
              ) || 0,
            isShow: _.get(ppHistoryContent, "suggestedAvgRent.isShow") || false,
          },
        ],
        suggestedTotalRent: [
          {
            value: _.get(ppHistoryContent, "suggestedTotalRent.value") ||
              _.get(
                _.find(
                  _.get(stock, "askingRent.details"),
                  (v) => v.type === "total_bottom",
                ),
                "value",
              ) || 0,
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "suggestedTotalRent.isShow"),
              (type === "Lease" || type === "SaleAndLease") &&
              !!_.get(
                _.find(
                  _.get(stock, "askingRent.details"),
                  (v) => v.type === "total_bottom",
                ),
                "value",
              )),
          },
        ],
        stockType: [
          {
            value: _.get(ppHistoryContent, `stockType.value.${langKey(intl.locale, "name")}`) || _.get(stock, `stockType.${langKey(intl.locale, "name")}`),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "stockType.isShow"), true),
          },
        ],
        floor: [
          {
            value: _.get(ppHistoryContent, `floor.value`) || _.get(stock, "floor", ""),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, `floor.isShow`, true)),
          },
        ],
        unit: [
          {
            value: _.get(ppHistoryContent, `unit.value`) || "",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, `unit.isShow`, true)),
          },
        ],
        customAddressZh: _.get(ppHistoryContent, "customAddressZh") || getFullAddress({ ...intl, locale: "zh" }, stock),
        customAddressEn: _.get(ppHistoryContent, "customAddressEn") || getFullAddress({ ...intl, locale: "en" }, stock),
        customBuilding: [
          {
            value: _.get(ppHistoryContent, `customBuilding.value`) || _.get(
              stock,
              `building.${langKey(intl.locale, "name")}`,
              "---",
            ),
            isShow: _.get(ppHistoryContent, `customBuilding.isShow`) || true, //!!_.get(stock, `building.${langKey(intl.locale, "name")}`),
          },
        ],
        customStreet: _.get(ppHistoryContent, `customStreet`) ||
          _.get(stock, `street.street.${langKey(intl.locale, "name")}`) ||
          "---",
        customStreetNo: _.get(ppHistoryContent, `customStreetNo`) ||
          _.get(stock, "street.number") || "---",
        customDistrict: _.get(ppHistoryContent, `customDistrict`) ||
          _.get(stock, `district.${langKey(intl.locale, "name")}`) || "---",
        areas: areaItems,
        area: [
          _.find(ppHistoryContent?.areas, v => v.areaNameEn === "Total") ||
          {
            areaName: "總面積",
            areaNameEn: "Total",
            gross: _.get(stock, "area.total") || 0,
            grossIsShow: isResidential ? false : !!_.get(stock, "area.total"),
            grossVerified: false,
            net: netAreaTotal,
            netIsShow: false, // all net area default unselect
            netVerified: false,
          },
        ],
        possession: [
          {
            value: _.get(ppHistoryContent?.possession?.value, "nameEn") || _.get(stock, `possession.nameEn`, ""),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "possession.isShow"), !!_.get(stock, `possession.nameEn`)),
          },
        ],
        currentState: [
          {
            value: _.get(ppHistoryContent, "currentState.value.nameEn") || _.get(stock, `currentState.nameEn`, ""),
            isShow: _.get(ppHistoryContent, "currentState.isShow") || false,
          }
        ],
        managementFee: [
          {
            value: _.get(ppHistoryContent, "managementFee.value") || _.get(stock, "managementFee.number") || 0,
            unit: managementFeeUnit,
            paidBy: _.get(ppHistoryContent, "managementFee.paidBy") || _.get(stock, "managementFee.paidBy") || "",
            paidByIsShow: ppHistoryContent ? getNotNullIsShow(_.get(ppHistoryContent, "managementFee.paidByIsShow"), _.get(ppHistoryContent, `includedFee.managementFee`, false))
              : _.get(stock, "managementFee.paidBy", "") === "Paid By Landlord",
            isShow: ppHistoryContent ? getNotNullIsShow(_.get(ppHistoryContent, `managementFee.isShow`), false) : !!_.get(stock, "managementFee.number"),
            totalMonthlyMgtFee: _.get(ppHistoryContent, "managementFee.totalMonthlyMgtFee", Number.isInteger(totalMonthlyMgtFee) ? totalMonthlyMgtFee : Math.round(totalMonthlyMgtFee + 0.5)),
            totalMonthlyMgtFeeIsShow: ppHistoryContent ? getNotNullIsShow(_.get(ppHistoryContent, `managementFee.totalMonthlyMgtFeeIsShow`), false)
              : _.get(stock, "managementFee.totalMonthlyMgtFeeIsShow", false),
          },
        ],
        gRent: [
          {
            value: _.get(ppHistoryContent, "gRent.value") || _.get(stock, "governmentRent.number") || 0,
            paidBy: _.get(ppHistoryContent, "gRent.paidBy") || _.get(stock, "governmentRent.paidBy") || "",
            paidByIsShow: getNotNullIsShow(_.get(ppHistoryContent, "gRent.paidByIsShow"), false),
            unit: _.get(ppHistoryContent, "gRent.unit") || _.get(stock, "governmentRent.type") || "/Qtr",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "gRent.isShow"), !!_.get(stock, "governmentRent.number") || false),
          }
        ],
        rates: [
          {
            value: _.get(ppHistoryContent, "rates.value", _.get(stock, "rates.number") || 0),
            paidBy: _.get(ppHistoryContent, "rates.paidBy") || _.get(stock, "rates.paidBy") || "",
            paidByIsShow: _.get(ppHistoryContent, "rates.paidByIsShow", _.get(ppHistoryContent, `includedFee.rates`, _.get(stock, "rates.paidBy", "") === "Paid By Landlord")),
            unit: _.get(ppHistoryContent, "rates.unit", _.get(stock, "rates.type")) || "/Qtr",
            isShow: _.get(ppHistoryContent, "rates.isShow", !!_.get(stock, "rates.number")) || false,
          }
        ],
        acFee: [
          {
            value: _.get(ppHistoryContent, "acFee.value", _.get(stock, "airCond.fee") || 0),
            paidBy: _.get(ppHistoryContent, "acFee.paidBy") || _.get(stock, "airCond.feePaidBy") || "",
            paidByIsShow: getNotNullIsShow(_.get(ppHistoryContent, "acFee.paidByIsShow"),
              _.get(ppHistoryContent, `includedFee.acFee`, _.get(stock, "airCond.feePaidBy", "") === "Paid By Landlord")),
            unit: _.get(ppHistoryContent, "acFee.unit") || _.get(stock, "airCond.feeType") || "/Qtr",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "acFee.isShow"), !!_.get(stock, "airCond.fee", false)),
          }
        ],
        includedFee: [
          {
            managementFee: getNotNullIsShow(_.get(ppHistoryContent, "includedFee.managementFee"),
              _.get(stock, "managementFee.paidBy", "") === "Paid By Landlord"),
            rates: getNotNullIsShow(_.get(ppHistoryContent, "includedFee.rates"),
              _.get(stock, "rates.paidBy", "") === "Paid By Landlord"),
            gRent: getNotNullIsShow(_.get(ppHistoryContent, "includedFee.gRent"),
              _.get(stock, "governmentRent.paidBy", "") === "Paid By Landlord"),
            acFee: getNotNullIsShow(_.get(ppHistoryContent, "includedFee.acFee"),
              _.get(stock, "airConditioningFee.paidBy", "") === "Paid By Landlord"),
          },
        ],
        allInclusive: getNotNullIsShow(_.get(ppHistoryContent, "allInclusive"), _.every(
          [
            _.get(stock, "managementFee.paidBy", "") === "Paid By Landlord",
            _.get(stock, "rates.paidBy", "") === "Paid By Landlord",
            _.get(stock, "governmentRent.paidBy", "") === "Paid By Landlord",
          ],
          Boolean,
        )),
        unitDepth: [
          {
            ft: _.get(ppHistoryContent, "unitDepth.ft") || _.get(stock, "unitDepth.ft") || "",
            in: _.get(ppHistoryContent, "unitDepth.in") || _.get(stock, "unitDepth.in") || "",
            isShow: getNotNullIsShow(ppHistoryContent?.unitDepth?.isShow,
              _.get(stock, "unitDepth.ft", 0) > 0 ||
              _.get(stock, "unitDepth.in", 0) > 0),
          },
        ],
        entranceWidth: [
          {
            ft: _.get(ppHistoryContent, "entranceWidth.ft") || _.get(stock, "doorWidth.ft") || "",
            in: _.get(ppHistoryContent, "entranceWidth.in") || _.get(stock, "doorWidth.in") || "",
            isShow: getNotNullIsShow(ppHistoryContent?.entranceWidth?.isShow,
              _.get(stock, "doorWidth.ft", 0) > 0 ||
              _.get(stock, "doorWidth.in", 0) > 0),
          },
        ],
        ceilingHeight: [
          {
            ft: _.get(ppHistoryContent, "ceilingHeight.ft") || _.get(stock, "ceilingHeight.ft") || "",
            in: _.get(ppHistoryContent, "ceilingHeight.in") || _.get(stock, "ceilingHeight.in") || "",
            isShow: _.get(ppHistoryContent, "ceilingHeight.isShow") || false,
          },
        ],
        currentTenants: mergeTenants(
          parseTenancyRecords(_.get(stock, "tenancyRecords", []), intl, "Current"),
          ppHistoryContent?.currentTenants || []
        ),
        advanceTenants: mergeTenants(
          parseTenancyRecords(_.get(stock, "tenancyRecords", []), intl, "Advance"),
          ppHistoryContent?.advanceTenants || []
        ),
        previousTenants: mergeTenants(
          parseTenancyRecords(_.get(stock, "tenancyRecords", []), intl, "Previous"),
          ppHistoryContent?.previousTenants || []
        ),
        formerTenants: mergeTenants(
          parseTenancyRecords(_.get(stock, "tenancyRecords", []), intl, "Former"),
          ppHistoryContent?.formerTenants || []
        ),
        remarks: _.get(ppHistoryContent, "remarks") || "",
        yield: [
          {
            value: _.get(ppHistoryContent, "yield.value", _.get(stock, "yield") || 0),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "yield.isShow"), !!_.get(stock, "yield")),
          },
        ],
        floorInChinese: _.get(ppHistoryContent, "floorInChinese") || stock?.floorInChinese,
      },
      general: {
        type,
        multiImg: ppHistoryContent ? _.get(ppHistoryContent, "multiImg") : "ONE",
        showEmployeePhoto: [
          {
            value: intl.formatMessage({
              id: "proposal.form.showemployeephoto",
            }),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "showEmployeePhoto"), true),
          },
        ],
        showContact: [
          {
            value: intl.formatMessage({
              id: "proposal.form.showcontact",
            }),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "showContact"), true),
          },
        ],
        exactFloor: [
          {
            value: intl.formatMessage({
              id: "proposal.form.exactFloor",
            }),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "exactFloor"), true),
          },
        ],
        showUnit: [
          {
            value: intl.formatMessage({
              id: `proposal.form.showShopNo`,
            }),
            isShow: true,
          },
        ],
        showBuilding: [
          {
            value: intl.formatMessage({
              id: "proposal.form.buiding",
            }),
            isShow: false,
          },
        ],
        proposalName: _.get(ppHistoryContent, "proposalName") ||
          (intl.locale === "zh"
            ? `${_.get(stock, "street.street.nameZh", "")}${_.get(
              stock,
              "street.number",
              "",
            )}號`
            : `${_.get(stock, "street.number", "")} ${_.get(
              stock,
              "street.street.nameEn",
            )}`),
        lang: _.get(ppHistoryContent, "lang") || "CHI",
        companyTitle: _.get(ppHistoryContent, "companyTitle") || "midlandici",
        customTitle: {
          value: _.get(ppHistoryContent, "customTitle.value") || "",
          isShow: _.get(ppHistoryContent, "customTitle.isShow") || false,
        },
        termRemarks,
      },
      media: {
        stockMedia: !ppHistoryContent ? [] : _.map(_.get(ppHistoryContent, "photos", []), (p) => _.get(p, "id")) || []
          .concat(media?.photo || [], media?.video || [])
          ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
          ?.map((v) => v.id),
        buildingMedia: !ppHistoryContent ? [] : []
          .concat(buildingMedia?.photo || [], buildingMedia?.video || [])
          ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
          ?.map((v) => v.id),
        streetMedia: !ppHistoryContent ? [] : []
          .concat(streetMedia?.photo || [], streetMedia?.video || [])
          ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
          ?.map((v) => v.id),
        googleMapPhoto: ppHistoryContent && _.get(ppHistoryContent, "googleMap.isPP", false) ? ["map"] : [],
        selectedMedia: ppHistoryContent && _.get(ppHistoryContent, "googleMap.isPP", false) ? ["map"] : [],
        mainPhoto: ppHistoryContent ? _.get(ppHistoryContent, "mainPhoto.id") : _.get(mainPhoto, "id", "map"),
        main1Photo: _.get(ppHistoryContent, "main1Photo.id") || "",
        lat: _.get(ppHistoryContent, "lat") || _.get(stock, "coordinates.latitude") || _.get(stock, "building.coordinates.latitude"),
        lng: _.get(ppHistoryContent, "lng") || _.get(stock, "coordinates.longitude") || _.get(stock, "building.coordinates.longitude"),
        govLat: _.get(ppHistoryContent, "govLat") || _.get(stock, "coordinates.latitude") || _.get(stock, "building.coordinates.latitude"),
        govLng: _.get(ppHistoryContent, "govLng") || _.get(stock, "coordinates.longitude") || _.get(stock, "building.coordinates.longitude"),
        attachmentMultiImgConfig: _.reduce(_.get(ppHistoryContent, "photos", []), (result, photo) => {
          if (!photo?.id || (photo.id === "map" && !_.get(ppHistoryContent, "googleMap.isPP", false))) {
            return result;
          }
          result[photo.id] = photo.multiImg;
          return result;
        }, {}),
      },
    };
    return form;
  };

  // const formatMediaFormValue = (mediaValue) => {
  //   // handle media state...
  //   const stockMediaData = _.get(
  //     media.find((m) => m.id === _.get(stock, "unicorn.id").toString()),
  //     "data",
  //     {},
  //   );
  //   const buildingMediaData = _.get(
  //     buildingMedia.find(
  //       (m) => m.id === _.get(stock, "building.unicorn.id", "").toString(),
  //     ),
  //     "data",
  //     {},
  //   );
  //   const mainPhoto =
  //     stockMediaData?.photo?.find((m) => m.tags && m.tags.includes("main")) ||
  //     buildingMediaData?.photo?.find(
  //       (m) => m.tags && m.tags.includes("main"),
  //     ) ||
  //     null;

  //   return {
  //     googleMap: {
  //       isPP: !!(_.get(mediaValue, "googleMapPhoto", []).length > 0),
  //       isMain1: _.get(mediaValue, "main1Photo") === "map",
  //       isMain2: false,
  //     },
  //     photos: _.concat(
  //       [],
  //       _.map(_.get(mediaValue, "stockMedia", []), (id) =>
  //         parseMedia(stockMediaData.photo, id),
  //       ),
  //       _.map(_.get(mediaValue, "buildingMedia", []), (id) =>
  //         parseMedia(buildingMediaData.photo, id),
  //       ),
  //     ),
  //     main1Photo:
  //       _.get(mediaValue, "main1Photo") !== "map"
  //         ? parseMedia(
  //             _.concat([], stockMediaData.photo, buildingMediaData.photo),
  //             _.get(mediaValue, "main1Photo"),
  //           )
  //         : null,
  //     mainPhoto: mainPhoto
  //       ? _.pick(mainPhoto, ["id", "mediumRoot", "photoContent"])
  //       : null,
  //     videos: [],
  //     lat: mediaValue.lat,
  //     lng: mediaValue.lng,
  //   };
  // };

  // const handleSubmit = (values) => {
  //   const { stock: stockValue, media: mediaValue, general } = values;

  //   const stockVariables = {
  //     ...stockValue,
  //     ...general,
  //     stockMongoId: _.get(stock, "_id"),
  //     floor: _.get(stockValue, "floor[0]"),
  //     floorInChinese: _.get(stock, "floorInChinese"),
  //     unit: _.get(stockValue, "unit[0]"),
  //     avgPrice: {
  //       ..._.get(stockValue, "avgPrice[0]"),
  //       value: _.get(stockValue, "avgPrice[0].value", 0),
  //     },
  //     totalPrice: {
  //       ..._.get(stockValue, "totalPrice[0]"),
  //       value: _.get(stockValue, "totalPrice[0].value", 0),
  //     },
  //     avgRent: {
  //       ..._.get(stockValue, "avgRent[0]"),
  //       value: _.get(stockValue, "avgRent[0].value", 0),
  //     },
  //     totalRent: {
  //       ..._.get(stockValue, "totalRent[0]"),
  //       value: _.get(stockValue, "totalRent[0].value", 0),
  //     },
  //     suggestedAvgPrice: {
  //       ..._.get(stockValue, "suggestedAvgPrice[0]"),
  //       value: _.get(stockValue, "suggestedAvgPrice[0].value", 0),
  //     },
  //     suggestedTotalPrice: {
  //       ..._.get(stockValue, "suggestedTotalPrice[0]"),
  //       value: _.get(stockValue, "suggestedTotalPrice[0].value", 0),
  //     },
  //     suggestedAvgRent: {
  //       ..._.get(stockValue, "suggestedAvgRent[0]"),
  //       value: _.get(stockValue, "suggestedAvgRent[0].value", 0),
  //     },
  //     suggestedTotalRent: {
  //       ..._.get(stockValue, "suggestedTotalRent[0]"),
  //       value: _.get(stockValue, "suggestedTotalRent[0].value", 0),
  //     },
  //     customBuilding: _.get(stockValue, "customBuilding[0]"),
  //     customStreet:
  //       _.get(
  //         stock,
  //         `streets.${_.get(stockValue, "streetType") || 0}.street.nameEn`,
  //       ) || "",
  //     customStreetNo:
  //       _.get(
  //         stock,
  //         `streets.${_.get(stockValue, "streetType") || 0}.number`,
  //       ) || "",
  //     areas: _.concat(
  //       _.get(stockValue, "areas"),
  //       _.get(stockValue, "area"),
  //     ).map((v) => ({
  //       ...v,
  //       gross: v.gross || 0,
  //       net: v.net || 0,
  //     })),
  //     possession: {
  //       value: _.defaultTo(
  //         findZhEnFromOptions(
  //           stockPossessions,
  //           _.get(stockValue, "possession[0].value"),
  //         ),
  //         null,
  //       ),
  //       isShow: _.get(stockValue, "possession[0].isShow"),
  //     },
  //     managementFee: _.get(stockValue, "managementFee[0]"),
  //     entranceWidth: {
  //       isShow: _.get(stockValue, "entranceWidth[0].isShow") || false,
  //       ft: parseInt(_.get(stockValue, "entranceWidth[0].ft", 0)) || 0,
  //       in: parseInt(_.get(stockValue, "entranceWidth[0].in", 0)) || 0,
  //     },
  //     unitDepth: {
  //       isShow: _.get(stockValue, "unitDepth[0].isShow") || false,
  //       ft: parseInt(_.get(stockValue, "unitDepth[0].ft", 0), 10) || 0,
  //       in: parseInt(_.get(stockValue, "unitDepth[0].in", 0), 10) || 0,
  //     },
  //     ceilingHeight: {
  //       isShow: _.get(stockValue, "ceilingHeight[0].isShow") || false,
  //       ft: parseInt(_.get(stockValue, "ceilingHeight[0].ft", 0), 10) || 0,
  //       in: parseInt(_.get(stockValue, "ceilingHeight[0].in", 0), 10) || 0,
  //     },
  //     currentTenants: _.map(_.get(stockValue, "currentTenants", []), (v) => ({
  //       tenant: {
  //         ...(_.get(v, "tenant") || {}),
  //         isShow: v.tenantIsShow,
  //       },
  //       rentalFee: {
  //         value: v.rentalFee || 0,
  //         isShow: v.rentalFeeIsShow,
  //       },
  //       period: {
  //         ...(_.get(v, "period") || {}),
  //         isShow: v.periodIsShow,
  //       },
  //       tenancy: {
  //         ...(_.get(v, "tenancy") || {}),
  //         isShow: v.tenancyIsShow,
  //       },
  //     })),
  //     yield: _.get(stockValue, "yield[0]"),
  //     hideEmployeePhoto: _.get(general, "hideEmployeePhoto[0].isShow"),
  //     hideContact: _.get(general, "hideContact[0].isShow"),
  //     districtNameZh: _.get(stock, "district.nameZh", ""),
  //     districtNameEn: _.get(stock, "district.nameEn", ""),
  //     streetNameZh: _.get(
  //       stock,
  //       `streets.${_.get(stockValue, "streetType") || 0}.street.nameZh`,
  //       "",
  //     ),
  //     streetNameEn: _.get(
  //       stock,
  //       `streets.${_.get(stockValue, "streetType") || 0}.street.nameEn`,
  //       "",
  //     ),
  //     streetNo: _.get(
  //       stock,
  //       `streets.${_.get(stockValue, "streetType") || 0}.number`,
  //       "",
  //     ),
  //     buildingNameZh: _.get(stock, "building.nameZh", ""),
  //     buildingNameEn: _.get(stock, "building.nameEn", ""),
  //     buildingDistrictNameZh: _.get(stock, "building.district.nameZh", ""),
  //     buildingDistrictNameEn: _.get(stock, "building.district.nameEn", ""),
  //     isSoleagent: !!(
  //       stock.soleagent &&
  //       stock.soleagent.periodStart != null &&
  //       stock.soleagent.periodEnd != null
  //     ),
  //     sbu,
  //   };

  //   delete stockVariables.streetType;
  //   delete stockVariables.area;

  //   const mediaVariables = formatMediaFormValue(mediaValue);

  //   createProposal({ ...stockVariables, ...mediaVariables });
  // };

  return (
    <ProposalForm
      onSubmit={createProposal}
      stockId={_.get(stock, "unicorn.id")}
      buildingId={_.get(stock, "building.unicorn.id")}
      streetId={_.get(stock, "street.street.unicorn.id")}
      initialValues={initializeFormState()}
      FormComponent={FieldSection}
      createDialogOpen={createDialogOpen}
      closeDialog={closeDialog}
    />
  );
}

Individual.propTypes = {
  detail: PropTypes.array.isRequired,
  currentStock: PropTypes.string.isRequired,
  stockPossessions: PropTypes.array.isRequired,
  stockCurrentStates: PropTypes.array.isRequired,
  media: PropTypes.array.isRequired,
  buildingMedia: PropTypes.array.isRequired,
  streetMedia: PropTypes.array.isRequired,
  createDialogOpen: PropTypes.bool.isRequired,
  closeDialog: PropTypes.func.isRequired,
  createProposal: PropTypes.func.isRequired,
  clearCreateProposal: PropTypes.func.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  detail: _.get(state, "stock.detail") || [],
  currentStock: _.get(state, "stock.currentDetail") || "",
  stockPossessions: _.get(state, "stock.possessions", []),
  stockCurrentStates: _.get(state, "stock.currentStates", []),
  media: _.get(state, "stock.media", []),
  buildingMedia: _.get(state, "building.media", []),
  streetMedia: _.get(state, "street.media", []),
  reCreatePPStocks: _.get(state, "proposal.reCreatePPStocks", []),
});

const mapDispatchToProps = (dispatch) => ({
  createProposal: () => dispatch(createProposal()),
  clearCreateProposal: () => dispatch(clearCreateProposal()),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(Individual));
