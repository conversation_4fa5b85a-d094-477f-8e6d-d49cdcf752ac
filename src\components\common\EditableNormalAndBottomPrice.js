import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import { convertCurrency, numberComma } from "../../helper/generalHelper";
import TotalAndAvgPrice from "./TotalAndAvgPrice";
import { injectIntl, FormattedMessage } from "react-intl";
import { Field, FieldArray } from "redux-form";
import InlineTextInput from "./InlineTextInput";
import { maxValue, minValue, number } from "../../core/formValidators";
import InputWithCheckBox from "./InputWithCheckBox";
import _ from "lodash";

function createMinvalue(min) {
  return minValue(min, " "); // set msg to a space to suppress the error message
}
function createMaxvalue(max) {
  return maxValue(max, " "); // set msg to a space to suppress the error message
}

const numberValidate = number(" "); // set msg to a space to suppress the error message

const minvaluezero = createMinvalue(0);
const maxvalue1 = createMaxvalue(200);
const maxvalue2 = createMaxvalue(999);
const maxvalue3 = createMaxvalue(9999);
const maxvalue4 = createMaxvalue(99999);
const maxvalue5 = createMaxvalue(9999999);
const maxvalue6 = createMaxvalue(99999999);
const maxvalue7 = createMaxvalue(999999999);
const maxvalue8 = createMaxvalue(9999999999);

// We can inject some CSS into the DOM.
const styles = {
  main: {
    lineHeight: 1.5,
    fontSize: "1.4em",
  },
  bottom: {
    lineHeight: 1.3,
    fontSize: "1em",
  },
  input: {
    width: "calc(100% - 6px)",
    paddingLeft: 3,
    paddingRight: 3,
    color: "#FFF",
  },
  askingField: {
    backgroundColor: "rgba(255, 255, 255, .25)",
    borderRadius: 4,
  },
  checkbox: {
    padding: "5px 0",
  },
  askingRow: {
    padding: "3px 0",
  },
};

function twoDec(n) {
  return Math.round((n + Number.EPSILON) * 100) / 100;
}

function EditableNormalAndBottomPrice(props) {
  const {
    classes,
    label,
    avgDecimal,
    intl,
    changeFieldValue,
    fieldNamePrefix,
    formFields,
    disabled,
    updateYield,
    suggested,
    autoCal = true,
    ...other
  } = props;

  let areaGross = 0;
  const getArea = (formFields) => {
    if (formFields.areaGross?.[0]?.isShow) {
      return parseFloat(formFields.areaGross[0].value || 0);
    }
    if (formFields.areaLettable?.[0]?.isShow) {
      return parseFloat(formFields.areaLettable[0].value || 0); 
    }
    if (formFields.areaNet?.[0]?.isShow) {
      return parseFloat(formFields.areaNet[0].value || 0);
    }
    if (formFields.areaSaleable?.[0]?.isShow) {
      return parseFloat(formFields.areaSaleable[0].value || 0);
    }
    return 0;
  };
  const area = getArea(formFields);
  if (area) {
    // COMM, IND
    areaGross = area;
  } else {
    //SHOPS
    const areas = formFields.areas || [];
    let first100 = true;
    areas.forEach((v) => {
      // "地下", "地舖" always 100%
      if (v.areaName === "地下" || v.areaName === "地舖") {
        areaGross += v.gross || 0;
        first100 = false;
      }
    });

    areas.forEach((v, i) => {
      // "xx閣" except "入則閣" always 0%
      if (v.areaName && v.areaName.endsWith("閣") && v.areaName !== "入則閣")
        return;
      // if no "地下", "地舖", the first item will be 100%
      if (first100 && i === 0) {
        areaGross += v.gross || 0;
        return;
      }
      // "地下", "地舖" has already been added
      if (v.areaName === "地下" || v.areaName === "地舖") return;
      // others 33.33%
      areaGross += v.gross ? v.gross / 3 : 0;
    });
  }

  const calculateYield = (price) => {
    if (!updateYield || !formFields.yield || !formFields.yield[0]) return;
    const currentRent =
      formFields.currentTenants &&
      formFields.currentTenants.reduce(
        (prev, curr) => prev + (curr.rentalFee || 0),
        0,
      );
    let y = "";
    if (currentRent && !isNaN(parseFloat(price)) && parseFloat(price) !== 0)
      // @ts-ignore
      y = twoDec(((currentRent * 12) / price) * 100);
    changeFieldValue(`${fieldNamePrefix}stock.yield[0]`, { ...formFields.yield[0], value: y });
  };

  const inputToDisplayAvg = (v) =>
    v && parseFloat(v) !== 0 ? numberComma(v, avgDecimal || 0) : "---";
  const calculateTotal = (value) => {
    if (!autoCal || isNaN(areaGross) || areaGross === 0) return;
    changeFieldValue(`${fieldNamePrefix}stock.total${label}[0]`, {
      ...formFields[`total${label}`][0],
      value: twoDec(value * areaGross),
    });
    calculateYield(value * areaGross);
  };
  const avg = (
    <FieldArray
      className={classes.askingField}
      customInputProps={{ className: classes.input }}
      name={`${fieldNamePrefix}stock.avg${label}`}
      component={InputWithCheckBox}
      renderComponent={InlineTextInput}
      changeFieldValue={changeFieldValue}
      type="number"
      inputToDisplay={inputToDisplayAvg}
      checkboxProps={{
        className: classes.checkbox,
        disabled: disabled,
      }}
      checkboxInFront={true}
      checkboxXs={3}
      disabled={disabled}
      noBottomBorder
      extraHandleChange={calculateTotal}
      label={label}
    />
  );

  const inputToDisplayTotal = (v) =>
    v && parseFloat(v) !== 0 ? numberComma(v, 3) + " " : "---";
  const calculateAvg = (value) => {
    if (!autoCal || isNaN(areaGross) || areaGross === 0) return;
    changeFieldValue(`${fieldNamePrefix}stock.avg${label}[0]`, {
      ...formFields[`avg${label}`][0],
      value: twoDec(value / areaGross),
    });
    calculateYield(value);
  };
  const total = (
    <FieldArray
      className={classes.askingField}
      customInputProps={{ className: classes.input }}
      name={`${fieldNamePrefix}stock.total${label}`}
      component={InputWithCheckBox}
      renderComponent={InlineTextInput}
      changeFieldValue={changeFieldValue}
      type="number"
      inputToDisplay={inputToDisplayTotal}
      checkboxProps={{
        className: classes.checkbox,
        disabled: disabled,
      }}
      checkboxInFront={true}
      checkboxXs={3}
      disabled={disabled}
      noBottomBorder
      extraHandleChange={calculateAvg}
      label={label}
    />
  );
  const trend = null;

  let generalLabel;
  if (label == "Rent") {
    generalLabel = intl.formatMessage({ id: "search.common.rent" });
  } else if (label == "Price") {
    generalLabel = intl.formatMessage({ id: "search.common.price" });
  }

  const calculateBottomTotal = (value) => {
    if (!autoCal || isNaN(areaGross) || areaGross === 0) return;
    changeFieldValue(`${fieldNamePrefix}stock.bottomTotal${label}[0]`, {
      ...formFields[`bottomTotal${label}`][0],
      value: twoDec(value * areaGross),
    });
  };
  let bottomAvg = (
    <FieldArray
      className={classes.askingField}
      customInputProps={{ className: classes.input }}
      name={`${fieldNamePrefix}stock.bottomAvg${label}`}
      component={InputWithCheckBox}
      renderComponent={InlineTextInput}
      changeFieldValue={changeFieldValue}
      type="number"
      inputToDisplay={inputToDisplayAvg}
      checkboxProps={{
        className: classes.checkbox,
        disabled: disabled,
      }}
      checkboxInFront={true}
      checkboxXs={3}
      disabled={disabled}
      noBottomBorder
      extraHandleChange={calculateBottomTotal}
      label={label}
    />
  );

  const calculateSuggestedTotal = (value) => {
    if (!autoCal || isNaN(areaGross) || areaGross === 0) return;
    changeFieldValue(`${fieldNamePrefix}stock.suggestedTotal${label}[0]`, {
      ...formFields[`suggestedTotal${label}`][0],
      value: twoDec(value * areaGross),
    });
  };
  if (suggested) {
    bottomAvg = (
      <FieldArray
        className={classes.askingField}
        customInputProps={{ className: classes.input }}
        name={`${fieldNamePrefix}stock.suggestedAvg${label}`}
        component={InputWithCheckBox}
        renderComponent={InlineTextInput}
        changeFieldValue={changeFieldValue}
        type="number"
        inputToDisplay={inputToDisplayAvg}
        checkboxProps={{
          className: classes.checkbox,
          disabled: disabled,
        }}
        checkboxInFront={true}
        checkboxXs={3}
        disabled={disabled}
        noBottomBorder
        extraHandleChange={calculateSuggestedTotal}
        label={label}
      />
    );
  }

  const calculateBottomAvg = (value) => {
    if (!autoCal || isNaN(areaGross) || areaGross === 0) return;
    changeFieldValue(`${fieldNamePrefix}stock.bottomAvg${label}[0]`, {
      ...formFields[`bottomAvg${label}`][0],
      value: twoDec(value / areaGross),
    });
  };
  let bottomTotal = (
    <FieldArray
      className={classes.askingField}
      customInputProps={{ className: classes.input }}
      name={`${fieldNamePrefix}stock.bottomTotal${label}`}
      component={InputWithCheckBox}
      renderComponent={InlineTextInput}
      changeFieldValue={changeFieldValue}
      type="number"
      inputToDisplay={inputToDisplayTotal}
      checkboxProps={{
        className: classes.checkbox,
        disabled: disabled,
      }}
      checkboxInFront
      checkboxXs={3}
      disabled={disabled}
      noBottomBorder
      extraHandleChange={calculateBottomAvg}
      label={label}
    />
  );

  const calculateSuggestedAvg = (value) => {
    if (!autoCal || isNaN(areaGross) || areaGross === 0) return;
    changeFieldValue(`${fieldNamePrefix}stock.suggestedAvg${label}[0]`, {
      ...formFields[`suggestedAvg${label}`][0],
      value: twoDec(value / areaGross),
    });
  };
  if (suggested) {
    bottomTotal = (
      <FieldArray
        className={classes.askingField}
        customInputProps={{ className: classes.input }}
        name={`${fieldNamePrefix}stock.suggestedTotal${label}`}
        component={InputWithCheckBox}
        renderComponent={InlineTextInput}
        changeFieldValue={changeFieldValue}
        type="number"
        inputToDisplay={inputToDisplayTotal}
        checkboxProps={{
          className: classes.checkbox,
          disabled: disabled,
        }}
        checkboxInFront
        checkboxXs={3}
        disabled={disabled}
        noBottomBorder
        extraHandleChange={calculateSuggestedAvg}
        label={label}
      />
    );
  }

  const bottomLabel = suggested
    ? intl.formatMessage({ id: "proposal.form.suggested" })
    : intl.formatMessage({ id: "stock.bottom" });

  return (
    <>
      <div className={classes.askingRow}>
        <TotalAndAvgPrice
          avg={avg}
          total={total}
          trend={trend}
          label={generalLabel}
          className={classes.main}
        />
      </div>
      <TotalAndAvgPrice
        avg={bottomAvg}
        total={bottomTotal}
        label={bottomLabel}
        className={classes.bottom}
      />
    </>
  );
}

EditableNormalAndBottomPrice.propTypes = {
  classes: PropTypes.object.isRequired,
  fieldNamePrefix: PropTypes.string,
  label: PropTypes.string,
  avgDecimal: PropTypes.number,
  disabled: PropTypes.bool,
  updateYield: PropTypes.bool,
  suggested: PropTypes.bool,
  autoCal: PropTypes.bool,
};

export default withStyles(styles)(injectIntl(EditableNormalAndBottomPrice));
