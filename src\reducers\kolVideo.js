import { Select } from "@material-ui/core";
import {
  LIST_TEAMS_KOL_VIDEOS_START,
  LIST_MORE_TEAMS_KOL_VIDEOS_START,
  LIST_TEAMS_KOL_VIDEOS_SUCCESS,
  LIST_TEAMS_KOL_VIDEOS_NULL_SUCCESS,
  LIST_TEAMS_KOL_VIDEOS_ERROR,
  CLEAR_TEAMS_KOL_VIDEOS,
} from "../constants/kolVideo";

const initialState = {
  listed: false,
  listing: false,
  kolVideos: [],
};

export default function kolVideo(state = initialState, action) {
  switch (action.type) {
    case LIST_TEAMS_KOL_VIDEOS_START:
      return {
        ...state,
        listed: false,
        listing: true,
        kolVideos: [],
      };
    case LIST_MORE_TEAMS_KOL_VIDEOS_START:
      return {
        ...state,
        listing: true,
      };
    case LIST_TEAMS_KOL_VIDEOS_SUCCESS:
      return {
        ...state,
        listed: true,
        listing: false,
        //kolVideos: state.kolVideos.concat(action.payload.kolVideos.media || [])
        kolVideos: action.payload.kolVideos.media || []
      };
    case LIST_TEAMS_KOL_VIDEOS_NULL_SUCCESS:
      return {
        ...state,
        listing: false,
        listed: true,
        kolVideos: []
      };
    case LIST_TEAMS_KOL_VIDEOS_ERROR:
      return {
        ...state,
        listed: false,
        listing: false,
        error: action.payload.error
      };
    case CLEAR_TEAMS_KOL_VIDEOS:
      return {
        ...state,
        listed: false,
        listing: false,
        kolVideos: [],
        error: null
      };
    default:
      return state;
  }
}
