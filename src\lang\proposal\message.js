export default {
  zh: {
    "proposal.proposals.list": "建議書(:proposalsCount個結果)",
    "proposal.pageHeader": "單盤建議書",
    "proposal.proposal": "建議書",
    "proposal.indvProposal": "單盤建議書",
    "proposal.listProposal": "多盤建議書",
    "proposal.listProposal.list": "列表格式",
    "proposal.listProposal.indv": "單盤格式",
    "proposal.form.stockinfo": "樓盤資料",
    "proposal.form.otherinfo": "其他資料",
    "proposal.form.hideemployeephoto": "隱藏員工相",
    "proposal.form.hidecontact": "隱藏聯絡資料",
    "proposal.form.hideMainPhoto": "隱藏主相",
    "proposal.form.hideTenancy": "隱藏租客資料",
    "proposal.form.customtitle": "自訂標題",
    "proposal.form.type": "類別",
    "proposal.form.forsale": "出售",
    "proposal.form.forlease": "出租",
    "proposal.form.salesandlease": "租及售",
    "proposal.form.streettype": "街道類型",
    "proposal.form.mainstreet": "主街",
    "proposal.form.substreet": "副街 {value}",
    "proposal.form.streetNo": "街號",
    "proposal.form.floortype": "層數類型",
    "proposal.form.actualfloor": "真實樓層",
    "proposal.form.veryhighfloor": "極高層",
    "proposal.form.highfloor": "高層",
    "proposal.form.midhigherfloor": "中高層",
    "proposal.form.midfloor": "中層",
    "proposal.form.midlowerfloor": "中低層",
    "proposal.form.lowfloor": "低層",
    "proposal.form.unit": "室",
    "proposal.form.net": "實",
    "proposal.form.gross": "建",
    "proposal.form.availabilitydate": "交吉日期",
    "proposal.form.completiondate": "落成年份",
    "proposal.form.buildingusage": "大廈用途",
    "proposal.form.title": "業權",
    "proposal.form.mgtFee": "管理費",
    "proposal.form.transport": "交通",
    "proposal.form.ceilingHeight": "樓高(呎/吋)",
    "proposal.form.entranceWidth": "門闊(呎/吋)",
    "proposal.form.unitDepth": "舖深(呎/吋)",
    "proposal.form.suggested": "建議",
    "proposal.form.roof": "天台(呎)",
    "proposal.form.terrace": "平台(呎)",
    "proposal.form.cockloft": "閣樓(呎)",
    "proposal.form.floorloading": "樓面負重(磅)",
    "proposal.form.perviewPDFloading": "PDF加载中…",
    "proposal.form.reset": "重設",
    "proposal.form.save": "儲存",
    "proposal.form.confirmsave": "儲存建議書？",
    "proposal.form.proposalname": "建議書名稱",
    "proposal.form.savesuccess": "成功儲存建議書 {filename}",
    "proposal.form.validation": `建議書名稱不能包含空白或以下任何字符: \ / ： * ？" < > | % & #`,
    "proposal.form.chineseremarks": "中文備註",
    "proposal.form.englishremarks": "英文備註",
    "proposal.form.tenant": "租客",
    "proposal.form.rent": "租金",
    "proposal.form.tenancy": "租約",
    "proposal.form.tenancyRemarks": "租約備註",
    "proposal.form.tenancyperiod": "租約期",
    "proposal.form.media.other": "其他",
    "proposal.form.media.more": "更多",
    "proposal.form.companyTitle": "公司名稱",
    "proposal.form.midlandici": "美聯工商舖",
    "proposal.form.hkp": "港置工商舖",
    "proposal.form.inclusive": "包",
    "proposal.form.allInclusive": "全包",
    "proposal.form.mainPhoto": "主相",
    "proposal.form.yield": "回報率(%)",
    "proposal.preview": "預覽",
    "proposal.createPreview.error": "未能預覽建議書",
    "proposal.createpdf": "創建PDF",
    "proposal.createpdf.error": "未能創建議書",
    "proposal.removepdf": "確定移除建議書?",
    "proposal.removeallpdf": "確定移除所有建議書?",
    "proposal.removepdf.success": "成功移除建議書",
    "proposal.list.count": "建議書結果",
    "proposal.create.exceed": "已創建超過 {quota} 份建議書，請先移除建議書",
    "proposal.create.mediaNotReady":
      "多媒體資源尚未完成加載，樓盤建議書將使用地圖為主相，是否繼續創建？",
    "proposal.list.proposalType": "建議書類別",
    "proposal.list.dateAndTime": "日期時間",
    "proposal.list.proposalName": "名稱",
    "proposal.list.individual": "單盤",
    "proposal.list.list": "多盤",
    "proposal.list.recreate": "重新建立",
    "proposal.list.download": "下載",
    "proposal.general.language": "顯示語言",
    "proposal.general.chinese": "繁中",
    "proposal.general.schinese": "簡中",
    "proposal.general.english": "英文",
    "proposal.general.chiAndEng": "繁 + 英",
    "proposal.general.schiAndEng": "簡 + 英",
    "proposal.stock.remarks": "樓盤備註",
    "proposal.chi.remarks": "備註(中文)",
    "proposal.eng.remarks": "備註(英文)",

    "listProposal.listProposal": "多盤建議書",
    "listProposal.pageHeader": "多盤建議書(:mode)",
    "listProposal.table.address": "地址",
    "listProposal.table.grossArea": "建築面積",
    "listProposal.table.netArea": "實用面積",
    "listProposal.table.possession": "交易狀況",
    "listProposal.table.price": "售價",
    "listProposal.table.avgPrice": "呎價",
    "listProposal.table.rent": "租金",
    "listProposal.table.avgRent": "呎租",
    "listProposal.table.ordering": "排序",
    "listProposal.table.finish": "完成",
  },
  en: {
    "proposal.proposals.list": "Proposal (:proposalsCount proposals found)",
    "proposal.pageHeader": "Individual Proposal",
    "proposal.proposal": "Proposal",
    "proposal.indvProposal": "Individual PP",
    "proposal.listProposal": "List PP",
    "proposal.listProposal.list": "List format",
    "proposal.listProposal.indv": "Individual format",
    "proposal.form.stockinfo": "Stock Info",
    "proposal.form.otherinfo": "Other Info",
    "proposal.form.hideemployeephoto": "Hide employee photo",
    "proposal.form.hidecontact": "Hide contact",
    "proposal.form.hideMainPhoto": "Hide main photo",
    "proposal.form.hideTenancy": "Hide tenancy",
    "proposal.form.customtitle": "Custom Title",
    "proposal.form.type": "Type",
    "proposal.form.forsale": "For Sale",
    "proposal.form.forlease": "For Lease",
    "proposal.form.salesandlease": "Sale+Lease",
    "proposal.form.streettype": "Street Type",
    "proposal.form.mainstreet": "Main Street",
    "proposal.form.substreet": "Side Street",
    "proposal.form.streetNo": "Street Number",
    "proposal.form.floortype": "Floor Type",
    "proposal.form.actualfloor": "Actual Floor",
    "proposal.form.veryhighfloor": "Very High Floor",
    "proposal.form.highfloor": "High Floor",
    "proposal.form.midhigherfloor": "Mid-Higher Floor",
    "proposal.form.midfloor": "Mid Floor",
    "proposal.form.midlowerfloor": "Mid-Lower Floor",
    "proposal.form.lowfloor": "Low Floor",
    "proposal.form.unit": "Unit",
    "proposal.form.net": "Net",
    "proposal.form.gross": "Gross",
    "proposal.form.suggested": "Suggested",
    "proposal.form.availabilitydate": "Availability",
    "proposal.form.completiondate": "Completion Date",
    "proposal.form.buildingusage": "Building Usage",
    "proposal.form.ceilingHeight": "Ceiling Height(ft/in)",
    "proposal.form.entranceWidth": "Entrance Width(ft/in)",
    "proposal.form.unitDepth": "Unit Depth(ft/in)",
    "proposal.form.roof": "Roof(ft)",
    "proposal.form.terrace": "Terrace(ft)",
    "proposal.form.cockloft": "Cockloft(ft)",
    "proposal.form.floorloading": "Floor Loading(lb)",
    "proposal.form.perviewPDFloading": "Loading PDF…",
    "proposal.form.title": "Title",
    "proposal.form.transport": "Transport",
    "proposal.form.reset": "Reset",
    "proposal.form.save": "Save",
    "proposal.form.confirmsave": "Save Proposal?",
    "proposal.form.proposalname": "Proposal Name",
    "proposal.form.savesuccess":
      "Proposal {filename} has been saved successfully.",
    "proposal.form.validation": `Proposal Name cannot contain whitespace or any of the following characters: \ / : * ? “ < > | % & #`,
    "proposal.form.chineseremarks": "Chinese Remarks",
    "proposal.form.englishremarks": "English Remarks",
    "proposal.form.tenant": "Tenant",
    "proposal.form.rent": "Rent",
    "proposal.form.tenancy": "Tenancy",
    "proposal.form.tenancyRemarks": "Tenancy Remarks",
    "proposal.form.tenancyperiod": "Tenancy Period",
    "proposal.form.media.other": "Other ",
    "proposal.form.media.more": "More",
    "proposal.form.companyTitle": "Company Title",
    "proposal.form.midlandici": "Midland ICI",
    "proposal.form.hkp": "HKP ICI",
    "proposal.form.inclusive": "Inclusive",
    "proposal.form.allInclusive": "All Included",
    "proposal.form.mainPhoto": "Main",
    "proposal.form.yield": "Yield(%)",
    "proposal.form.mgtFee": "Mgt Fee",
    "proposal.preview": "Preview",
    "proposal.createPreview.error": "Failed to preview proposal.",
    "proposal.createpdf": "Create PDF",
    "proposal.createpdf.error": "Failed to create proposal.",
    "proposal.create.mediaNotReady":
      "Stock media is not ready yet, and therefore map will be used as main photo for all stocks in the proposal. Are you sure to proceed?",
    "proposal.removepdf": "Confirm to remove proposal?",
    "proposal.removepdfall": "Confirm to remove all proposal?",
    "proposal.removepdf.success": "Proposal has been removed successfully.",
    "proposal.list.count": "{resultStr} found",
    "proposal.create.exceed":
      "No. of proposals should not exceed {quota}. Please remove created proposal first.",
    "proposal.list.proposalType": "Proposal Type",
    "proposal.list.dateAndTime": "Date and Time",
    "proposal.list.proposalName": "Proposal Name",
    "proposal.list.individual": "Individual",
    "proposal.list.list": "List",
    "proposal.list.recreate": "Re-create",
    "proposal.list.download": "Download",
    "proposal.general.language": "Display Language",
    "proposal.general.chinese": "Trad. Chi",
    "proposal.general.schinese": "Simp. Chi.",
    "proposal.general.english": "Eng.",
    "proposal.general.chiAndEng": "Trad. Chi. + Eng.",
    "proposal.general.schiAndEng": "Simp. Chi + Eng.",
    "proposal.stock.remarks": "Property Remarks",
    "proposal.chi.remarks": "Remarks (Chi)",
    "proposal.eng.remarks": "Remarks (Eng)",

    "listProposal.listProposal": "List Proposal",
    "listProposal.pageHeader": "List Proposal(:mode)",
    "listProposal.table.address": "Address",
    "listProposal.table.grossArea": "Area(G)",
    "listProposal.table.netArea": "Net Area",
    "listProposal.table.possession": "Possession",
    "listProposal.table.price": "Price",
    "listProposal.table.avgPrice": "Unit Price",
    "listProposal.table.rent": "Rent",
    "listProposal.table.avgRent": "Unit Rent",
    "listProposal.table.ordering": "Ordering",
    "listProposal.table.finish": "Finish",
  },
};
