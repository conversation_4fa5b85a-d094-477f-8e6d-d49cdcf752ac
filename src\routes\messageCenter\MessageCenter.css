/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

@import "../../components/variables.css";

.root {
  /*padding-left: 20px;*/
  /*padding-right: 20px;*/
}

.container {
  margin: 0 auto;
  padding: 0 0 40px;
  max-width: var(--max-content-width);
}

.paper {
  padding: 16px;
  text-align: center;
}

.div {
  /* display: flex;
  flex-direction: row wrap;
  width: 100%; */
}

.paperLeft {
  flex: 1;
  height: 100%;
  margin: 10px;
  text-align: center;
  padding: 10px;
}

.paperRight {
  height: 600px;
  flex: 4;
  margin: 10px;
  text-align: center;
}

.bar {
  boxShadow: "none";
  backgroundColor: "rgba(0, 0, 0, .6)";
  position: "sticky";
  top: 52px;
  left: 0;
  zIndex: 900;
}
