import React from "react";
import { withStyles } from "@material-ui/core/styles";
import Paper from "@material-ui/core/Paper";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    padding: 6,
    marginBottom: "1vh",
  },
};

function ProposalFormPaper(props) {
  const { classes, className, children, ...other } = props;

  return (
    <Paper className={`${classes.root} ${className}`} elevation={0} {...other}>
      {children}
    </Paper>
  );
}

export default withStyles(styles)(ProposalFormPaper);
