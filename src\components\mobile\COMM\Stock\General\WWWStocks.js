import React, {useCallback, useEffect, useMemo, useState} from "react";
import {connect} from "react-redux";
import {withStyles} from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import {FormattedMessage, injectIntl} from "react-intl";
import FieldVal from "@/components/common/FieldVal";
import _, {get} from "lodash";
import {fetchCurrentWWWStock, fetchWWWStockDesc, updateWWWStock, updateWWWStockDesc} from "@/actions/wwwStock";
import WWWStockTag from "@/components/common/WWWStockTag";
import moment from "moment";
import {numberComma} from "@/helper/generalHelper";
import {Field, reduxForm} from "redux-form";
import {Edit} from "@material-ui/icons";
import BottomButtons from "@/components/common/BottomButtons";
import DatePicker from "@/components/common/DatePicker";
import TextInput from "@/components/common/TextInput";
import CircularProgress from "@material-ui/core/CircularProgress";
import {Backdrop, Snackbar} from "@material-ui/core";
import {PERMISSIONS} from "@/constants/auth";

const styles = (theme) => ({
  root: {
    padding: "1vh 0",
  },
  fieldContainer: {
    padding: 8,
    "& .MuiTextField-root .MuiFormHelperText-root.Mui-error": {
      marginTop: 0,
      color: "#f44336 !important",
    },
  },
  generalProp: {
    padding: "2vh 0",
  },
  gridContent: {
    padding: "1vw 2vw"
  },
  lmrAlign: {
    paddingLeft: "2vw",
  },
  card: {
    padding: 0,
    backgroundColor: "transparent",
  },
  moreButton: {
    textAlign: "center",
    width: "100%",
  },
  buttonText: {
    textTransform: "none",
  },
  noData: {
    fontSize: "1.125em",
    paddingLeft: "2vw",
  },
  agentCard: {
    width: "100vw",
    padding: "1vw 2vw",
    borderRadius: "4px",
    backgroundColor: "rgba(132, 132, 132, .1)",
    "&:not(:last-child)": {
      marginBottom: "1vh"
    }
  },
  cancelButton: {
    backgroundColor: "red",
  },
  backdrop: {
    zIndex: 99,
    color: '#fff',
  },
});


function WWWStocks(props) {
  const {
    intl,
    classes,
    dispatch,
    uid,
    permissions,
    stockId,
    wwwStock,
    wwwStockDesc,
    getCurrentWWWStock, // Fun
    getWWWStockDesc,  // Fun
    setWWWStockDesc, // Fun
    setWWWStock, // Fun

    wwwStockUpdating,
    wwwStockUpdated,
    wwwStockDescUpdating,
    wwwStockDescUpdated,
    wwwStockUpdateError,

    // Redux-form props
    initialize,
    handleSubmit,
    reset,
    invalid,
    submitting,

  } = props;

  const [isExpanding, setIsExpanding] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  // console.log("wwwStock:", wwwStock, wwwStockDesc)
  const agents = get(wwwStock, 'agents', []);
  const mediaCutOffDate = get(wwwStock, 'mediaCutoffDate', '');
  const propertyScore = get(wwwStock, 'wwwScoreTotal', '---');
  const stockBasicInfo = get(wwwStock, 'stockBasicInfo', {});
  const wwwChannelFull = get(wwwStock, 'wwwChannelFull', {});
  const sumAgentScore = _.sumBy(wwwStock.agents, 'scoreTotal');
  const stockType = get(stockBasicInfo, intl.locale === 'zh' ? 'stockTypeZh' : 'stockType', '---');
  const searchArea = get(stockBasicInfo, 'searchArea', '');
  const featuresChi = get(wwwStockDesc, '0.www_desc_chi', '');
  const featuresEng = get(wwwStockDesc, '0.www_desc_eng', '');
  const {ALLOW_TO_EDIT_START_DATE} = PERMISSIONS;

  const formatMediaCutOffDate = (mediaCutOffDate, format = 'DD/MM/YYYY') => {
    return mediaCutOffDate ? moment(+mediaCutOffDate).format(format) : "---";
  }

  useEffect(() => {
    if (stockId) {
      Promise.all([
        getCurrentWWWStock(stockId, {intl}),
        getWWWStockDesc({stockId, empId: uid}, {intl})
      ]);
    }
  }, [stockId])

  useEffect(() => {
    if (!editMode) { // 仅在非编辑模式下初始化
      initialize({
        mediaCutOffDate: formatMediaCutOffDate(mediaCutOffDate, 'YYYY/MM/DD'),
        featuresChi,
        featuresEng
      });
    }
  }, [featuresChi, featuresEng, mediaCutOffDate, editMode]);

  useEffect(() => {
    // reload data
    if (wwwStockUpdated || wwwStockDescUpdated) {
      getCurrentWWWStock(stockId, {intl});
      getWWWStockDesc({stockId, empId: uid}, {intl});
    }
  }, [wwwStockUpdated, wwwStockDescUpdated]);

  const editable = !!permissions[ALLOW_TO_EDIT_START_DATE];

  const validateMediaCutOffDate = useCallback((value) => {
    if (!value || !moment(value, 'YYYY/MM/DD', true).isValid()) {
      return intl.formatMessage({id: "wwwStock.validation.invalid.mediaCutoffDate"});
    }
    return undefined;
  }, [intl]);

  const validateFeaturesChi = useCallback((value) => {
    if (value && value.length > 70) {
      return intl.formatMessage({id: "wwwStock.validation.maxLength.featuresChi"});
    }
    return undefined;
  }, [intl]);

  const validateFeaturesEng = useCallback((value) => {
    if (value && value.length > 160) {
      return intl.formatMessage({id: "wwwStock.validation.maxLength.featuresEng"});
    }
    return undefined;
  }, [intl]);

  const generalMapping = useMemo(() => ({
    [intl.formatMessage({id: "wwwStock.mediaCutOffDate"})]: {
      key: "mediaCutOffDate",
      component: DatePicker,
      validate: validateMediaCutOffDate,
      value: formatMediaCutOffDate(mediaCutOffDate),
      xs: 4,
      editable: editable,
      required: true,
    },
    [intl.formatMessage({id: "wwwStock.propertyScore"})]: {component: TextInput, value: propertyScore, xs: 2},
    [intl.formatMessage({id: "wwwStock.stockType"})]: {component: TextInput, value: stockType, xs: 2},
    [intl.formatMessage({id: "wwwStock.area.area"})]: {
      component: TextInput,
      value: numberComma(searchArea) || "---",
      xs: 4
    },
    [intl.formatMessage({id: "wwwStock.featuresChi"})]: {
      key: "featuresChi",
      value: featuresChi,
      xs: 12,
      maxLength: 70,
      editable: true,
      component: TextInput,
      validate: validateFeaturesChi
    },
    [intl.formatMessage({id: "wwwStock.featuresEng"})]: {
      key: "featuresEng",
      value: featuresEng,
      xs: 12,
      maxLength: 150,
      editable: true,
      component: TextInput,
      validate: validateFeaturesEng
    },
  }), [mediaCutOffDate, featuresChi, featuresEng, validateFeaturesChi, validateFeaturesEng, intl.locale]);

  const handleSectionToggle = (expanded) => {
    setIsExpanding(expanded);
    if (!expanded) setEditMode(false);
  };


  const calcExposureRate = (scoreTotal) => {
    return Math.round(scoreTotal / sumAgentScore * 100) || 0;
  }

  const handleFieldClick = (isEditable) => {
    if (isEditable && !editMode) {
      setEditMode(true);
    }
  };

  const handleCancel = () => {
    reset();
    setEditMode(false);
  };

  const handleConfirm = handleSubmit(async (formValues) => {
    try {
      const originalMediaCutOffDate = formatMediaCutOffDate(mediaCutOffDate, 'YYYY/MM/DD');
      if (formValues.mediaCutOffDate && formValues.mediaCutOffDate !== originalMediaCutOffDate) {
        await setWWWStock({
          wwwStock: {
            empId: uid,
            stockId: stockId,
            mediaCutoffDate: formValues.mediaCutOffDate,
          },
        }, {intl});
      }

      if (
        (formValues.featuresChi !== undefined && formValues.featuresChi !== featuresChi) ||
        (formValues.featuresEng !== undefined && formValues.featuresEng !== featuresEng)
      ) {
        await setWWWStockDesc({
          wwwStockDesc: {
            empId: uid,
            stockId: stockId,
            www_desc_chi: formValues.featuresChi,
            www_desc_eng: formValues.featuresEng,
          },
        }, {intl});
      }

      setSnackbarMessage(intl.formatMessage({id: "wwwStock.update.success.message"}));
      setSnackbarOpen(true);
      setEditMode(false);

    } catch (error) {
      setSnackbarMessage(intl.formatMessage({id: "wwwStock.update.fail.message"}));
      setSnackbarOpen(true);
    }
  });

  const buttons = editMode ? [
    {
      label: <FormattedMessage id="wwwStock.update.cancel"/>,
      className: classes.cancelButton,
      onClick: handleCancel,
    },
    {
      label: <FormattedMessage id="wwwStock.update.confirm"/>,
      onClick: handleConfirm,
      disabled: invalid || submitting
    }
  ] : [];

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };


  const renderField = (field, fieldName) => (
    <Grid container item xs={field.xs} key={fieldName} className={classes.fieldContainer}>
      {field.editable && editMode ? (
        <Field
          required={field.required}
          name={field.key}
          component={field.component}
          label={fieldName}
          // inputProps={{ maxLength: field.maxLength }}
          multiline={true}
          validate={field.validate}
        />
      ) : (
        <FieldVal
          field={
            <div style={{display: 'flex', alignItems: 'center'}}>
              {fieldName}
              {field.editable && (
                <Edit
                  fontSize="small"
                  style={{
                    marginLeft: 8,
                    cursor: 'pointer',
                    color: '#757575',
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleFieldClick(true);
                  }}
                />
              )}
            </div>
          }
        >
          {field.value || "---"}
        </FieldVal>
      )}
    </Grid>
  )

  return (
    <div className={classes.root}>

      <Backdrop className={classes.backdrop} open={wwwStockUpdating || wwwStockDescUpdating}>
        <CircularProgress/>
      </Backdrop>

      {/*<LoadingOverlay loading={wwwStockUpdating} />*/}
      <DetailBoxSection
        expandable
        isExpanding={isExpanding}
        callback={handleSectionToggle}
        text={intl.formatMessage({id: "wwwStock.advertisement"})}
      >

        <Grid container className={classes.gridContent}>
          {_.map(generalMapping, renderField)}
        </Grid>

        <Grid container className={classes.gridContent}>
          {_.map(agents, (agent, i) => (
            <Grid className={classes.agentCard} key={i}>
              <Grid container alignContent={"space-between"}>
                <Grid item xs={6} style={{textAlign: "left"}}>
                  <FieldVal field={[intl.formatMessage({id: "wwwStock.agent"})]}>
                    {get(agent, 'empDept')} {get(agent, 'empName')}
                  </FieldVal>
                </Grid>
                <Grid item xs={6} style={{textAlign: "right"}}>
                  <FieldVal field={[intl.formatMessage({id: "wwwStock.exposureRate"})]}>
                    {calcExposureRate(get(agent, 'scoreTotal'))}%
                  </FieldVal>
                </Grid>
              </Grid>
              <Grid container className={classes.root}>
                <WWWStockTag
                  uid={uid}
                  agent={agent}
                  wwwChannelFull={wwwChannelFull}
                />
              </Grid>
            </Grid>
          ))}
        </Grid>
      </DetailBoxSection>

      {editMode && <BottomButtons buttons={buttons}/>}

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
      />
    </div>
  );
}


const mapStateToProps = (state) => ({
  uid: state.auth.user.login.info.emp_id,
  wwwStock: state.wwwStock.currentWWWStock || {},
  wwwStockDesc: state.wwwStock.wwwStockDesc || {},

  wwwStockUpdating: state.wwwStock.wwwStockUpdating || false,
  wwwStockUpdated: state.wwwStock.wwwStockUpdated || false,
  wwwStockDescUpdating: state.wwwStock.wwwStockDescUpdating || false,
  wwwStockDescUpdated: state.wwwStock.wwwStockDescUpdated || false,
  wwwStockUpdateError: state.wwwStock.wwwStockUpdateError || "",
  permissions: state.employee.permissions,
});


const mapDispatchToProps = (dispatch) => {
  return {
    getCurrentWWWStock: (...args) => dispatch(fetchCurrentWWWStock(...args)),
    getWWWStockDesc: (...args) => dispatch(fetchWWWStockDesc(...args)),
    setWWWStock: (...args) => dispatch(updateWWWStock(...args)),
    setWWWStockDesc: (...args) => dispatch(updateWWWStockDesc(...args)),
  };
};


export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(
  reduxForm({
    form: 'StockForm', // 表单唯一标识（自定义）
    enableReinitialize: true,
    keepDirtyOnReinitialize: true,
  })(withStyles(styles)(injectIntl(WWWStocks)))
);
