import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormHelperText from '@material-ui/core/FormHelperText';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import FormControl from '@material-ui/core/FormControl';
import FormLabel from '@material-ui/core/FormLabel';
import InputLabel from '@material-ui/core/InputLabel';


const useStyles = makeStyles(theme => ({
  formControl: {
    margin: theme.spacing(1),
  },
  radioGroup: {
    flexDirection: 'row',
  }
}));

export default function RadioButtonsGroup(props) {
  const radios = props.radios || [];
  const radioControl = props.radioControl || <Radio color="primary" />;

  const classes = useStyles();

  function handleChange(event) {
    props.onChange(event.target.value);
  }

  return (
    <FormControl component="fieldset" className={classes.formControl} variant="outlined">
      <FormLabel component="legend">{props.legend}</FormLabel>
      <InputLabel htmlFor="component-simple"  variant="outlined">Name</InputLabel>
      <RadioGroup id  ="component-simple" className={classes.radioGroup} aria-label={props.legend} value={props.value} onChange={handleChange}>
        {radios.map(radio => 
          <FormControlLabel value={radio.value} control={radioControl} label={radio.label} />
        )}          
      </RadioGroup>
    </FormControl>
  );
}