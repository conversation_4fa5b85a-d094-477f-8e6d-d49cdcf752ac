import mongodb from "../data/mongodb";
import schedule from "node-schedule";
import config from "../config";
import _, { reject } from "lodash";

export const unlockStock = (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    const query = {
      emp_id: req.body.emp_id,
    };
    const col = mongodb.db.collection("unlock");
    col.find(query).count(function (err, count) {
      if (count >= parseInt(req.body.maxQuota/*config.generalDailyQuota*/)) {
        res.send({ unlocked: false });
      } else {
        col
          .find({
            emp_id: req.body.emp_id,
            stockid: req.body.stockid,
          })
          .toArray((err, docs) => {
            if (docs && docs.length > 0) {
              res.send({ unlocked: false });
            } else {
              col.insertOne(
                {
                  emp_id: req.body.emp_id,
                  stockid: req.body.stockid,
                  createDate: new Date(),
                },
                function (err, r) {
                  if (err) {
                    reject(err);
                  } else {
                    res.send({ stockid: req.body.stockid, unlocked: true });
                  }
                },
              );
            }
          });
      }
    });
  }
};

export const unlockListStock = (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    const { emp_id, stockid } = req.body;
    const col = mongodb.db.collection("unlock");
    col.find({ emp_id, stockid: { $nin: stockid } }).count((err, count) => {
      // check if exceed quota after unlocking stocks
      if (count + stockid.length > parseInt(req.body.maxQuota/*config.generalDailyQuota*/)) {
        res.send({ unlocked: false });
      } else {
        // insert unlock stocks
        stockid.forEach((id) => {
          col.find({ emp_id, stockid: id }).toArray((err, docs) => {
            if (_.isEmpty(docs)) {
              col.insertOne(
                { emp_id, stockid: id, createDate: new Date() },
                (err, r) => {
                  if (err) {
                    reject(err);
                  }
                },
              );
            }
          });
        });
        res.send({ stockid, unlocked: true });
      }
    });
  }
};

export const getUnlockCount = (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    const query = {
      emp_id: req.body.emp_id,
    };
    const col = mongodb.db.collection("unlock").find(query);
    col.count(function (err, count) {
      col.toArray((err, docs) => {
        const unlockedStockIds = [];
        docs.map((item, index) => {
          unlockedStockIds.push(docs[index].stockid);
        });
        res.send({ count: count, unlockedStockIds: unlockedStockIds });
      });
    });
  }
};

export const checkUnlockStock = (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    const query = {
      stockid: req.params.stockid,
      emp_id: req.user.login.info.emp_id,
    };
    const col = mongodb.db.collection("unlock").find(query);
    col.toArray((err, docs) => {
      if (docs && docs.length > 0) {
        next();
      } else {
        res.redirect("/unlockStockfirst");
      }
    });
  }
};

export const clearUnlockRecord = () => {
  console.log(new Date());
  const task = schedule.scheduleJob("0 16 * * *", function () {
    console.log(new Date());
    console.log(
      "Schedule task for clearing unlock record at 00:00am every day...",
    );
    const col = mongodb.db.collection("unlock");
    col.deleteMany(function (err, res) {
      if (err) {
        reject(err);
      } else {
        console.log("Removed all docs in collection");
      }
    });
  });
};
