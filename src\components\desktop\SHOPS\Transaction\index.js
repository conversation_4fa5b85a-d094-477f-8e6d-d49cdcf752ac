import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import List from "./List";
import LoadingOverlay from "../../../LoadingOverlay";


const styles = theme => ({
  root: {
    padding: "1vh 3vw",
  },
  notFound: {
    margin: "1em 0",
    textAlign: "center",
    fontWeight: "bold"
  },
});

class Transaction extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {
    const { classes, transactions, listed, listing } = this.props;

    const hasData = transactions.length > 0;

    return (
      <div className={classes.root}>
        {listed && hasData && <List transactions={transactions} />}

        {(listing || !listed) && <LoadingOverlay />}

        {!listing && listed && !hasData && (
          <div className={classes.notFound}>
            Transaction not found
          </div>
        )}
      </div>
    );
  }
}

const mapStateToProps = state => ({
  transactions: state.transaction.transactions ? state.transaction.transactions : [],
  listed: state.transaction.listed ? state.transaction.listed : false,
  listing: state.transaction.listing ? state.transaction.listing : false
});

export default connect(mapStateToProps)(withStyles(styles)(Transaction));
