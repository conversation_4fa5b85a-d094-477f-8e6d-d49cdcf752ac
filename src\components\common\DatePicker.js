import React from "react";
import { withStyles } from "@material-ui/core/styles";
import TextField from "@material-ui/core/TextField";
import moment from "moment";

const styles = theme => ({});

function DatePicker(props) {
  const {
    classes,
    label,
    input: { value, onChange, onBlur, ...inputProps },
    meta: { touched, invalid, error },
    ...custom
  } = props;

  // 将值转换为YYYY-MM-DD格式（Material-UI日期输入要求的格式）
  const formattedValue = value ?
    moment(value, 'YYYY/MM/DD').format('YYYY-MM-DD') :
    '';

  function handleChange(event) {
    // 将值转换回您需要的格式
    const newValue = event.target.value;
    onChange(newValue); // 或者根据需要转换格式
  }

  return (
    <TextField
      id={label}
      label={label}
      value={formattedValue}
      onChange={handleChange}
      onBlur={onBlur}
      className={classes.textField}
      error={touched && invalid}
      helperText={touched && error}
      InputLabelProps={{
        shrink: true,
      }}
      type="date"
      {...inputProps}
      {...custom}
    />
  );
}

export default withStyles(styles)(DatePicker);
