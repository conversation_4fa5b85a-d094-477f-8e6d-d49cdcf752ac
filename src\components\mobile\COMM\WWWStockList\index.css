/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

.root {
  /* min-height: calc(100vh - 60px - 76.5px - 52px); */
  margin-bottom: 60px;
}
.bg {
  width: 100%;
  height: 100vh;
  background-image: url(../../../../files/bg/ind_watermark_result_list.png);
  background-repeat: no-repeat;
  background-position: center;
  background-color: #f5f5f5;
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
}

.card {
  padding: 1vw 2vw;
}

.count {
  padding: 1vw 3vw;
  /* background-color: #fff; */
}

.container {
  margin: 0 auto;
  padding: 20px 0;
  max-width: var(--max-content-width);
}

.sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 128.5px;
}

.spacer {
  width: 100%;
  height: 40px;
}

.listTop {
  display: flex;
  justify-content: space-between;
}

.listPP {
  background-color: #e3e3e3;
  color: #73716e;
  font-weight: 500;
  font-size: 0.85em;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  border-radius: 5px;
  margin: 1vw 3vw;
  padding: 0 8px;
}

.enabled {
  background-color: #28cd67;
  color: white;
}
