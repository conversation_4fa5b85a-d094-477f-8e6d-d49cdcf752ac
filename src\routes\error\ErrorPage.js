/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import withStyles from "isomorphic-style-loader/lib/withStyles";
import s from "./ErrorPage.css";

class ErrorPage extends React.Component {
  static propTypes = {
    error: PropTypes.shape({
      name: PropTypes.string.isRequired,
      message: PropTypes.string.isRequired,
      reason: PropTypes.string.isRequired,
      stack: PropTypes.string.isRequired
    })
  };

  static defaultProps = {
    error: null
  };

  render() {
    const { message, reason } = this.props;

    if (__DEV__ && this.props.error) {
      return (
        <div>
          <h1>{this.props.error.name}</h1>
          <pre>{this.props.error.stack}</pre>
        </div>
      );
    }

    let errorType = "Error";
    if (message === "Only mobile device can access")
      errorType = "Access Denied";

    return (
      <div>
        <h1>{errorType}</h1>
        {message ? (
          <div className={s.message}>
            {message}
            {reason && <div className={s.reason}>{reason}</div>}
          </div>
        ) : (
          <p>Sorry, a critical error occurred on this page.</p>
        )}
        <p>
          <a href="/login" className={s.link}>
            Go back
          </a>
        </p>
      </div>
    );
  }
}

export { ErrorPage as ErrorPageWithoutStyle };
export default withStyles(s)(ErrorPage);
