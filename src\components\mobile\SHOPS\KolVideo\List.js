/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import _ from "lodash";
import InfiniteList from "../../../common/InfiniteList";
import KolVideoCard from "./KolVideoCard";
import {
  listTeamsKolVideos,
  clearTeamsKolVideos,
  batchRemoveKolVideo,
  batchApprovedKolVideo
} from "../../../../actions/kolVideo";
import LoadingOverlay from "../../../LoadingOverlay";
import BottomButtons from "@/components/common/BottomButtons";
import { injectIntl } from "react-intl";
import SelectFieldArrayOutput from "@/components/common/SelectFieldArrayOutput";

const styles = theme => ({
  root: {
    padding: "1vw 2vw"
  },
  card: {
    marginBottom: "1vh"
  },
  greyBtn: {
    backgroundColor: "#626262",
    "&:hover, &:active": {
      backgroundColor: "#626262",
    }
  },
  redBtn: {
    backgroundColor: "#ff0000",
    "&:hover, &:active": {
      backgroundColor: "#626262",
    }
  },
});

class List extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    kolVideos: PropTypes.array,
    listTeamsKolVideos: PropTypes.func.isRequired,
    batchRemoveKolVideo: PropTypes.func.isRequired,
    batchApprovedKolVideo: PropTypes.func.isRequired
  };

  constructor(props) {
    super(props);
    this.state = {
      selectedKolVideo:[],
      filterType: "all",
      filteredMedia: []
    };
  }

  async fatchData () {
    await this.props.listTeamsKolVideos();
    this.setState({ ...this.state, selectedKolVideo: [], filteredMedia: [...this.props.kolVideos] });
  }

  componentDidMount() {
    this.fatchData();
  }

  toggle = (event, mediumId) => {
    let tempSelectedKolVideos = [...this.state.selectedKolVideo];
    if (tempSelectedKolVideos.indexOf(mediumId) === -1) {
      tempSelectedKolVideos.push(mediumId);
    } else if (tempSelectedKolVideos.indexOf(mediumId) !== -1) {
      tempSelectedKolVideos.splice(tempSelectedKolVideos.indexOf(mediumId), 1);
    }
    this.setState({ ...this.state, selectedKolVideo: tempSelectedKolVideos });
  };

  selectedAll = () => {
    this.setState({ ...this.state, selectedKolVideo: this.props.kolVideos.map(element => element.id) });
  }

  cancelSelectedAll = () => {
    this.setState({ ...this.state, selectedKolVideo: [] });
  }

  handleTypeChange = (value) => {
    this.setState({ 
      ...this.state, 
      filterType: value,
      selectedKolVideo: [],
      filteredMedia: this.props.kolVideos.filter(element => {
        if (value === 'unprocessed') {
          return element.approval !== 'approved'
        }
        if (value === 'processed') {
          return element.approval === 'approved'
        }
        return element;
      })});
  };

  render() {
    const {
      classes,
      listing,
      listed,
      kolVideos,
      intl
    } = this.props;

    const {selectedKolVideo, filterType, filteredMedia} = this.state;

    const filterTypes = [
      {
        value: "all",
        label: intl.formatMessage({
          id: "search.form.all",
        }),
      },
      // {
      //   value: "unprocessed",
      //   label: intl.formatMessage({
      //     id: "stock.kol.unprocessed",
      //   }),
      // },
      // {
      //   value: "processed",
      //   label: intl.formatMessage({
      //     id: "stock.kol.processed",
      //   }),
      // },
    ];

    const bottomButtons = [
      {
        className: classes.greyBtn,
        label: intl.formatMessage({
          id: "search.form.mark.all",
        }),
        onClick: () => {
          this.selectedAll();
        },
      },
      {
        className: classes.greyBtn,
        label: intl.formatMessage({ id: "search.form.unmark.all" }),
        onClick: () => {
          this.cancelSelectedAll();
        },
      },
      {
        className: classes.redBtn,
        label: intl.formatMessage({
          id: "common.remove",
        }),
        onClick: async () => {
          if (selectedKolVideo?.length) {
            await this.props.batchRemoveKolVideo({ids: selectedKolVideo});
            this.fatchData();
          }
        },
      },
      {
        label: intl.formatMessage({ id: "stock.kol.approve" }),
        onClick: async () => {
          if (selectedKolVideo?.length) {
            await this.props.batchApprovedKolVideo({ids: selectedKolVideo});
            this.fatchData();
          }
        },
      },
    ];

    return (
      <div className={classes.root}>
        {listed && (
          <>
          {kolVideos.length > 0 && <SelectFieldArrayOutput
            label={intl.formatMessage({
              id: "home.kol.video",
            })}
            //className={classes.mediaTypeSelect}
            ranges={filterTypes}
            input={{
              value: filterType,
              onChange: this.handleTypeChange,
              onBlur: () => {},
            }}
            meta={{}}
            isArrayOutput={false}
            isClearable={false}
          />}
          <InfiniteList list={filteredMedia} fetchMoreData={() => {}}>
            {({ ...props }) => <KolVideoCard className={classes.card} toggle={this.toggle} selectedKolVideo={selectedKolVideo} {...props} />}
          </InfiniteList>
          {kolVideos.length > 0 && filteredMedia.length > 0 && <BottomButtons buttons={bottomButtons} />}
          </>
        )}
        {listing && <LoadingOverlay />}
      </div>
    );
  }
}

const mapStateToProps = state => ({
  kolVideos: state.kolVideo.kolVideos ? state.kolVideo.kolVideos : [],
  listed: state.kolVideo.listed ? state.kolVideo.listed : false,
  listing: state.kolVideo.listing ? state.kolVideo.listing : false,
});

const mapDispatchToProps = dispatch => {
  return {
    listTeamsKolVideos: (...args) => dispatch(listTeamsKolVideos(...args)),
    clearTeamsKolVideos: () => dispatch(clearTeamsKolVideos()),
    batchRemoveKolVideo: (...args) => dispatch(batchRemoveKolVideo(...args)),
    batchApprovedKolVideo: (...args) => dispatch(batchApprovedKolVideo(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(List)));
