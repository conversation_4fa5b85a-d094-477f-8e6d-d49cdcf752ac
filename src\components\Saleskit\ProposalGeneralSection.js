import React, { useMemo, useEffect, useState, useRef } from "react";
import { connect, useDispatch } from "react-redux";
import { Grid, IconButton } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import PropTypes from "prop-types";
import { FieldArray, change, reduxForm, getFormValues, getFormInitialValues } from "redux-form";
import { injectIntl } from "react-intl";
import EmojiPicker from "emoji-picker-react";
import SentimentSatisfiedAltIcon from '@material-ui/icons/SentimentSatisfiedAlt';

import ProposalFormPaper from "../common/ProposalFormPaper";
import {
  getCompanyTitleOptions,
  getLangOptions,
  getTypeOptions,
  getMultiImgOptions,
  getFontFamilyOptions,
} from "./helpers";
import InputWithCheckBoxCustom from "../common/InputWithCheckBoxCustom";
import MuiSelectArrayOutput from "../common/MuiSelectArrayOutput";
import InputWithCheckBox from "../common/InputWithCheckBox";
import InlineTextInput from "../common/InlineTextInput";
import InlineTextInputField from "../common/InlineTextInputField";
import InlineTextField from "../common/InlineTextField";
import Switch from "../common/Switch";
import { sbu } from "../../config";
import {
  getProposalName,
  getStockFloorType,
  getFullAddress,
  parseAddressObj,
} from "../Saleskit/helpers";
import _ from "lodash";

const useStyles = makeStyles(() => ({
  section: {
    paddingTop: "1vh",
  },
  remarksFieldMargin: {
    marginBottom: 3,
  },
}));

function ProposalGeneralSection({
  isListProposal,
  onTypeChange,
  changeFieldValue,
  intl,
  initFormState,
  isReCreatePP = false,
  formState,
  currentStock,
  detail,
  stockMedia,
  buildingMedia,
}) {
  const classes = useStyles();
  const dispatch = useDispatch();
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const customTitleInputRef = useRef(null);

  const typeOptions = getTypeOptions(intl);
  const companyTitleOptions = getCompanyTitleOptions(intl);
  const langOptions = getLangOptions(intl);
  const fontFamilyOptions = getFontFamilyOptions(intl);
  const multiImgOptions = getMultiImgOptions()
  const [buildingOrStock, setBuildingOrStock] = useState("building");

  /** `reCreatepp`時防止初始化時觸發 useEffect 導致`history`值被覆蓋 */
  const initialLoad = React.useRef(!isReCreatePP);
  React.useEffect(() => {
    if (!initialLoad.current && formState !== initFormState) {
      initialLoad.current = true;
    }
  }, [formState, initFormState]);

  const stock = useMemo(
    () => _.find(detail, ["_id", currentStock]),
    [detail, currentStock],
  );

  const onChangeBuildingOrStock = (v) => {
    _.forEach(detail, (stockTemp) => {
      const stockMediaData = stockMedia.find(item => item.id === stockTemp.unicorn.id.toString())?.data || {};
      const buildingMediaData = buildingMedia.find(item => item.id === stockTemp.building.unicorn.id.toString())?.data || {};

      let mainPhoto = "";

      if (buildingOrStock === "building") {
        mainPhoto =
          _.find(
            _.get(stockMediaData, "photo", []),
            m => m?.tags?.includes("main"),
          ) || {}
      } else if (buildingOrStock === "stock") {
        mainPhoto =
          _.find(
            _.get(buildingMediaData, "photo", []),
            m => m?.tags?.includes("main"),
          ) || {}
      }

      changeFieldValue(`stocks.${stockTemp._id}.media.mainPhoto`, _.get(mainPhoto, "id", ""));
    });

    if (buildingOrStock === "building") {
      setBuildingOrStock(v);
    } else if (buildingOrStock === "stock") {
      setBuildingOrStock(v);
    }
  };

  useEffect(() => {
    if (!initialLoad.current) {
      return;
    }
    // 检查customTitle的value是否有值
    const customTitleValue = _.get(formState, "general.customTitle.value");
    if (customTitleValue) {
      // 如果有值则设置isShow为true
      changeFieldValue("general.customTitle.isShow", true);
    }
  }, [_.get(formState, "general.customTitle.value")]);

  useEffect(() => {
    if (!initialLoad.current) {
      return;
    }
    const exactFloor = _.get(formState, "general.exactFloor.0.isShow");
    changeFieldValue(`general.proposalName`, getProposalName(intl, stock, !exactFloor
      ? getStockFloorType(
        _.get(stock, "building.floors") || [],
        _.get(stock, "floor"),
      )
      : "Actual Floor", _.get(formState, "general.showUnit.0.isShow"), isListProposal ? 'ListProposal' : 'Proposal', null, _.get(formState, "general.lang"), _.get(formState, "general.type")))
  }, [currentStock, _.get(formState, "general.lang"), _.get(formState, "general.showUnit"), _.get(formState, "general.exactFloor"), _.get(formState, "general.type")]);

  useEffect(() => {
    if (!initialLoad.current) {
      return;
    }
    changeFieldValue(`general.useNameAsPdf`, true)
  }, [_.get(formState, "general.lang")]);

  useEffect(() => {
    if (!initialLoad.current) {
      return;
    }
    _.forEach(detail, (stockTemp) => {
      changeFieldValue(`${`stocks.${stockTemp._id}.stock`}.possession.0.isShow`, _.get(formState, "general.showPossession.0.isShow"));
    });
  }, [_.get(formState, "general.showPossession")]);

  useEffect(() => {
    if (!initialLoad.current) {
      return;
    }

    _.forEach(detail, (stockTemp) => {
      changeFieldValue(`${`stocks.${stockTemp._id}.stock`}.customBuilding.0.isShow`, _.get(formState, "general.showBuilding.0.isShow"));
      if (sbu === "SHOPS") {
        changeFieldValue(`${`stocks.${stockTemp._id}.stock`}.customAddressEn`, getFullAddress({ ...intl, locale: "en" }, stockTemp, undefined, _.get(formState, `${`stocks.${stockTemp._id}.stock`}.unit.0.isShow`, true), _.get(formState, "general.showBuilding.0.isShow")))
        changeFieldValue(`${`stocks.${stockTemp._id}.stock`}.customAddressZh`, getFullAddress({ ...intl, locale: "zh" }, stockTemp, undefined, _.get(formState, `${`stocks.${stockTemp._id}.stock`}.unit.0.isShow`, true), _.get(formState, "general.showBuilding.0.isShow")))
      }
    });
  }, [_.get(formState, "general.showBuilding")]);

  const firstShowUnitEffectRef = React.useRef(true);
  
  useEffect(() => {
    if (!initialLoad.current) {
      return;    
    }
    _.forEach(detail, (stockTemp) => {
      const unit = _.get(parseAddressObj(stockTemp), `unit.${intl.locale}`) || "";
      changeFieldValue(`stocks.${stockTemp._id}.stock.unit.0.value`, _.get(formState, "general.showUnit.0.isShow") ? unit : "");
      changeFieldValue(`stocks.${stockTemp._id}.stock.unit.0.isShow`, _.get(formState, "general.showUnit.0.isShow"));
      if (sbu === "SHOPS") {
        changeFieldValue(`stocks.${stockTemp._id}.stock.customAddressEn`, getFullAddress({ ...intl, locale: "en" }, stockTemp, undefined, _.get(formState, "general.showUnit.0.isShow"), _.get(formState, `${`stocks.${stockTemp._id}.stock`}.customBuilding.0.isShow`, true)))
        changeFieldValue(`stocks.${stockTemp._id}.stock.customAddressZh`, getFullAddress({ ...intl, locale: "zh" }, stockTemp, undefined, _.get(formState, "general.showUnit.0.isShow"), _.get(formState, `${`stocks.${stockTemp._id}.stock`}.customBuilding.0.isShow`, true)))
      }
    });
  }, [_.get(formState, "general.showUnit")]);

  useEffect(() => {
    if (!initialLoad.current) {
      return;
    }
    const exactFloor = _.get(formState, "general.exactFloor.0.isShow");
    _.forEach(detail, (stockTemp) => {
      changeFieldValue(`stocks.${stockTemp._id}.stock.floorType`, !exactFloor ? getStockFloorType(_.get(stockTemp, "building.floors") || [], _.get(stockTemp, "floor")) : "Actual Floor")
    });
  }, [_.get(formState, "general.exactFloor")]);

  // useEffect(() => {
  //   _.forEach(detail, (stockTemp) => {
  //     changeFieldValue(`${isListProposal ? `stocks.${stockTemp._id}.stock` : 'stock'}.currentState.0.isShow`, _.get(formState, "general.showCurrentState.0.isShow"))
  //   });
  // }, [_.get(formState, "general.showCurrentState")]);

  useEffect(() => {
    if (!initialLoad.current) {
      return;
    }
    let shouldShowCurrentState = false;
    _.forEach(detail, (stockTemp) => {
      if (_.get(formState, `${`stocks.${stockTemp._id}.stock`}.currentState.0.isShow`)) {
        shouldShowCurrentState = true;
        return false;
      }
    });
    if (shouldShowCurrentState && !_.get(formState, "general.showCurrentState.0.isShow")) {
      changeFieldValue(`general.showCurrentState.0.isShow`, true);
    }
  });

  const allMedia = [...stockMedia, ...buildingMedia];
  const targetPhotoContents = new Set(["arcCert", "areaPlan", "floorPlan", "plan", "positionPlan", "map", ...(sbu === "COMM" ? ["layout"] : [])]);
  const matchingMediaIds = []
  _.forEach(allMedia, (item) => {
    _.forEach(_.get(item, 'data.photo', []), (i) => {
      if (targetPhotoContents.has(i.photoContent)) {
        matchingMediaIds.push(i.id)
      }
    })
  })

  const multiImagesChange = (e) => {
    const cloneStocks = _.cloneDeep(_.get(formState, 'stocks'))
    _.forEach(_.values(cloneStocks), (item) => {
      if (item.media && item.media.attachmentMultiImgConfig) {
        _.forEach(_.keys(item.media.attachmentMultiImgConfig), (key) => {
          item.media.attachmentMultiImgConfig[key] = ["map", "govMap"].includes(key) ? 'ONE' : matchingMediaIds.includes(key) ? item.media.attachmentMultiImgConfig[key] : e.value
        })
      }
    })
    changeFieldValue('stocks', cloneStocks)

    changeFieldValue('general.multiImg', e.value)
  }

  const onEmojiClick = (emojiData) => {
    const { selectionStart, selectionEnd } = customTitleInputRef.current;
    const currentTitle = _.get(formState, "general.customTitle.value", "") || "";
    // 在光标位置插入表情
    const newTitle = currentTitle.substring(0, selectionStart) + 
                     emojiData.emoji + 
                     currentTitle.substring(selectionEnd);
    changeFieldValue("general.customTitle.value", newTitle);
    setShowEmojiPicker(false);
  };

  const getFieldConfig = () => ({
    tenant: {
      check: (tenant) => _.get(tenant, "tenant.nameEn") || _.get(tenant, "tenant.nameZh")
    },
    tenantShop: {
      check: (tenant) => _.get(tenant, "tenantShop.nameEn") || _.get(tenant, "tenantShop.nameZh")
    },
    rentalFee: {
      check: (tenant) => _.get(tenant, "rentalFee")
    },
    period: {
      check: (tenant) => _.get(tenant, "period.min") || _.get(tenant, "period.max")
    },
    tenancy: {
      check: (tenant) => _.get(tenant, "tenancy.nameEn") || _.get(tenant, "tenancy.nameZh")
    },
    tenancyRemarks: {
      check: (tenant) => _.get(tenant, "tenancyRemarks")
    }
  });

  const clickShowTenancyCheckbox = (args) => {
    const fieldNames = [
      "period",
      "rentalFee",
      "tenancy",
      "tenant",
      "tenancyRemarks",
      ...sbu === "SHOPS" ? ["tenantShop"] : [],
    ];
    const tenancyStatuses = [
      "currentTenants",
      // ...sbu === "SHOPS" ? [
      //   "advanceTenants",
      //   "previousTenants",
      //   "formerTenants",
      // ] : [],
    ]
    tenancyStatuses.forEach(tenancyStatus => {
      _.forEach(detail, (stockTemp) => {
        _.get(formState, `${`stocks.${stockTemp._id}.stock`}.${tenancyStatus}`, []).forEach((tenant, index) => {
          fieldNames.forEach(fieldName => {
            if (_.get(args, "1.isShow")) {
              const fieldConfig = getFieldConfig();
              if (fieldConfig[fieldName] && fieldConfig[fieldName].check(tenant)) {
                changeFieldValue(`stocks.${stockTemp._id}.stock.${tenancyStatus}.${index}.${fieldName}IsShow`, true);
              }
            } else {
              changeFieldValue(`stocks.${stockTemp._id}.stock.${tenancyStatus}.${index}.${fieldName}IsShow`, false);
            }
          });
        })
      });
    })
  }

  return (
    <ProposalFormPaper>
      <Grid container spacing={1}>
        {!isListProposal && (
          <Grid item xs={12}>
            <InputWithCheckBoxCustom
              name="general.companyTitle"
              label={intl.formatMessage({
                id: "proposal.form.companyTitle",
              })}
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              fakeCheckbox
              renderComponent={MuiSelectArrayOutput}
              ranges={companyTitleOptions}
              isArrayOutput={false}
              fullWidth
            />
          </Grid>
        )}
        <Grid item xs={12}>
          <InputWithCheckBoxCustom
            name="general.type"
            label={intl.formatMessage({
              id: "proposal.form.type",
            })}
            checkboxInFront
            aligntoLabel
            checkboxXs={1}
            fakeCheckbox
            renderComponent={MuiSelectArrayOutput}
            ranges={typeOptions}
            isArrayOutput={false}
            extraHandleChange={onTypeChange}
            fullWidth
          />
        </Grid>
        <Grid item xs={12}>
          <InputWithCheckBoxCustom
            name="general.multiImg"
            label={intl.formatMessage({
              id: "proposal.form.multiImages",
            })}
            checkboxInFront
            aligntoLabel
            checkboxXs={1}
            fakeCheckbox
            renderComponent={MuiSelectArrayOutput}
            input={{
              name: 'general.multiImg',
              value: _.get(formState, 'general.multiImg', 'FOUR'),
              onBlur: () => { },
              onChange: () => { }
            }}
            ranges={multiImgOptions}
            isArrayOutput={false}
            extraHandleChange={multiImagesChange}
            fullWidth
          />
        </Grid>
        <Grid item xs={12}>
          <InputWithCheckBoxCustom
            name="general.lang"
            label={intl.formatMessage({
              id: "proposal.form.lang",
            })}
            checkboxInFront
            aligntoLabel
            checkboxXs={1}
            fakeCheckbox
            renderComponent={MuiSelectArrayOutput}
            ranges={langOptions}
            isArrayOutput={false}
            fullWidth
          />
        </Grid>
        <Grid item xs={12}>
          <InputWithCheckBoxCustom
            name="general.fontFamily"
            label={intl.formatMessage({
              id: "proposal.form.fontFamily",
            })}
            checkboxInFront
            aligntoLabel
            checkboxXs={1}
            fakeCheckbox
            renderComponent={MuiSelectArrayOutput}
            ranges={fontFamilyOptions}
            isArrayOutput={false}
            fullWidth
          />
        </Grid>
        {isListProposal && (
        <>
        <Grid item xs={12}>
          <InputWithCheckBoxCustom
            name={sbu === 'COMM' ? 'general.termRemarks' : 'general.termRemarks.nameZh'}
            className={classes.remarksFieldMargin}
            label={intl.formatMessage({
              id: "proposal.form.chiRemarks",
            })}
            checkboxInFront
            aligntoLabel
            checkboxXs={1}
            fakeCheckbox={sbu !== 'COMM'}
            jsonField={sbu === 'COMM' ? "nameZh" : undefined}
            isShowField={sbu === 'COMM' ? "isShow" : undefined}
            changeFieldValue={sbu === 'COMM' ? changeFieldValue : undefined}
            renderComponent={InlineTextInputField}
            multiline
            fullWidth
          />
        </Grid>
        <Grid item xs={12}>
          <InputWithCheckBoxCustom
            name="general.termRemarks.nameEn"
            className={classes.remarksFieldMargin}
            label={intl.formatMessage({
              id: "proposal.form.engRemarks",
            })}
            checkboxInFront
            aligntoLabel
            checkboxXs={1}
            fakeCheckbox
            fakeCheckboxEle={<div style={{ width: '24px', height: '24px' }} />}
            renderComponent={InlineTextInputField}
            multiline
            fullWidth
          />
        </Grid>
        <Grid item xs={12}>
          <InputWithCheckBoxCustom
            name="general.customTitle"
            label={
              <div onClick={e => e.preventDefault()} style={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
                <span>{intl.formatMessage({id: "proposal.form.customtitle",})}</span>
                {
                  <IconButton
                    size="small"
                    style={{
                      width: 48,
                      borderRadius: 18,
                      border: '1px solid gray',
                    }}
                    onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  >
                    <SentimentSatisfiedAltIcon style={{ fontSize: 18, color: 'gray' }} />
                  </IconButton>
                }
              </div>
            }
            checkboxInFront
            aligntoLabel
            checkboxXs={1}
            changeFieldValue={changeFieldValue}
            renderComponent={InlineTextField}
            fullWidth
            inputProps={{
              ref: customTitleInputRef,
            }}
          />
          {showEmojiPicker && (
            <div style={{ position: 'absolute', zIndex: 1000 }}>
              <EmojiPicker onEmojiClick={onEmojiClick} searchDisabled={true} skinTonesDisabled={true} previewConfig={{ showPreview: false }} />
            </div>
          )}
        </Grid>
        </>
        )}
        <Grid item xs={12} container>
          <Grid item xs={6}>
            <FieldArray
              name={`general.showEmployeePhoto`}
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              component={InputWithCheckBox}
              renderComponent={InlineTextInput}
              changeFieldValue={changeFieldValue}
              fullWidth
              disabled
              noBottomBorder
              allowWrap={false}
            />
          </Grid>
          <Grid item xs={6}>
            <FieldArray
              name={`general.showContact`}
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              component={InputWithCheckBox}
              renderComponent={InlineTextInput}
              changeFieldValue={changeFieldValue}
              fullWidth
              disabled
              noBottomBorder
              allowWrap={false}
            />
          </Grid>
        </Grid>
        <Grid item xs={12} container>
          <Grid item xs={6}>
            <FieldArray
              name={`general.exactFloor`}
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              component={InputWithCheckBox}
              renderComponent={InlineTextInput}
              changeFieldValue={changeFieldValue}
              fullWidth
              disabled
              noBottomBorder
              allowWrap={false}
            />
          </Grid>
          <Grid item xs={6}>
            <FieldArray
              name={`general.showUnit`}
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              component={InputWithCheckBox}
              renderComponent={InlineTextInput}
              changeFieldValue={changeFieldValue}
              fullWidth
              disabled
              noBottomBorder
              allowWrap={false}
            />
          </Grid>
        </Grid>
        {!isListProposal && sbu === 'SHOPS' && (
          <Grid item xs={12} container>
            <Grid item xs={6}>
              <FieldArray
                name={`general.showBuilding`}
                checkboxInFront
                aligntoLabel
                checkboxXs={1}
                component={InputWithCheckBox}
                renderComponent={InlineTextInput}
                changeFieldValue={changeFieldValue}
                fullWidth
                disabled
                noBottomBorder
                allowWrap={false}
              />
            </Grid>
            <Grid item xs={6}>
            </Grid>
          </Grid>
        )}
        {isListProposal && (
          <>
            <Grid item xs={12} container>
              <Grid item xs={6}>
                <FieldArray
                  name={`general.${sbu !== 'SHOPS' ? 'showMainPhoto' : 'showBuilding'}`}
                  checkboxInFront
                  aligntoLabel
                  checkboxXs={1}
                  component={InputWithCheckBox}
                  renderComponent={InlineTextInput}
                  changeFieldValue={changeFieldValue}
                  fullWidth
                  disabled
                  noBottomBorder
                  allowWrap={false} />
                {sbu === "COMM" && (
                  <div style={{ paddingLeft: 24 }}>
                    <Switch
                      value={buildingOrStock}
                      textL={intl.formatMessage({ id: "proposal.form.buiding" })}
                      textR={intl.formatMessage({ id: "search.header.stock" })}
                      valleft="building"
                      valright="stock"
                      onChange={onChangeBuildingOrStock}
                    />
                  </div>
                )}
              </Grid>
              <Grid item xs={6}>
                <FieldArray
                  name={`general.${sbu !== 'SHOPS' ? 'showTenancy' : 'showMainPhoto'}`}
                  checkboxInFront
                  aligntoLabel
                  checkboxXs={1}
                  component={InputWithCheckBox}
                  renderComponent={InlineTextInput}
                  changeFieldValue={(...args) => {
                    changeFieldValue(...args);
                    clickShowTenancyCheckbox(args);
                  }}
                  fullWidth
                  disabled
                  noBottomBorder
                  allowWrap={false} />
              </Grid>
            </Grid>
            <Grid item xs={12} container>
              <Grid item xs={6}>
                <FieldArray
                  name={`general.${sbu !== 'SHOPS' ? 'showPossession' : 'showTenancy'}`}
                  checkboxInFront
                  aligntoLabel
                  checkboxXs={1}
                  component={InputWithCheckBox}
                  renderComponent={InlineTextInput}
                  changeFieldValue={(...args) => {
                    changeFieldValue(...args);
                    clickShowTenancyCheckbox(args);
                  }}
                  fullWidth
                  disabled
                  noBottomBorder
                  allowWrap={false} />
              </Grid>
              <Grid item xs={6}>
                <FieldArray
                  name={`general.${sbu !== 'SHOPS' ? 'showCurrentState' : 'showPossession'}`}
                  checkboxInFront
                  aligntoLabel
                  checkboxXs={1}
                  component={InputWithCheckBox}
                  renderComponent={InlineTextInput}
                  changeFieldValue={(...args) => {
                    changeFieldValue(...args);
                    if (sbu !== 'SHOPS') {
                      _.forEach(detail, (stockTemp) => {
                        changeFieldValue(`stocks.${stockTemp._id}.stock.currentState.0.isShow`, _.get(args, "1.isShow"));
                      });
                    }
                  }}
                  fullWidth
                  disabled
                  noBottomBorder
                  allowWrap={false} />
              </Grid>
            </Grid>
            {sbu === 'SHOPS' && (
              <Grid item xs={12} container>
                <Grid item xs={6}>
                  <FieldArray
                    name={`general.showCurrentState`}
                    checkboxInFront
                    aligntoLabel
                    checkboxXs={1}
                    component={InputWithCheckBox}
                    renderComponent={InlineTextInput}
                    changeFieldValue={(...args) => {
                      changeFieldValue(...args);
                      _.forEach(detail, (stockTemp) => {
                        changeFieldValue(`stocks.${stockTemp._id}.stock.currentState.0.isShow`, _.get(args, "1.isShow"));
                      });
                    }}
                    fullWidth
                    disabled
                    noBottomBorder
                    allowWrap={false} />
                </Grid>
                <Grid item xs={6}>
                </Grid>
              </Grid>
            )}
          </>
        )}
      </Grid>
    </ProposalFormPaper>
  );
}

ProposalGeneralSection.defaultProps = {
  isListProposal: false,
};

ProposalGeneralSection.propTypes = {
  isListProposal: PropTypes.bool,
  onTypeChange: PropTypes.func.isRequired,
  changeFieldValue: PropTypes.func.isRequired,
  intl: PropTypes.object.isRequired,
  formState: PropTypes.object.isRequired,
  currentStock: PropTypes.string.isRequired,
  detail: PropTypes.array.isRequired,
  stockMedia: PropTypes.array.isRequired,
  buildingMedia: PropTypes.array.isRequired,
};

const mapStateToProps = (state) => ({
  formState: getFormValues("proposal")(state),
  initFormState: getFormInitialValues("proposal")(state),
  isReCreatePP: _.get(state, "proposal.isReCreatePP", false),
  detail: _.get(state, "stock.detail", []),
  currentStock: _.get(state, "stock.currentDetail", ""),
  stockMedia: _.get(state, "stock.media", []),
  buildingMedia: _.get(state, "building.media", []),
});
export default connect(
  mapStateToProps,
)(injectIntl(ProposalGeneralSection));
