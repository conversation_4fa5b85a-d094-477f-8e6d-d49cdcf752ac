import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import squareSvg from "../../../files/icons/transparent-square.svg";
import { injectIntl } from "react-intl";
import MediumProperties from "./MediumProperties";

const styles = {
  image: {
    width: "100%",
    cursor: "pointer",
    backgroundSize: "cover",
    backgroundPosition: "center",
    backgroundRepeat: "no-repeat",
  },
  imageContainer: {
    overflow: "hidden",
    position: "relative",
  },
};

class Photo extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    script: PropTypes.object,
    handleOpenLightbox: PropTypes.func,
  };

  render() {
    const {
      classes,
      script,
      handleOpenLightbox,
      intl,
    } = this.props;

    const regex = /(https?:\/\/[^\s"']+)/;
    const match = script.script.match(regex);
    const srcUrl = match && match[0];

    return (
      <div className={classes.imageContainer}>
        <a
          href={srcUrl}
        >
          <img
            className={classes.image}
            alt=""
            role="presentation"
            onClick={handleOpenLightbox}
            src={squareSvg}
            style={{
              backgroundImage: `url(${script.thumbnail}?)`,
            }}
          />
        </a>
        <MediumProperties medium={script} />
      </div>
    );
  }
}
export default withStyles(styles)(injectIntl(Photo));
