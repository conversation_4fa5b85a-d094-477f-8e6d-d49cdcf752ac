import React from "react";
import Layout from "../../../../components/Layout";

const title = "Create List Proposal";
async function action({ store, query }) {
  const { auth } = store.getState();
  if (!auth.user || !auth.user.authorized) {
    return { redirect: "/login" };
  }

  const Stock = await require.ensure(
    [],
    (require) => require("../stock/Stock").default,
    "stock",
  );

  let headerRef = React.createRef();

  return {
    chunks: ["listProposal"],
    title,
    component: (
      <Layout headerRef={headerRef} backToListStep={1}>
        <Stock
          stockid={JSON.parse(query.ids)}
          headerRef={headerRef}
          defaultTab={2}
        />
      </Layout>
    ),
  };
}

export default action;
