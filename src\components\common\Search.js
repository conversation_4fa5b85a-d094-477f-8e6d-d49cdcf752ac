import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import Select from "react-select";
import { emphasize, makeStyles, useTheme } from "@material-ui/core/styles";
import Typography from "@material-ui/core/Typography";
import NoSsr from "@material-ui/core/NoSsr";
import TextField from "@material-ui/core/TextField";
import Paper from "@material-ui/core/Paper";
import { injectIntl } from "react-intl";

const useStyles = makeStyles(theme => ({
  root: {
    // marginLeft: theme.spacing(1),
    // marginRight: theme.spacing(1)
    // width: '100%',
  },
  input: {
    display: "flex",
    // padding: "2px 0 2px 14px",
    height: "auto",
    cursor: "pointer"
  },
  valueContainer: {
    display: "flex",
    flexWrap: "wrap",
    flex: 1,
    alignItems: "center",
    overflow: "hidden"
  },
  noOptionsMessage: {
    padding: theme.spacing(1, 2)
  },
  singleValue: {
    fontSize: 16
  },
  placeholder: {
    // position: "absolute",
    left: 16,
    bottom: 8,
    fontSize: 16
  },
  paper: {
    position: "absolute",
    marginTop: theme.spacing(1),
    left: 0,
    right: 0,
    zIndex: 10
  }
}));

function NoOptionsMessage(props) {
  return (
    <Typography
      color="textSecondary"
      className={props.selectProps.classes.noOptionsMessage}
      {...props.innerProps}
    >
      {props.children}
    </Typography>
  );
}

NoOptionsMessage.propTypes = {
  /**
   * The children to be rendered.
   */
  children: PropTypes.node,
  /**
   * Props to be passed on to the wrapper.
   */
  innerProps: PropTypes.object.isRequired,
  selectProps: PropTypes.object.isRequired
};

function inputComponent({ inputRef, ...props }) {
  return <div ref={inputRef} {...props} />;
}

inputComponent.propTypes = {
  inputRef: PropTypes.oneOfType([
    PropTypes.func,
    PropTypes.shape({
      current: PropTypes.any.isRequired
    })
  ])
};

function Control(props) {
  const {
    children,
    innerProps,
    innerRef,
    selectProps: { classes, TextFieldProps }
  } = props;

  return (
    <TextField
      fullWidth
      InputProps={{
        inputComponent,
        inputProps: {
          className: classes.input,
          ref: innerRef,
          children,
          ...innerProps
        }
      }}
      {...TextFieldProps}
    />
  );
}

Control.propTypes = {
  /**
   * Children to render.
   */
  children: PropTypes.node,
  /**
   * The mouse down event and the innerRef to pass down to the controller element.
   */
  innerProps: PropTypes.shape({
    onMouseDown: PropTypes.func.isRequired
  }).isRequired,
  innerRef: PropTypes.oneOfType([
    PropTypes.oneOf([null]),
    PropTypes.func,
    PropTypes.shape({
      current: PropTypes.any.isRequired
    })
  ]),
  selectProps: PropTypes.object.isRequired
};

function Placeholder(props) {
  const { selectProps, innerProps = {}, children } = props;
  return (
    <Typography
      color="textSecondary"
      className={selectProps.classes.placeholder}
      {...innerProps}
    >
      {children}
    </Typography>
  );
}

Placeholder.propTypes = {
  /**
   * The children to be rendered.
   */
  children: PropTypes.node,
  /**
   * props passed to the wrapping element for the group.
   */
  innerProps: PropTypes.object,
  selectProps: PropTypes.object.isRequired
};

function SingleValue(props) {
  return (
    <Typography
      className={props.selectProps.classes.singleValue}
      {...props.innerProps}
    >
      {props.children}
    </Typography>
  );
}

SingleValue.propTypes = {
  /**
   * The children to be rendered.
   */
  children: PropTypes.node,
  /**
   * Props passed to the wrapping element for the group.
   */
  // innerProps: PropTypes.any.isRequired,
  selectProps: PropTypes.object.isRequired
};

function ValueContainer(props) {
  return (
    <div className={props.selectProps.classes.valueContainer}>
      {props.children}
    </div>
  );
}

ValueContainer.propTypes = {
  /**
   * The children to be rendered.
   */
  children: PropTypes.node,
  selectProps: PropTypes.object.isRequired
};

function Menu(props) {
  return (
    <Paper
      square
      className={props.selectProps.classes.paper}
      {...props.innerProps}
    >
      {props.children}
    </Paper>
  );
}

Menu.propTypes = {
  /**
   * The children to be rendered.
   */
  children: PropTypes.element.isRequired,
  /**
   * Props to be passed to the menu wrapper.
   */
  innerProps: PropTypes.object.isRequired,
  selectProps: PropTypes.object.isRequired
};

const components = {
  Control,
  Menu,
  // NoOptionsMessage,
  // Placeholder,
  // SingleValue,
  ValueContainer
};

function IntegrationReactSelect(props, context) {
  const searchItems = props.searchItems ? props.searchItems : [];
  const [inputLang, setInputLang] = React.useState("nameEn");

  const items = searchItems.map(item => ({
    value: item[props.valueField ? props.valueField : "_id"],
    label: item[props.labelField ? props.labelField : inputLang]
  }));

  const { selectedData, setSelectedData, intl, customInputProps, disabled } = props;
  const { value, name, onChange, onBlur } = props.input;
  const { touched, invalid, error } = props.meta;

  const classes = useStyles();
  const theme = useTheme();
  const [single, setSingle] = React.useState(selectedData || []);

  React.useEffect(() => {
    setSingle(selectedData);
  }, [selectedData]);

  function handleChangeSingle(newValue) {
    setSingle(newValue);
    if (setSelectedData) setSelectedData(name, newValue);
    if (newValue && newValue.length > 0) {
      const arr = [];
      newValue.map(item => {
        arr.push(item.value);
      });
      onChange(arr);
    } else {
      onChange([]);
    }
  }

  function handleInputChange(newValue, { action }) {
    newValue.toLowerCase().match(/[a-z]/i) || newValue == ""
      ? setInputLang("nameEn")
      : setInputLang("nameZh");
    return newValue;
  }

  const selectStyles = {
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected ? "#33CCCC" : "#fff",
      ":active": {
        backgroundColor: state.isSelected ? "#33CCCC" : "#33CCCC"
      }
    }),
    placeholder: (styles, { isDisabled }) => ({
      ...styles,
      color: isDisabled ? "rgba(0, 0, 0, 0.38)" : "hsl(0,0%,20%)",
    }),
  };

  const noOptionsMessage = inputValue => {
    if (!inputValue) {
      return "It's null";
    }
    return "No results found";
  };

  return (
    <div className={classes.root}>
      <NoSsr>
        <Select
          isMulti
          classes={classes}
          styles={selectStyles}
          inputId={name || null}
          onInputChange={handleInputChange}
          TextFieldProps={{
            label: props.label,
            error: touched && invalid,
            helperText: touched && error,
            InputLabelProps: {
              htmlFor: name || null,
              shrink: true
            },
            margin: "dense",
            variant: context.browserDetect == "mobile" ? "outlined" : "standard",
            ...customInputProps
          }}
          placeholder={
            props.placeholder ||
            intl.formatMessage({
              id: "search.form.select"
            })
          }
          noOptionsMessage={noOptionsMessage}
          options={items}
          components={components}
          value={value !== "" ? single : null}
          onChange={handleChangeSingle}
          isClearable={true}
          isDisabled={disabled}
          onBlur={() => onBlur()}
        />
      </NoSsr>
    </div>
  );
}

IntegrationReactSelect.contextTypes = {
  browserDetect: PropTypes.string
};

export default injectIntl(IntegrationReactSelect);
