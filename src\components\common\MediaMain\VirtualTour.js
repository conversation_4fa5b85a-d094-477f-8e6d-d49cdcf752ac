import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../DetailBoxSection";
import { injectIntl } from "react-intl";

const styles = {
  virtualTourThumbnail: {
    width: "100%",
    cursor: "pointer",
  },
};

class VirtualTour extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    media: PropTypes.object,
    prefix: PropTypes.string,
  };

  render() {
    const {
      classes,
      media,
      prefix,
      intl,
    } = this.props;

    const virtualTours = media.virtualTour || [];

    return (
      <DetailBoxSection
        text={prefix + "Virtual Tour"}
        num={virtualTours.length}
        expandable={true}
        isExpanding={true}
      >
        <Grid container spacing={1}>
          {virtualTours.map((v, i) => (
            <Grid item xs={12} key={v.id}>
              <a
                href={
                  "https://my.matterport.com/show/?m=" + v.virtualTourId
                }
              >
                <img
                  alt=""
                  className={classes.virtualTourThumbnail}
                  src={v.thumbnail}
                />
              </a>
            </Grid>
          ))}
        </Grid>
      </DetailBoxSection>
    );
  }
}
export default withStyles(styles)(injectIntl(VirtualTour));
