import React, { useState } from "react";
import PropTypes from "prop-types";
import { CircularProgress, Grid, Typography } from "@material-ui/core";
import { injectIntl } from "react-intl";
import { connect } from "react-redux";
import _ from "lodash";

import DialogFrame from "../common/DialogFrame";
import Dialog from "../common/Dialog";
import { listLandSearchPdf } from '@/actions/stock';
import SearchConsentDialog from './SearchConsentDialog';
import { takeLandsearchLog } from '@/actions/log';
import FormButton from './FormButton';
import { makeStyles, withStyles } from "@material-ui/core/styles";


const useStyles = makeStyles({
  buttonMain: {
    minWidth: "50px",
    height: "auto",
  },
  title: {
    lineHeight: 1.6,
    fontSize: "1.175em",
    marginBottom: 5,
  },
});

const Loading = withStyles({
  root: {
    width: "18px !important",
    height: "18px !important",
    color: "#FFF",
  },
})(CircularProgress);


function LandSearchPdfDialog({
  open,
  close,
  landSearchPdfList,
  takeLog,
  landSearchRefNo,
  stockId,
  isLoading,
  intl,
}) {
  const classes = useStyles();
  const [consentFile, setConsentFile] = useState(null);

  const renderConsentContent = () => (
    <>
      <p>就此次查閱查冊，本人謹此確認：</p>
      <p>
        (i) 無意及不會違反《個人資料(私隱)條例》(第 486 章) (《私隱條例》)
        的情況下使用透過查閱獲取的個人資料(“該等料”)；
      </p>
      <p>
        (ii)
        無意及不會將該等資料用於與備存及供公眾人士查閱相關紀錄的宗旨無關之目的；及
      </p>
      <p>
        (iii)
        明白本人的個人資料可能就此次查閱查冊會在《私隱條例》許可下被披露或轉交予執法機關。
      </p>
    </>
  );

  const sendLog = (callback, requireHkId, logSeq = null, status = null) =>
    takeLog(
      stockId,
      landSearchRefNo,
      consentFile,
      status,
      logSeq,
    ).then(callback);

  const downloadFile = () => {
    const a = document.createElement("a");
    a.href = consentFile;
    a.target = "_blank";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };
  consentFile && console.log({ consentFile })

  return (
    <>
      <Dialog open={open} handleClose={close} fullWidth={true}>
        <Grid container>
          <Typography className={classes.title}>
            {intl.formatMessage({ id: "stock.landsearch.files" })}
          </Typography>
        </Grid>
        <Grid container spacing={1}>
          {isLoading ? <Loading /> : ((landSearchRefNo && landSearchPdfList && landSearchPdfList[landSearchRefNo]?.length) ?
            landSearchPdfList[landSearchRefNo].map((file, index) => {
              return (
                <Grid item key={index}>
                  <FormButton
                    className={classes.buttonMain}
                    disabled={false}
                    id="unlockStockButton"
                    onClick={() =>
                      setConsentFile(file)
                    }
                  >
                    {intl.formatMessage({
                      id: "stock.landsearch.file",
                    })}{" "}
                    {index + 1}
                  </FormButton>
                </Grid>
              );
            }) :
            (<Grid item>
              {intl.formatMessage({
                id: "stock.landsearch.noFileMsg",
              })}
            </Grid>))}
        </Grid>
        <DialogFrame
          buttonMain={intl.formatMessage({ id: "common.cancel" })}
          handleMain={close}
        />
      </Dialog>

      <SearchConsentDialog
        open={!_.isNull(consentFile)}
        handleClose={() => setConsentFile(null)}
        renderChildren={renderConsentContent}
        okMsg="確認及繼續查閱"
        cancelMsg="取消查閱"
        okCallback={downloadFile}
        sendLog={sendLog}
        isApplyConsent={false}
      />
    </>

  );
}

LandSearchPdfDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  isLoading: PropTypes.bool.isRequired,
  close: PropTypes.func.isRequired,
  takeLog: PropTypes.func.isRequired,
  landSearchRefNo: PropTypes.string.isRequired,
  stockId: PropTypes.string.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  landSearchPdfList: _.get(state.stock, "landSearchPdfList"),
});

const mapDispatchToProps = (dispatch) => ({
  listLandSearchPdf: (args) => dispatch(listLandSearchPdf(args)),
  takeLog: (stockId, refNo, docPath, status, logSeq) =>
    dispatch(
      takeLandsearchLog({ stockId, refNo, docPath, location: "OPEN SEARCH DOCUMENT", status, logSeq }),
    ),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(LandSearchPdfDialog));
