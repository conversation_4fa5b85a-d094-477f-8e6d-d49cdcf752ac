import React from "react";
import {withStyles} from "@material-ui/core/styles";
import {useIntl} from 'react-intl';
import LanguageIcon from "@material-ui/icons/Language";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    display: "flex",
    gap: 2,
  },
  blue: {
    color: "#1890ff",
  },
  yellow: {
    color: "#faad14",
  },
  grey: {
    // color: "#d9d9d9",
  },
  transparent: {
    color: "#666",
  },
  score: {
    lineHeight: "24px"
  }
};


function WWWScoreIcon(props) {
  const intl = useIntl();
  const {classes, isOnline, score, isCurrentAgent} = props;

  return (
    <div className={classes.root}>
      {isOnline ? (<div className={isCurrentAgent ? classes.blue: classes.grey}><LanguageIcon/></div>) : ""}
      {score ? <div className={classes.score}>{score}分</div> : ""}
    </div>
  );
}

export default withStyles(styles)(WWWScoreIcon);
