import React from "react";
import {withStyles} from "@material-ui/core/styles";
import {useIntl} from 'react-intl';
import {get} from "lodash";

const styles = {
  root: {
    width: "max-content",
    padding: 2,
    color: "#888",
    fontSize: "0.8em",
    textAlign: "center",
    borderRadius: 4,
    backgroundColor: "#E3E3E3",
    display: "inline-block",
    margin: 2,
  },
  propertyTags: {
    display: "flex",
    flexWrap: "wrap",
    "& > *:not(:last-child)": {
      marginRight: "1vw",
    },
  },
  blue: {
    backgroundColor: "#1890ff",
    color: "white",
  },
  green: {
    backgroundColor: "#13CE66",
    color: "white",
  },
  grey: {
    backgroundColor: "#d9d9d9",
    color: "#666",
  },
};

function WWWStockTag(props) {
  const intl = useIntl();
  const {
    classes,
    agent = {},       // 单agent模式使用
    agents = [],      // 多agent模式使用
    wwwChannelFull = {},
    uid,
    mode = 'single'  //
  } = props;

  // 处理soleAgent字段同步
  const wwwChannelFullSafe = {
    ...wwwChannelFull,
    soleAgent: get(wwwChannelFull, "soleagent", false),
  };

  // 根据模式决定当前agent
  const currentAgent = mode === 'StockSearchPage'
    ? (Array.isArray(agents) && agents.find(a => a.wwwBy === uid)) || {}
    : agent;

  const getAgentId = (agent) => get(agent, 'wwwBy') || get(agent, 'empId');
  const isCurrentUser = uid === getAgentId(currentAgent);

  // 标签显示逻辑
  const getTagVisibility = (key) => {
    // 搜索模式或当前用户: 显示所有标签
    if (mode === 'StockSearchPage' || isCurrentUser) return true;
    // 单模式其他用户: 只显示true的标签
    return get(currentAgent, key, false);
  };

  // 标签颜色逻辑
  const getTagClassName = (key) => {
    // 当前用户或搜索模式
    if (isCurrentUser || mode === 'StockSearchPage') {
      if (get(currentAgent, key, false)) return 'blue';
      if (get(wwwChannelFullSafe, key, false)) return 'grey';
      return 'green';
    }
    // 其他用户单模式
    return 'grey';
  };

  const tags = [
    { key: "eaaOwner", label: intl.formatMessage({ id: "wwwStock.tag.eaaOwner" }) },
    { key: "soleAgent", label: intl.formatMessage({ id: "wwwStock.tag.soleAgent" }) },
    { key: "video", label: intl.formatMessage({ id: "wwwStock.tag.video" }) },
    { key: "photo", label: intl.formatMessage({ id: "wwwStock.tag.photo" }) },
    { key: "kolVideo", label: intl.formatMessage({ id: "wwwStock.tag.kolVideo" }) },
  ];

  return (
    <div className={classes.propertyTags}>
      {tags.map((tag, index) => (
        getTagVisibility(tag.key) && (
          <span
            key={index}
            className={`${classes.root} ${classes[getTagClassName(tag.key)]}`}
          >
            {tag.label}
          </span>
        )
      ))}
    </div>
  );
}

export default withStyles(styles)(WWWStockTag);
