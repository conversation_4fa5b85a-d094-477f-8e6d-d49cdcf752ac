import React, { useEffect } from "react";
import TextField from "@material-ui/core/TextField";
import Autocomplete from "@material-ui/lab/Autocomplete";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles({
  popupIndicator: {
    color: "#FFF",
  },
});
const CustomAutocompleteField = ({
  input,
  label,
  ranges,
  initialValue,
  defaultValue,
  ...rest
}) => {
  const classes = useStyles();
  const handleChange = (event, value) => {
    input.onChange(value?.value);
  };
  
  // 使用useEffect设置初始值
  useEffect(() => {
    // 优先使用initialValue，其次是defaultValue
    const valueToSet = initialValue !== undefined ? initialValue : defaultValue;
    if (valueToSet !== undefined && !input.value) {
      input.onChange(valueToSet);
    }
  }, [initialValue, defaultValue, input]);

  const initialSelectedValue = ranges.find(option => option.value === input.value);

  return (
    <Autocomplete
      {...rest}
      options={ranges}
      getOptionLabel={(option) => option.label}
      value={initialSelectedValue || ''}
      onChange={handleChange}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          variant="outlined"
          margin="normal"
          fullWidth
        />
      )}
      classes={{ 
        popupIndicator: classes.popupIndicator,
      }}
    />
  );
};

export default CustomAutocompleteField;