import React from "react";
import { connect } from "react-redux";
import { Field, FieldArray } from "redux-form";
import { injectIntl } from "react-intl";
import { withStyles } from "@material-ui/core/styles";
import Grid from "@material-ui/core/Grid";
import ProposalFormPaper from "../../../../common/ProposalFormPaper";
import InlineTextField from "../../../../common/InlineTextField";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import InputWithCheckBox from "../../../../common/InputWithCheckBox";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    paddingTop: "1vh",
  },
};

function FieldSection(props) {
  const {
    classes,
    intl,
    changeFieldValue,
  } = props;

  return (
    <div className={classes.root}>
      <DetailBoxSection
        text={intl.formatMessage({
          id: "stock.photo.buildinginfo",
        })}
        expandable={true}
      >
        <ProposalFormPaper>
          <Grid container spacing={1}>
            <Grid item xs={6}>
              <FieldArray
                name="usage"
                label={intl.formatMessage({
                  id: "proposal.form.buildingusage",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={2}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                disabled
              />
            </Grid>

            <Grid item xs={6}>
              <FieldArray
                name="title"
                label={intl.formatMessage({
                  id: "proposal.form.title",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={2}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                disabled
              />
            </Grid>

            <Grid item xs={6}>
              <FieldArray
                name="inTakeDate"
                label={intl.formatMessage({
                  id: "proposal.form.completiondate",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={2}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                disabled
              />
            </Grid>

            <FieldArray
              name="developers"
              label={intl.formatMessage({
                id: "search.form.developer",
              })}
              component={InputWithCheckBox}
              checkboxInFront={true}
              aligntoLabel={true}
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              xs={6}
              disabled
            />

            <Grid item xs={12}>
              <FieldArray
                name="managementCompany"
                label={intl.formatMessage({
                  id: "building.mgtcompany",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={1}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                disabled
              />
            </Grid>

            <Grid item xs={12}>
              <FieldArray
                name="transport"
                label={intl.formatMessage({
                  id: "proposal.form.transport",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={1}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                disabled
              />
            </Grid>

            <Grid item xs={6}>
              <FieldArray
                name="passengerLift"
                label={intl.formatMessage({
                  id: "building.passengerlift",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={2}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                disabled
              />
            </Grid>

            <Grid item xs={6}>
              <FieldArray
                name="cargoLift"
                label={intl.formatMessage({
                  id: "building.cargolift",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={2}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                disabled
              />
            </Grid>

            <Grid item xs={6}>
              <FieldArray
                name="airConditioningType"
                label={intl.formatMessage({
                  id: "stock.airconditioningtype",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={2}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                disabled
              />
            </Grid>

            <Grid item xs={6}>
              <FieldArray
                name="haveCarPark"
                label={intl.formatMessage({
                  id: "tips.stocktag.carpark",
                })}
                component={InputWithCheckBox}
                checkboxInFront={true}
                aligntoLabel={true}
                checkboxXs={2}
                renderComponent={InlineTextField}
                changeFieldValue={changeFieldValue}
                disabled
              />
            </Grid>

          </Grid>
        </ProposalFormPaper>
      </DetailBoxSection>
    </div>
  );
}

const mapStateToProps = (state) => ({
});

const mapDispatchToProps = (dispatch) => {
  return {

  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(FieldSection)));
