import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { MuiThemeProvider, createMuiTheme } from "@material-ui/core/styles";
import {  AppBar, Tabs, Tab, Typography } from "@material-ui/core";
import { withStyles, createStyles, } from "@material-ui/core/styles";
import _ from "lodash";
import { FormattedMessage, injectIntl } from "react-intl";

import StockDetailGeneral from "./General";
import UpdateHistory from "./UpdateHistory";
import DetailHandController from "../../../common/DetailHandController";
import DetailBtnController from "../../../common/DetailBtnController";
import DetailStatusBar from "../../../common/DetailStatusBar";
import {
  getReverseLangKey,
  getDisplayStockId,
  getHandsMapping,
} from "../../../../helper/generalHelper";
import Media from "../../../Media";
import { getStatusDisplay } from "./constants";
import Saleskit from "../../../Saleskit";
import ApplySearch from "../../../ApplySearch";
import { gtagHandler, addActivityLog } from "../../../../actions/log";
import { queryWWWUsedCount } from "@/actions/wwwStock";

function TabPanel(props) {
  const { children, value, index, ...other } = props;
  const [loaded, setLoaded] = React.useState(false);

  if (!loaded && value === index) setLoaded(true);

  return (
    <Typography
      component="div"
      role="tabpanel"
      hidden={value !== index}
      id={`full-width-tabpanel-${index}`}
      aria-labelledby={`full-width-tab-${index}`}
      {...other}
    >
      {loaded ? <>{children}</> : null}
    </Typography>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.any.isRequired,
  value: PropTypes.any.isRequired,
};

function a11yProps(index) {
  return {
    id: `full-width-tab-${index}`,
    "aria-controls": `full-width-tabpanel-${index}`,
  };
}

const styles = createStyles((theme) => ({
  root: {
    display: "flex",
    flexDirection: "column",
    overflow: "hidden",
  },
  bar: {
    // height: 57,
    boxShadow: "none",
    backgroundColor: "rgba(0, 0, 0, .6)",
    position: "relative",
    top: 0,
    left: 0,
    zIndex: 900,
  },
  tabAlwaysActive: {
    minHeight: 57,
    paddingTop: 6,
    backgroundColor: "#33CCCC",
  },
  uploadIcon: {
    width: 42,
    height: 30,
  },
  uploadIconSvg: {
    maxWidth: 42,
    maxHeight: 30,
  },
  grey: {
    "&.Mui-selected": {
      backgroundColor: "#f5f5f5",
    },
  },
}));

const theme = createMuiTheme({
  overrides: {
    MuiTabs: {
      root: {
        minHeight: 36,
      },
      indicator: {
        height: 0,
        backgroundColor: "#33CCCC",
      },
    },
    MuiTab: {
      root: {
        flex: "1 0 auto",
        minHeight: 36,
        fontSize: "0.875em",
        lineHeight: 1.2,
        textTransform: "none",
        padding: "6px 8px",
        "&$selected": {
          backgroundColor: "#FFF",
          color: "rgba(0, 0, 0, .6)",
        },
      },
      textColorInherit: {
        color: "#FFF",
        opacity: 1,
      },
      wrapper: {
        "&& > *:first-child": {
          marginBottom: 0,
        },
      },
    },
  },
});

function Stock({
  defaultTab,
  detail,
  currentDetail,
  classes,
  favoriteStockIds,
  markStockIds,
  headerRef,
  multipleStocks,
  mode,
  gtagHandler,
  addActivityLog,
  intl,
  employee,
  queryWWWUsedCount,
}) {
  const [value, setValue] = useState(defaultTab || 0);
  const [hand, setHand] = useState(-1);

  const tabConfigs = [
    { labelKey: 'stock.general' },
    { labelKey: 'stock.updatehistory'},
    { labelKey: 'stock.media'},
    { labelKey: 'stock.saleskit', className: classes.grey },
    { labelKey: 'stock.applySearch', className: classes.grey },
  ];

  const currentStock =
    _.find(detail, (stock) => stock._id === currentDetail) ||
    _.get(detail, "[0]", {});

  useEffect(() => {
    if (!_.isEmpty(currentStock)) {
      const reverseLangKey = getReverseLangKey(intl);

      // update header
      const id =
        currentStock.unicorn && Number.isInteger(currentStock.unicorn.id)
          ? currentStock.unicorn.id
          : null;
      const buildingName = _.get(currentStock, `building[${reverseLangKey}]`);
      const unit = _.get(currentStock, "unit", "---");
      const floor = _.get(currentStock, "floor", "---");
      let concatHeader = buildingName + " " + floor + ", " + unit;
      concatHeader = _.truncate(concatHeader, { length: 25 });

      if (id !== null && headerRef.current) headerRef.current.changeTitle(concatHeader);
      if (id !== null && headerRef.current) headerRef.current.changeSubTitle(getDisplayStockId(id));

      // set the default hand to be the latest hand
      // setHand(getHandsMapping(currentStock.handsInfo).length);
      gtagHandler("View Stock", {
        StockID: getDisplayStockId(id),
      });
    }
  }, [currentStock]);

  React.useEffect(() => {
    if (employee?._id) {
      const { _id: emp_id } = employee;
      Promise.all([
        queryWWWUsedCount({ emp_id }, { intl }),
      ]);
    }
  }, [employee, queryWWWUsedCount]);

  const handleChange = (event, value) => {
    // dont change the content if 'upload media' button (index: 3) is clicked
    const selectedTabLabelKey = tabConfigs[value].labelKey;
    addActivityLog(`tab.property.${selectedTabLabelKey}`, "read");

    if (value === 5) return;

    setValue(value);
  };

  const handleHandChange = (hand) => {
    if (!isNaN(parseInt(hand))) setHand(parseInt(hand));
  };

  const getStockId = (detail) => {
    return detail.unicorn && Number.isInteger(detail.unicorn.id)
      ? detail.unicorn.id
      : null;
  };

  const sourceLangKey = intl.locale === "zh" ? "sourceZh" : "source";

  const buildingId = _.get(currentStock, "building.unicorn.id", null);

  const stockId = getStockId(currentStock);
  const status = _.get(currentStock, "status", null);

  const buildingUsage = _.defaultTo(
    _.get(currentStock, `building.buildingUsage${intl.locale === "zh" ? "Zh" : ""}`),
    "---",
  );
  const source = currentStock[sourceLangKey] || "---";

  const statusDisplay = getStatusDisplay(status, intl);

  // get the range of hands
  // let hands = [1];
  // if (currentStock && currentStock.handsInfo && currentStock.handsInfo.length > 0) {
  //   hands = currentStock.handsInfo.map(v => v.hands);
  // }
  // let maxHand = Math.max(...hands);
  let ranges = [...Array(currentStock.currentHands || 1).keys()]
    .map((v) => v + 1)
    .map((v) => {
      return { value: parseInt(v), label: parseInt(v) };
    });
  ranges.unshift({
    value: -1,
    label: intl.formatMessage({ id: "search.form.all" }),
  });

  const handController = (
    <DetailHandController
      ranges={ranges}
      hand={hand}
      handleChange={handleHandChange}
      status={statusDisplay}
      usageOrType={buildingUsage}
      source={source}
      mongoid={currentStock._id}
      stockid={stockId}
      favoriteStockIds={favoriteStockIds}
      currentHand={currentStock.currentHands}
    />
  );

  const btnController = (
    <DetailBtnController
      status={statusDisplay}
      mongoid={currentStock._id}
      stockid={stockId}
      favoriteStockIds={favoriteStockIds}
      markStockIds={markStockIds}
    />
  )

  const statusBar = (
    <DetailStatusBar
      currentHand={currentStock.currentHands}
      status={status}
      usageOrType={buildingUsage}
      source={source}
      mongoid={currentStock._id}
      stockid={stockId}
      favoriteStockIds={favoriteStockIds}
      markStockIds={markStockIds}
    />
  );

  return (
    <div className={classes.root}>
      <MuiThemeProvider theme={theme}>
        <AppBar position="static" className={classes.bar}>
        {!multipleStocks && (
          <Tabs
            value={value}
            onChange={handleChange}
            variant="fullWidth"
            aria-label="full width tabs example"
            centered
          >
            {tabConfigs.map((tab, i) => {
              return (
                <Tab
                  labelKey={tab.labelKey}
                  label={<FormattedMessage id={tab.labelKey} />}
                  className={tab.className}
                  {...a11yProps(i)}
                />
              )
            })}
          </Tabs>
        )}
        </AppBar>
      </MuiThemeProvider>
      <TabPanel value={value} index={0}>
        <StockDetailGeneral handController={handController} btnController={btnController}/>
      </TabPanel>
      <TabPanel value={value} index={1}>
        <UpdateHistory
          handController={handController}
          hand={hand}
          handsMapping={currentStock.handsInfo}
        />
      </TabPanel>
      <TabPanel value={value} index={2}>
        <Media stockId={stockId} buildingId={buildingId} />
      </TabPanel>
      <TabPanel value={value} index={3}>
        <Saleskit multipleStocks={multipleStocks} mode={mode} />
      </TabPanel>
      <TabPanel value={value} index={4}>
        <ApplySearch />
      </TabPanel>
    </div>
  );
}

Stock.propTypes = {
  detail: PropTypes.array,
  currentDetail: PropTypes.string.isRequired,
  favoriteStockIds: PropTypes.array.isRequired,
  markStockIds: PropTypes.array.isRequired,
  employee: PropTypes.object,
  queryWWWUsedCount: PropTypes.func,
};

const mapStateToProps = (state) => ({
  detail: _.get(state, "stock.detail") || [],
  currentDetail: _.get(state, "stock.currentDetail") || "",
  favoriteStockIds: _.get(state, "stock.favoriteStockIds") || [],
  markStockIds: _.get(state, "stock.markStockIds") || [],
  employee: _.get(state, "employee.employees.0"),
});

const mapDispatchToProps = (dispatch) => {
  return {
    gtagHandler: (...args) => dispatch(gtagHandler(...args)),
    addActivityLog: (...args) => dispatch(addActivityLog(...args)),
    queryWWWUsedCount: (...args) => dispatch(queryWWWUsedCount(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(Stock)));
