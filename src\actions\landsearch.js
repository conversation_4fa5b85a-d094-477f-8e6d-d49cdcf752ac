import _ from "lodash";
import { reset } from "redux-form";

import {
  CREATE_APPLY_SEARCH,
  CREATE_APPLY_SEARCH_SUCCESS,
  CREATE_APPLY_SEARCH_ERROR,
  LIST_DD_TYPES,
  LIST_DD_TYPES_ERROR,
  LIST_DD_TYPES_SUCCESS,
  CLEAR_APPLY_SEARCH_STATE,
} from "@/constants/landsearch";

export const queryDDType =
  () =>
  async (dispatch, getState, { graphqlRequest, api, getQuery, universalRequest }) => {
    try {
      dispatch({
        type: LIST_DD_TYPES,
      });

      const query = await getQuery("QUERY_DD_TYPE");

      const res = await universalRequest("/landsearch/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: {},
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });


      if (res.errors) {
        throw new Error(res.errors[0].message);
      }

      dispatch({
        type: LIST_DD_TYPES_SUCCESS,
        payload: {
          ddTypes: _.get(res, "data.ddTypeLookup"),
        },
      });
    } catch (e) {
      console.error(e);
      dispatch({
        type: LIST_DD_TYPES_ERROR,
        payload: {
          error: e,
        },
      });
    }
  };

export const applySearchDetail =
  (form) =>
  async (dispatch, getState, { universalRequest }) => {
    dispatch({
      type: CREATE_APPLY_SEARCH,
    });

    try {
      const res = await universalRequest("/applyLandSearch", {
        method: "POST",
        headers: {
          "content-type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
        body: JSON.stringify({ details: form }),
      });

      if (res.status !== 200 || !_.isNil(_.get(res, "errors.0"))) {
        dispatch({
          type: CREATE_APPLY_SEARCH_ERROR,
          payload: {
            errors: res.errors[0].message,
          },
        });
      } else {
        dispatch({
          type: CREATE_APPLY_SEARCH_SUCCESS,
        });
      }
    } catch (e) {
      console.error(e);
      dispatch({
        type: CREATE_APPLY_SEARCH_ERROR,
        payload: {
          errors: e,
        },
      });
    }
  };

export const clearApplySearch = (resetForm) => (dispatch) => {
  if (resetForm) dispatch(reset("applySearch"));
  dispatch({
    type: CLEAR_APPLY_SEARCH_STATE,
  });
};
