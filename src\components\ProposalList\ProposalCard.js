import React, { useState } from "react";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/core/styles";
import { injectIntl, FormattedMessage } from "react-intl";
import moment from "moment";
import _ from "lodash";
import DeleteIcon from "@material-ui/icons/Delete";
import LaunchIcon from "@material-ui/icons/Launch";

import { link, sbu } from "../../config";
import SubmitDialog from "../common/SubmitDialog";
import { gtagHandler, takeLog } from "../../actions/log";
import { getDisplayStockId } from "../../helper/generalHelper";
import UnlockDialogItem from "../common/UnlockDialogItem";
import history from "../../core/history";

const styles = (theme) => ({
  root: {
    borderRadius: 4,
    padding: "1vw 2vw",
    backgroundColor: "#FFF",
    display: "flex",
  },
  date: {
    fontSize: ".875em",
    color: "#777",
  },
  name: {
    fontSize: "1.175em",
    wordBreak: "break-word",
  },
  link: {
    color: "inherit",
    textDecoration: "none",
    flex: 1,
  },
  binIcon: {
    margin: "auto 0 auto 1vw",
  },
  centerText: {
    textAlign: "center",
  },
  listProposalTag: {
    backgroundColor: "#FFFC00",
    fontWeight: 400,
    fontSize: "14px",
    borderRadius: "4px",
    padding: "0 2vw",
    marginLeft: 5,
    color: "#000000",
  },
});

function ProposalCard({
  classes,
  detail,
  className,
  unlockedStockIds,
  removeProposal,
  removingProposal,
  removedProposal,
  removeProposalError,
  takeLog,
  gtagHandler,
  intl,
}) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [unlockDialogOpen, setUnlockDialogOpen] = useState(false);

  const isListProposal = !!detail.isListProposal;

  const handleClickOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const handleClickOpenUnlockDialog = () => {
    setUnlockDialogOpen(true);
  };

  const handleCloseUnlockDialog = () => {
    setUnlockDialogOpen(false);
  };

  const submitDialogCallback = () => {
    window.location.reload();
  };

  const submitRemoveProposalDialog = async (hash) => {
    await removeProposal({ hash, isListProposal });
  };

  const handleOnClickDownload = (id) => {
    const msg = JSON.stringify({
      action: "download-proposal",
      stockId: id,
      mongoId: detail.id,
      proposalName: detail.proposalName,
    });
    window.dataLayer.push({ stockId: id });
    takeLog(msg);

    const ids = _.map(_.get(detail, "proposals"), v => getDisplayStockId(v.stockId));
    gtagHandler("Download Proposal", {
      StockID: isListProposal ? _.join(ids, ","): id,
    });
  };

  const goStockDetail = () => {
    const ids = isListProposal
      ? detail.proposals.map((pp) => pp.stockMongoId)
      : [detail.stockMongoId];
    const mode = detail.mode ? detail.mode : isListProposal ? "list" : "indv";
    history.push(
      `/stock?mode=${mode}&ids=${encodeURIComponent(JSON.stringify(ids))}&defaultTab=3&isReCreate=1&hash=${detail.urlHash}&isListProposal=${isListProposal ? "1" : "0"}`,
    );
  };

  const proposalName = detail.proposalName || "---";
  const createdDate = detail.createdDate
    ? moment
        .utc(detail.createdDate, "ddd MMM DD YYYY HH:mm:ss")
        .utcOffset(8)
        .format("YYYY-MM-DD HH:mm")
    : "";
  const hash = detail.urlHash || "";
  const displayStockId = detail.stockId
    ? getDisplayStockId(detail.stockId)
    : "";
  const mongoId = detail.stockMongoId;
  const isUnlocked = isListProposal
    ? _.every(
        detail.proposals.map((pp) =>
          unlockedStockIds.includes(pp.stockMongoId),
        ),
        Boolean,
      )
    : unlockedStockIds.indexOf(mongoId) >= 0;

  const salesmanname = detail.salesman.nameEn
    ? detail.salesman.nameEn
    : detail.salesman.nickname
    ? detail.salesman.nickname
    : null;

  let param1 = "";
  if (!isListProposal) {
    if (sbu === "SHOPS") {
      param1 += _.get(detail, "streetNameZh") || "";
      param1 += _.get(detail, "streetNo") ? `${detail.streetNo}號` : "";
      param1 +=
        _.get(detail, "unit.isShow") && _.get(detail, "unit.value")
          ? `${detail.unit.value}號舖`
          : "";
    } else {
      const actualFloor = _.get(detail, "floorType.nameEn") === "Actual Floor";
      const floor =
        _.get(detail, "floor.isShow") && _.get(detail, "floor.value")
          ? `${detail.floor.value}樓`
          : "";

      param1 += _.get(detail, "building.nameZh") || "";
      param1 += actualFloor ? floor : _.get(detail, "floorType.nameZh");
      param1 +=
        _.get(detail, "unit.isShow") && _.get(detail, "unit.value")
          ? `${detail.unit.value}室`
          : "";
    }
  }

  return (
    <div className={`${classes.root} ${className}`}>
      <a
        href={`${link.proposalPdf.prefix}/${hash}${
          isListProposal
            ? link.proposalPdf.listProposalSuffix
            : link.proposalPdf.suffix
        }&param1=${
          isListProposal
            ? "ListProposal"
            : encodeURIComponent(param1.replaceAll("/", ""))
        }&name=${salesmanname}&ref=${
          isListProposal
            ? detail.proposals
                .map((pp) => getDisplayStockId(pp.stockId))
                .join(" - ")
            : displayStockId
        }${detail.useNameAsPdf ? `&pdfName=${detail.proposalName}` : ''}`}
        onClick={() => handleOnClickDownload(displayStockId)}
        target="_blank"
        className={classes.link}
        rel="noreferrer"
      >
        <div className={classes.name}>{proposalName} </div>
        <div className={classes.date}>
          {createdDate}
          <span className={classes.listProposalTag}>
            {isListProposal ? (
              <span>
                <FormattedMessage id="proposal.listProposal" />
                {detail.mode === "indv" && (
                  <>
                    (<FormattedMessage id="proposal.listProposal.indv" />)
                  </>
                )}
              </span>
            ) : (
              <FormattedMessage id="proposal.indvProposal" />
            )}
          </span>
        </div>
      </a>

      <div
        className={classes.binIcon}
        onClick={isUnlocked ? goStockDetail : handleClickOpenUnlockDialog}
      >
        <LaunchIcon />
      </div>
      <div className={classes.binIcon}>
        <DeleteIcon onClick={handleClickOpenDialog} />
      </div>

      <SubmitDialog
        dialogOpen={dialogOpen}
        handleCloseDialog={handleCloseDialog}
        submitting={removingProposal}
        submitted={removedProposal}
        succCallback={submitDialogCallback}
        errCallback={submitDialogCallback}
        error={removeProposalError}
        submit={() => {
          submitRemoveProposalDialog(hash);
        }}
        submitBtnText={intl.formatMessage({ id: "common.ok" })}
        succMsg={intl.formatMessage({ id: "proposal.removepdf.success" })}
      >
        <div className={classes.centerText}>
          {intl.formatMessage(
            { id: "proposal.removepdf" },
            { filename: detail.proposalName },
          )}
        </div>
      </SubmitDialog>

      <UnlockDialogItem
        dialogOpen={unlockDialogOpen}
        handleCloseDialog={handleCloseUnlockDialog}
        stockid={
          isListProposal
            ? detail.proposals
                .filter((pp) => !unlockedStockIds.includes(pp.stockMongoId))
                .map((pp) => pp.stockMongoId)
            : mongoId
        }
        stockunicornid={isListProposal ? "" : detail.stockId}
        unlock4ProposalCb={goStockDetail}
        defaultTab={3}
      />
    </div>
  );
}

const mapStateToProps = (state) => ({
  unlockedStockIds: _.get(state, "stocklist.unlockedStockIds") || [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    takeLog: (msg) => dispatch(takeLog(msg)),
    gtagHandler: (...args) => dispatch(gtagHandler(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(ProposalCard)));
