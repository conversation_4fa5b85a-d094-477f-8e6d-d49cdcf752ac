import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";

// We can inject some CSS into the DOM.
const styles = {
  container: {
    lineHeight: "25px",
    fontSize: "0.875em",
    position: "relative",
    display: "inline-block",
    cursor: "pointer"
  },
  bg: {
    height: "25px",
    color: "#717171",
    borderRadius: "12.5px",
    background: "#DFDFDF",
    display: "inline-flex",
    overflow: "hidden"
  },
  btnText: {
    fontSize: 14,
    maxWidth: "100%",
    padding: "0 13px",
    overflow: "hidden",
    transition: "color .3s ease, max-width .3s ease, padding .3s ease"
  },
  togglerLayer: {
    width: "100%",
    height: "25px",
    color: "transparent",
    position: "absolute",
    top: 0,
    left: 0,
    display: "inline-flex",
    overflow: "hidden"
  },
  zero: {
    maxWidth: 0,
    padding: 0
  },
  toggler: {
    height: "25px",
    borderRadius: "12.5px",
    backgroundColor: "#FFB817",
    flex: 1
  },
  disabled: {
    backgroundColor: "#888"
  },
  switchOff: {
    backgroundColor: "#959595"
  },
  cover: {
    width: "100%",
    height: "25px",
    color: "#FFF",
    position: "absolute",
    top: 0,
    left: 0,
    display: "inline-flex",
    overflow: "hidden"
  },
  hideText: {
    color: "#717171"
  }
};

function Switch(props) {
  // const {
  //   input: { value, onChange }
  // } = props;
  const {
    classes,
    textL,
    textR,
    className,
    disabled,
    onChange,
    onOffSwitch, // for onOffSwitch, toggler will turn grey when switch OFF
    ...other
  } = props;
  let value = props.value ? props.value : "";

  let { valleft, valright } = props;
  if (typeof valleft === "undefined") valleft = textL;
  if (typeof valright === "undefined") valright = textR;

  function handleClick() {
    if (disabled !== true) onChange(value === valleft ? valright : valleft);
  }

  return (
    <div className={className} {...other}>
      <div className={classes.container} onClick={handleClick}>
        <div className={classes.bg}>
          <div className={classes.btnText}>{textL}</div>
          <div className={classes.btnText}>{textR}</div>
        </div>
        <div className={classes.togglerLayer}>
          <div
            className={`${classes.btnText} ${
              value === valleft ? classes.zero : ""
            }`}
          >
            {textL}
          </div>
          <div
            className={`${classes.toggler} ${
              disabled === true ? classes.disabled : ""
            } ${
              onOffSwitch && value === valleft ? classes.switchOff : ""
            }`}
          />
          <div
            className={`${classes.btnText} ${
              value === valright ? classes.zero : ""
            }`}
          >
            {textR}
          </div>
        </div>
        <div className={classes.cover}>
          <div
            className={`${classes.btnText} ${
              value === valright ? classes.hideText : ""
            }`}
          >
            {textL}
          </div>
          <div
            className={`${classes.btnText} ${
              value === valleft ? classes.hideText : ""
            }`}
          >
            {textR}
          </div>
        </div>
      </div>
    </div>
  );
}

Switch.propTypes = {
  classes: PropTypes.object.isRequired,
  textL: PropTypes.string,
  textR: PropTypes.string,
  // onChange: PropTypes.func.isRequired,
  // input: PropTypes.object.isRequired,
  className: PropTypes.string
};

export default withStyles(styles)(Switch);
