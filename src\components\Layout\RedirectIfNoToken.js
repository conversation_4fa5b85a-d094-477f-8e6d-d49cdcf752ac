import React, { useState, useEffect } from "react";
import { connect } from "react-redux";
import { injectIntl } from "react-intl";
import _ from "lodash";
import axios from "axios";
import { setTokensAndPermission } from "@/actions/auth";
import { sso, publicHost, cas_client_id, cas_client_secret, mode } from "@/config";

const RedirectIfNoToken = ((props) => {
  const {
    redirectTargetPrefix = `${sso}/oauth2.0/authorize?response_type=code&client_id=${cas_client_id}&redirect_uri=`,
    setTokensAndPermission
  } = props;

  useEffect(() => {
    const redirectUrl = mode === "production" ? `${window.location.protocol}//${window.location.hostname}` : publicHost;
    const params = typeof window !== 'undefined' ? new URL(document.location).searchParams : null;
    const accessToken = localStorage.getItem("casAccessToken");

    // Check if URL has a 'code' parameter
    if (params.has("code")) {
      axios
        .post(
          `${sso}/oauth2.0/accessToken?grant_type=authorization_code&client_id=${cas_client_id}&client_secret=${encodeURIComponent(
            cas_client_secret
          )}&code=${_.last(
            params.getAll("code"),
          )}&redirect_uri=${redirectUrl}`
        )
        .then((res) => {
          // Store access token in localStorage
          if (res.data.access_token) {
            setTokensAndPermission(res.data);
            // Optionally clear URL parameters after successful token retrieval
            window.history.replaceState({}, document.title, window.location.pathname);
          }
        })
        .catch((error) => {
          console.log({ error });
          localStorage.removeItem("casAccessToken");
          localStorage.removeItem("casRefreshToken");
        });
    } else if (!accessToken) {
      window.location =
        redirectTargetPrefix +
        encodeURIComponent(redirectUrl);
    }
  }, []);

  return null; // Return null since this component only handles side effects
});


const mapStateToProps = (state) => {
  return {};
};

const mapDispatchToProps = (dispatch) => {
  return {
    setTokensAndPermission: (...args) => dispatch(setTokensAndPermission(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)((injectIntl(RedirectIfNoToken)));
