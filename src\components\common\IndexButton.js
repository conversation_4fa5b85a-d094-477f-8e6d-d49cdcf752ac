import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import FormButton from "./FormButton";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    width: "100%",
    height: "auto",
    color: "#FFF",
    padding: "10px",
    boxShadow: "none",
    backgroundColor: "transparent",
    textTransform: "none",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    "&:hover": {
      color: "#FFF",
      backgroundColor: "transparent"
    },
    "&:disabled": {
      color: "#FFF",
      opacity: 0.4
    }
  },
  icon: {
    width: "100%"
  },
  text: {
    fontSize: "1.3em"
  }
};

function IndexButton(props) {
  const { classes, children, className, icon, ...other } = props;

  return (
    <FormButton className={clsx(classes.root, className)} {...other}>
      <div>
        <div className={classes.icon}>{icon}</div>
        <div className={classes.text}>{children}</div>
      </div>
    </FormButton>
  );
}

IndexButton.propTypes = {
  children: PropTypes.node,
  icon: PropTypes.node,
  classes: PropTypes.object.isRequired,
  className: PropTypes.string
};

export default withStyles(styles)(IndexButton);
