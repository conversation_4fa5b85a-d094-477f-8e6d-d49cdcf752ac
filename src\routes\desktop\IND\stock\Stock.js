/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import withStyles from "isomorphic-style-loader/lib/withStyles";
import StockDetail from "../../../../components/desktop/IND/Stock";
import s from "./Stock.css";
import { listStockDetail, clearStock, listmyFavorite } from "../../../../actions/stock";

class Stock extends React.Component {
  static propTypes = {
    listStockDetail: PropTypes.func.isRequired,
    clearStock: PropTypes.func.isRequired
  };

  constructor(props) {
    super(props);
  }

  componentDidMount() {
    const variables = {
      _id: this.props.stockid.id
    };
    this.props.listStockDetail(variables);
    this.props.listmyFavorite();
  }

  componentWillUnmount() {
    // this.props.clearStock();
  }

  render() {
    const { headerRef, defaultTab } = this.props;

    return (
      <div className={s.root}>
        <div className={s.div}>
          <div>
            <StockDetail headerRef={headerRef} defaultTab={defaultTab} />
          </div>
        </div>
      </div>
    );
  }
}

const mapDispatchToProps = dispatch => {
  return {
    listStockDetail: graphqlvariable =>
      dispatch(listStockDetail(graphqlvariable)),
    listmyFavorite: () => dispatch(listmyFavorite()),
    clearStock: () => dispatch(clearStock())
    // listBuildingDetails: (graphqlvariable) => dispatch(listBuildingDetails(graphqlvariable))
  };
};

export default connect(null, mapDispatchToProps)(withStyles(s)(Stock));
