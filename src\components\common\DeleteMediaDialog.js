import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
// import { submit, touch, getFormSyncErrors, formValueSelector, change } from 'redux-form';
// import {
//   clearProgressingMedium,
//   createMedium,
// } from "../../../actions/medium";
import Dialog from "./Dialog";
import DialogFrame from "./DialogFrame";
import { MuiThemeProvider, createMuiTheme } from "@material-ui/core/styles";
import {
  clearProgressingMedium,
  deleteMedium
} from "../../actions/medium";
import { withStyles } from "@material-ui/styles";
import CircularProgress from "@material-ui/core/CircularProgress";
import { injectIntl } from "react-intl";
import { listStockMedia } from "../../actions/stock";

const theme = createMuiTheme({
  overrides: {
    MuiOutlinedInput: {
      root: {
        "& $notchedOutline": {
          borderColor: "#FFF"
        },
        "&:hover $notchedOutline": {
          borderColor: "#FFF"
        },
        "&.Mui-focused $notchedOutline": {
          borderColor: "#FFF"
        }
      }
    },
    MuiInputBase: {
      root: {
        color: "#FFF"
      }
    },
    MuiFormLabel: {
      root: {
        color: "#FFF",
        "&.Mui-focused": {
          color: "#FFF"
        }
      }
    },
    MuiSelect: {
      icon: {
        color: "#FFF"
      }
    },
    circularLoader: {
      textAlign: "center",
      "& > div": {
        color: "#FFF"
      }
    }
  }
});

const styles = theme => ({
  font18: {
    fontSize: "1.125em",
    textAlign: "center"
  },
  circularLoader: {
    textAlign: "center",
    "& > div": {
      color: "#FFF"
    }
  },
  errortext: {
    color: "#F44336",
    fontSize: "0.875em",
    textAlign: "center",
    marginTop: "2vh"
  }
});

class DeleteMediaDialog extends React.Component {
  static propTypes = {
    dialogOpen: PropTypes.bool,
    handleCloseDialog: PropTypes.func,
    deletingMedium: PropTypes.bool,
    deletedMedium: PropTypes.bool,
    deleteMedium: PropTypes.func.isRequired,
    callback: PropTypes.func
  };

  constructor(props) {
    super(props);
    this.state = {
      step: 1
    };
  }

  callbackAfterDelete = () => {
    const { detail, listStockMedia, userInfo } = this.props;

    if (detail && detail.unicorn && Number.isInteger(detail.unicorn.id)) {
      let variables = {
        sid: detail.unicorn.id.toString(),
        empId: userInfo.emp_id,
      };
      listStockMedia(variables, 5000);
    }
  };

  resetAndClose = () => {
    this.props.handleCloseDialog();
    this.props.deletedMedium && this.callbackAfterDelete(); // load images after deleted
    this.props.deletedMedium && this.props.callback && this.props.callback(); // extra callback if needed
    this.props.clearProgressingMedium();
  };

  removeMedium(id) {
    this.props.deleteMedium(id);
  }

  render() {
    const {
      classes,
      dialogOpen,
      deletingMedium,
      deletedMedium,
      deleteError,
      intl
    } = this.props;

    const mediaId = this.props.mediaId ? this.props.mediaId : null;

    return (
      <Dialog
        open={dialogOpen}
        handleClose={this.resetAndClose}
        fullWidth={true}
      >
        <MuiThemeProvider theme={theme}>
          {deletedMedium ? (
            <DialogFrame
              buttonMain={intl.formatMessage({
                id: "common.ok"
              })}
              handleMain={this.resetAndClose}
            >
              <div className={classes.font18}>
                {intl.formatMessage({
                  id: "stock.photo.removesuccess"
                })}
              </div>
            </DialogFrame>
          ) : (
            <DialogFrame
              buttonMain={
                !deletingMedium &&
                intl.formatMessage({
                  id: "common.remove"
                })
              }
              handleMain={() => this.removeMedium(mediaId)}
            >
              <div className={classes.font18}>
                {intl.formatMessage({
                  id: "stock.photo.removemessage"
                })}
              </div>
              {deleteError && (
                <div className={classes.errortext}>
                  Only personal photo can be removed.
                </div>
              )}
              {deletingMedium && (
                <div className={classes.circularLoader}>
                  <CircularProgress />
                </div>
              )}
            </DialogFrame>
          )}
        </MuiThemeProvider>
      </Dialog>
    );
  }
}

const mapStateToProps = state => ({
  userInfo:
    state.auth &&
    state.auth.user &&
    state.auth.user.login &&
    state.auth.user.login.info
      ? state.auth.user.login.info
      : {},
  detail: state.stock.detail ? state.stock.detail : {},
  deletingMedium: state.medium.deletingMedium
    ? state.medium.deletingMedium
    : false,
  deletedMedium: state.medium.deletedMedium
    ? state.medium.deletedMedium
    : false,
  deleteError: state.medium.error ? state.medium.error : null
});

const mapDispatchToProps = dispatch => {
  return {
    deleteMedium: id => dispatch(deleteMedium(id)),
    clearProgressingMedium: () => dispatch(clearProgressingMedium()),
    listStockMedia: (...args) => dispatch(listStockMedia(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(DeleteMediaDialog)));
