import React from "react";

const title = "Company";

async function action({ store, query }) {
  const { auth } = store.getState();
  if (!auth.user || !auth.user.authorized) {
    return { redirect: "/login" };
  }

  const ApplyCompanySearchPage = await require.ensure(
    [],
    (require) => require("./ApplyCompanySearchPage").default,
    "ApplyCompanySearchPage",
  );

  return {
    chunks: ["ApplyCompanySearch"],
    title,
    component: <ApplyCompanySearchPage queryFromUri={query} />,
  };
}

export default action;
