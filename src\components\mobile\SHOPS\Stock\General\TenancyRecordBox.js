import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import FieldValArrBox from "../../../../common/FieldValArrBox";
import { injectIntl } from "react-intl";
import TenancyRecordBoxPerson from "../../../../common/TenancyRecordBoxPerson";

// We can inject some CSS into the DOM.
const styles = {
  root: {

  },
  currentBox: {
    backgroundColor: "rgba(255, 255, 100, .4)"
  },
  advanceBox: {
    backgroundColor: "rgba(255, 255, 100, .4)"
  },
  formerBox: {

  },
};

function TenancyRecordBox(props) {
  const {
    classes,
    status,
    persons = [],
    floor = "---",
    shopNumber = "---",
    area = "---",
    tenancyPeriod = "---",
    rent = "---",
    options = [],
    business = "---",
    mongoId,
    stockId,
    intl,
    ...others
  } = props;

  let displayStatus;
  let boxClass = "";
  if (status === "Advance") {
    displayStatus = intl.formatMessage({ id: "stock.advancetenancy" });
    boxClass = classes.advanceBox;
  }
  if (status === "Current") {
    displayStatus = intl.formatMessage({ id: "stock.currenttenancy" });
    boxClass = classes.currentBox;
  }
  if (status === "Previous")
    displayStatus = intl.formatMessage({ id: "stock.previoustenancy" });
  if (status === "Former") {
    displayStatus = intl.formatMessage({ id: "stock.formertenancy" });
    boxClass = classes.formerBox;
  }
  const tenantField = intl.formatMessage(
    { id: "stock.tenant" },
    { status: displayStatus },
  );

  // make sure there are exactly three options
  let optionsAppended = options.concat({}, {}).slice(0, 2);
  let optionItems = [];
  optionsAppended.forEach((v, i) => {
    optionItems.push({
      field: intl.formatMessage({ id: "stock.option" }) + " (" + (i + 1)+ ")",
      val: optionsAppended[i].period || "---",
      xs: 6,
    });
    optionItems.push({
      field: intl.formatMessage({ id: "stock.rent" }) + " (" + (i + 1)+ ")",
      val: optionsAppended[i].rent || "---",
      xs: 6,
    });
  });

  let tenants = JSON.parse(JSON.stringify(persons));
  if (tenants.length === 0) tenants.push({});
  tenants = tenants.map((v, i) => {
    return {
      extra: <TenancyRecordBoxPerson field={tenantField + (tenants.length > 1 ? ` (${i + 1})` : "")} tenant={v || {}} mongoId={mongoId} stockId={stockId} />
    };
  });

  const items = [
    {
      field: intl.formatMessage({ id: "stock.floor" }),
      val: floor,
      xs: 6,
    },
    {
      field: intl.formatMessage({ id: "stock.shopnumber" }),
      val: shopNumber,
      xs: 6,
    },
    {
      field: intl.formatMessage({ id: "stock.tenancyperiod" }),
      val: tenancyPeriod,
    },
    {
      field: intl.formatMessage({ id: "stock.latestrent" }),
      val: rent,
    },
    {
      field: intl.formatMessage({ id: "stock.industry" }),
      val: business,
    },
    {
      field: intl.formatMessage({ id: "stock.shoparea" }),
      val: area,
    },
    ...optionItems,
    ...tenants,
  ];

  return (
    <FieldValArrBox
      className={`${classes.root} ${boxClass}`}
      items={items}
      {...others}
    />
  );
}

TenancyRecordBox.propTypes = {
  classes: PropTypes.object.isRequired,
  status: PropTypes.string.isRequired,
  persons: PropTypes.arrayOf(PropTypes.object),
  floor: PropTypes.string,
  shopNumber: PropTypes.string,
  area: PropTypes.string,
  tenancyPeriod: PropTypes.string,
  rent: PropTypes.string,
  options: PropTypes.arrayOf(PropTypes.object),
  business: PropTypes.string,
  mongoId: PropTypes.string,
  stockId: PropTypes.number,
};

export default withStyles(styles)(injectIntl(TenancyRecordBox));
