import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import Button from "@material-ui/core/Button";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    minWidth: "50px",
    lineHeight: "24px",
    color: "rgba(0, 0, 0, 0.54)",
    borderRadius: "12px",
    padding: "0px 6px",
    textTransform: "none",
    "& span": { pointerEvents: "none" },
  },
};

function PillButton(props) {
  const { classes, children, className, ...other } = props;

  return (
    <Button
      variant="outlined"
      className={clsx(classes.root, className)}
      {...other}
    >
      {children}
    </Button>
  );
}

PillButton.propTypes = {
  children: PropTypes.node,
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
};

export default withStyles(styles)(PillButton);
