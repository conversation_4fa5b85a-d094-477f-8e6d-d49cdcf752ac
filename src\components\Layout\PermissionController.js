import React, { useState, useEffect } from "react";
import { connect } from "react-redux";
import { injectIntl } from "react-intl";
import _ from "lodash";
import { getEmployeePermission } from "@/actions/employee";

const PermissionController = ((props) => {
  const {
    getEmployeePermission
  } = props;

  useEffect(() => {
    getEmployeePermission();
  }, []);

  return null; // Return null since this component only handles side effects
});


const mapStateToProps = (state) => {
  return {};
};

const mapDispatchToProps = (dispatch) => {
  return {
    getEmployeePermission: (...args) => dispatch(getEmployeePermission(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)((injectIntl(PermissionController)));
