/* eslint-disable import/prefer-default-export */

import axios from "axios";
import Cookies from "universal-cookie";

import { sso, cas_client_id, cas_client_secret, deployDomain } from "@/config";

import {
  LOGIN_START,
  LOGIN_SENDCODE,
  LOGIN_VERIFYCODE_SUCCESS,
  LOGIN_VERIFYCODE_FAILED,
  LOGIN_ERROR,
  LOGOUT_START,
  LOGOUT_SUCCESS,
  CHECK_AUTH_FAILED,
  CLEAR_AUTH,
  SET_LOGIN_INFO,
  GET_JWE_TOKEN_SUCCESS,
  GET_JWE_TOKEN_FAILED,
} from "../constants/auth";

import { getEmployeePermission } from "./employee";
import { getCookieDomain } from "@/helper/generalHelper";

export function login(/*{ departmentCode, username, password },*/ intl) {
  return async (dispatch, getState, { universalRequest }) => {
    dispatch({
      type: LOGIN_START,
    });
    try {
      const departmentCode = "NIL";
      const username = "NIL";
      const password = "NIL";
      const json = await universalRequest("/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          departmentCode,
          username,
          password,
        }),
        credentials: "include",
      });

      if (json.error) {
        console.log(json.error);
        dispatch({
          type: LOGIN_ERROR,
          payload: {
            error: json.error,
          },
        });

        // take log for failed login
        /* const msg = JSON.stringify({
          action: "login",
          status: "failed",
          message: JSON.stringify({
            departmentCode: 'TEST',
            username: 'TEST',
            password: 'TEST'
          }),
        });
        // use normal fetch with custom header, not from createFetch
        await takeLog("login-attempt", msg, fetch, true);
        */
        window.dataLayer.push({ firstLoginSuccess: false });
        window.location.replace("/logout");
      } else {
        dispatch({
          type: LOGIN_SENDCODE,
          payload: json.user.login,
        });
        window.dataLayer.push({ firstLoginSuccess: true });
        // window.location.replace("/");
      }
    } catch (error) {
      dispatch({
        type: LOGIN_ERROR,
        payload: { error: error.message },
      });
      // throw error;
    }
  };
}

export function verifycode({ token, user }, intl) {
  return async (dispatch, getState, { universalRequest, takeLog }) => {
    const emp_id = user.info.emp_id;
    dispatch({
      type: LOGIN_START,
    });

    try {
      const json = await universalRequest("/totp-verify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token, emp_id }),
      });

      if (json.valid == false) {
        dispatch({
          type: LOGIN_VERIFYCODE_FAILED,
          payload: {
            error: intl.formatMessage({
              id: "login.message.wrongpin",
            }),
          },
        });
      } else {
        dispatch(setTokensAndPermission({
          access_token: json.casAccessToken,
          refresh_token: json.casRefreshToken,
          expires_in: json.casAccessTokenExpiresIn
        }));

        dispatch({
          type: LOGIN_VERIFYCODE_SUCCESS,
          payload: user,
        });

        // take log for successful login
        const msg = JSON.stringify({
          action: "login",
          status: "success",
        });
        // use normal fetch with custom header, not from createFetch
        await takeLog(emp_id, msg, fetch, false, json.token);

        // window.location.replace("/");
      }
      return json;
      // const result = await response.json();
    } catch (error) {
      dispatch({
        type: LOGIN_VERIFYCODE_FAILED,
        payload: { error: error.message },
      });
      // throw error;
    }
  };
}

export function logout() {
  return async (dispatch, getState, { fetch, universalRequest }) => {
    dispatch({
      type: LOGOUT_START,
    });

    // document.cookie = "id_token=; expires=Thu, 01 Jan 1970 00:00:01 GMT;";
    localStorage.removeItem("casAccessToken");
    localStorage.removeItem("casAccessTokenExpiresIn");
    localStorage.removeItem("casRefreshToken");
    window.location.href = "/logout";

    dispatch({
      type: LOGOUT_SUCCESS,
    });

    return true;
  };
}

export function checkAuth() {
  return async (dispatch, getState, { fetch, universalRequest }) => {
    try {
      const { emp_id } = getState().auth.user.login.info;
      const validlogin = await universalRequest("/checklogin", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          emp_id,
        }),
      });
      if (validlogin.duplicated == false) {
        // delete current client session
        window.location.replace("/");
      } else {
      }
    } catch (error) {
      dispatch({
        type: CHECK_AUTH_FAILED,
        payload: { error: error.message },
      });
      // throw error;
    }
  };
}

export function clearAuth() {
  return async (dispatch) => {
    dispatch({
      type: CLEAR_AUTH,
    });
  };
}

export const setLoginInfo = (loginInfo) => ({
  type: SET_LOGIN_INFO,
  payload: {
    loginInfo,
  },
});

export function getJweToken() {
  return async (dispatch, getState, { fetch, universalRequest }) => {
    try {
      const casAccessToken = localStorage.getItem("casAccessToken");
      const casRefreshToken = localStorage.getItem("casRefreshToken");
      const token = await universalRequest("/jweToken", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          casAccessToken,
          casRefreshToken,
          kid: "SalesKit",
          source: "msearch",
        }),
      });

      dispatch({
        type: GET_JWE_TOKEN_SUCCESS,
        payload: token,
      });
    } catch (error) {
      dispatch({
        type: GET_JWE_TOKEN_FAILED,
        payload: { error: error.message },
      });
    }
  };
}

export const setTokens = (payload) => async dispatch => {
  const { access_token, refresh_token, expires_in } = payload;

  const cookies = new Cookies();
  cookies.set("casAccessToken", access_token, {
    domain: getCookieDomain(deployDomain)
  });
  cookies.set("casRefreshToken", refresh_token, {
    domain: getCookieDomain(deployDomain)
  });
  cookies.set("casAccessTokenExpiresIn", new Date() * 1 + 1000 * expires_in, {
    domain: getCookieDomain(deployDomain)
  });

  localStorage.setItem("casAccessToken", access_token);
  localStorage.setItem("casRefreshToken", refresh_token);
  localStorage.setItem("casAccessTokenExpiresIn", new Date() * 1 + 1000 * expires_in);
  window.dispatchEvent(new StorageEvent("storage"));
}

export const setTokensAndPermission = (payload) => async dispatch => {
  const { config } = payload;
  dispatch(setTokens(payload));
  if (!(config && config.bypassPermission)) {
    await dispatch(getEmployeePermission());
  }
}

export const fetchRefreshTokenSuccess = payload => async dispatch => {
  dispatch(setTokensAndPermission(payload.data));
};

export const fetchRefreshToken = refreshToken => async dispatch => {
  try {
    dispatch(
      fetchRefreshTokenSuccess(
        await axios.post(
          `${sso}/oauth2.0/accessToken?grant_type=refresh_token&client_id=${cas_client_id}&client_secret=${encodeURIComponent(
            cas_client_secret,
          )}&refresh_token=${refreshToken}`,
        ),
      ),
    );
  } catch (error) {
    console.log(error);
  }
};
