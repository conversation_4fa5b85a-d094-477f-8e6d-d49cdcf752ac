import React from "react";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/core/styles";
import clsx from "clsx";

import LoadingOverlay from "../LoadingOverlay";
import { sbu } from "../../config";
import bgcommUrl from "../../files/bg/comm_watermark_stock_detail.png";
import bgindUrl from "../../files/bg/ind_watermark_stock_detail.png";
import bgshopsUrl from "../../files/bg/shop_watermark_result_detail.png";
import BottomButtons from "./BottomButtons";

let url;
if (sbu === "COMM") {
  url = bgcommUrl;
} else if (sbu === "IND") {
  url = bgindUrl;
} else if (sbu === "SHOPS") {
  url = bgshopsUrl;
}

const useStyles = makeStyles({
  root: {
    display: "flex",
    flexDirection: "column",
  },
  notFound: {
    margin: "1em 0",
    textAlign: "center",
    fontWeight: "bold",
  },
  circularLoader: {
    textAlign: "center",
    marginTop: "18vh",
    "& > div": {
      // color: "#AAA"
    },
  },
  wrapper: {
    padding: (props) => (props.isMobile ? "1vw 2vw" : "0 16px"),
    flex: "1 0 auto",
  },
  wrapperWithBottomBtns: {
    marginBottom: 60,
  },
  bg: {
    width: "100%",
    height: "100vh",
    backgroundColor: (props) => (props.isMobile ? "#FFF" : "#F0F0F0"),
    backgroundImage: (props) => (props.isMobile ? `url(${url})` : "none"),
    backgroundRepeat: "no-repeat",
    backgroundPosition: "center",
    position: "fixed",
    left: 0,
    top: 0,
    zIndex: -1,
  },
});

function DetailTabPanelFrame(props, context) {
  const {
    children,
    listed,
    listing,
    hasData,
    notFoundText,
    wrapperProps = {},
    showBg,
    bottomButtons,
  } = props;
  const { className: wrapperClassName, ...customWrapperProps } = wrapperProps;

  const { browserDetect } = context;
  const classes = useStyles({
    isMobile: browserDetect === "mobile",
  });

  return (
    <div className={classes.root}>
      {showBg && <div className={classes.bg} />}
      {!listing && listed && hasData && (
        <>
          <div
            className={clsx(classes.wrapper, wrapperClassName, {
              [classes.wrapperWithBottomBtns]: bottomButtons.length > 0,
            })}
            {...customWrapperProps}
          >
            {children}
          </div>
          {bottomButtons && <BottomButtons buttons={bottomButtons} />}
        </>
      )}
      {(listing || !listed) && (
        <div className={classes.circularLoader}>
          <LoadingOverlay />
        </div>
      )}
      {!listing && listed && !hasData && (
        <div className={classes.notFound}>{notFoundText || "Not found"}</div>
      )}
    </div>
  );
}

DetailTabPanelFrame.defaultProps = {
  wrapperProps: {},
  listed: true,
  listing: false,
  hasData: true,
  showBg: true,
  bottomButtons: [],
  notFoundText: "Page not found. Please retry.",
};

DetailTabPanelFrame.propTypes = {
  children: PropTypes.node.isRequired,
  listed: PropTypes.bool,
  listing: PropTypes.bool,
  hasData: PropTypes.bool,
  notFoundText: PropTypes.string,
  wrapperProps: PropTypes.object,
  showBg: PropTypes.bool,
  bottomButtons: PropTypes.array,
};

DetailTabPanelFrame.contextTypes = {
  browserDetect: PropTypes.string,
};

export default DetailTabPanelFrame;
