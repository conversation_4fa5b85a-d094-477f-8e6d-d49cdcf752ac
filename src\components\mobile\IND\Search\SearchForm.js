import React, { useEffect } from "react";
import { reduxForm } from "redux-form";
import clsx from "clsx";
import _ from "lodash";
import { FormattedMessage } from "react-intl";
import { withStyles } from "@material-ui/core/styles";
import ButtonGroup from "@material-ui/core/ButtonGroup";
import validate from "./validate";
import FormButton from "@/components/common/FormButton";
import FieldSection from "./FormSection/FieldSection";
import { consolidateType, defaultSearchQuery } from "@/helper/generalHelper";

const styles = (theme) => ({
  root: {
    backgroundColor: "#FFF",
    // paddingBottom: "7vh"
  },
  content: {
    flex: "1 1 auto",
  },
  searchbutton: {
    textTransform: "none",
    fontSize: "1.125em",
    height: "60px",
    minWidth: "auto",
    lineHeight: "1em",
  },
  clearbutton: {
    background: "#6be6a1",
  },
  saveButton: {
    background: "#777",
  },
  groupBtnContainer: {
    width: "100%",
    zIndex: "999",
    position: "fixed",
    left: 0,
    bottom: 0,
  },
});

let SearchForm = (props) => {
  const {
    classes,
    handleSubmit,
    error,
    submitting,
    reset,
    pristine,
    selectedData,
    setSelectedData,
    clearSelectedData,
    expanded,
    initialize,
    initialValues,
    fromChildToParentCallback,
    handleSaveClick,
    handleLoadClick,
    retainMarkStocks,
    handleRetainMarkStocksClick,
  } = props;

  useEffect(() => {
    if (!_.isEmpty(_.get(selectedData, "unicornId"))) {
      initialize({
        limit: 50,
        offset: 0,
        ...consolidateType,
        unicornId: _.map(_.get(selectedData, "unicornId"), (v) => v.value),
      });
      clearSelectedData({
        unicornId: _.get(selectedData, "unicornId"),
      });
    }
  }, [_.get(selectedData, "unicornId")]);

  const handleResetClick = () => {
    initialize(defaultSearchQuery);
    clearSelectedData();
  };

  fromChildToParentCallback(initialize);

  return (
    <form onSubmit={handleSubmit} className={classes.root}>
      <FieldSection
        selectedData={selectedData}
        setSelectedData={setSelectedData}
        expanded={expanded}
        initialValues={initialValues}
        retainMarkStocks={retainMarkStocks}
        onRetainMarkStocksClick={handleRetainMarkStocksClick}
      >
        <div className={classes.groupBtnContainer}>
          <ButtonGroup fullWidth>
            <FormButton
              onClick={handleSaveClick}
              className={clsx(classes.searchbutton, classes.saveButton)}
            >
              <FormattedMessage id="search.button.save" />
            </FormButton>
            <FormButton
              onClick={handleLoadClick}
              className={clsx(classes.searchbutton, classes.saveButton)}
            >
              <FormattedMessage id="search.button.load" />
            </FormButton>
            <FormButton
              disabled={submitting}
              onClick={handleResetClick}
              className={clsx(classes.searchbutton, classes.clearbutton)}
            >
              <FormattedMessage id="search.button.clear" />
            </FormButton>
            <FormButton type="submit" className={classes.searchbutton}>
              <FormattedMessage id="search.button.search" />
            </FormButton>
          </ButtonGroup>
        </div>
      </FieldSection>
    </form>
  );
};

SearchForm = reduxForm({
  form: "searchForm",
  enableReinitialize: true,
  keepDirtyOnReinitialize: true, // must add this to get the initialize function work
  // I think this a bug of redux form and this does not keep dirty fields actually
  validate,
  onChange: (values, dispatch, props, previousValues) => {
    if (!props.expanded) {
      if (
        props.dirty &&
        !_.isEqual(values.buildingSourcesId, previousValues.buildingSourcesId)
      ) {
        props.submit();
      }
    }
  },
})(SearchForm);

export default withStyles(styles)(SearchForm);
