import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import { injectIntl } from "react-intl";
import FieldVal from "../../../../common/FieldVal";

const styles = theme => ({
  root: {
    padding: "1vh 0",
  },
  lmrAlign: {
    paddingLeft: "2vw",
  },
});

class ConsultantShare extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;

    const commission = detail.commission ? detail.commission : "---";

    const commissionHeader = intl.formatMessage({
      id: "stock.commission",
    });

    let mapping = {
      [commissionHeader]: { value: commission, xs: 12 },
    };

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.consultantshare"
          })}
        >
          <Grid container spacing={2} className={classes.lmrAlign}>
            {Object.keys(mapping).map((v, i) => (
              <Grid item xs={mapping[v].xs} key={v}>
                <FieldVal field={v}>
                  {mapping[v].value}
                </FieldVal>
              </Grid>
            ))}
          </Grid>
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(ConsultantShare));
