import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import { convertCurrency, numberComma } from "../../helper/generalHelper";
import TotalAndAvgPrice from "./TotalAndAvgPrice";
import { injectIntl } from "react-intl";

// We can inject some CSS into the DOM.
const styles = {
  main: {
    lineHeight: 1.3,
    fontSize: "1.5em"
  },
  bottom: {
    lineHeight: 1.3,
    fontSize: "1em"
  }
};

function NormalAndBottomPrice(props) {
  const { classes, data, label, avgDecimal, intl, ...other } = props;
  const avg =
    data && data.average
      ? "@" + numberComma(data.average, avgDecimal || 0)
      : "---";
  const total =
    data && data.total ? "$" + convertCurrency(data.total, undefined, { decimals: 3 }) + " " : "---";
  const trend = data && data.trend ? data.trend : null;

  let generalLabel;
  if (label == "Rent") {
    generalLabel = intl.formatMessage({ id: "search.common.rent" });
  } else if (label == "Price") {
    generalLabel = intl.formatMessage({ id: "search.common.price" });
  }

  let bottomAvg = "---";
  if (
    data &&
    data.details &&
    data.details.filter(v => v.type === "average_bottom").length > 0
  )
    bottomAvg =
      "@" +
      numberComma(
        data.details.filter(v => v.type === "average_bottom")[0].value,
        avgDecimal || 0
      );
  let bottomTotal = "---";
  if (
    data &&
    data.details &&
    data.details.filter(v => v.type === "total_bottom").length > 0
  )
    bottomTotal =
      "$" +
      convertCurrency(
        data.details.filter(v => v.type === "total_bottom")[0].value,
        undefined,
        { decimals: 3 },
      ) +
      " ";

  return (
    <>
      <TotalAndAvgPrice
        avg={avg}
        total={total}
        trend={trend}
        label={generalLabel}
        className={classes.main}
      />
      <TotalAndAvgPrice
        avg={bottomAvg}
        total={bottomTotal}
        label={intl.formatMessage({ id: "stock.bottom" })}
        className={classes.bottom}
      />
    </>
  );
}

NormalAndBottomPrice.propTypes = {
  classes: PropTypes.object.isRequired,
  data: PropTypes.object,
  label: PropTypes.string,
  avgDecimal: PropTypes.number
};

export default withStyles(styles)(injectIntl(NormalAndBottomPrice));
