import React from "react";
import { withStyles } from "@material-ui/core/styles";
import CallIcon from "./CallIcon";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    height: 34,
    background: "#56F371",
    borderRadius: 17,
    position: "relative",
    cursor: "pointer",
    display: "flex",
    alignItems: "center",
  },
  icon: {
    backgroundColor: "transparent",
    pointerEvents: "none",
  },
  text: {
    color: "#FFF",
    fontSize: "1.125em",
    textAlign: "center",
    paddingRight: 34,
    flex: 1,
    pointerEvents: "none",
  },
};

function CallIconLong(props) {
  const { classes, className, children, ...other } = props;

  return (
    <div className={`${classes.root} ${className}`} {...other}>
      <CallIcon className={classes.icon} />
      <div className={classes.text}>{children}</div>
    </div>
  );
}

export default withStyles(styles)(CallIconLong);
