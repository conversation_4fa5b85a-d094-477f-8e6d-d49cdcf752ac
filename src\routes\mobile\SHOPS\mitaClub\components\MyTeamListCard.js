import React from "react";
import { Box, makeStyles, Paper, Typography } from "@material-ui/core";
import { convertCurrency } from "../../../../../helper/generalHelper";
import Image89 from "./asset/image89.svg";
import Image62 from "./asset/image62.svg";

const useDataTableSectionStyles = makeStyles((theme) => ({
  container: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    height: "32px",
    gap: "16px",
    padding: "0 16px",
  },
  teamName: {
    // width: "30%",
    flex: 3,
    // "width": "86px",
    "minWidth": "68px",
    "height": "18px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "14px",
    "lineHeight": "18px",
    "letterSpacing": "0.02em",
    "color": "#222222",
    // "flex": "none",
    // "flexGrow": 1,
  },
  accumulatedSalesAmount: {
    flex: 2.2,
    "minWidth": "64px",
    "height": "18px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "14px",
    "lineHeight": "18px",
    "textAlign": "right",
    "letterSpacing": "0.02em",
    "color": "#222222",
    // "flex": "none",
    // "flexGrow": 0,
  },
  accumulatedSalesCase: {
    flex: 1.7,
    "minWidth": "48px",
    "height": "18px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "14px",
    "lineHeight": "18px",
    "textAlign": "right",
    "letterSpacing": "0.02em",
    "color": "#222222",
    // "flex": "none",
    // "flexGrow": 0,
  },
  iconsContainer: {
    flex: 3.1,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-evenly",
    minWidth: "88px",
  },
  iconText: {
    "width": "40px",
    "height": "18px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "14px",
    "lineHeight": "18px",
    "textAlign": "center",
    "letterSpacing": "0.02em",
    "color": "#222222",
    "flex": "none",
    "order": 0,
    "flexGrow": 0,

  },
  headerIcon: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center"
  },
  iconImage: {
    height: "24px",
    width: "24px"
  }
}));

/**
 * @typedef {Object} DataTableSectionProps
 * @property {boolean} isHeader
 * @property {string} teamName
 * @property {number | string} accumulatedSalesAmount
 * @property {number | string} accumulatedSalesCase
 * @property {number | string | any} diamondNumber
 * @property {number | string | any} platinumNumber
 * @property {React.CSSProperties=} style
 * 
 * @param {DataTableSectionProps} props 
 */
const DataTableSection = (props) => {
  const {
    isHeader = false,
    teamName = "",
    accumulatedSalesAmount = 0,
    accumulatedSalesCase = 0,
    diamondNumber = 0,
    platinumNumber = 0,
  } = props;
  const classes = useDataTableSectionStyles();

  return (
    <Box
      display="flex"
      alignItems="center"
      justifyContent="space-between"
      height="32px"
      className={classes.container}
      style={props.style || {}}
    >
      <Typography
        variant="subtitle2"
        className={classes.teamName}
      >
        {teamName}
      </Typography>

      <Typography
        variant="subtitle2"
        className={classes.accumulatedSalesAmount}
      >
        {isHeader ? accumulatedSalesAmount : `$${convertCurrency(accumulatedSalesAmount)}`}
      </Typography>

      <Typography
        variant="subtitle2"
        className={classes.accumulatedSalesCase}
      >
        {isHeader ? accumulatedSalesCase : `${accumulatedSalesCase}單`}
      </Typography>

      <Box className={classes.iconsContainer}>
        <Typography
          variant="subtitle2"
          className={`${classes.iconText} ${isHeader ? classes.headerIcon : ''}`}
        >
          {isHeader ? <img src={diamondNumber} className={classes.iconImage} /> : null}
          {!isHeader ? diamondNumber : null}
        </Typography>

        <Typography
          variant="subtitle2"
          className={`${classes.iconText} ${isHeader ? classes.headerIcon : ''}`}
        >
          {isHeader ? <img src={platinumNumber} className={classes.iconImage} /> : null}
          {!isHeader ? platinumNumber : null}
        </Typography>
      </Box>
    </Box>
  );
};

const listHead = Object.freeze({
  /** @type {boolean} */
  isHeader: true,
  /** @type {string} */
  teamName: "",
  /** @type {string | number} */
  accumulatedSalesAmount: "業績",
  /** @type {string | number} */
  accumulatedSalesCase: "單數",
  /** @type {string | number} */
  diamondNumber: Image89,
  /** @type {string | number} */
  platinumNumber: Image62
});
const MyTeamListCard = (props) => {
  const teamList = React.useMemo(() => {
    const teamList = [listHead];
    if (!props.mitaclubAgentMap) { return teamList; };

    const { targetValues = [] } = props;
    Object.keys(props.mitaclubAgentMap).forEach((TeamCode) => {
      const agents = props.mitaclubAgentMap[TeamCode];

      const team = props.mitaclubTeams.find((t) => t.TeamCode === TeamCode);
      const result = {
        teamName: TeamCode,
        isHeader: false,
        accumulatedSalesAmount: +(team?.AccumulatedSalesAmount || 0),
        accumulatedSalesCase: +(team?.AccumulatedSalesCase || 0),
        diamondNumber: 0,
        platinumNumber: 0,
      };

      agents.forEach((agent) => {
        // result.accumulatedSalesAmount += (+agent.AccumulatedSalesAmount || 0);
        // result.accumulatedSalesCase += (+agent.AccumulatedSalesCase || 0);
        if (+agent.AccumulatedSalesAmount >= targetValues[0]) {
          result.diamondNumber += 1;
        } else if (+agent.AccumulatedSalesAmount >= targetValues[1]) {
          result.platinumNumber += 1;
        }
      });

      teamList.push(result);
    });

    return teamList.sort((a, b) => a.isHeader ? 1 : (+b.accumulatedSalesAmount - +a.accumulatedSalesAmount));
  }, [props.mitaclubTeams, props.mitaclubAgentMap, props.targetValues]);

  return (
    <Box sx={{ display: "flex", flexDirection: "column" }}>
      <Typography
        variant="subtitle1"
        align="center"
        style={{
          fontWeight: "bold",
          fontFamily: "Microsoft JhengHei UI, Helvetica",
          color: "#222222",
          letterSpacing: "0.32px",
        }}
      >
        你的團隊
      </Typography>

      <Paper elevation={3}>
        <div
          style={{
            "padding": "16px 0",
            "flex": "none",
            overflowX: "scroll",
          }}
        >
          {teamList.map((data, index) => (
            <DataTableSection
              key={data.teamName}
              {...data}
              style={(index % 2) === 0 ? { backgroundColor: "#F8F8F8" } : {}}
            />
          ))}
        </div>
      </Paper>
    </Box>
  );
};

export default MyTeamListCard;
