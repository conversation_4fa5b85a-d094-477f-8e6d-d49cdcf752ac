import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import ButtonGroup from "@material-ui/core/ButtonGroup";
import { injectIntl, FormattedMessage } from "react-intl";
import DetailTabPanelFrame from "../../../../common/DetailTabPanelFrame";
import {
  changeCurrentStock,
  listStockMedia,
} from "../../../../../actions/stock";
import { getProposalCount } from "../../../../../actions/proposal";
import { listBuildingMedia } from "../../../../../actions/building";
import { listStreetMedia } from "../../../../../actions/street";
import FormButton from "../../../../common/FormButton";
import ProposalCreate from "./ProposalCreate";
import {
  getDisplayStockId,
  get<PERSON>ang<PERSON>ey,
} from "../../../../../helper/generalHelper";
import { generalProposalQuota } from "../../../../../config";
import SelectField from "../../../../common/SelectField";
import history from "../../../../../core/history";

const styles = (theme) => ({
  wrapper: {
    backgroundColor: "#f5f5f5",
  },
  proposalWrapper: {
    minHeight: "calc(100vh - 88px - 2vw)",
  },
  listProposalWrapper: {
    minHeight: "calc(100vh - 168px - 2vw)",
  },
  fixBtnContainer: {
    width: "100%",
    zIndex: "999",
    position: "fixed",
    left: 0,
    bottom: 0,
  },
  fixBtn: {
    textTransform: "none",
    fontSize: "1.125em",
    height: "60px",
    minWidth: "auto",
    lineHeight: "1em",
  },
  sectionWrapper: {
    paddingTop: "1vh",
  },
  title: {
    color: "#EC1F26",
  },
  num: {
    backgroundColor: "#EC1F26",
  },
  link: {
    textDecoration: "none",
  },
  formContainer: {
    paddingBottom: "8vh",
  },
});

function Media({
  classes,
  media,
  listedMedia,
  listingMedia,
  buildingMedia,
  listedBuildingMedia,
  listingBuildingMedia,
  streetMedia,
  listedStreetMedia,
  listingStreetMedia,
  detail,
  currentDetail,
  listed: listedStockData,
  listing: listingStockData,
  listStockMedia,
  listBuildingMedia,
  listStreetMedia,
  getProposalCount,
  handleClickOpenDialog,
  changeCurrentStock,
  userInfo,
  createdPdf,
  intl,
}) {
  const [createProposalDialogOpen, setCreateProposalDialogOpen] = useState(
    false,
  );
  const [exceedQuota, setExceedQuota] = useState(false);
  const [currentStock, setCurrentStock] = useState({});
  const [isListProposal, setIsListProposal] = useState(false);

  useEffect(() => {
    // can only access history when component did mount
    if (history)
      setIsListProposal(history.location.pathname === "/listProposal");
  }, []);

  useEffect(() => {
    if (detail && detail.length > 0) {
      listStockMediaAfterIdReturned();
    }
  }, [detail]);

  useEffect(() => {
    if (detail.length > 0 && detail[currentDetail]) {
      setCurrentStock(detail[currentDetail]);
    }
  }, [detail, currentDetail]);

  const listStockMediaAfterIdReturned = () => {
    const sid = { stockId: [], buildingId: [], streetId: [] };
    detail.forEach((stockElem) => {
      if (stockElem) {
        if (stockElem.unicorn && Number.isInteger(stockElem.unicorn.id)) {
          sid.stockId.push(stockElem.unicorn.id.toString());
        }
        if (
          stockElem.building &&
          stockElem.building.unicorn &&
          Number.isInteger(stockElem.building.unicorn.id)
        ) {
          sid.buildingId.push(stockElem.building.unicorn.id.toString());
        }
        if (
          stockElem.street &&
          stockElem.street.street &&
          stockElem.street.street.unicorn &&
          Number.isInteger(stockElem.street.street.unicorn.id)
        ) {
          sid.streetId.push(stockElem.street.street.unicorn.id.toString());
        }
      }
    });

    // get stock and building media
    listStockMedia({
      sid: sid.stockId,
      empId: userInfo.emp_id,
    });
    // listBuildingMedia({
    //   sid: sid.buildingId,
    //   empId: userInfo.emp_id,
    // });
    listStreetMedia({
      sid: sid.streetId,
      empId: userInfo.emp_id,
    });

    getProposalCount();
  };

  const handleOpenCreateProposalDialog = () => {
    const proposalscount = createdPdf;
    if (proposalscount >= generalProposalQuota) {
      setExceedQuota(true);
    } else {
      setExceedQuota(false);
    }
    setCreateProposalDialogOpen(true);
    window.dataLayer.push({
      stockId: getDisplayStockId(currentStock.unicorn.id),
    });
  };

  const handleCloseCreateProposalDialog = () => {
    setCreateProposalDialogOpen(false);
  };

  const getAddress = ({ street, district }) => {
    const langKey = getLangKey(intl);
    if (langKey === "nameZh") {
      return `${district[langKey]} ${street.street[langKey]} ${street.number}`;
    } else {
      return `${street.number} ${street.street[langKey]}, ${district[langKey]}`;
    }
  };

  const addressOptions = () =>
    detail.map((d) => ({
      value: d._id,
      label: getAddress(d),
    }));

  const handleAddressChange = (e) => {
    const { value } = e.target;
    const idx = detail.findIndex((d) => d._id === value);
    changeCurrentStock(idx);
  };

  const stockMediaData = media?.filter((m) => m.id === currentStock?.unicorn?.id?.toString())?.[0]?.data || {};
  const buildingMediaData = buildingMedia?.filter((m) => m.id === currentStock?.building?.unicorn?.id?.toString())?.[0]?.data || {};
  const streetMediaData = streetMedia?.filter((m) => m.id === currentStock?.street?.street?.unicorn?.id?.toString())?.[0]?.data || {};

  const hasStockData = stockMediaData && Object.keys(stockMediaData).length > 0;
  const hasBuildingData = buildingMediaData && Object.keys(buildingMediaData).length > 0;
  const hasStreetData = streetMediaData && Object.keys(streetMediaData).length > 0; 

  const hasData = hasStockData || hasBuildingData || hasStreetData;
  const listing = listingStockData || listingMedia || listingBuildingMedia || listingStreetMedia;
  const listed = listedStockData && (listedMedia || listedBuildingMedia || listedStreetMedia);

  return (
    <div className={classes.wrapper}>
      {currentStock &&
      Object.keys(currentStock).length > 0 &&
      isListProposal ? (
        <div>
          <SelectField
            label={intl.formatMessage({ id: "proposal.general.address" })}
            ranges={addressOptions()}
            input={{
              value: currentStock._id,
              onChange: handleAddressChange,
            }}
            meta={{}}
            variant="outlined"
            fullWidth
          />
        </div>
      ) : null}
      <DetailTabPanelFrame
        wrapperProps={{
          className: isListProposal
            ? classes.listProposalWrapper
            : classes.proposalWrapper,
        }}
        hasData={hasData}
        listing={listing}
        listed={listed}
        notFoundText="Stock media not found"
      >
        <div className={classes.formContainer}>
          <ProposalCreate
            createProposalDialogOpen={createProposalDialogOpen}
            exceedQuota={exceedQuota}
            handleCloseCreateProposalDialog={handleCloseCreateProposalDialog}
          />
        </div>

        <div className={classes.fixBtnContainer}>
          <ButtonGroup fullWidth>
            <FormButton
              className={classes.fixBtn}
              onClick={handleOpenCreateProposalDialog}
              id={"createProposalButton"}
            >
              <FormattedMessage id="proposal.createpdf" />
            </FormButton>
            <FormButton
              className={classes.fixBtn}
              onClick={handleClickOpenDialog}
            >
              <FormattedMessage id="stock.upload" />
            </FormButton>
          </ButtonGroup>
        </div>
      </DetailTabPanelFrame>
    </div>
  );
}

Media.propTypes = {
  classes: PropTypes.object.isRequired,
  media: PropTypes.array,
  listedMedia: PropTypes.bool,
  listingMedia: PropTypes.bool,
  buildingMedia: PropTypes.array,
  listedBuildingMedia: PropTypes.bool,
  listingBuildingMedia: PropTypes.bool,
  streetMedia: PropTypes.array,
  listedStreetMedia: PropTypes.bool,
  listingStreetMedia: PropTypes.bool,
  detail: PropTypes.array,
  currentDetail: PropTypes.number,
  listed: PropTypes.bool,
  listStockMedia: PropTypes.func,
  listBuildingMedia: PropTypes.func,
  listStreetMedia: PropTypes.func,
  getProposalCount: PropTypes.func,
  handleClickOpenDialog: PropTypes.func,
  userInfo: PropTypes.object,
};

const mapStateToProps = (state) => ({
  userInfo:
    state.auth &&
    state.auth.user &&
    state.auth.user.login &&
    state.auth.user.login.info
      ? state.auth.user.login.info
      : {},
  media: state.stock.media ? state.stock.media : [],
  listedMedia: state.stock.listedMedia ? state.stock.listedMedia : false,
  listingMedia: state.stock.listingMedia ? state.stock.listingMedia : false,
  buildingMedia: state.building.media ? state.building.media : [],
  listedBuildingMedia: state.building.listedMedia
    ? state.building.listedMedia
    : false,
  listingBuildingMedia: state.building.listingMedia
    ? state.building.listingMedia
    : false,
  streetMedia: state.street.media ? state.street.media : [],
  listedStreetMedia: state.street.listedMedia ? state.street.listedMedia : false,
  listingStreetMedia: state.street.listingMedia ? state.street.listingMedia : false,
  detail: state.stock.detail ? state.stock.detail : [],
  currentDetail: state.stock.currentDetail ? state.stock.currentDetail : 0,
  listing: state.stock.listing ? state.stock.listing : false,
  listed: state.stock.listed ? state.stock.listed : false,
  createdPdf: state.proposal.createdPdf ? state.proposal.createdPdf : 0,
});

const mapDispatchToProps = (dispatch) => {
  return {
    listStockMedia: (...args) => dispatch(listStockMedia(...args)),
    listBuildingMedia: (...args) => dispatch(listBuildingMedia(...args)),
    listStreetMedia: (...args) => dispatch(listStreetMedia(...args)),
    getProposalCount: () => dispatch(getProposalCount()),
    changeCurrentStock: (current) => dispatch(changeCurrentStock(current)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(Media)));
