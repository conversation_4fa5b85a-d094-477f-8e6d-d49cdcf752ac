import React from "react";
import {
  Box,
  makeStyles,
} from "@material-ui/core";
import { connect } from "react-redux";
import QualificationCard from "./QualificationCard";
import { convertCurrency } from "@/helper/generalHelper";
import GoldCard from "./GoldCard";
import _ from "lodash";

const membershipTiers = [
  {
    name: "白金",
    value: 1_280_000/* "1.28M" */,
    current: true,
    color: "#FFB100",
  },
  {
    name: "鑽石",
    value: 2_560_000/* "2.56M" */,
    current: false,
    color: "linear-gradient(135deg,rgba(159, 173, 193, 1) 0%, rgba(220, 225, 232, 1) 15%, rgba(255, 255, 255, 1) 25%, rgba(192, 206, 225, 1) 40%, rgba(192, 206, 225, 1) 60%, rgba(255, 255, 255, 1) 75%, rgba(220, 225, 232, 1) 85%, rgba(159, 173, 193, 1) 100%)", // "#9FADC1",
  },
];

const progressMaxColor = "linear-gradient(135deg,rgba(217, 172, 75, 1) 0%, rgba(255, 227, 163, 1) 15%, rgba(255, 255, 255, 1) 25%, rgba(232, 204, 144, 1) 40%, rgba(230, 182, 80, 1) 50%, rgba(232, 204, 144, 1) 60%, rgba(255, 255, 255, 1) 75%, rgba(255, 232, 179, 1) 85%, rgba(217, 172, 75, 1) 100%)";
const qualificationLevels = [
  {
    badge: "鑽石",
    title: "升級至鑽石級別",
    description: "首 15 名年度業績達到 256 萬或以上",
  },
  {
    badge: "白⾦",
    title: "升級至⽩⾦級別",
    description: "首 40 名年度業績達到128 萬或以上",
  }
];

const useStyles = makeStyles((theme) => ({
  root: {
    // paddingBottom: "32px",
  },
  contentStack: {
    display: "flex",
    flexDirection: "column",
    width: "100%",
    gap: theme.spacing(3),
    // marginTop: "-85px",
  },
}));

function CardBox(props) {
  const classes = useStyles();

  const performanceData = React.useMemo(() => {
    return props.mitaclubInfo || {};
  }, [props.mitaclubInfo]);

  const metricsValue = React.useMemo(() => {
    if (!performanceData) {
      return "0 / 0";
    }
    const { AccumulatedSalesAmount = 0, AccumulatedSalesCase = 0 } = performanceData;
    return `$${convertCurrency(+AccumulatedSalesAmount)} / ${AccumulatedSalesCase}單`;
  }, [performanceData]);

  return (
    <Box style={{ padding: "0 12px" }}>
      {/* <Box style={{ marginTop: "-85px", padding: "0 12px", borderRadius: "12px", backgroundColor: "white" }}> */}
      <Box className={classes.contentStack}>
        {/* 业绩卡片 */}
        <GoldCard
          // title={""}
          updateDate={performanceData.ExecutionDate}
          currentPerformance={(+performanceData.AccumulatedSalesAmount || 0)}
          metricsValue={metricsValue}
          membershipTiers={membershipTiers}
          progressMaxColor={progressMaxColor}
          personalType={"Agent"}
        />

        {/* 资格卡片 */}
        <QualificationCard
          titleType={"營業員"}
          qualificationLevels={qualificationLevels}
        />
      </Box>
      {/* </Box> */}
    </Box>
  );
}

function Agent(props) {
  const classes = useStyles();

  return (
    <Box className={classes.root}>
      <CardBox mitaclubInfo={props.mitaclubInfo} />
    </Box>
  );
}

const mapStateToProps = (state) => ({
  mitaclubInfo: _.get(state, "employee.mitaclubInfo[0]"),
});

const mapDispatchToProps = (dispatch) => ({
  // getEmployeesMitaclubAgent: (empId) => dispatch(getEmployeesMitaclubAgent({ empId })),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(Agent);
