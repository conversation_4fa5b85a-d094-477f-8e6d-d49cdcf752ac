import React from "react";
import { Grid } from "@material-ui/core";
import PropTypes from "prop-types";
import { Field } from "redux-form";
import { injectIntl } from "react-intl";
import { makeStyles } from "@material-ui/core/styles";
import { connect } from "react-redux";
import _ from "lodash";

import ReadOnlyText from "@/components/common/ReadOnlyText";
import { getDisplayStockId } from "@/helper/generalHelper";
import TextInput from "@/components/common/TextInput";
import { getFullAddress } from "@/components/Saleskit/helpers";
import { sbu } from "@/config";

const useStyles = makeStyles({
  multiInputContainer: {
    marginLeft: "-8px !important",
    marginRight: "-8px !important",
  },
  multiline: {
    "& .MuiInputBase-multiline": {
      padding: 0,
      lineHeight: 1.4,
    },
  },
});

function StockInfo({ stock, intl }) {
  const classes = useStyles();

  return (
    <>
      <Grid item xs={12}>
        <ReadOnlyText
          label={intl.formatMessage({ id: "stock.applySearch.propertyId" })}
          value={getDisplayStockId(_.get(stock, "unicorn.id"))}
        />
      </Grid>

      <Grid item xs={12}>
        <ReadOnlyText
          className={classes.multiline}
          multiline
          label={intl.formatMessage({ id: "stock.applySearch.address" })}
          value={getFullAddress(intl, stock)}
        />
      </Grid>

      <Grid item container spacing={2} className={classes.multiInputContainer}>
        <Grid item xs={5}>
          <Field
            required
            name={sbu === "SHOPS" ? "shopNumber" : "unit"}
            component={TextInput}
            showPlaceholder={false}
            label={intl.formatMessage({
              id:
                sbu === "SHOPS"
                  ? "stock.applySearch.shopno"
                  : "stock.applySearch.unit",
            })}
          />
        </Grid>
        <Grid item xs={5}>
          <Field
            required
            showPlaceholder={false}
            name="floor"
            component={TextInput}
            label={intl.formatMessage({ id: "stock.applySearch.floor" })}
          />
        </Grid>
      </Grid>
    </>
  );
}
StockInfo.propTypes = {
  stock: PropTypes.object.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  stock: _.get(state, "stock.detail.0") || {},
});

export default connect(mapStateToProps)(injectIntl(StockInfo));
