import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    display: "flex",
    alignItems: "center"
  },
  stroke: {
    height: 1,
    margin: "0 1vw",
    backgroundColor: "rgba(0, 0, 0, 0.23)",
    flex: 1
  }
};

function Stroke(props) {
  const { classes, className, classNameStroke, left, right, ...other } = props;

  return (
    <div className={`${classes.root} ${className}`} {...other}>
      {left}
      <div className={`${classes.stroke} ${classNameStroke}`} />
      {right}
    </div>
  );
}

Stroke.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  classNameStroke: PropTypes.string,
  left: PropTypes.node,
  right: PropTypes.node
};

export default withStyles(styles)(Stroke);
