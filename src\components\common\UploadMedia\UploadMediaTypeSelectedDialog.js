import React from "react";
import Dialog from "../Dialog";
import { MuiThemeProvider, createMuiTheme } from "@material-ui/core/styles";
import { injectIntl } from "react-intl";
import _ from "lodash";
import DialogButton from "../DialogButton";

const theme = createMuiTheme({
});

const mediaTypeMapping = Object.freeze({
  video: "vrVideo",
  kol_video: "kol",
});
function UploadMediaTypeSelectedDialog(props) {
  const {
    dialogOpen,
    onSelected,
    mediaTypes,
    intl,
  } = props;

  const getClickHandle = React.useCallback((key) => {
    return () => {
      onSelected(key);
    }
  }, []);

  return (
    <Dialog
      open={dialogOpen}
      fullWidth={true}
      onBackdropClick={props.handleCloseDialog}
    >
      <MuiThemeProvider theme={theme}>
        <h3>{intl.formatMessage({ id: "stock.upload.all" })}</h3>
        <div style={{ margin: "0 auto", paddingBottom: "20px", display: "grid", rowGap: "10px" }}>
          {mediaTypes.map((mediaType = "") => (
            <DialogButton onClick={getClickHandle(mediaType)}>
              {intl.formatMessage({ id: `media.${mediaTypeMapping[mediaType] || mediaType}` })}
            </DialogButton>
          ))}
        </div>
      </MuiThemeProvider>
    </Dialog>
  );
}

export default injectIntl(UploadMediaTypeSelectedDialog);
