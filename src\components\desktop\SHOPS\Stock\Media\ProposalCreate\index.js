/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { submit, change } from "redux-form";
import { injectIntl } from "react-intl";
import TextField from "@material-ui/core/TextField";
import DialogFrame from "../../../../../common/DialogFrame";
import Dialog from "../../../../../common/Dialog";
import ProposalForm from "../../../Proposal/ProposalForm";
import {
  createProposal,
  clearCreateProposal,
} from "../../../../../../actions/proposal";
import {
  possessionLangIdMapping,
  getDefaultRemarks,
} from "../../../Proposal/FormSection/selectOptions";
import langFile from "../../../../../../lang/SHOPS/messages";
import SubmitDialog from "../../../../../common/SubmitDialog";
import {
  getLangKey,
  goToProposalList,
  getProposalTenancyDesc,
  parsePeriod,
} from "../../../../../../helper/generalHelper";
import { sbu, generalProposalQuota } from "../../../../../../config";

class ProposalCreate extends React.Component {
  static propTypes = {
    stockData: PropTypes.object,
    media: PropTypes.object,
    buildingMedia: PropTypes.object,
    userInfo: PropTypes.object,
    createProposal: PropTypes.func.isRequired,
    creatingProposal: PropTypes.bool,
    createdProposal: PropTypes.bool,
    createProposalError: PropTypes.string,
    clearCreateProposal: PropTypes.func.isRequired,
    dispatchSubmitForm: PropTypes.func.isRequired,
    createProposalDialogOpen: PropTypes.bool,
    handleCloseCreateProposalDialog: PropTypes.func.isRequired,
  };

  constructor(props) {
    super(props);
    const { stockData, media, buildingMedia } = props;
    this.state = {
      parsedStockData: this.stockDataToFormData(
        stockData,
        media,
        buildingMedia,
      ),
      proposalName: this.parseDefaultProposalName(stockData),
    };
  }

  submit = (values) => {
    // console.log(values);
    const { media, buildingMedia, stockData } = this.props;
    const photos = [].concat(media?.photo || [], buildingMedia?.photo || []);
    const videos = [].concat(media?.video || [], buildingMedia?.video || []);
    const getPhotoInfoFromRedux = (id) => {
      let photo = photos?.filter((v) => v.id === id)?.[0] || null;
      if (photo)
        photo = {
          id: photo.id,
          mediumRoot: photo.mediumRoot,
          photoContent: photo.photoContent,
        };
      return photo;
    };
    const getVideoInfoFromRedux = (id) => {
      let video = videos?.filter((v) => v.id === id)?.[0] || null;
      if (video)
        video = {
          id: video.id,
          mediumRoot: video.mediumRoot,
          youtubeId: video.youtubeMrId || video.youtubeHkpId || video.youtubeId,
        };
      return video;
    };
    const getPossessionInfoFromLangFile = (value) => {
      return {
        nameZh:
          value &&
          possessionLangIdMapping[value] &&
          langFile.zh[possessionLangIdMapping[value]]
            ? langFile.zh[possessionLangIdMapping[value]]
            : "",
        nameEn:
          value &&
          possessionLangIdMapping[value] &&
          langFile.en[possessionLangIdMapping[value]]
            ? langFile.en[possessionLangIdMapping[value]]
            : "",
      };
    };

    const currTenancyRecords = stockData?.tenancyRecords?.filter(
      (v) => v.status === "Current" && !v.deleted,
    );

    // stock photo which has the main tag
    const mainStockPhotoId = media?.photo?.filter(
      (v) => v.tags && v.tags.includes("main"),
    )?.[0]?.id;
    // building photo which has the main tag
    const mainBuildingPhotoId = buildingMedia?.photo?.filter(
      (v) => v.tags && v.tags.includes("main"),
    )?.[0]?.id;
    // main stock photo has higher priority
    const mainPhoto = getPhotoInfoFromRedux(
      mainStockPhotoId || mainBuildingPhotoId || null,
    );

    let parsedData = {
      ...values,
      type: values?.type,
      floor: values?.floor?.[0],
      unit: values?.unit?.[0],
      avgPrice: {
        ...values?.avgPrice?.[0],
        value: values?.avgPrice?.[0]?.value || 0,
      },
      totalPrice: {
        ...values?.totalPrice?.[0],
        value: values?.totalPrice?.[0]?.value || 0,
      },
      suggestedAvgPrice: {
        ...values?.suggestedAvgPrice?.[0],
        value: values?.suggestedAvgPrice?.[0]?.value || 0,
      },
      suggestedTotalPrice: {
        ...values?.suggestedTotalPrice?.[0],
        value: values?.suggestedTotalPrice?.[0]?.value || 0,
      },
      avgRent: {
        ...values?.avgRent?.[0],
        value: values?.avgRent?.[0]?.value || 0,
      },
      totalRent: {
        ...values?.totalRent?.[0],
        value: values?.totalRent?.[0]?.value || 0,
      },
      suggestedAvgRent: {
        ...values?.suggestedAvgRent?.[0],
        value: values?.suggestedAvgRent?.[0]?.value || 0,
      },
      suggestedTotalRent: {
        ...values?.suggestedTotalRent?.[0],
        value: values?.suggestedTotalRent?.[0]?.value || 0,
      },
      customBuilding: values?.customBuilding?.[0],
      customStreet: stockData?.streets?.[values?.streetType]?.street?.nameEn,
      customStreetNo: stockData?.streets?.[values?.streetType]?.number,
      areas: values?.areas?.concat(values?.area).map((v) => {
        return {
          ...v,
          gross: v.gross || 0,
          net: v.net || 0,
        };
      }),
      possession: {
        ...values?.possession?.[0],
        value: getPossessionInfoFromLangFile(values?.possession?.[0]?.value),
      },
      managementFee: {
        ...values?.managementFee?.[0],
        value: values?.managementFee?.[0]?.value || 0,
      },
      entranceWidth: {
        ...values?.entranceWidth?.[0],
        ft: values?.entranceWidth?.[0]?.ft || 0,
        in: values?.entranceWidth?.[0]?.in || 0,
      },
      unitDepth: {
        ...values?.unitDepth?.[0],
        ft: values?.unitDepth?.[0]?.ft || 0,
        in: values?.unitDepth?.[0]?.in || 0,
      },
      currentTenants: values?.currentTenants?.map((v, i) => {
        return {
          tenant: {
            nameEn: currTenancyRecords[i]?.tenant?.nameEn,
            nameZh: currTenancyRecords[i]?.tenant?.nameZh,
            isShow: v.tenantIsShow,
          },
          rentalFee: {
            value: v.rentalFee || 0,
            isShow: v.rentalFeeIsShow,
          },
          period: {
            min: currTenancyRecords[i]?.expiry?.minDate,
            max: currTenancyRecords[i]?.expiry?.maxDate,
            isShow: v.periodIsShow,
          },
          tenancy: {
            nameEn: getProposalTenancyDesc(currTenancyRecords[i], "en"),
            nameZh: getProposalTenancyDesc(currTenancyRecords[i], "zh"),
            isShow: v.tenancyIsShow,
          },
        };
      }),
      yield: {
        ...values?.yield?.[0],
        value: values?.yield?.[0]?.value || 0,
      },
      customTitle: values?.customTitle?.[0],
      hideEmployeePhoto: !!values?.hideEmployeePhoto?.[0]?.isShow,
      hideContact: !!values?.hideContact?.[0]?.isShow,
      photos: []
        .concat(values?.stockMedia || [], values?.buildingMedia || [])
        .map(getPhotoInfoFromRedux)
        .filter((v) => v !== null),
      videos: []
        .concat(values?.stockMedia || [], values?.buildingMedia || [])
        .map(getVideoInfoFromRedux)
        .filter((v) => v !== null),
      googleMap: {
        isPP: !!(values?.googleMapPhoto && values?.googleMapPhoto.length > 0),
        isMain1: values?.main1Photo === "map",
        isMain2: values?.main2Photo === "map",
      },
      main1Photo:
        values?.main1Photo && getPhotoInfoFromRedux(values?.main1Photo),
      main2Photo:
        values?.main2Photo && getPhotoInfoFromRedux(values?.main2Photo),
      mainPhoto: mainPhoto, // stock or building photo which has the main tag

      // additional data which is required by proposal but not shown in the form (or is not a field in the form)
      stockId: stockData?.unicorn?.id,
      stockMongoId: stockData?._id,
      sbu: sbu,
      isSoleagent: !!(
        (stockData.soleagent &&
          stockData.soleagent.saleData &&
          stockData.soleagent.saleData.periodStart &&
          stockData.soleagent.saleData.periodEnd) ||
        (stockData.soleagent &&
          stockData.soleagent.rentData &&
          stockData.soleagent.rentData.periodStart &&
          stockData.soleagent.rentData.periodEnd)
      ),
      districtNameZh: stockData?.district?.nameZh,
      districtNameEn: stockData?.district?.nameEn,
      streetNameZh: stockData?.streets?.[values?.streetType]?.street?.nameZh,
      streetNameEn: stockData?.streets?.[values?.streetType]?.street?.nameEn,
      streetNo: stockData?.streets?.[values?.streetType]?.number,
      buildingNameZh: stockData?.building?.nameZh,
      buildingNameEn: stockData?.building?.nameEn,
      buildingDistrictNameZh: stockData?.building?.district?.nameZh,
      buildingDistrictNameEn: stockData?.building?.district?.nameEn,
      floorInChinese: stockData?.floorInChinese,
      lng: stockData?.building?.coordinates?.longitude,
      lat: stockData?.building?.coordinates?.latitude,
      proposalName: this.state.proposalName,
    };

    // these photo and video fields are merged into photos and videos
    delete parsedData.stockMedia;
    delete parsedData.buildingMedia;
    // googleMapPhoto is converted to includeGoogleMap field
    delete parsedData.googleMapPhoto;
    // streetType is used to select the correct street name
    delete parsedData.streetType;
    // area is concatenated with areas
    delete parsedData.area;

    // console.log(parsedData)

    this.props.createProposal(parsedData);
  };

  stockDataToFormData = (detail, media, buildingMedia) => {
    // console.log(detail)
    const langKey = getLangKey(this.props.intl);

    const possibleFeeType = ["/SqFt", "/Qtr", "/Month", "/SY"];
    const possiblePaidBy = ["Paid By Tenant", "Paid By Landlord"];

    const areas = detail?.area?.areas || [];
    let areaItems = [];
    let netAreaTotal = 0;
    areas.forEach((v) => {
      if (areaItems.filter((type) => type.areaName === v.areaName).length === 0)
        areaItems.push({
          areaName: v.areaName,
          areaNameEn: v.areaNameEn,
          gross: 0,
          grossIsShow: false,
          grossVerified: false,
          net: 0,
          netIsShow: false,
          netVerified: false,
        });

      let areaItem = areaItems.filter(
        (type) => type.areaName === v.areaName,
      )[0];
      let areaType = v.type.toLowerCase();
      if (areaType === "gross") {
        areaItem.gross = v.value;
        areaItem.grossIsShow = v.value && parseFloat(v.value) > 0;
        areaItem.grossVerified = v.verified;
        // area of 閣樓, 自建樓 is unselectable
        if (areaItem.areaName === "閣樓" || areaItem.areaName === "自建閣") {
          areaItem.grossIsShow = false;
        }
      }
      if (areaType === "net") {
        areaItem.net = v.value;
        areaItem.netIsShow = false; // all net area default unselect
        areaItem.netVerified = v.verified;
        netAreaTotal += v.value;
      }
    });

    const currTenancyRecords = detail?.tenancyRecords?.filter(
      (v) => v.status === "Current" && !v.deleted,
    );

    let defaultType = "Sale";
    let defaultRemarks = getDefaultRemarks("Sale");
    switch (detail?.status?.nameEn) {
      case "Lease":
        defaultType = "Lease";
        defaultRemarks = getDefaultRemarks("Lease");
        break;
      case "Sale+Lease":
        defaultType = "SaleAndLease";
        defaultRemarks = getDefaultRemarks("SaleAndLease");
        break;
    }

    return {
      streetType: 0,
      type: defaultType,
      avgPrice: [
        {
          value: detail?.askingPrice?.average,
          isShow: false,
        },
      ],
      totalPrice: [
        {
          value: detail?.askingPrice?.total,
          isShow: defaultType === "Sale" || defaultType === "SaleAndLease",
        },
      ],
      suggestedAvgPrice: [
        {
          value:
            detail?.askingPrice?.details?.filter(
              (v) => v.type === "average_bottom",
            )[0]?.value || 0,
          isShow: false,
        },
      ],
      suggestedTotalPrice: [
        {
          value:
            detail?.askingPrice?.details?.filter(
              (v) => v.type === "total_bottom",
            )[0]?.value || 0,
          isShow:
            (defaultType === "Sale" || defaultType === "SaleAndLease") &&
            !!detail?.askingPrice?.details?.filter(
              (v) => v.type === "total_bottom",
            )[0]?.value,
        },
      ],
      avgRent: [
        {
          value: detail?.askingRent?.average,
          isShow: false,
        },
      ],
      totalRent: [
        {
          value: detail?.askingRent?.total,
          isShow: defaultType === "Lease" || defaultType === "SaleAndLease",
        },
      ],
      suggestedAvgRent: [
        {
          value:
            detail?.askingRent?.details?.filter(
              (v) => v.type === "average_bottom",
            )[0]?.value || 0,
          isShow: false,
        },
      ],
      suggestedTotalRent: [
        {
          value:
            detail?.askingRent?.details?.filter(
              (v) => v.type === "total_bottom",
            )[0]?.value || 0,
          isShow:
            (defaultType === "Lease" || defaultType === "SaleAndLease") &&
            !!detail?.askingRent?.details?.filter(
              (v) => v.type === "total_bottom",
            )[0]?.value,
        },
      ],
      floor: [
        {
          value: detail?.floor,
          isShow: true,
        },
      ],
      unit: [
        {
          value: detail?.unit,
          isShow: true,
        },
      ],
      customBuilding: [
        {
          value: detail?.building?.[langKey] || "---",
          isShow: !!detail?.building?.[langKey],
        },
      ],
      customStreet: detail?.street?.street?.[langKey] || "---",
      customStreetNo: detail?.street?.number || "---",
      customDistrict: detail?.district?.[langKey] || "---",
      areas: areaItems,
      area: [
        {
          areaName: "總面積",
          areaNameEn: "Total",
          gross: detail?.area?.total,
          grossIsShow: !!detail?.area?.total,
          grossVerified: false,
          net: netAreaTotal,
          netIsShow: false, // all net area default unselect
          netVerified: false,
        },
      ],
      possession: [
        {
          value: detail?.possession?.nameEn,
          isShow: !!detail?.possession?.nameEn,
        },
      ],
      managementFee: [
        {
          value: detail?.managementFee?.number || "",
          unit:
            possibleFeeType.indexOf(detail?.managementFee?.type) >= 0
              ? detail?.managementFee?.type
              : "",
          paidBy:
            possiblePaidBy.indexOf(detail?.managementFee?.paidBy) >= 0
              ? detail?.managementFee?.paidBy
              : "",
          isShow: !!detail?.managementFee?.number,
        },
      ],
      remarks: defaultRemarks,
      entranceWidth: [
        {
          ft: detail?.doorWidth?.ft || "",
          in: detail?.doorWidth?.in || "",
          isShow: !!(detail?.doorWidth?.ft || detail?.doorWidth?.in),
        },
      ],
      unitDepth: [
        {
          ft: detail?.unitDepth?.ft || "",
          in: detail?.unitDepth?.in || "",
          isShow: !!(detail?.unitDepth?.ft || detail?.unitDepth?.in),
        },
      ],
      currentTenants: currTenancyRecords?.map((v) => {
        const minDate = v?.expiry?.minDate;
        const maxDate = v?.expiry?.maxDate;
        return {
          tenant: v?.tenant?.[langKey] || "---",
          tenantIsShow: !!v?.tenant?.[langKey],
          rentalFee: v?.rentalFee,
          rentalFeeIsShow: !!v?.rentalFee && !!v?.tenant?.[langKey],
          period: parsePeriod(minDate, maxDate, this.props.intl),
          periodIsShow: !!(minDate || maxDate) && !!v?.tenant?.[langKey],
          tenancy: getProposalTenancyDesc(v, this.props.intl.locale),
          tenancyIsShow: !!v?.tenant?.[langKey],
        };
      }),
      yield: [
        {
          value: detail?.yield || "",
          isShow: !!detail?.yield,
        },
      ],
      customTitle: [
        {
          value: "",
          isShow: false,
        },
      ],
      hideEmployeePhoto: [
        {
          value: this.props.intl.formatMessage({
            id: "proposal.form.hideemployeephoto",
          }),
          isShow: false,
        },
      ],
      hideContact: [
        {
          value: this.props.intl.formatMessage({
            id: "proposal.form.hidecontact",
          }),
          isShow: false,
        },
      ],
      stockMedia: []
        .concat(media?.photo || [], media?.video || [])
        ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
        ?.map((v) => v.id),
      buildingMedia: []
        .concat(buildingMedia?.photo || [], buildingMedia?.video || [])
        ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
        ?.map((v) => v.id),
      main1Photo: "map",
    };
  };

  parseDefaultProposalName = (detail) => {
    const langKey = getLangKey(this.props.intl);
    const streetName = detail?.street?.street?.[langKey] || "";
    const streetNo = detail?.street?.number || "";
    return this.props.intl.locale === "zh"
      ? streetName + streetNo + "號"
      : streetNo + " " + streetName;
  };

  handleCloseSubmitDialog = () => {
    this.props.handleCloseCreateProposalDialog();
    this.props.clearCreateProposal();
  };

  submitDialogCallback = () => {
    goToProposalList();
  };

  handleNameChange = (e) => {
    this.setState({ proposalName: e.target.value });
  };

  render() {
    const {
      changeFieldValue,
      formFields,
      creatingProposal,
      createdProposal,
      createProposalError,
      dispatchSubmitForm,
      createProposalDialogOpen,
      exceedQuota,
      intl,
    } = this.props;
    const { proposalName } = this.state;

    return (
      <div>
        <div>
          <ProposalForm
            onSubmit={this.submit}
            initialValues={this.state.parsedStockData}
            changeFieldValue={changeFieldValue}
            formFields={formFields}
            handleProposalNameChange={(v) => this.setState({ proposalName: v })}
          />
        </div>

        {exceedQuota ? (
          <Dialog
            open={createProposalDialogOpen}
            handleClose={this.props.handleCloseCreateProposalDialog}
          >
            {intl.formatMessage(
              { id: "proposal.create.exceed" },
              { quota: generalProposalQuota },
            )}
            <DialogFrame
              buttonMain={intl.formatMessage({
                id: "common.ok",
              })}
              handleMain={this.handleCloseSubmitDialog}
            ></DialogFrame>
          </Dialog>
        ) : (
          <SubmitDialog
            dialogOpen={createProposalDialogOpen}
            handleCloseDialog={this.handleCloseSubmitDialog}
            succCallback={this.submitDialogCallback}
            submitting={creatingProposal}
            submitted={createdProposal}
            error={createProposalError}
            submit={dispatchSubmitForm}
            submitBtnText={intl.formatMessage({ id: "proposal.form.save" })}
            succMsg={intl.formatMessage(
              { id: "proposal.form.savesuccess" },
              { filename: proposalName },
            )}
          >
            <TextField
              label={intl.formatMessage({ id: "proposal.form.proposalname" })}
              value={proposalName}
              onChange={this.handleNameChange}
              variant="outlined"
              fullWidth
            />
          </SubmitDialog>
        )}
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  userInfo:
    state.auth &&
    state.auth.user &&
    state.auth.user.login &&
    state.auth.user.login.info
      ? state.auth.user.login.info
      : {},
  stockData: state.stock.detail ? state.stock.detail : {},
  media: state.stock.media ? state.stock.media : {},
  buildingMedia: state.building.media ? state.building.media : {},
  formFields:
    state.form.proposalForm && state.form.proposalForm.values !== undefined
      ? state.form.proposalForm.values
      : {},
  creatingProposal: state.proposal.creatingProposal
    ? state.proposal.creatingProposal
    : false,
  createdProposal: state.proposal.createdProposal
    ? state.proposal.createdProposal
    : false,
  createProposalError:
    state.proposal.createProposalError &&
    state.proposal.createProposalError.message
      ? state.proposal.createProposalError.message
      : null,
});

const mapDispatchToProps = (dispatch) => {
  return {
    dispatchSubmitForm: () => dispatch(submit("proposalForm")),
    changeFieldValue: (field, value) =>
      dispatch(change("proposalForm", field, value)),
    createProposal: (...args) => dispatch(createProposal(...args)),
    clearCreateProposal: (...args) => dispatch(clearCreateProposal(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(ProposalCreate));
