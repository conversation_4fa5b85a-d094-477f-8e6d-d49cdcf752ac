import React, { useRef, useEffect } from "react";
import TextField from "@material-ui/core/TextField";
import { makeStyles } from "@material-ui/core/styles";
import CreatableSelect from "react-select/creatable";
import Paper from "@material-ui/core/Paper";
import { injectIntl } from "react-intl";
import _ from "lodash";

const useStyles = makeStyles((theme) => ({
  root: {
    // marginLeft: theme.spacing(1),
    // marginRight: theme.spacing(1)
    // width: '100%',
  },
  input: {
    display: "flex",
    // padding: "2px 0 2px 14px",
    height: "auto",
    cursor: "pointer",
  },
  valueContainer: {
    display: "flex",
    flexWrap: "wrap",
    flex: 1,
    alignItems: "center",
    overflow: "hidden",
  },
  paper: {
    position: "absolute",
    marginTop: theme.spacing(1),
    left: 0,
    right: 0,
    zIndex: 999,
  },
}));

function Control(props) {
  const {
    children,
    innerProps,
    innerRef,
    selectProps: { classes, TextFieldProps },
  } = props;

  return (
    <TextField
      fullWidth
      InputProps={{
        inputComponent,
        inputProps: {
          className: classes.input,
          ref: innerRef,
          children,
          ...innerProps,
        },
      }}
      {...TextFieldProps}
    />
  );
}

function Menu(props) {
  return (
    <Paper
      square
      className={props.selectProps.classes.paper}
      {...props.innerProps}
    >
      {props.children}
    </Paper>
  );
}

function ValueContainer(props) {
  return (
    <div className={props.selectProps.classes.valueContainer}>
      {props.children}
    </div>
  );
}

function inputComponent({ inputRef, ...props }) {
  return <div ref={inputRef} {...props} />;
}

const components = {
  Control,
  Menu,
  ValueContainer,
};

function ReactSelectCreatable(props) {
  const {
    input,
    optionsdata,
    apiaction,
    selectedData,
    setSelectedData,
    customInputProps,
    disabled,
    autoCreateOnBlur,
    intl,
  } = props;
  const { value, name, onChange, onBlur } = input;
  const { touched, invalid, error } = props.meta;
  const [single, setSingle] = React.useState(selectedData || []);
  const [inputLang, setInputLang] = React.useState("nameEn");
  const classes = useStyles();

  const [inputValue, setInputValue] = React.useState("");
  let searchHistory = props.history ? props.history : [];

  React.useEffect(() => {
    setSingle(selectedData);
  }, [selectedData]);

  function getoptions() {
    let data =
      searchHistory.length > 0 && !inputValue ? searchHistory : optionsdata;

    const options = data.map((item) => ({
      value: item._id,
      label: item[inputLang],
    }));

    if (apiaction && !(options && options.length > 0)) {
      const graphqlvariable = {
        name: "",
        limit: 5,
        offset: 0,
        sorter: [{ field: "nameEn", order: "ASC" }],
      };
      apiaction(graphqlvariable);
    }
    return options;
  }

  function handleInputChange(newValue, { action }) {
    setInputLang(
      newValue.toLowerCase().match(/[a-z]/i) || newValue === ""
        ? "nameEn"
        : "nameZh",
    );
    setInputValue(newValue);

    if (newValue !== "" && apiaction) {
      const graphqlvariable = {
        name: newValue.trim(),
        limit: 5,
        offset: 0,
        sorter: [{ field: inputLang, order: "ASC" }],
      };
      apiaction(graphqlvariable);
    }
    return newValue;
  }

  function handleClick(newValue) {
    setSingle(newValue);
    if (setSelectedData) setSelectedData(name, newValue);
    if (newValue && newValue.length > 0) {
      onChange(newValue.map((item) => item.value));
    } else {
      onChange([]);
    }
  }

  const selectStyles = {
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected ? "#33CCCC" : "#fff",
      ":active": {
        backgroundColor: state.isSelected ? "#33CCCC" : "#33CCCC",
      },
    }),
    placeholder: (styles, { isDisabled }) => ({
      ...styles,
      color: isDisabled ? "rgba(0, 0, 0, 0.38)" : "hsl(0,0%,20%)",
    }),
  };

  const handleMenuClose = () => {
    if (autoCreateOnBlur) {
      if (inputValue && !_.find(single, { value: inputValue })) {
        handleClick([
          ...(single || []),
          { value: inputValue, label: inputValue },
        ]);
      }
    }
  };

  const noOptionsMessage = ({ inputValue }) => {
    if (!inputValue) {
      return "Please input and press enter";
    }
    return `${inputValue} already exists`;
  };

  return (
    <div className={classes.root}>
      <CreatableSelect
        isMulti
        // loadOptions={loadOptions}
        defaultOptions
        options={getoptions()}
        onChange={handleClick}
        onInputChange={handleInputChange}
        onMenuClose={handleMenuClose}
        classes={classes}
        styles={selectStyles}
        noOptionsMessage={noOptionsMessage}
        TextFieldProps={{
          label: props.label,
          error: touched && invalid,
          helperText: touched && error,
          InputLabelProps: {
            htmlFor: name || null,
            shrink: true,
          },
          margin: "dense",
          variant: "outlined",
          ...customInputProps,
        }}
        placeholder={
          props.placeholder ||
          intl.formatMessage({
            id: "search.form.select",
          })
        }
        components={components}
        value={value !== "" ? single : null}
        isClearable
        isDisabled={disabled}
        instanceId={name}
      />
    </div>
  );
}

export default injectIntl(ReactSelectCreatable);
