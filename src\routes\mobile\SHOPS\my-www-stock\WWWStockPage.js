/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import {connect} from "react-redux";
import withStyles from "isomorphic-style-loader/lib/withStyles";
import {injectIntl} from "react-intl";

import s from "./WWWStock.css";
import WWWStockList from "@/components/mobile/SHOPS/WWWStockList";

class WWWStockPage extends React.Component {
  static propTypes = {};

  constructor(props) {
    super(props);
    this.state = {
      isFirstFetch: true,
      appIsMounted: false,
      init: null,
    };
  }

  componentDidMount() {
    const {headerRef} = this.props;

    if (headerRef.current)
      headerRef.current.changeSubTitle("");

    requestAnimationFrame(() => {
      this.setState({appIsMounted: true});
    });
  }


  render() {
    return (
      <div>
        {this.state.appIsMounted && (
          <React.Fragment>
            <WWWStockList isFirstFetch={this.state.isFirstFetch}/>
          </React.Fragment>
        )}
      </div>
    );
  }
}

const mapStateToProps = (state) => ({});

const mapDispatchToProps = (dispatch) => ({});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(s)(injectIntl(WWWStockPage)));
