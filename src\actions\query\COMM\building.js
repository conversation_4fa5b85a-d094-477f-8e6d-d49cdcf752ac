export const LIST_SIMPLE_BUILDINGS_SOURCE_QUERY = `
query ($name: String, $first: Int){
  buildingSourceSearch (name: $name, first: $first){
    id
    nameEn
    nameZh
    previousNames{
      nameEn
      nameZh
    }
    districtDetail{
      abbr
    }
  }
}
`;

export const LIST_BUILDINGS_QUERY = `
  query ($name: String, $limit: Int, $offset: Int, $sorter: [buildingSorter]) {
    buildings(name: $name, limit: $limit, offset: $offset, sort: $sorter) {
      _id
      nameEn
      nameZh
    }
  }
  `;

export const LIST_UNIT_VIEWS_QUERY = `
  {
    unitViews {
      _id
      nameZh
      nameEn
    }
  }
  `;

export const LIST_FLOOR_ORDERING_QUERY = `
  query {
    listFloorOrdering(all: true){
      _id
      floor
      ordering
      maxordering
    }
  }
`;

export const LIST_BUILDING_DETAIL_QUERY = `
  query($_id: ID!) {
    source(id: $_id) {
      id
      unicornId
      nameEn
      nameZh
      streets {
        nameEn
        streetNo
      }
      districtDetail {
        nameEn
      }
      usage
      grade
      closeToMTR
      inTakeDate
      managementFeeZh
      managementFeeEn
      isIncludeAirCondCharge
      developers {
        nameEn
      }
      managementCompanies {
        nameEn
      }
      # managementCompanyPerson
      # managementCompanyContact
      managementCompanyPeople
      managementCompanyContacts
      floors {
        type
        name
      }
      lobby
      wall
      ceiling
      numberOfPassengerLift
      haveCargoLift
      numberOfFloors
      floorArea {
        minArea
        maxArea
      }
      totalArea
      efficiency
      ceilingHeightZh
      ceilingHeightEn
      loadingArea
      carPark
      carParkHourlyParking
      carParkMonthlyParking
      airConditioning
      airConditioningOpeningTimeRemark
      airConditioningExtraCharge
      surroundings
      liftDistributionZh
      liftDistributionEn
      remarkZh
      remarkEn
      previousNames {
        nameEn
        nameZh
      }
    }
  }
  `;

export const LIST_BUILDING_MEDIA_QUERY = `
  query($sid: ID!, $empId: String) {
    building(id: $sid) {
      id
      photo: media(type:"photo", empId: $empId) {
        id
        type
        tags
        markByWWW
        scoring 
        manualOffline  
        employeeId
        availableStartTime
        availableEndTime
        createdDate
        filename
        description
        originalFilename
        approval
        status
        ... on Photo {
          mediumRoot
          photoContent
        }
      }

      document: media(type: "document", empId: $empId) {
        id
        type
        tags
        markByWWW
        scoring 
        manualOffline  
        employeeId
        createdDate
        lastModified
        approval
        description
        processing
        status
        ...on Document {
          filename
          originalFilename
          mediumRoot
          documentContent
        }
      }
      video: media(type:["video", "embedded_script"], empId: $empId) {
        id
        type
        tags
        markByWWW
        scoring 
        manualOffline  
        employeeId
        availableStartTime
        availableEndTime
        createdDate
        filename
        description
        originalFilename
        approval
        processing
        status
        ... on Video {
          youtubeId
          youtubeMrId
          youtubeHkpId
          youkuId
          vimeoId
          mediumRoot
          thumbnail
        }
        ... on EmbeddedScript {
          script
          thumbnail
        }
      }
      kolVideo: media(type:["kol_video"], empId: $empId) {
        id
        type
        tags
        markByWWW
        scoring 
        manualOffline  
        employeeId
        availableStartTime
        availableEndTime
        createdDate
        lastModified
        approval
        filename
        description
        originalFilename
        processing
        status
        ... on KolVideo {
          mediumRoot
          thumbnail
          characteristicZh
          characteristicEn
          buildingName
          address
          propertyRefId
          buildingId
          vimeoId
          operator {
            eName
          }
        }
      }
      virtualTour: media(type:"virtual_tour", empId: $empId) {
        id
        type
        tags
        markByWWW
        scoring 
        manualOffline  
        employeeId
        availableStartTime
        availableEndTime
        createdDate
        approval
        status
        ... on VirtualTour {
          virtualTourId
          thumbnail
        }
      }
    }
  }
  `;
