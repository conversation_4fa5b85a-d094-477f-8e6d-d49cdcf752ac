## Contributing to React Starter Kit

React Starter Kit is currently the most widely adopted Node.js/React boilerplate used by many
tech startups around the globe. We're working hard to keep it up to date, making sure that it
follows best practices and high coding standards, paying extremely close attention to details.

Your contributions are welcome and are greatly appreciated! Every little bit helps, and credit
will always be given.

Please take a moment to review this document in order to make the contribution process easy and
effective for everyone involved.

### Conduct

Please, follow the [golden rule](https://en.wikipedia.org/wiki/Golden_Rule). Be respectful, even to
those that are disrespectful.

### Feedback

Feedback is the breakfast for champions! We'd love to hear your opinions, discuss potential
improvements, architecture, theory, internal implementation, etc. Please, join or start a new
conversation in our [issue tracker](https://github.com/kriasoft/react-starter-kit/issues),
[Gitter](https://gitter.im/kriasoft/react-starter-kit) chat room, or let's talk face-to-face on
[Appear.in](https://appear.in/react) or [Skype](http://hatscripts.com/addskype?koistya).

### Documentation

We need your help with improving documentation to the project. This might be the easiest way for
you to contribute, because you don't even need to clone the repo but can edit or create new `.md`
files right from GitHub website as described [here](https://help.github.com/articles/editing-files-in-your-repository/).

### Bugs & Feature Requests

Before opening an issue, please:

* Check the [Getting Started](https://github.com/kriasoft/react-starter-kit/blob/master/docs/getting-started.md) guide.
* Search the [issue tracker](https://github.com/kriasoft/react-starter-kit/issues) to make sure
  your issue hasn’t already been reported.
* If your issue sounds more like a question, please post it on StackOverflow.com instead with the
  tag [react-starter-kit](http://stackoverflow.com/questions/tagged/react-starter-kit).

### Pull Requests

Before you submit a [pull request](https://help.github.com/articles/using-pull-requests/) from your
forked repo, check that it meets these guidelines:

* If the pull request adds functionality, the docs should be updated as part of the same PR.
* Create a separate PR for each small feature or bug fix.
* [Squash](http://stackoverflow.com/questions/5189560/squash-my-last-x-commits-together-using-git)
  your commits into one for each PR.
* Run `yarn test` to make sure that your code style is OK and there are no any regression bugs.
* When contributing to an opt-in feature, apply the `[feature/...]` tag as a prefix to your PR title

#### Style Guide

Our linter will catch most styling issues that may exist in your code. You can check the status
of your code styling by simply running: `yarn lint`

However, there are still some styles that the linter cannot pick up. If you are unsure about
something, looking at [Airbnb's Style Guide](https://github.com/airbnb/javascript) will guide you
in the right direction.

### License

By contributing to React Starter Kit, you agree that your contributions will be licensed under its
[MIT license](https://github.com/kriasoft/react-starter-kit/blob/master/LICENSE.txt).
