import React from "react";
import PropTypes from "prop-types";
import moment from "moment";
import { withStyles } from "@material-ui/styles";
import DetailBoxTitle from "../../../../common/DetailBoxTitle";
import {
  number<PERSON>omma,
  get<PERSON><PERSON><PERSON><PERSON>,
  convertNewlineToBr,
  paresFloorShopNo,
} from "../../../../../helper/generalHelper";
import clsx from "clsx";
import { injectIntl } from "react-intl";
import FormButton from "../../../../common/FormButton";
import Link from "../../../../Link";
import PropertyTagBar from "../../../../common/PropertyTagBar";
import NormalAndBottomPrice from "../../../../common/NormalAndBottomPrice";
import InfoIcon from "@material-ui/icons/Info";
import Grid from "@material-ui/core/Grid";
import FieldVal from "../../../../common/FieldVal";
import FieldValHorizontal from "../../../../common/FieldValHorizontal";
import AreaItem from "../../../../common/AreaItem";

const styles = (theme) => ({
  root: {
    // padding: "0 2vw"
  },
  textClass: {
    fontSize: "1.175em",
  },
  nameAndBtn: {
    display: "flex",
    alignItems: "center",
    "& > *:last-child": {
      flex: "0 0 auto",
    },
  },
  right: {
    textAlign: "right",
  },
  twoLevelItem: {
    marginBottom: "1vh",
    "& > *:not(:first-child)": {
      color: "rgba(255, 255, 255, 0.75)",
    },
    padding: "0 4px",
    borderRadius: 4,
    color: "#fff",
  },
  rentItem: {
    backgroundColor: "rgba(0, 197, 197, .75)",
  },
  priceItem: {
    backgroundColor: "rgba(232, 0, 0, .75)",
  },
  greyItem: {
    backgroundColor: "rgba(132, 132, 132, .1)",
  },
  rentItemNoValue: {
    backgroundColor: "rgba(140, 190, 190, 0.2)",
  },
  priceItemNoValue: {
    backgroundColor: "rgba(200, 170, 170, 0.2)",
  },
  btn: {
    height: 22,
    lineHeight: "22px",
    // fontSize: ".875em",
    padding: "0 10px",
    marginLeft: "2vw",
    textTransform: "none",
    background: "#9932CC",
    minWidth: 0,
    display: "flex",
  },
  btnIcon: {
    width: 16,
    height: 16,
    marginLeft: 4,
  },
  link: {
    textDecoration: "none",
  },
  section: {
    marginBottom: "2vh",
  },
  areaForm: {
    display: "flex",
    justifyContent: "flex-end",
  },
  areaSmallFont: {
    fontSize: ".75em",
  },
  lmrAlign: {
    paddingLeft: "2vw",
  },
  commentBox: {
    padding: "8px",
    marginLeft: "-8px",
    borderRadius: "4px",
    backgroundColor: "rgba(255, 255, 100, .4)",
    lineHeight: "1.5",
  },
  updateDateFields: {
    padding: "0 2vw",
    margin: "5vh 0 3vh",
    textAlign: "right",
  },
  subStreet: {
    color: "#777",
  },
  mainAreaItem: {
    color: "#777",
    paddingTop: ".8vh",
    marginTop: ".8vh",
    borderTop: "1px solid #777",
  },
});

class StockDetail extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langKey = getLangKey(intl);

    const unit = detail.unit || "---";
    const floor = detail.floor || "---";
    const floorInChinese = detail.floorInChinese || "---";
    const buildingName =
      detail.building && detail.building[langKey]
        ? detail.building[langKey]
        : "---";
    const streetNo =
      detail.street && detail.street.number ? detail.street.number : "";
    const streetName =
      detail.street && detail.street.street && detail.street.street[langKey]
        ? detail.street.street[langKey]
        : "---";
    let subStreets = detail.streets || [];
    subStreets = subStreets.slice(1).map(v => {
      let name = v.street && v.street[langKey] ? v.street[langKey] : "---";
      let number = v.number || "";
      if (intl.locale === "zh")
        return name + " " + number + intl.formatMessage({ id: "stock.number" });
      else
        return number + " " + name;
    });
    const district =
      detail.district && detail.district[langKey]
        ? detail.district[langKey]
        : "---";
    const buildingId =
      detail.building && detail.building._id ? detail.building._id : null;
    const buildingunicornId =
      detail.building && detail.building.unicorn && detail.building.unicorn.id
        ? detail.building.unicorn.id
        : null;
    const floorUnitZh = floor + " " + unit + " " + intl.formatMessage({ id: "stock.numberofshop" });
    const floorUnitEn = floor + ", " + intl.formatMessage({ id: "stock.numberofshop" }) + " " + unit;
    const floorUnitRow = intl.locale === "zh" ? floorUnitZh : floorUnitEn;
    const streetDistrictZh = district + " " + streetName + " " + (streetNo ? streetNo + intl.formatMessage({ id: "stock.number" }) : "");
    const streetDistrictEn = streetNo + " " + streetName + ", " + district;
    const streetDistrictRow = intl.locale === "zh" ? streetDistrictZh : streetDistrictEn;

    const areaTotal = {
      type: intl.formatMessage({ id: "stock.area.gross" }),
      value: detail.area && detail.area.total,
    };
    const areas = detail.area && detail.area.areas ? detail.area.areas : [];
    let areaData = {};
    let netAreaTotal = 0;
    areas.forEach(v => {
      const key = v[intl.locale === "zh" ? "areaName" : "areaNameEn"];
      if (!areaData[key])
        areaData[key] = {
          gross: { type: intl.formatMessage({ id: "stock.area.gross" }) },
          net: { type: intl.formatMessage({ id: "stock.area.net" }) },
        };

      let areaType = v.type.toLowerCase();
      if (areaType === "gross") {
        areaData[key].gross.value = v.value;
        areaData[key].gross.verified = v.verified;
      }
      if (areaType === "net") {
        areaData[key].net.value = v.value;
        areaData[key].net.verified = v.verified;
        netAreaTotal += v.value
      }
    });

    const remarksQuick =
      detail.remarks && detail.remarks.quick
        ? convertNewlineToBr(detail.remarks.quick)
        : "---";
    const createDate =
      detail.recordOperation && detail.recordOperation.createDate
        ? detail.recordOperation.createDate
        : "---";
    const lastUpdateDate =
      detail.recordOperation && detail.recordOperation.lastUpdateDate
        ? detail.recordOperation.lastUpdateDate
        : "---";

    const status =
      detail && detail.status && detail.status.nameEn
        ? detail.status.nameEn
        : null;
    let priceContainer, leaseContainer;
    let askingPrice = detail.askingPrice;
    let askingRent = detail.askingRent;
    switch (status) {
      case "Sale": //出售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        askingRent = {};
        break;
      case "Lease": //出租
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        askingPrice = {};
        break;
      case "Sale+Lease": //租及售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Pending": //封盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Search": //查冊盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Don't Call": //拒致電
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Sold": //已售
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Leased": //已租
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "History": //舊資料
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "Cancel": //錯盤
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      case "CU": //未定
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
      default:
        priceContainer = classes.priceItem;
        leaseContainer = classes.rentItem;
        break;
    }
    const hasPrice = askingPrice && (askingPrice.total || askingPrice.average);
    const hasRent = askingRent && (askingRent.total || askingRent.average);

    const buildingInfoBtn = buildingId && (
      <Link className={classes.link} to={"/building/" + buildingId}>
        <FormButton
          className={classes.btn}
          onClick={() => {
            window.dataLayer.push({ buildingId: buildingunicornId });
          }}
        >
          {intl.formatMessage({
            id: "stock.building",
          })}
          <InfoIcon className={classes.btnIcon} />
        </FormButton>
      </Link>
    );

    const now = Date.now();
    const saleData =
      detail.propertyAdvertisements && detail.propertyAdvertisements.saleData
        ? detail.propertyAdvertisements.saleData
        : {};
    const rentData =
      detail.propertyAdvertisements && detail.propertyAdvertisements.rentData
        ? detail.propertyAdvertisements.rentData
        : {};
    const dataArr = [saleData, rentData];
    let tagEaa = false;
    for (let i = 0; i < dataArr.length; i++) {
      let minDate = dataArr[i].minDate
        ? moment(dataArr[i].minDate, "YYYY-MM-DD").format("x")
        : null;
      let maxDate = dataArr[i].maxDate
        ? moment(dataArr[i].maxDate, "YYYY-MM-DD").format("x")
        : null;

      if (minDate && maxDate) {
        tagEaa = minDate < now && now < maxDate;
      } else if (minDate) {
        tagEaa = minDate < now;
      } else if (maxDate) {
        tagEaa = now < maxDate;
      }
    }
    const tagMtg = !!detail.mortgagee;
    const tagSB = !!(
      detail.soleagent &&
      detail.soleagent.saleData &&
      detail.soleagent.saleData.periodStart &&
      detail.soleagent.saleData.periodEnd
    );
    const tagSR = !!(
      detail.soleagent &&
      detail.soleagent.rentData &&
      detail.soleagent.rentData.periodStart &&
      detail.soleagent.rentData.periodEnd
    );
    const tagMLL = !!detail.isShoppingMallStock;
    const tagSSS = !!detail.isSingleSideStock;
    const tagFnR = !!detail.isFrontAndRearPortion;
    const tagKey = !!detail.haveKey;
    const tagPD = !!detail.haveKey; // TODO: field is deleted (TBC)
    const tagREP = !!(detail.leasingInfo && detail.leasingInfo.isHandOver);
    const tagBD = detail.promote && detail.promote.boardStatus === "已掛板";
    const tagWWW  = !!detail.isWWW;
    const tagVideo  = !!detail.haveVideo;
    const tagsOverride = {
      [intl.formatMessage({ id: "stock.tag.sole" })]: null,
      [intl.formatMessage({ id: "stock.tag.carpark" })]: null,
      [intl.formatMessage({ id: "stock.tag.new" })]: null,
      [intl.formatMessage({ id: "stock.tag.equaity" })]: null,
      [intl.formatMessage({ id: "stock.tag.soleagentrent" })]: tagSR,
      [intl.formatMessage({ id: "stock.tag.soleagentbuy" })]: tagSB,
      [intl.formatMessage({ id: "stock.tag.mortgagee" })]: tagMtg,
      [intl.formatMessage({ id: "stock.tag.eaa" })]: tagEaa,
      [intl.formatMessage({ id: "stock.tag.shoppingmall" })]: tagMLL,
      [intl.formatMessage({ id: "stock.tag.singleside" })]: tagSSS,
      [intl.formatMessage({ id: "stock.tag.frontandrear" })]: tagFnR,
      [intl.formatMessage({ id: "stock.tag.key" })]: tagKey,
      // [intl.formatMessage({ id: "stock.tag.pending" })]: tagPD,
      [intl.formatMessage({ id: "stock.tag.replacement" })]: tagREP,
      [intl.formatMessage({ id: "stock.tag.www" })]: tagWWW,
      [intl.formatMessage({ id: "stock.video" })]: tagVideo,
    };

    return (
      <div className={classes.root}>
        <DetailBoxTitle
          text={streetDistrictRow}
          text2={paresFloorShopNo(floor, floorInChinese, unit, intl)}
          textClass={classes.textClass}
          subtitle2={
            <div className={classes.nameAndBtn}>
              <div>{buildingName}</div>
              <div>{buildingInfoBtn}</div>
            </div>
          }
        >
          <div className={classes.section}>
            <div className={classes.subStreet}>
              {subStreets.map((v, i) =>
                <div key={i}>{v}</div>
              )}
            </div>
          </div>
          <div className={`${classes.section} ${classes.areaForm}`}>
            <div>
              {Object.keys(areaData).map((v, i) =>
                <AreaItem key={i} areaName={v} type1={areaData[v].net} type2={areaData[v].gross} />
              )}
              <AreaItem
                className={classes.mainAreaItem}
                areaName={intl.formatMessage({ id: "stock.area.total" })}
                type1={{ type: intl.formatMessage({ id: "stock.area.net" }), value: netAreaTotal }}
                type2={areaTotal}
              />
            </div>
          </div>
          <div className={classes.right}>
            <div
              className={clsx(
                classes.twoLevelItem,
                leaseContainer,
                !hasRent && classes.rentItemNoValue
              )}
            >
              <NormalAndBottomPrice
                data={askingRent}
                avgDecimal={2}
                label="Rent"
              />
            </div>

            <div
              className={clsx(
                classes.twoLevelItem,
                priceContainer,
                !hasPrice && classes.priceItemNoValue
              )}
            >
              <NormalAndBottomPrice data={askingPrice} label="Price" />
            </div>
          </div>

          <div className={classes.section}>
            <PropertyTagBar detail={detail} tagsOverride={tagsOverride} />
          </div>

          <Grid container spacing={2} className={classes.lmrAlign}>
            <Grid item xs={12}>
              <FieldVal
                field={intl.formatMessage({
                  id: "stock.comment",
                })}
              />
              <FieldVal className={classes.commentBox}>{remarksQuick}</FieldVal>
            </Grid>
          </Grid>

          <Grid container className={classes.updateDateFields}>
            <Grid item xs={12}>
              <FieldValHorizontal
                field={intl.formatMessage({ id: "stock.stockcreate" })}
              >
                {createDate}
              </FieldValHorizontal>
            </Grid>
            <Grid item xs={12}>
              <FieldValHorizontal
                field={intl.formatMessage({ id: "stock.stockupdate" })}
              >
                {lastUpdateDate}
              </FieldValHorizontal>
            </Grid>
          </Grid>
        </DetailBoxTitle>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(StockDetail));
