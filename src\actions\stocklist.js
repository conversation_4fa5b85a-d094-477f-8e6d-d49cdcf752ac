/* eslint-disable import/prefer-default-export */
import _ from "lodash";
import fetch from "node-fetch";
import U<PERSON><PERSON> from "uuid/v1";

import {
  LIST_SEARCHRESULT_START,
  LIST_MORESEARCHRESULT_START,
  LIST_SEARCHRESULT_SUCCESS,
  LIST_SEARCHRESULT_ERROR,
  LIST_SEARCHRESULTNULL_SUCCESS,
  LIST_UNLOCKEDSEARCHRESULT_START,
  LIST_MOREUNLOCKEDSEARCHRESULT_START,
  LIST_UNLOCKEDSEARCHRESULT_SUCCESS,
  LIST_UNLOCKEDSEARCHRESULT_ERROR,
  LIST_UNLOCKEDSEARCHRESULTNULL_SUCCESS,
  LIST_SEARCHRESULTBYKEYWORDS_SUCCESS,
  LIST_SEARCHRESULTBYKEYWORDSNULL_SUCCESS,
  LIST_DECORATION_START,
  LIST_DECORATION_SUCCESS,
  LIST_DECORATION_ERROR,
  LIST_POSSESSION_START,
  LIST_<PERSON>OSSESSION_SUCCESS,
  LIST_POSSESSION_ERROR,
  LIST_USAGE_START,
  LIST_USAGE_SUCCESS,
  LIST_USAGE_ERROR,
  LIST_CURRENTSTATE_START,
  LIST_CURRENTSTATE_SUCCESS,
  LIST_CURRENTSTATE_ERROR,
  LIST_STOCKTYPE_START,
  LIST_STOCKTYPE_SUCCESS,
  LIST_STOCKTYPE_ERROR,
  UNLOCK_STOCK_START,
  UNLOCK_STOCK_SUCCESS,
  UNLOCK_STOCK_ERROR,
  GET_UNLOCKCOUNT_SUCCESS,
  CLEAR_SEARCHRESULT,
  CLEAR_UNLOCKEDSEARCHRESULT,
  LIST_QUERY_SAVEHISTORY,
  GET_SEARCHHISTORY,
  SET_SEARCHRESULT_ANCHOR,
  CLEAR_SEARCHRESULT_ANCHOR,
  SAVE_SEARCH_START,
  SAVE_SEARCH_SUCCESS,
  SAVE_SEARCH_UPSERT_SUCCESS,
  SAVE_SEARCH_ERROR,
  CLEAR_SAVE_SEARCH,
  LIST_SEARCH_START,
  LIST_SEARCH_SUCCESS,
  LIST_SEARCH_ERROR,
  CLEAR_LIST_SEARCH,
  LIST_COMPANY_START,
  LIST_COMPANY_SUCCESS,
  LIST_COMPANY_ERROR,
  CLEAR_LIST_COMPANY,
} from "../constants/stocklist";
import { listMarkStock } from "./stock";
import config from "../config";
import { addActivityLog } from "./log";

export function listStockList(
  variables,
  { isFetchingMore, isUnlockPage, intl, selectedData },
  storeField = "building",
) {
  return async (
    dispatch,
    getState,
    { graphqlRequest, api, getQuery, takeLog, delay, universalRequest },
  ) => {
    const { emp_id } = getState().auth.user.login.info;
    const items = getState()[`${storeField}`][`${storeField}s`];
    let requiredField = storeField == "streets" ? `${storeField}` : storeField;

    if (variables[requiredField] && variables[requiredField].length > 0) {
      let selectedItems;
      const lastItem =
        variables[requiredField][variables[requiredField].length - 1];
      const foundItem = items.find((item) => {
        return item._id === lastItem;
      });

      selectedItems = foundItem;

      dispatch({
        type: LIST_QUERY_SAVEHISTORY,
        payload: selectedItems != null ? selectedItems : [],
      });
    }

    const uuid = UUID();
    if (isFetchingMore) {
      dispatch({
        type: isUnlockPage
          ? LIST_MOREUNLOCKEDSEARCHRESULT_START
          : LIST_MORESEARCHRESULT_START,
        payload: {
          variables,
          selectedData,
          uuid,
        },
        checkrefreshToken: true,
      });
    } else {
      dispatch({
        type: isUnlockPage
          ? LIST_UNLOCKEDSEARCHRESULT_START
          : LIST_SEARCHRESULT_START,
        payload: {
          variables,
          selectedData,
          uuid,
        },
        checkrefreshToken: true,
      });
    }

    try {
      !isUnlockPage && (await dispatch(listMarkStock()));
      if (!isUnlockPage && variables.sorter && variables.sorter.length === 1 && variables.sorter[0].field === "PP Stock") {
        variables.sorter = [];
        variables.stockIds = getState().stock.markStockIds;
        if (getState().stock.markStockIds.length <= 0) {
          dispatch({
            type: isUnlockPage
              ? LIST_UNLOCKEDSEARCHRESULTNULL_SUCCESS
              : LIST_SEARCHRESULTNULL_SUCCESS,
            payload: {
              data: {
                stocks: [],
                stocksCount: 0,
              },
              uuid,
            },
          });
          return;
        }
      } else if (variables?.sorter) {
        delete variables.stockIds;
      }
      const newVariables = !isUnlockPage
        ? {
            ...variables,
            includeId: getState().stock.markStockIds,
          }
        : variables;
      newVariables.sbu = [config.sbu];

      // console.log("after called LIST_SEARCHRESULT_START");
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));
      // await delay(2000);

      const token = getState().auth.user.oauth;
      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: token,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("LIST_STOCKLIST_QUERY");

      const resp = await universalRequest("/search/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: newVariables
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      dispatch(addActivityLog("property.search", "read", { ...newVariables }));

      if (resp.errors) {
        throw new Error(resp.errors[0].message);
      }

      const { data } = resp;

      if (window.location.pathname == "/detailsearch") {
        // take log for search result
        const msg = JSON.stringify({
          action: "stocklist",
          status: "success",
          parameters: newVariables,
        });
        await takeLog(emp_id, msg, fetch, false, token);
      }

      if (data.stocks && data.stocks.length > 0) {
        dispatch({
          type: isUnlockPage
            ? LIST_UNLOCKEDSEARCHRESULT_SUCCESS
            : LIST_SEARCHRESULT_SUCCESS,
          payload: {
            data,
            uuid,
          },
        });
      } else {
        dispatch({
          type: isUnlockPage
            ? LIST_UNLOCKEDSEARCHRESULTNULL_SUCCESS
            : LIST_SEARCHRESULTNULL_SUCCESS,
          payload: {
            data,
            uuid,
          },
        });
      }
    } catch (error) {
      let errorMsg;
      if (error.message == "Request timeout.") {
        errorMsg = intl.formatMessage({
          id: "common.timeout.error",
        });
      } else {
        errorMsg = intl.formatMessage({
          id: "common.disconnect.error",
        });
      }

      dispatch({
        type: isUnlockPage
          ? LIST_UNLOCKEDSEARCHRESULT_ERROR
          : LIST_SEARCHRESULT_ERROR,
        payload: {
          error: {
            message: errorMsg,
          },
        },
      });
      // throw new Error(error);
    }
  };
}

export function listStockListbyKeywords(variables, isFetchingMore) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, universalRequest },
  ) => {
    if (isFetchingMore) {
      dispatch({
        type: LIST_MORESEARCHRESULT_START,
        payload: {
          variables,
        },
      });
    } else {
      dispatch({
        type: LIST_SEARCHRESULT_START,
        payload: {
          variables,
        },
      });
    }

    try {
      await dispatch(listMarkStock());
      const newVariables = {
        ...variables,
        includeId: getState().stock.markStockIds,
      };
      newVariables.sbu = [config.sbu];

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("LIST_STOCKLIST_BY_KEYWORDS_QUERY");

      const resp = await universalRequest("/stock/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: newVariables
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (resp.errors) {
        throw new Error(resp.errors[0].message);
      }

      const { data } = resp;

      if (data.stocksOr && data.stocksOr.length > 0) {
        dispatch({
          type: LIST_SEARCHRESULTBYKEYWORDS_SUCCESS,
          payload: {
            data,
          },
        });
      } else {
        dispatch({
          type: LIST_SEARCHRESULTBYKEYWORDSNULL_SUCCESS,
          payload: {
            data,
          },
        });
      }
    } catch (error) {
      dispatch({
        type: LIST_SEARCHRESULT_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };
}

export function listDecorations() {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_DECORATION_START,
      checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("LIST_DECORATIONS_QUERY");

      const data = await universalRequest("/stock/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: {}
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_DECORATION_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_DECORATION_ERROR,
        payload: { error: error.message },
      });
      // throw error;
    }
  };
}

export function listPossessions() {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_POSSESSION_START,
      checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("LIST_POSSESSIONS_QUERY");

      const data = await universalRequest("/stock/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: {}
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_POSSESSION_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_POSSESSION_ERROR,
        payload: { error: error.message },
      });
      // throw error;
    }
  };
}

export function listUsages() {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_USAGE_START,
      checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("LIST_USAGES_QUERY");

      const data = await universalRequest("/stock/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: {}
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_USAGE_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_USAGE_ERROR,
        payload: { error: error.message },
      });
      // throw error;
    }
  };
}

export function listStockTypes() {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_STOCKTYPE_START,
      checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("LIST_STOCKTYPES_QUERY");

      const data = await universalRequest("/stock/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: {}
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_STOCKTYPE_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_STOCKTYPE_ERROR,
        payload: { error: error.message },
      });
      // throw error;
    }
  };
}

export function listCurrentStates() {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_CURRENTSTATE_START,
      checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
        },
      };

      const query = await getQuery("LIST_CURRENTSTATES_QUERY");

      const data = await universalRequest("/stock/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables: {}
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_CURRENTSTATE_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_CURRENTSTATE_ERROR,
        payload: { error: error.message },
      });
      // throw error;
    }
  };
}

export function unlockStock(stockid) {
  return async (dispatch, getState, { universalRequest, takeLog }) => {
    const { emp_id } = getState().auth.user.login.info;

    dispatch({
      type: UNLOCK_STOCK_START,
    });

    try {
      const token = getState().auth.user.oauth;
      const maxQuota = !_.isEmpty(getState().employee.permissions)
        ? !_.isNil(
            getState().employee.permissions[
              config.permissions.PERMISSION_VIEW_STOCK_QUOTA
            ],
          )
          ? getState().employee.permissions[
              config.permissions.PERMISSION_VIEW_STOCK_QUOTA
            ]
          : 0
        : config.generalDailyQuota;

      const data = await universalRequest(
        typeof stockid === "object" ? "/unlockListStock" : "/unlockStock",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ emp_id, stockid, maxQuota }),
        },
      );

      // take log for unlock stock
      const msg = JSON.stringify({
        action: "unlock",
        status: "success",
        stockid,
      });
      await takeLog(emp_id, msg, fetch, false, token);

      dispatch({
        type: UNLOCK_STOCK_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: UNLOCK_STOCK_ERROR,
        payload: { error: error.message },
      });
      // throw error;
    }
  };
}

export function getUnlockCount() {
  return async (dispatch, getState, { universalRequest }) => {
    const { emp_id } = getState().auth.user.login.info;

    try {
      const data = await universalRequest("/getUnlockCount", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ emp_id }),
      });

      dispatch({
        type: GET_UNLOCKCOUNT_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: UNLOCK_STOCK_ERROR,
        payload: { error: error.message },
      });
      // throw error;
    }
  };
}

export function clearStockList(isUnlockPage) {
  return async (dispatch) => {
    dispatch({
      type: isUnlockPage ? CLEAR_UNLOCKEDSEARCHRESULT : CLEAR_SEARCHRESULT,
    });
  };
}

export function getHistoryField() {
  return async (dispatch) => {
    dispatch({
      type: GET_SEARCHHISTORY,
    });
  };
}

export function setAnchor(px) {
  return async (dispatch) => {
    dispatch({
      type: SET_SEARCHRESULT_ANCHOR,
      payload: {
        px,
      },
    });
  };
}

export function clearAnchor() {
  return async (dispatch) => {
    dispatch({
      type: CLEAR_SEARCHRESULT_ANCHOR,
    });
  };
}

export function saveSearch({ query, name }) {
  return async (dispatch, getState, { universalRequest }) => {
    dispatch({
      type: SAVE_SEARCH_START,
    });

    try {
      const { data, status, errors } = await universalRequest("/saveSearch", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ query, name }),
      });

      if (errors) throw new Error(errors[0].message);

      if (status === 200) {
        dispatch({
          type: SAVE_SEARCH_UPSERT_SUCCESS,
          payload: {
            _id: data._id,
            name,
            query,
          },
        });
      } else if (status === 201) {
        dispatch({
          type: SAVE_SEARCH_SUCCESS,
          payload: {
            _id: data._id,
            name,
            query,
          },
        });
      } else {
        throw new Error("Unknown status: " + status);
      }
    } catch (error) {
      dispatch({
        type: SAVE_SEARCH_ERROR,
        payload: { error: error.message },
      });
    }
  };
}

export function clearSaveSearch() {
  return async (dispatch) => {
    dispatch({
      type: CLEAR_SAVE_SEARCH,
    });
  };
}

export function listSearch() {
  return async (dispatch, getState, { universalRequest }) => {
    dispatch({
      type: LIST_SEARCH_START,
    });

    try {
      const { data, errors } = await universalRequest("/listSearch", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({}),
      });

      if (errors) throw new Error(errors[0].message);

      dispatch({
        type: LIST_SEARCH_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_SEARCH_ERROR,
        payload: { error: error.message },
      });
    }
  };
}

export function clearListSearch() {
  return async (dispatch) => {
    dispatch({
      type: CLEAR_LIST_SEARCH,
    });
  };
}

export function listCompanies(variables) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_COMPANY_START,
      checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
        },
      };

      const query = `
      query ($name: String, $limit: Int, $offset: Int, $sorter: [contactsCompanySorter]) {
        contactsCompanies(name: $name, limit: $limit, offset: $offset, sort: $sorter) {
          _id
          nameEn
          nameZh
        }
      }
      `;

      const data = await universalRequest("/contact/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_COMPANY_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_COMPANY_ERROR,
        payload: { error: error.message },
      });
      // throw error;
    }
  };
}

export function clearListCompanies() {
  return async (dispatch) => {
    dispatch({
      type: CLEAR_LIST_COMPANY,
    });
  };
}
