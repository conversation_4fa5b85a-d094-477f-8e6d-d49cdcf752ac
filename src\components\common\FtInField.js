import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import InlineTextInput from './InlineTextInput';

// We can inject some CSS into the DOM.
const styles = {
  root: {

  },
  label: {
    lineHeight: 1.3,
    color: "#777",
    fontSize: "0.875em"
  },
  inputs: {
    display: "flex",
    "& > div": {
      width: "50%",
    },
  },
};

function FtInField(props) {
  const {
    classes,
    className,
    label,
    customLabelProps = {},
    input : { value, onChange, ...otherInput },
    ...others
  } = props;

  const handleFootOnChange = (v) => {
    onChange({ ...value, ft: v });
  };

  const handleInchOnChange = (v) => {
    onChange({ ...value, in: v });
  };

  return (
    <div className={clsx(classes.root, className)}>
      <label
        {...customLabelProps}
        className={clsx(classes.label, customLabelProps.className)}
      >
        {label}
      </label>
      <div className={classes.inputs}>
        <InlineTextInput
          input={{
            value: value.ft,
            onChange: handleFootOnChange,
            ...otherInput,
          }}
          inputToDisplay={v => v && parseFloat(v) !== 0 ? v + "\'" : "---"}
          type="number"
          min={0}
          {...others}
        />
        <InlineTextInput
          input={{
            value: value.in,
            onChange: handleInchOnChange,
            ...otherInput,
          }}
          inputToDisplay={v => v && parseFloat(v) !== 0 ? v + "\"" : "---"}
          type="number"
          min={0}
          {...others}
        />
      </div>
    </div>
  );
}

FtInField.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  label: PropTypes.string,
  customLabelProps: PropTypes.object,
  input: PropTypes.object.isRequired,
};

export default withStyles(styles)(FtInField);
