import url from "url";
import fetch from "node-fetch";
import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import encodeFormData from "./encodeFormData";
import {
  publicHost,
  loginRestriction,
  api,
  sbu,
  cas_profile_host,
  mode,
} from "../config";
import mongodb from "../data/mongodb";

function getICSCode() {
  let ics;
  switch (sbu) {
    case "COMM":
      ics = "O";
      break;
    case "SHOPS":
      ics = "S";
      break;
    case "IND":
      ics = "I";
      break;
    default:
      ics = "";
      break;
  }
  return ics;
}

function processGenerateSecret(resp) {
  return new Promise((resolve, reject) => {
    fetch(url.resolve(publicHost, "totp-secret"), {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    })
      .then((res) => res.json())
      .then((json) => {
        mongodb.sessionDb
          .collection("user")
          .insertOne(
            { user: resp, secret: json.secret, lastupdate: new Date() },
            (err, r) => {
              if (err) {
                reject(err);
              } else {
                resolve(json);
              }
            },
          );
      });
  });
}

function processGenerateTokenbySecret(resp, secret) {
  return new Promise((resolve, reject) => {
    // Generate token by the current secret
    const query = { secret, emp_id: resp.login.info.emp_id };
    const encodedformData = encodeFormData(query);
    fetch(url.resolve(publicHost, "totp-generate"), {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: encodedformData,
    })
      .then((res) => res.json())
      .then((coderes) => {
        resolve(coderes);
      });
  });
}

passport.serializeUser((user, done) => {
  if (user) {
    // find if the user has secret in db by emp_id
    const query = {
      "user.login.info.emp_id": user.login.info.emp_id,
    };

    mongodb.sessionDb
      .collection("user")
      .find(query)
      .sort({ _id: -1 })
      .limit(1)
      .toArray((err, docs) => {
        if (docs && docs[0]) {
          // generate token for this user
          const { user: userInfo, secret } = docs[0];
          processGenerateTokenbySecret(userInfo, secret).then((json) => {
            done(null, { ...userInfo, authorized: false });
          });
        } else {
          // create a secret for user
          console.log(
            "No user record found from mongodb, creating a new secret...",
          );
          processGenerateSecret(user).then((secret) => {
            console.log("secret created successfully");
            processGenerateTokenbySecret(user, secret.secret).then((result) => {
              console.log(result);
              done(null, { ...user, authorized: false });
            });
          });
        }
      });
  } else {
    done(null, false, { message: "Fail to serialize user" });
  }
});

passport.deserializeUser((user, done) => {
  done(null, user);
});

passport.use(
  new LocalStrategy(
    {
      passReqToCallback: true,
    },
    (req, username, password, done) => {
      if (req.user) {
        done(null, req.user);
      } else if (req.cookies.casAccessToken) {
        fetch(
          `${cas_profile_host}?access_token=${req.cookies.casAccessToken}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          },
        )
          .then((data2) => data2.json())
          .then(async (result2) => {
            if (result2.error) throw result2.error;
            let json = {
              login: {
                status: true,
                info: result2.attributes,
              },
            };

            let itUsers;
            let bannedUsers;
            try {
              itUsers = loginRestriction.itUsers
                .split(",")
                .map((v) => v.trim());
              if (loginRestriction.bannedUsers) {
                bannedUsers = loginRestriction.bannedUsers
                  .split(",")
                  .map((v) => v.trim());
              }
            } catch (e) {
              done(
                null,
                false,
                "Fail to parse IT_USERS/BANNED_USERS. Pls check the ENV",
              );
              return;
            }

            if (
              bannedUsers &&
              bannedUsers.indexOf(json.login.info.emp_id) > -1
            ) {
              done(null, false, "Banned user");
            } else if (itUsers.indexOf(json.login.info.emp_id) > -1) {
              done(null, json);
            } /*if (mode === "production")*/ else {
              getempInfo(json).then((emp) => {
                if (
                  emp.data &&
                  emp.data.employees &&
                  emp.data.employees.length != 0
                ) {
                  const empInfo = emp.data.employees[0];
                  // check if the user is I/O/S agent
                  const icsCode = getICSCode();
                  if (
                    !(
                      empInfo.emp_type === "FL" &&
                      empInfo.dept.ics === icsCode &&
                      empInfo.company === "MRICI"
                    )
                  ) {
                    done(null, false, `Not ${sbu} user`);
                  } else {
                    done(null, json);
                  }
                } else {
                  done(null, false, `Not ${sbu} user`);
                }
              });
            } /*else if (mode == "development") {
               done(null, false, "No access to development");
             }*/
          })
          .catch((error) => {
            done(null, false, error);
          });
      } else {
        done(null, false, `Not ${sbu} user`);
      }

      /*   const encodedformData = encodeFormData(formData);


      fetch(authCentralHost, {
        method: "POST",
        body: encodedformData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      })
        .then((response) => response.text())
        .then((data) => {
          console.log(data);
          let itUsers;
          let bannedUsers;
          try {
            itUsers = loginRestriction.itUsers.split(",").map((v) => v.trim());
            if (loginRestriction.bannedUsers) {
              bannedUsers = loginRestriction.bannedUsers
                .split(",")
                .map((v) => v.trim());
            }
          } catch (e) {
            done(
              null,
              false,
              "Fail to parse IT_USERS/BANNED_USERS. Pls check the ENV"
            );
            return;
          }

          parser.parseString(data, async (err, json) => {
            if (json.login.status == "false") {
              done(null, false, json.login.message);
            } else if (
              bannedUsers &&
              bannedUsers.indexOf(json.login.info.emp_id) > -1
            ) {
              done(null, false, "Banned user");
            } else if (itUsers.indexOf(json.login.info.emp_id) > -1) {
              json.login.dept_code = departmentCode;
              json.login.user_id =  username;
              json.login.password = password;
              done(null, json);
            } else if (mode === "production") {
              getempInfo(json).then((emp) => {
                if (
                  emp.data &&
                  emp.data.employees &&
                  emp.data.employees.length != 0
                ) {
                  const empInfo = emp.data.employees[0];
                  // check if the user is I/O/S agent
                  const icsCode = getICSCode();
                  if (
                    !(
                      empInfo.emp_type === "FL" &&
                      empInfo.dept.ics === icsCode &&
                      empInfo.company === "MRICI"
                    )
                  ) {
                    done(null, false, `Not ${sbu} user`);
                  } else {
                    json.login.dept_code = departmentCode;
                    json.login.user_id =  username;
                    json.login.password = password;
                    done(null, json);
                  }
                } else {
                  done(null, false, `Not ${sbu} user`);
                }
              });
            } else if (mode == "development") {
              done(null, false, "No access to development");
            }
          });
        })
        .catch((error) => done(null, null, error));*/
    },
  ),
);

function getempInfo(data) {
  const employee_gql_query = `
  query ($emp_id: [String]) {
    employees(emp_id: $emp_id) {
      emp_type
      dept{
        ics
      }
      company
    }
  }
`;

  const employee_gql_variables = {
    emp_id: data.login.info.emp_id ? data.login.info.emp_id : "",
  };

  return fetch(api.employeeInternal, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      query: employee_gql_query,
      variables: employee_gql_variables,
    }),
  })
    .then((data) => data.json())
    .then((result) => {
      return result;
    })
    .catch((e) => {
      console.log(e);
    });
}

export default passport;
