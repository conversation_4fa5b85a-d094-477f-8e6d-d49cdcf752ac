import React from "react";
import { connect } from "react-redux";
import { createMuiTheme, MuiThemeProvider } from "@material-ui/core/styles";
import { injectIntl } from "react-intl";
import { addActivityLog } from "@/actions/log";
import { AppBar, Tabs } from "@material-ui/core";
import PropTypes from "prop-types";
import _ from "lodash";

const tabsTheme = createMuiTheme({
  overrides: {
    MuiAppBar: {
      root: {
        boxShadow: "none",
      },
      colorPrimary: {
        backgroundColor: "#777",
        color: "#000",
      },
      positionSticky: {
        top: 52,
      },
    },
    MuiTabs: {
      root: {
        minHeight: 36,
      },
      indicator: {
        height: 0,
      },
    },
    MuiTab: {
      root: {
        flex: "1 0 auto",
        minHeight: 36,
        fontSize: "0.875em",
        lineHeight: 1.2,
        textTransform: "none",
        padding: "6px 8px",
        fontWeight: 600,
        "&$selected": {
          backgroundColor: "#FFF",
          color: "rgba(0, 0, 0, .6)",
        },
      },
      textColorInherit: {
        color: "#FFF",
      },
      wrapper: {
        "&& > *:first-child": {
          marginBottom: 0,
        },
      },
    },
  },
});

function StyledTabs({ children, position, tab, handleTabChange, addActivityLog }) {
  const childrenArray = React.Children.toArray(children) ?? [];

  return (
    <MuiThemeProvider theme={tabsTheme}>
      <AppBar position={position}>
        <Tabs
          value={tab}
          onChange={(e, value) => {
            const selectedTabLabelKey = _.get(childrenArray, `[${value}].props.labelKey`, "");
            addActivityLog(selectedTabLabelKey, "read");
            handleTabChange(value)
          }}
          variant="fullWidth"
          centered
        >
          {children}
        </Tabs>
      </AppBar>
    </MuiThemeProvider>
  );
}

StyledTabs.defaultProps = {
  position: "static",
};

StyledTabs.propTypes = {
  children: PropTypes.node.isRequired,
  tab: PropTypes.number.isRequired,
  position: PropTypes.string,
  handleTabChange: PropTypes.func.isRequired,
};

const mapDispatchToProps = (dispatch) => ({
  addActivityLog: (...args) => dispatch(addActivityLog(...args)),
});

export default connect(
  null,
  mapDispatchToProps,
)(injectIntl(StyledTabs));

// export default StyledTabs;
