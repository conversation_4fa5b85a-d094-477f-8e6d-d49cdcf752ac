import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import DetailTabPanelFrame from "../../../../common/DetailTabPanelFrame";
import UpdateHistoryMain from "./UpdateHistoryMain";


const styles = theme => ({

});

class UpdateHistory extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    listing: PropTypes.bool,
    detail: PropTypes.object,
  };

  constructor(props) {
    super(props);
  }

  render() {
    const { classes, detail, listed, listing, handController, hand } = this.props;

    const hasData = Object.keys(detail).length > 0;

    return (
      <DetailTabPanelFrame hasData={hasData} listing={listing} listed={listed} notFoundText="Stock not found">
        {handController}
        <UpdateHistoryMain detail={detail} hand={hand} />
      </DetailTabPanelFrame>
    );
  }
}

const mapStateToProps = state => ({
  detail: state.stock.detail ? state.stock.detail : {},
  listed: state.stock.listed ? state.stock.listed : false,
  listing: state.stock.listing ? state.stock.listing : false
});

export default connect(mapStateToProps)(withStyles(styles)(UpdateHistory));
