import React, { useMemo } from "react";
import { Grid, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import { Field } from "redux-form";
import { injectIntl } from "react-intl";
import PropTypes from "prop-types";
import clsx from "clsx";

import UnderlineSingleSelect from "../../common/UnderlineSingleSelect";
import {
  LANDSEARCH_STATUS,
  LAND_ITEMS,
  landSearchRemark,
} from "../../../constants/landsearch";
import StockInfo from "./StockInfo";

const useStyles = makeStyles({
  section: {
    margin: "5px 0",
  },
  multiInputContainer: {
    marginLeft: "-8px !important",
    marginRight: "-8px !important",
  },
  remarks: {
    color: "#555555",
    fontSize: "13px",
  },
});

function LandSearch({ intl }) {
  const classes = useStyles();
  const landSearchStatus = useMemo(
    () =>
      LANDSEARCH_STATUS.map((status) => ({
        value: status.value,
        label: status[intl.locale],
      })),
    [intl.locale],
  );

  const landSearchItems = useMemo(
    () =>
      LAND_ITEMS.map((item) => ({
        value: item.value,
        label: item[intl.locale],
      })),
    [intl.locale],
  );

  return (
    <Grid item container direction="column" className={classes.section}>
      <Grid
        item
        container
        spacing={2}
        className={clsx(classes.section, classes.multiInputContainer)}
      >
        <Grid item xs={5}>
          <Field
            name="landSearch"
            label={intl.formatMessage({ id: "stock.applySearch.landSearch" })}
            component={UnderlineSingleSelect}
            type="select-multiple"
            options={landSearchStatus}
          />
        </Grid>
        <Grid item xs={7}>
          <Field
            name="item"
            label={intl.formatMessage({
              id: "stock.applySearch.landItem",
            })}
            component={UnderlineSingleSelect}
            type="select-multiple"
            options={landSearchItems}
          />
        </Grid>
      </Grid>

      <Grid item container direction="column" className={classes.section}>
        <StockInfo />
      </Grid>

      <Grid item style={{ marginTop: 15 }}>
        {landSearchRemark.map((statement, index) => (
          <Typography
            key={`landSearchRemark-${index}`}
            component="div"
            className={classes.remarks}
          >
            {statement}
          </Typography>
        ))}
      </Grid>
    </Grid>
  );
}

LandSearch.propTypes = {
  intl: PropTypes.object.isRequired,
};

export default injectIntl(LandSearch);
