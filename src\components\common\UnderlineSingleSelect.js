import React from "react";
import { MenuItem, TextField } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import clsx from "clsx";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import PropTypes from "prop-types";

const useStyles = makeStyles({
  select: {
    width: "100%",
    "& .MuiFormLabel-asterisk": {
      color: "red",
    },
  },
  selectMenu: {
    "& > .MuiSelect-selectMenu": {
      backgroundColor: "lightgreen",
    },
  },
});

function UnderlineSingleSelect({
  className,
  label,
  options,
  input,
  ...custom
}) {
  const classes = useStyles();

  return (
    <TextField
      className={clsx(classes.select, className)}
      select
      label={label}
      value={input.value}
      onChange={input.onChange}
      onBlur={() => {}}
      InputLabelProps={{
        shrink: true,
      }}
      SelectProps={{
        IconComponent: ExpandMoreIcon,
      }}
      {...custom}
    >
      {options.map((opt) => (
        <MenuItem key={opt.value} value={opt.value}>
          {opt.label}
        </MenuItem>
      ))}
    </TextField>
  );
}

UnderlineSingleSelect.defaultProps = {
  label: "",
  className: "",
};

UnderlineSingleSelect.propTypes = {
  className: PropTypes.string,
  label: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),
  options: PropTypes.array.isRequired,
  input: PropTypes.object.isRequired,
};

export default UnderlineSingleSelect;
