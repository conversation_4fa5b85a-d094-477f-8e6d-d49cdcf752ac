import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import FieldVal from "../../../../common/FieldVal";
import { injectIntl } from "react-intl";
import DetailBoxSection from "../../../../common/DetailBoxSection";

const styles = (theme) => ({
  root: {
    padding: "1vh 0",
  },
  lmrAlign: {
    paddingLeft: "2vw",
  },
});

class Promote extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;

    const promote = detail.promote || {};

    const boardStatus = promote.boardStatus || "---";
    const upBoardDate = promote.upBoardDate || "---";
    const boardDescription = promote.boardDescription || "---";
    const newsStatus = promote.newsStatus || "---";
    const posterDate = promote.posterDate || "---";
    const posterDescription = promote.posterDescription || "---";

    const boardStatusHeader = intl.formatMessage({
      id: "stock.boardstatus",
    });
    const boardDateHeader = intl.formatMessage({
      id: "stock.boarddate",
    });
    const boardDescriptionHeader = intl.formatMessage({
      id: "stock.boarddescription",
    });
    const posterStatusHeader = intl.formatMessage({
      id: "stock.posterstatus",
    });
    const posterDateHeader = intl.formatMessage({
      id: "stock.posterdate",
    });
    const posterDescriptionHeader = intl.formatMessage({
      id: "stock.posterdescription",
    });


    let promoteMapping = {
      [boardStatusHeader]: { value: boardStatus, xs: 6 },
      [boardDateHeader]: { value: upBoardDate, xs: 6 },
      [boardDescriptionHeader]: { value: boardDescription, xs: 6 },
      [posterStatusHeader]: { value: newsStatus, xs: 6 },
      [posterDateHeader]: { value: posterDate, xs: 6 },
      [posterDescriptionHeader]: { value: posterDescription, xs: 6 },
    };

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.boardorposter",
          })}
        >
          <Grid container spacing={2} className={classes.lmrAlign}>
            {Object.keys(promoteMapping).map((v, i) => (
              <Grid item xs={promoteMapping[v].xs} key={v}>
                <FieldVal field={v}>
                  {promoteMapping[v].value}
                </FieldVal>
              </Grid>
            ))}
          </Grid>
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(Promote));
