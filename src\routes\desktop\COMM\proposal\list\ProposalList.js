import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import ProposalListComponent from "../../../../../components/ProposalList";
import {
  clearProposals,
  listProposals,
} from "../../../../../actions/proposal";
import { getUnlockCount } from "../../../../../actions/stocklist";

class ProposalList extends React.Component {
  static propTypes = {
    listProposals: PropTypes.func.isRequired,
    clearProposals: PropTypes.func.isRequired,
  };

  componentDidMount() {
    const variables = {
      limit: 50,
      offset: 0,
    };
    this.props.listProposals(variables);

    this.props.getUnlockCount();
  }

  componentWillUnmount() {
    this.props.clearProposals();
  }

  render() {
    return (
      <ProposalListComponent />
    );
  }
}

const mapDispatchToProps = dispatch => {
  return {
    listProposals: (...args) => dispatch(listProposals(...args)),
    clearProposals: () => dispatch(clearProposals()),
    getUnlockCount: () => dispatch(getUnlockCount()),
  };
};

export default connect(
  null,
  mapDispatchToProps
)(ProposalList);
