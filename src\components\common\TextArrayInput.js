// import React from "react";
// import { createMuiTheme } from "@material-ui/core/styles";
// import { ThemeProvider } from "@material-ui/styles";
// import TextField from "@material-ui/core/TextField";

// const TextInput = ({
//   label,
//   input,
//   meta: { touched, invalid, error },
//   ...custom
// }) => {
//   return (
//     // <ThemeProvider theme={theme}>
//     <TextField
//       label={label}
//       placeholder={label}
//       error={touched && invalid}
//       helperText={touched && error}
//       // variant="outlined"
//       {...input}
//       {...custom}
//     />
//     // </ThemeProvider>
//   );
// };

// export default TextInput;

import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import TextField from "@material-ui/core/TextField";

// We can inject some CSS into the DOM.
const styles = {
  // root: {
  //   borderColor: "blue"
  // },
  // notchedOutline: {
  //   borderWidth: "1px",
  //   borderColor: "green !important"
  // }
};

function TexArrayInput(props) {
  const {
    classes,
    className,
    label,
    input,
    meta: { touched, invalid, error },
    ...custom
  } = props;
  // const { classes, children, className, ...other } = props;

  const { value, name, onChange, onBlur, ...otherProps } = props.input;
  const [single, setSingle] = React.useState(null);

  function handleChangeSingle(event) {
    const { value } = event.target;
    if (value != null) {
      const arr = [];
      arr.push(value);
      onChange(arr);
    }
  }

  return (
    <TextField
      className={clsx(classes.root, className)}
      label={label}
      placeholder={label}
      error={touched && invalid}
      helperText={touched && error}
      InputLabelProps={{
        shrink: true
      }}
      value={single}
      onChange={handleChangeSingle}
      {...input}
      {...custom}
    />
  );
}

{
  /* <Button className={clsx(classes.root, className)} {...other}>
  {children || "class names"}
</Button>; */
}
export default withStyles(styles)(TexArrayInput);
