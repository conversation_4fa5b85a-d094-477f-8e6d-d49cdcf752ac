import React from "react";
import PropTypes from "prop-types";
import { createStyles, withStyles } from "@material-ui/styles";
import { injectIntl } from "react-intl";
import DeleteOutlineIcon from "@material-ui/icons/DeleteOutline";
import { sbu } from "../../../config";
import Popup from "./Popup";
import FormButton from "../FormButton";
import moment from "moment";
import { connect } from "react-redux";
import { updateMedium } from "@/actions/medium";
import PdfImg from "./PdfImg";
import { dateFormatter } from "@/helper/generalHelper";

const styles = createStyles({
  popup: {
    width: "100%",
  },
  caption: {
    wordBreak: "break-word",
  },
  documentBtnRow: {
    marginTop: "3vh",
    display: "flex",
    justifyContent: "center",
    gap: "2vw",
  },
  deleteIcon: {
    width: 30,
    height: 30,
    color: "#FFF",
  },
  downloadButton: {
    // height: "48px",
    width: "150px",
    lineHeight: "1.25",
  },
  pdfImg: {
    maxHeight: "50vh",
    overflow: "hidden",
  },
});

const DocumentPopup = ({
  src,
  file,
  classes,
  document,
  handlePopupClose,
  deletable,
  handleDownload,
  handleOpenDeleteMediaDialog,
  intl,
  isApproveMediaPage,
  updateMedium
}) => {
  const isPersonal = document?.approval !== "approved";

  const handleShareToPublic = React.useCallback(() => {
    if (!document?.id) return;
    updateMedium({
      id: document.id,
      approval: "waiting",
    });
  }, [document]);

  return (
    <Popup
      className={classes.popup}
      onCloseRequest={handlePopupClose}
      caption={
        document && (
          isApproveMediaPage ? <>
            {(document.buildingName && document.propertyRefId) && (
              <div className={classes.caption}>
                {"樓盤: "}
                {document.buildingName} ({document.propertyRefId})
              </div>
            )}
            {(document.createdDate && document.employee) && (
              <div className={classes.caption}>
                {"擁有人: "}
                {dateFormatter(document.createdDate || "")} {document.employee?.branchId} {intl.locale === 'zh' ? document.employee?.cName : document.employee?.eName}
              </div>
            )}
            <div className={classes.caption}>
                {"多媒體類型: Document"}
              </div>
            {(document.originalFilename) && (
              <div className={classes.caption}>
                {"名稱: "}
                {document.originalFilename}
              </div>
            )}
          </> : <>
            <div className={classes.caption}>
              {intl.formatMessage({ id: "stock.photo.name" })}
              {": "}
              {document.originalFilename}
            </div>
            <div className={classes.caption}>
              {"上傳者"}
              {": "}
              {document?.operator?.eName || ""}
            </div>
            <div className={classes.caption}>
              {"上傳時間"}
              {": "}
              {moment(document.createdDate).format("HH:mm:ss YYYY-MM-DD")}
            </div>
          </>
        )
      }
      toolbarButtons={
        deletable && isPersonal
          ? [
            <DeleteOutlineIcon
              className={classes.deleteIcon}
              onClick={() => handleOpenDeleteMediaDialog(document.id)}
            />
          ]
          : []
      }
    >
      <PdfImg src={src} file={file} page="1" className={classes.pdfImg} />
      {(src || file || document?.approval === "pending") && (
        <div className={classes.documentBtnRow}>
          {(src || file) && (
            <FormButton className={classes.downloadButton} onClick={handleDownload}>
              {intl.formatMessage({ id: "media.download" })}
            </FormButton>
          )}
          {(document?.approval === "pending" || document?.approval === "rejected") &&
            <FormButton className={classes.downloadButton} onClick={handleShareToPublic}>
              {intl.formatMessage({ id: "media.update.shareToPublic" })}
            </FormButton>
          }
        </div>
      )}
    </Popup>
  );
};

DocumentPopup.propTypes = {
  classes: PropTypes.object.isRequired,
  document: PropTypes.object,
  src: PropTypes.string,
  file: PropTypes.object,
  handlePopupClose: PropTypes.func.isRequired,
  deletable: PropTypes.bool,
  handleOpenDeleteMediaDialog: PropTypes.func,
  handleDownload: PropTypes.func,
};

const mapStateToProps = state => ({
  // ref: https://stackoverflow.com/questions/48819138/how-do-you-get-syncerrors-out-of-state-using-redux-form-selectors
  isApproveMediaPage: state.medium.isApproveMediaPage,
});

/** @param {React.Dispatch<any>} dispatch */
const mapDispatchToProps = (dispatch) => ({
  updateMedium: (...args) => dispatch(updateMedium(...args)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(DocumentPopup)));
