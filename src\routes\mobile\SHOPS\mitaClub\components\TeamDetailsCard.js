import React from "react";
import { Avatar, Box, Button, Paper, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import Image62 from "./asset/image62.svg";
import Image89 from "./asset/image89.svg";
import { convertCurrency } from "@/helper/generalHelper";
import { connect } from "react-redux";
import _ from "lodash";

const useStyles = makeStyles(theme => ({
  paper: {
    padding: "16px",
    "display": "flex",
    "flexDirection": "column",
    "alignItems": "center",
    "gap": "24px",
    "background": "#FFFFFF",
    "boxShadow": "0px 2px 16px rgba(57, 46, 6, 0.1)",
    "borderRadius": "8px",
    "flex": "none",
    "alignSelf": "stretch",
    "flexGrow": 0,
  },
  headerContainer: {
    "display": "flex",
    "flexDirection": "row",
    "justifyContent": "space-between",
    "alignItems": "center",
    "gap": "8px",
    "flex": "none",
    flexWrap: "wrap",
    "alignSelf": "stretch",
    "flexGrow": 0,
  },
  teamNameText: {
    "height": "20px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "16px",
    "lineHeight": "20px",
    "letterSpacing": "0.02em",
    "color": "#222222",
    "flex": "none",
    "flexGrow": 0
  },
  metricsContainer: {
    marginLeft: "auto",
    textAlign: "right",
    "display": "flex",
    "flexDirection": "row",
    "justifyContent": "center",
    "alignItems": "flex-start",
    "padding": "0px",
    "gap": "8px",
    "height": "39px",
    "flex": "none",
    "flexGrow": 0,
  },
  metricBox: {
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-end",
    gap: 4,
  },
  performanceBox: {
    width: 80,
  },
  ordersBox: {
    // width: 64,
  },
  labelText: {
    "width": "49px",
    "height": "15px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "12px",
    "lineHeight": "15px",
    "letterSpacing": "0.02em",
    "color": "#727162",
    "flex": "none",
    "order": 0,
    "flexGrow": 0,
  },
  valueText: {
    // "width": "60px",
    "height": "20px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "16px",
    "lineHeight": "20px",
    "letterSpacing": "0.02em",
    "color": "#222222",
    "flex": "none",
    "flexGrow": 0,
  },
  progressSection: {
    width: "100%",
    paddingLeft: 16,
    paddingRight: 16,
  },
  progressContainer: {
    display: "flex",
    flexDirection: "column",
    width: "100%",
    gap: 8,
  },
  progressBar: {
    position: "relative",
    display: "flex",
    width: "100%",
    height: 16,
    backgroundColor: "#F2F0EB",
    borderRadius: 16,
    overflow: "hidden",
    borderStyle: "inset",
    borderColor: "lightgrey",
    background: "#FFF",
  },
  progressGold: {
    position: "absolute",
    display: "flex",
    height: "100%",
    backgroundColor: "#D9AC4B",
    borderRadius: "16px",
    justifyContent: "center",
    alignItems: "center",
  },
  progressBlue: {
    position: "absolute",
    display: "flex",
    height: "100%",
    backgroundColor: "#C0CEE1",
    borderRadius: "16px",
    justifyContent: "center",
    alignItems: "center",
  },
  progressGray: {
    display: "flex",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    background: "#FFF",
  },
  progressText: {
    "height": "18px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "14px",
    "lineHeight": "18px",
    "letterSpacing": "0.02em",
    "color": "#222222",
    "flex": "none",
    "flexGrow": 0,
  },
  levelInfoContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "baseline",
    width: "100%",
  },
  levelInfoColumn: {
    display: "flex",
    flexWrap: "wrap",
    flexDirection: "column",
  },
  levelInfoMetric: {
    display: "flex",
    alignItems: "center",
    gap: 4,
  },
  levelInfoText: {
    // "height": "18px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "14px",
    "lineHeight": "18px",
    "letterSpacing": "0.02em",
    "color": "#222222",
    "flex": "none",
    "flexGrow": 0
  },
  directorsContainer: {
    width: "100%",
    overflowX: "auto",
  },
  agentItemContainer: {
    width: "100%",
    height: "32px",
    // display: "flex",
    // alignItems: "center",
    // padding: "4px 0",
    backgroundColor: "#f8f8f8",
    // gap: 16,
    boxSizing: "border-box",
  },
  agentAvatar: {
    width: 24,
    height: 24,
    flexShrink: 0,
  },
  englishName: {
    // // width: "calc(150 / 286 * 100%)",
    width: "125px",
    height: "32px",
    fontWeight: 700,
    // height: "18px",
    fontFamily: "'Microsoft JhengHei UI'",
    fontStyle: "normal",
    fontSize: "14px",
    lineHeight: "18px",
    letterSpacing: "0.02em",
    color: "#222222",
    textAlign: "left",
    // flex: "1 1 auto",
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
    wordBreak: "break-all",
    // paddingLeft: 16,
    paddingRight: 8,
  },
  accumulatedSalesAmount: {
    // width: "calc(64 / 286 * 100%)",
    width: "85px",
    fontWeight: 700,
    // width: "64px",
    height: "18px",
    fontFamily: "'Microsoft JhengHei UI'",
    fontStyle: "normal",
    fontSize: "14px",
    lineHeight: "18px",
    // textAlign: "left",
    textAlign: "right",
    letterSpacing: "0.02em",
    color: "#222222",
    // flex: "0 0 auto",
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
    wordBreak: "break-all",
    paddingLeft: 8,
    paddingRight: 8,
  },
  accumulatedSalesCase: {
    // width: "calc(48 / 286 * 100%)",
    width: "50px",
    fontWeight: 700,
    // width: "80px",
    height: "18px",
    fontFamily: "'Microsoft JhengHei UI'",
    fontStyle: "normal",
    fontSize: "14px",
    lineHeight: "18px",
    textAlign: "right",
    letterSpacing: "0.02em",
    color: "#222222",
    // flex: "0 0 auto",
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
    wordBreak: "break-all",
    paddingLeft: 8,
    // paddingRight: 16,
  },
  showMoreButton: {
    "&:hover": {
      backgroundColor: "#f0e5c7",
    },

    "display": "flex",
    "flexDirection": "row",
    "alignItems": "center",
    "padding": "4px 16px",
    "gap": "16px",
    "width": "90px",
    "height": "26px",
    "background": "#F7EDD6",
    "borderRadius": "16px",
    "flex": "none",
    "flexGrow": 0,
    margin: "0 auto",
  },
}));

/**
 * @typedef {Object} LevelInfo
 * @property {string} name - 當前 `level name`
 * @property {number} current - 當前 `level` 達標人數
 * @property {number} total - 總人數
 */
/** @type {Readonly<Array<LevelInfo>>} */
const agentLevelInfos = Object.freeze([
  { name: "營業員達標鑽石", current: 0, total: 0, },
  { name: "營業員達標⽩⾦", current: 0, total: 0, },
]);

/** @type {Readonly<Array<LevelInfo>>} */
const managerLevelInfos = Object.freeze([
  { name: "主管達標白金", current: 0, total: 0, },
]);

/**
 * @typedef AgentInfo
 * @property {React.ReactNode} EnglishName
 * @property {string} TeamCode
 * @property {string} AccumulatedSalesAmount
 * @property {string} AccumulatedSalesCase
 * @property {string} Avatar
 */

/**
 * 
 * @param {Object} props 
 * @param {boolean=} props.isManager
 * @param {Array<Object>} props.mitaclubInfo
 * @param {AgentInfo=} props.teamInfo
 * @param {number[]} props.targetValues
 * @param {boolean=} props.hideIcon
 * @param {boolean=} props.hideProgressBar
 * @param {React.ReactNode=} props.teamName
 * @param {Array<AgentInfo>} props.agents
 * 
 * @returns 
 */
const TeamDetailsCard = (props) => {
  const classes = useStyles();

  const teamData = React.useMemo(() => {
    const result = {
      teamName: null,
      progressValues: [],
      levelInfos: _.cloneDeep(props.isManager ? managerLevelInfos : agentLevelInfos),
    };
    if (!props.agents?.length) { return result; }

    const { targetValues = [], mitaclubInfo = [], agents = [] } = props;
    const curTeamInfo = props.teamInfo || mitaclubInfo.find(item => item.TeamCode === agents[0].TeamCode) || {};
    const teamAccumulatedSalesAmount = +curTeamInfo.AccumulatedSalesAmount || 0;
    const teamAccumulatedSalesCase = +curTeamInfo.AccumulatedSalesCase || 0;

    const performanceAndTotalOrders = props.agents.reduce((prev, cur) => {
      // API 返回的 AccumulatedSalesAmount / AccumulatedSalesCase 和實際上計算 agents 的結果不一樣, 暫時以 API 數據爲準
      // prev.teamPerformance += +cur.AccumulatedSalesAmount;
      // prev.totalOrders += +cur.AccumulatedSalesCase;
      if (+cur.AccumulatedSalesAmount >= props.targetValues[0]) {
        prev.levelInfos[0].current++;
      } else if (+cur.AccumulatedSalesAmount >= props.targetValues[1]) {
        // 因爲主管只有 1 個 levelInfos, 所以主管的 達標人數 計算在第一條。
        prev.levelInfos[!props.isManager ? 1 : 0].current++;
      }
      prev.levelInfos[0].total = props.agents.length;
      if (!props.isManager) {
        prev.levelInfos[1].total = props.agents.length;
      }
      return prev;
    }, {
      ...result,
      // teamPerformance: 0,
      // totalOrders: 0,
    });

    const progressValues = props.hideProgressBar ? [] : props.agents.sort((a, b) => +a.AccumulatedSalesAmount - +b.AccumulatedSalesAmount)
      .map((item, index) => {
        const value = +item.AccumulatedSalesAmount;
        const color = value >= targetValues[0] ? "#D9AC4B" : value >= targetValues[1] ? "#C0CEE1" : "#F2F1EC";
        return {
          value,
          color,
          label: item.EnglishName,
        }
      });
    return {
      ...performanceAndTotalOrders,
      teamPerformance: teamAccumulatedSalesAmount,
      totalOrders: teamAccumulatedSalesCase,
      teamName: props.teamName || props.agents[0].TeamCode,
      progressValues,
    };
  }, [
    props.teamInfo,
    props.hideProgressBar,
    props.teamName,
    props.agents,
    props.isManager,
    props.targetValues,
    props.mitaclubInfo,
  ]);

  // Calculate progress percentages for the progress bar
  const totalProgress = React.useMemo(() => teamData.progressValues.reduce(
    (sum, item) => sum + item.value,
    0,
  ), [teamData.progressValues]);

  /** - 三條進度條從`X軸 坐標0`開始的`進度位置` */
  const progressPercentages = React.useMemo(() => {
    if (props.hideProgressBar) { return []; }
    const agentsLength = props.agents.length;
    const allLevelInfoCount = teamData.levelInfos.reduce((sum, item) => sum + item.current, 0);

    const result = teamData.levelInfos.map(
      (item) => ((item.current / agentsLength) * 100) || 0,
    );

    if (props.isManager) {
      result.push(0); // manager(主管) 只有一條進度條, 這裏進行補充占位
    }
    // 加入第三條進度條表示還有多少人未達標
    result.push(((agentsLength - allLevelInfoCount) / agentsLength * 100) || 100);
    return result;
  }, [props.hideProgressBar, teamData.levelInfos, totalProgress, props.agents, props.isManager]);

  const [showAgentCount, setShowAgentCount] = React.useState(0);
  const handleShowMoreDirectors = React.useCallback(() => {
    setShowAgentCount((prev) => {
      const newCount = prev + 10;
      return Math.min(newCount, props.agents.length);
    });
  }, [props.agents]);

  const progressPercentagePaddingLefts = React.useMemo(() => {
    if (props.hideProgressBar) { return []; }
    const result = [
      0,
      progressPercentages[0] || 0,
      (progressPercentages[0] + progressPercentages[1]) || 0,
    ];
    return result;
  }, [props.hideProgressBar, progressPercentages, props.isManager]);

  return (
    <Paper
      elevation={0}
      className={classes.paper}
    >
      {/* Header with team name and performance metrics */}
      <Box className={classes.headerContainer}>
        {typeof teamData.teamName !== "string" ? teamData.teamName : <Typography
          variant="h6"
          className={classes.teamNameText}
        >
          {props.isManager ? "主管" : teamData.teamName}
        </Typography>}

        {!props.isManager && <Box className={classes.metricsContainer}>
          <Box className={`${classes.metricBox} ${classes.performanceBox}`}>
            <Typography
              variant="caption"
              className={classes.labelText}
            >
              團隊業績
            </Typography>
            <Typography
              variant="h6"
              className={classes.valueText}
            >
              ${convertCurrency(teamData.teamPerformance)}
            </Typography>
          </Box>

          <Box className={`${classes.metricBox} ${classes.ordersBox}`}>
            <Typography
              variant="caption"
              className={classes.labelText}
            >
              總單數
            </Typography>
            <Typography
              variant="h6"
              className={classes.valueText}
            >
              {teamData.totalOrders}單
            </Typography>
          </Box>
        </Box>}
      </Box>

      {/* Progress section */}
      {props.hideProgressBar ? null : <Box className={classes.progressSection}>
        <Box className={classes.progressContainer}>
          {/* Custom progress bar */}
          <Box className={classes.progressBar}>
            {/* Gold segment */}
            {(teamData?.levelInfos?.[0]?.current > 0) && <Box
              className={classes.progressGold}
              style={{
                width: `${progressPercentages[0]}%`,
                zIndex: 1,
                paddingLeft: `${progressPercentagePaddingLefts[0]}%`,
                ...(props.isManager ? { backgroundColor: "#C0CEE1" } : {})
              }}
            >
              <Typography
                variant="body2"
                className={classes.progressText}
              >
                {teamData.levelInfos?.[0]?.current}
              </Typography>
            </Box>}

            {/* Blue segment */}
            {(teamData?.levelInfos?.[1]?.current > 0) && <Box
              className={classes.progressBlue}
              style={{
                width: `${progressPercentages[1]}%`,
                zIndex: 0,
                paddingLeft: `${progressPercentagePaddingLefts[1]}%`,
              }}
            >
              <Typography
                variant="body2"
                className={classes.progressText}
              >
                {teamData.levelInfos?.[1]?.current}
              </Typography>
            </Box>}

            {/* Light gray segment */}
            {(props.isManager || (
              teamData?.levelInfos?.[0]?.current +
              teamData?.levelInfos?.[1]?.current
            ) < props.agents.length) && <Box
              className={classes.progressGray}
              style={{
                width: `${progressPercentages[2]}%`,
                paddingLeft: `${progressPercentagePaddingLefts[2]}%`,
              }}
            >
                <Typography
                  variant="body2"
                  className={classes.progressText}
                  style={{ color: "white", }}
                >
                  {props.isManager ? (props.agents.length - teamData.levelInfos[0].current) :
                    `${props.agents.length - (teamData?.levelInfos[0].current +
                      teamData?.levelInfos[1].current)}`
                  }
                </Typography>
              </Box>}
          </Box>

          {/* LevelInfo metrics */}
          <Box className={classes.levelInfoContainer}>
            <Box className={classes.levelInfoColumn}>
              {teamData.levelInfos.map((levelInfo, index) => (
                <Typography
                  key={index}
                  variant="body2"
                  className={classes.levelInfoText}
                >
                  {levelInfo.name}
                </Typography>
              ))}
            </Box>

            <Box className={classes.levelInfoColumn}>
              {teamData.levelInfos.map((levelInfo, index) => (
                <Box
                  key={index}
                  className={classes.levelInfoMetric}
                >
                  <Typography
                    variant="body2"
                    className={classes.levelInfoText}
                  >
                    {levelInfo.current}
                  </Typography>
                  <Typography
                    variant="body2"
                    className={classes.levelInfoText}
                  >
                    /
                  </Typography>
                  <Typography
                    variant="body2"
                    className={classes.levelInfoText}
                  >
                    {levelInfo.total}名
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>}

      <Box style={{
        gap: "8px",
        width: "100%",
        display: "flex",
        flexDirection: "column",
      }}>
        <DirectorsList
          agents={props.agents}
          showAgentCount={showAgentCount}
          targetValues={props.targetValues}
          className={classes.directorsContainer}
          isManager={props.isManager}
          hideIcon={props.hideIcon}
        />

        {/* Show more button */}
        {showAgentCount < props.agents.length && <Button
          variant="contained"
          disableElevation
          className={classes.showMoreButton}
          onClick={handleShowMoreDirectors}
        >
          顯示更多
        </Button>}
      </Box>
    </Paper>
  );
};

const DirectorsList = (props) => {
  const classes = useStyles();
  const { agents, showAgentCount, targetValues = [] } = props;

  const orderingAgents = React.useMemo(() => {
    if (!agents?.length) { return []; }
    return agents.sort((a, b) => +b.AccumulatedSalesAmount - +a.AccumulatedSalesAmount ||
      +b.AccumulatedSalesCase - +a.AccumulatedSalesCase)
      .slice(0, showAgentCount);
  }, [agents, showAgentCount]);

  return (
    <div className={classes.directorsContainer}>
      <table style={{ width: "100%", minWidth: "250px", borderCollapse: "collapse", tableLayout: "fixed" }}>
        <tbody>
          {orderingAgents.map((agent, index) => {
            const { EnglishName } = agent;
            let avatar = null;
            if (+agent.AccumulatedSalesAmount >= targetValues[0]) {
              avatar = Image89;
            } else if (+agent.AccumulatedSalesAmount >= targetValues[1]) {
              avatar = Image62;
            }
            return (
              <tr
                key={EnglishName}
                className={classes.agentItemContainer}
                style={{ backgroundColor: index % 2 === 0 ? "#F8F8F8" : "white" }}
              >
                <td className={classes.englishName} style={props.hideIcon ? { width: "64px" } :{}}>
                  <div style={{
                    display: "flex",
                    alignItems: "center",
                    columnGap: "16px",
                    width: "100%",
                    overflow: "hidden",
                  }}>
                    {!props.hideIcon && (avatar ? (
                      <img
                        alt={EnglishName}
                        src={avatar}
                        className={classes.agentAvatar}
                        style={{ display: "block", flexShrink: 0 }}
                      />
                    ) : <div className={classes.agentAvatar} />)}
                    <span style={{
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                      flex: 1
                    }}>
                      {EnglishName}
                    </span>
                  </div>
                </td>
                {props.isManager && <td className={classes.accumulatedSalesCase}>
                  {agent.TeamCode}
                </td>}
                <td className={classes.accumulatedSalesAmount}>
                  ${convertCurrency(+agent.AccumulatedSalesAmount || 0)}
                </td>
                {!props.isManager && <td className={classes.accumulatedSalesCase}>
                  {agent.AccumulatedSalesCase}單
                </td>}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

const mapStateToProps = (state) => ({
  mitaclubAgentMap: _.get(state, "employee.mitaclubAgentMap"),
  mitaclubInfo: _.get(state, "employee.mitaclubInfo") || [],
});

const mapDispatchToProps = (dispatch) => ({
  // getEmployeesMitaclubAgent: (empId) => dispatch(getEmployeesMitaclubAgent({ empId })),
  // getMitaclubEmpsByTeamCode: (teamCode) => dispatch(getMitaclubEmpsByTeamCode({ teamCode })),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
  // )((injectIntl(TeamDetailsCard)));
)(TeamDetailsCard);
