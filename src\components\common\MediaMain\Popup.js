import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import clsx from "clsx";
import CloseIcon from '@material-ui/icons/Close';

const styles = {
  root: {
    position: "fixed",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "transparent",
    zIndex: 1000,
  },
  container: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: "hidden",
  },
  transform: {
    backgroundColor: "rgba(0, 0, 0, 0.85)",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    width: "100%",
    height: "100%",
    transition: "opacity 300ms ease 0s",
    opacity: 0,
  },
  opened: {
    opacity: 100,
  },
  header: {
    height: 30,
    padding: "10px 10px",
    display: "flex",
    flexDirection: "row-reverse",
    position: "absolute",
    top: 0,
    right: 0,
    zIndex: 1,
    "& > *": {
      marginLeft: 10,
    },
  },
  icon: {
    width: 30,
    height: 30,
    color: "#FFF",
  },
  footer: {
    color: "#FFF",
    padding: "10px 20px",
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  inner: {
    width: "100%",
    height: "calc(100% - 100px)",
    position: "absolute",
    top: 50,
    left: 0,
    right: 0,
    bottom: 50,
    display: "flex",
    alignItems: "center",
  },
};

function Popup(props) {
  const {
    classes,
    children,
    onCloseRequest,
    toolbarButtons = [],
    caption,
    ...custom
  } = props;

  const [opened, setOpened] = React.useState(false);

  React.useEffect(() => setOpened(true), []);

  const handleClose = () => {
    setOpened(false);
    setTimeout(() => {
      onCloseRequest();
    }, 300);
  };

  const stopPropagation = e => {
    e.stopPropagation();
  };

  return (
    <div className={classes.root}>
      <div className={classes.container}>
        <div className={clsx(classes.transform, opened ? classes.opened : undefined)} onClick={handleClose}>
          <div className={classes.header} onClick={stopPropagation}>
            <div>
              <CloseIcon
                className={classes.icon}
                onClick={handleClose}
              />
            </div>
            {toolbarButtons.map((v, i) => <div key={i}>{v}</div>)}
          </div>
          <div className={classes.inner}>
            <div onClick={stopPropagation} {...custom}>{children}</div>
          </div>
          <div className={classes.footer}>{caption}</div>
        </div>
      </div>
    </div>
  );
}

Popup.propTypes = {
  classes: PropTypes.object.isRequired,
  children: PropTypes.node,
  open: PropTypes.bool,
  onCloseRequest: PropTypes.func.isRequired,
  toolbarButtons: PropTypes.arrayOf(PropTypes.node),
  caption: PropTypes.node,
};

export default withStyles(styles)(Popup);
