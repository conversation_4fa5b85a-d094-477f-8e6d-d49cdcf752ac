import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import VaryingVal from "./VaryingVal";

// We can inject some CSS into the DOM.
const styles = {
  item: {
    textAlign: "right",
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end"
  },
  nums: {
    display: "flex"
  },
  total: {
    width: "34vw",
    paddingLeft: "1vw",
  },
  avg: {
    width: "34vw",
    paddingLeft: "1vw",
  }
};

function TotalAndAvgPrice(props) {
  const { classes, avg, total, trend, label, className, ...other } = props;

  return (
    (avg || total) && (
      <VaryingVal
        type={trend}
        className={`${classes.item} ${className}`}
        label={label}
        arrowPos="label"
      >
        <div className={classes.nums}>
          <div className={classes.total}>{total}</div>
          <div className={classes.avg}>{avg}</div>
        </div>
      </VaryingVal>
    )
  );
}

TotalAndAvgPrice.propTypes = {
  classes: PropTypes.object.isRequired,
  avg: PropTypes.node,
  total: PropTypes.node,
  trend: PropTypes.string,
  label: PropTypes.string,
  className: PropTypes.string
};

export default withStyles(styles)(TotalAndAvgPrice);
