/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import withStyles from "isomorphic-style-loader/lib/withStyles";
import _ from "lodash";
import InfiniteScroll from "react-infinite-scroll-component";
import { FormattedMessage, injectIntl } from "react-intl";
import s from "./index.css";
import { listUnitViews } from "../../../../actions/building";
import {
  listStockList,
  listStockListbyKeywords,
  clearStockList,
  listDecorations,
  getUnlockCount,
  clearAnchor,
  setAnchor,
} from "../../../../actions/stocklist";
import { listDistricts, clearDistricts } from "../../../../actions/district";
import SearchResultCard from "../Search/SearchResultCard";
import StockSortingBar from "../Search/StockSortingBar";
import LoadingOverlay from "../../../LoadingOverlay";
import StockListCount from "../../../common/StockListCount";
import ErrorsDialog from "../../../common/ErrorsDialog";
import { getDefaultSearchResultURL } from "../../../../helper/generalHelper";
import { clearStock } from "../../../../actions/stock";
import { gtagHandler } from "../../../../actions/log";

const defaultQuery = {
  limit: 50,
  offset: 0,
};

const defaultSorter = [
  { field: "matchedStreetsNumber", order: "asc" },
  { field: "floorInput", order: "asc" },
  { field: "unit", order: "asc" },
];

class Stocklist extends React.Component {
  static propTypes = {
    stocks: PropTypes.array,
    buildings: PropTypes.array,
    unlockedStockIds: PropTypes.array,
    stocklistlisting: PropTypes.bool,
    districtlisting: PropTypes.bool,
    buildinglisting: PropTypes.bool,
    listed: PropTypes.bool,
    hasMore: PropTypes.bool,
    count: PropTypes.number,
    listDistricts: PropTypes.func.isRequired,
    clearDistricts: PropTypes.func.isRequired,
    listStockList: PropTypes.func.isRequired,
    clearStockList: PropTypes.func.isRequired,
    listDecorations: PropTypes.func.isRequired,
    getUnlockCount: PropTypes.func.isRequired,
    clearStockDetail: PropTypes.func.isRequired,
    markStockIds: PropTypes.array.isRequired,
  };

  constructor(props) {
    super(props);
    this.state = {
      offset: 0,
      isUnlocked: false,
      queryvariables: this.props.queryvariablesFromUrl || {},
      firstLoad: !this.props.listed,
    };
  }

  fetchData = (variables, isMore) => {
    let options = {
      isFetchingMore: isMore,
      isUnlockPage: false,
      intl: this.props.intl,
      selectedData: this.props.selectedDataFromUrl,
    };
    let storeField = "street";
    if (this.props.isAdvanced === "true") {
      this.props.listStockList(variables, options, storeField);
    } else {
      this.props.listStockListbyKeywords(variables, isMore, storeField);
    }
  };

  fetchMoreData = () => {
    if (this.props.stocklistlisting) return;

    let variables = this.state.queryvariables;
    let newQuery = {
      ...variables,
      offset: variables.offset + 50,
    };
    this.setState({ queryvariables: newQuery });
    this.fetchData(newQuery, true);
  };

  firstFetch = (variables) => {
    this.props.getUnlockCount();
    // let variables = this.state.queryvariables;
    // variables.offset += 50;

    this.props.gtagHandler("Search Stock");
    this.fetchData(variables, false);
  };

  checkQueryAndFetch = (query) => {
    let clone = query ? JSON.parse(JSON.stringify(query)) : defaultQuery;
    if (
      !clone.sorter ||
      !Array.isArray(clone.sorter) ||
      clone.sorter.length === 0
    )
      clone.sorter = defaultSorter;

    this.firstFetch(clone);
    this.updateQuery(clone);
  };

  componentDidMount() {
    if (this.props.stock && this.props.stock._id && this.props.listed) {
      // scroll to the clicked stock
      if (this.cardRefs[this.props.stock._id]) {
        const scrollPx =
          this.cardRefs[this.props.stock._id].getBoundingClientRect().top +
          window.scrollY -
          180;
        window.scroll({
          top: scrollPx,
          behavior: "smooth",
        });
      }

      this.props.clearStockDetail();

      // queryvariablesFromUrl does not have sorter. the state need to be updated by the queryvariables from redux which has sorter
      this.updateQuery(this.props.queryvariables);
    } else if (!this.props.listed) {
      // only the first time user enter this page, we do data fetch
      this.checkQueryAndFetch(this.props.queryvariablesFromUrl);
    } else {
      this.updateQuery(this.props.queryvariables);

      // scroll back to the anchor position where the user left last time
      let storedAnchorY = this.props.anchorY;
      if (storedAnchorY)
        window.scroll({
          top: storedAnchorY,
          behavior: "smooth",
        });
      this.props.clearAnchor();
    }
  }

  componentDidUpdate(prevProps) {
    if (
      !_.isEqual(
        prevProps.queryvariablesFromUrl,
        this.props.queryvariablesFromUrl,
      ) ||
      (prevProps.markStockIds &&
        prevProps.markStockIds.length > 0 &&
        this.props.markStockIds.length === 0)
    ) {
      this.setState({ firstLoad: false });
      this.checkQueryAndFetch(this.props.queryvariablesFromUrl);
    }

    if (
      !prevProps.listed && !!this.props.listed
    ) {
      this.setState({ firstLoad: false });
    }
  }

  componentWillUnmount() {
    this.props.setAnchor(window.scrollY);
  }

  updateQuery = (newQuery) => {
    this.setState({ queryvariables: newQuery });
  };

  refreshPage = () => {
    window.location.replace(getDefaultSearchResultURL());
  };

  sortingBarFetchData = (variables, isMore) => {
    this.setState({ firstLoad: false });
    this.fetchData(variables, isMore);
  };

  cardRefs = {};

  // using callback refs. https://stackoverflow.com/questions/52448143/dealing-with-ref-within-a-loop-in-react
  setRef = (id) => (ref) => {
    // check whether the element mount or unmount. https://stackoverflow.com/questions/41838833/issue-storing-ref-elements-in-loop
    if (ref) {
      this.cardRefs[id] = ref;
    } else {
      delete this.cardRefs[id];
    }
  };

  render() {
    const {
      stocklistlisting,
      districtlisting,
      listed,
      stocks,
      unlockedStockIds,
      hasMore,
      stocksCount,
      expanded,
      error,
      intl,
    } = this.props;
    const {
      queryvariables,
      firstLoad,
    } = this.state;

    const errors = [];
    if (error)
      errors.push({
        message: error.message,
        btn: intl.formatMessage({ id: "common.refresh" }),
        btnHandleClick: this.refreshPage,
      });

    // console.log("stocks", stocks);
    return (
      !expanded && (
        <div className={s.root}>
          <div className={s.bg} />

          <StockSortingBar
            queryvariables={queryvariables}
            updateQuery={this.updateQuery}
            fetchData={this.sortingBarFetchData}
            expanded={expanded}
          />
          <div className={s.spacer} />

          <div className={s.listTop}>
            <StockListCount
              stocksCount={stocksCount}
              queryvariables={queryvariables}
            />
          </div>

          {listed ? (
            <div className={s.card}>
              <InfiniteScroll
                dataLength={stocks.length}
                next={this.fetchMoreData}
                hasMore={hasMore}
                style={{ overflowY: "hidden", marginBottom: 60 }}
                endMessage={
                  <p style={{ textAlign: "center" }}>
                    <b>
                      <FormattedMessage id="search.noresult" />
                    </b>
                  </p>
                }
              >
                {stocks.map((stock, index) => (
                  <div key={index} ref={this.setRef(stock._id)}>
                    <SearchResultCard
                      result={stock}
                      isUnlocked={unlockedStockIds.indexOf(stock._id) > -1}
                    />
                  </div>
                ))}
              </InfiniteScroll>
            </div>
          ) : null}

          {(districtlisting || (stocklistlisting && !firstLoad)) && <LoadingOverlay />}

          <ErrorsDialog errors={errors} />
        </div>
      )
    );
  }
}

const mapStateToProps = (state) => ({
  user: state.auth.user ? state.auth.user : [],
  stocks: state.stocklist.stocks ? state.stocklist.stocks : [],
  unlockedStockIds: state.stocklist.unlockedStockIds
    ? state.stocklist.unlockedStockIds
    : [],
  stocksCount: state.stocklist.stocksCount ? state.stocklist.stocksCount : 0,
  stocklistlisting: state.stocklist.listing ? state.stocklist.listing : false,
  districtlisting: state.district.listing ? state.district.listing : false,
  listed: state.stocklist.listed ? state.stocklist.listed : false,
  hasMore: state.stocklist.hasMore ? state.stocklist.hasMore : false,
  error: state.stocklist.error ? state.stocklist.error : null,
  queryvariables: state.stocklist.queryvariables
    ? state.stocklist.queryvariables
    : {},
  stock: state.stock.detail ? state.stock.detail : null,
  anchorY: state.stocklist.searchResultAnchor
    ? state.stocklist.searchResultAnchor
    : null,
  markStockIds: _.get(state, "stock.markStockIds") || [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    listStockList: (...args) => dispatch(listStockList(...args)),
    listStockListbyKeywords: (graphqlvariable, isfetchingmore) =>
      dispatch(listStockListbyKeywords(graphqlvariable, isfetchingmore)),
    getUnlockCount: () => dispatch(getUnlockCount()),
    clearStockList: (...args) => dispatch(clearStockList(...args)),
    listDistricts: () => dispatch(listDistricts()),
    clearDistricts: () => dispatch(clearDistricts()),
    listDecorations: () => dispatch(listDecorations()),
    listUnitViews: () => dispatch(listUnitViews()),
    clearStockDetail: () => dispatch(clearStock()),
    clearAnchor: () => dispatch(clearAnchor()),
    setAnchor: (...args) => dispatch(setAnchor(...args)),
    gtagHandler: (...args) => dispatch(gtagHandler(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(s)(injectIntl(Stocklist)));
