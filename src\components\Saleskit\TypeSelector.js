import React from "react";
import { Grid } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import PropTypes from "prop-types";
import clsx from "clsx";
import { FormattedMessage } from "react-intl";

import PillButton from "../common/PillButton";

const useStyles = makeStyles(() => ({
  wrapper: {
    padding: "5px 0",
  },
  pill: {
    height: 30,
    width: 110,
    padding: "0px 10px",
    borderRadius: 17,
    border: "none",
    backgroundColor: "lightgray",
    "&:hover": {
      backgroundColor: "#33CCCC",
    },
  },
  selectedPill: {
    backgroundColor: "#33CCCC",
    color: "#ffffff",
  },
}));

function TypeSelector({ type, setType }) {
  const classes = useStyles();
  return (
    <Grid className={classes.wrapper} container justify="center" spacing={1}>
      <Grid item>
        <PillButton
          className={clsx(classes.pill, {
            [classes.selectedPill]: type === "list",
          })}
          onClick={() => setType("list")}
          disableRipple
        >
          <FormattedMessage id="proposal.listProposal" />
        </PillButton>
      </Grid>
      <Grid item>
        <PillButton
          className={clsx(classes.pill, {
            [classes.selectedPill]: type === "indv",
          })}
          onClick={() => setType("indv")}
          disableRipple
        >
          <FormattedMessage id="proposal.indvProposal" />
        </PillButton>
      </Grid>
    </Grid>
  );
}

TypeSelector.propTypes = {
  type: PropTypes.string.isRequired,
  setType: PropTypes.func.isRequired,
};

export default TypeSelector;
