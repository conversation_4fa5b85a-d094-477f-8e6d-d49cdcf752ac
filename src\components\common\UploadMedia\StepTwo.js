import React from "react";
import PropTypes from "prop-types";
import { Field } from "redux-form";
import { date } from '../../../core/formValidators';
import { withStyles } from "@material-ui/styles";
import PillCheckBox from "../PillCheckBox";
import TextInput from "../TextInput";
import { MuiThemeProvider, createMuiTheme } from "@material-ui/core/styles";


const styles = theme => ({
  root: {
    padding: "6vh 0"
  },
  firstTextRow: {
    fontSize: "1.125em",
    marginBottom: "2vh"
  },
  font14: {
    fontSize: "0.875em"
  },
  photoTagCheckBoxContainer: {
    display: "flex",
    alignItems: "center",
    flexWrap: "wrap"
  },
  photoTagCheckBox: {
    margin: "1vh 2vw 1vh 0"
  },
  hr: {
    height: 1,
    margin: "1vh 0",
    backgroundColor: "#FFF",
  },
  dateInputRow: {
    display: "flex",
    "& > *": {
      flex: 1
    },
    "& > *:first-child": {
      marginRight: "2vw"
    }
  },
  dateInput: {
    "& input::-webkit-clear-button, & input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button": {
      display: "none"
    },
  },
  dateOptionRow: {
    display: "flex",
    justifyContent: "flex-end"
  }
});

const theme = createMuiTheme({
  overrides: {
    MuiOutlinedInput: {
      root: {
        "& $notchedOutline": {
          borderColor: "#FFF"
        },
        "&:hover $notchedOutline": {
          borderColor: "#FFF"
        },
        "&.Mui-focused $notchedOutline": {
          borderColor: "#FFF"
        },
      },
    },
    MuiInputBase: {
      root: {
        color: "#FFF",
      }
    },
    MuiFormLabel: {
      root: {
        color: "#FFF",
        "&.Mui-focused": {
          color: "#FFF"
        },
      }
    },
  }
});

const dateValidator = date("YYYY-MM-DD");

class StepTwo extends React.Component {
  render() {
    const { classes,  files, noDateLimit } = this.props;

    return (
      <div className={classes.root}>
        <div className={classes.firstTextRow}>{files.length} media is going to upload,</div>
        <div className={classes.font14}>Please select photo attribute:</div>
        <div className={classes.photoTagCheckBoxContainer}>
          <Field name="isBuildingOutlook" component={PillCheckBox} text="Building Outlook" className={classes.photoTagCheckBox} />
          <Field name="isInterior" component={PillCheckBox} text="Interior" className={classes.photoTagCheckBox} />
          <Field name="isFloorPlan" component={PillCheckBox} text="Floor Plan" className={classes.photoTagCheckBox} />
          <Field name="isLobby" component={PillCheckBox} text="Lobby" className={classes.photoTagCheckBox} />
          <Field name="isEntry" component={PillCheckBox} text="Entry" className={classes.photoTagCheckBox} />
        </div>
        <div className={classes.hr} />
        <div className={classes.dateInputRow}>
          <MuiThemeProvider theme={theme}>
            <Field
              name="availableStartTime"
              type="date"
              margin="normal"
              label="Start date"
              component={TextInput}
              className={classes.dateInput}
              variant="outlined"
              validate={noDateLimit ? null : dateValidator}
              disabled={noDateLimit}
            />
            <Field
              name="availableEndTime"
              type="date"
              margin="normal"
              label="End date"
              component={TextInput}
              className={classes.dateInput}
              variant="outlined"
              validate={noDateLimit ? null : dateValidator}
              disabled={noDateLimit}
            />
          </MuiThemeProvider>
        </div>
        <div className={classes.dateOptionRow}>
          <Field name="noDateLimit" component={PillCheckBox} text="No date limit" />
        </div>
      </div>
    );
  }
}

export default withStyles(styles)(StepTwo);
