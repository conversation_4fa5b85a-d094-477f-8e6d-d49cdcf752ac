import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import AppBar from "@material-ui/core/AppBar";
import Tabs from "@material-ui/core/Tabs";
import Tab from "@material-ui/core/Tab";
import Typography from "@material-ui/core/Typography";
import _ from "lodash";
import Cookies from "universal-cookie";
import { FormattedMessage, injectIntl } from "react-intl";
import Media from "./Media";
import StockDetailGeneral from "./General";
import UpdateHistory from "./UpdateHistory";
import UploadMediaDialog from "../../../common/UploadMedia/UploadMediaDialog";
import DetailHandController from "../../../common/DetailHandController";
import { MuiThemeProvider, createMuiTheme } from "@material-ui/core/styles";
import { listStockMedia } from "../../../../actions/stock";
import { get<PERSON><PERSON><PERSON><PERSON>, getRever<PERSON><PERSON><PERSON><PERSON><PERSON>, getDisplayStockId } from "../../../../helper/generalHelper";

function TabPanel(props) {
  const { children, value, index, ...other } = props;
  const [loaded, setLoaded] = React.useState(false);

  if (!loaded && value === index) setLoaded(true);

  return (
    <Typography
      component="div"
      role="tabpanel"
      hidden={value !== index}
      id={`full-width-tabpanel-${index}`}
      aria-labelledby={`full-width-tab-${index}`}
      {...other}
    >
      {loaded ? <div>{children}</div> : null}
    </Typography>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.any.isRequired,
  value: PropTypes.any.isRequired,
};

function a11yProps(index) {
  return {
    id: `full-width-tab-${index}`,
    "aria-controls": `full-width-tabpanel-${index}`,
  };
}

const styles = (theme) => ({
  root: {
    flexGrow: 1,
    height: 250,
  },
  bar: {
    // height: 57,
    boxShadow: "none",
    backgroundColor: "rgba(0, 0, 0, .6)",
    position: "relative",
    top: 0,
    left: 0,
    zIndex: 900,
  },
  tabAlwaysActive: {
    minHeight: 57,
    paddingTop: 6,
    backgroundColor: "#33CCCC",
  },
  uploadIcon: {
    width: 42,
    height: 30,
  },
  uploadIconSvg: {
    maxWidth: 42,
    maxHeight: 30,
  },
  grey: {
    "&.Mui-selected": {
      backgroundColor: "#f5f5f5",
    },
  },
});

const theme = createMuiTheme({
  overrides: {
    MuiTabs: {
      root: {
        minHeight: 36,
      },
      indicator: {
        height: 0,
        backgroundColor: "#33CCCC",
      },
    },
    MuiTab: {
      root: {
        minHeight: 36,
        fontSize: "0.875em",
        lineHeight: 1.2,
        textTransform: "none",
        "&$selected": {
          backgroundColor: "#FFF",
          color: "rgba(0, 0, 0, .6)",
        },
      },
      textColorInherit: {
        color: "#FFF",
        opacity: 1,
      },
      wrapper: {
        "&& > *:first-child": {
          marginBottom: 0,
        },
      },
    },
  },
});

function getLatestHand(detail) {
  const updateHistory = detail.updateHistory ? detail.updateHistory : [];
  let latestHand = 0;
  for (let i = 0; i < updateHistory.length; i++) {
    if (updateHistory[i].deleted) continue;
    if (isNaN(parseInt(updateHistory[i].hands))) continue;
    if (parseInt(updateHistory[i].hands) > latestHand)
      latestHand = parseInt(updateHistory[i].hands);
  }
  return latestHand;
}

class Stock extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      value: props.defaultTab || 0,
      dialogOpen: false,
      hand: 1,
      id: null,
    };
  }

  componentDidUpdate(prevProps) {
    const cookies = new Cookies();
    const { detail, headerRef, unlockfinished, intl } = this.props;
    const reverseLangKey = getReverseLangKey(intl);

    // update header
    const id =
      detail.unicorn && Number.isInteger(detail.unicorn.id)
        ? detail.unicorn.id
        : null;
    const buildingName =
      detail && detail.building && detail.building[reverseLangKey]
        ? detail.building[reverseLangKey]
        : null;
    const unit = detail && detail.unit ? detail.unit : "---";
    const floor = detail && detail.floor ? detail.floor : "---";
    let concatHeader = buildingName + " " + floor + ", " + unit;
    concatHeader = _.truncate(concatHeader, { length: 25 });

    if (id !== null) headerRef.current.changeTitle(concatHeader);
    if (id !== null)
      headerRef.current.changeSubTitle(getDisplayStockId(id));

    // set the default hand to be the currentHands
    if (
      !isNaN(parseInt(detail.currentHands)) &&
      detail.currentHands !== prevProps.detail.currentHands
    ) {
      this.setState({ hand: detail.currentHands });
    }

    // const stockId = this.getStockId(detail);
    // const displaystockId = this.getDisplayStockId(stockId);

    // if (displaystockId !== "" && unlockfinished) {
    //   cookies.set("selectedStock", displaystockId);
    //   window.dataLayer.push({ stockId: displaystockId });
    // }
  }

  handleChange = (event, value) => {
    // dont change the content if 'upload media' button (index: 3) is clicked
    if (value === 3) return;

    this.setState({ value });
  };

  handleClickOpenDialog = () => {
    this.setState({ dialogOpen: true });
  };
  handleCloseDialog = () => {
    this.setState({ dialogOpen: false });
  };

  handleHandChange = (hand) => {
    if (!isNaN(parseInt(hand))) this.setState({ hand: parseInt(hand) });
  };

  getStockId(detail) {
    return detail.unicorn && Number.isInteger(detail.unicorn.id)
      ? detail.unicorn.id
      : null;
  }

  callbackAfterUpload = () => {
    const { detail, listStockMedia, userInfo } = this.props;

    if (detail && detail.unicorn && Number.isInteger(detail.unicorn.id)) {
      let variables = {
        sid: detail.unicorn.id.toString(),
        empId: userInfo.emp_id,
      };
      listStockMedia(variables, 5000);
    }
  };

  render() {
    const { classes, intl, detail } = this.props;
    const { value, dialogOpen, hand } = this.state;
    const langKey = getLangKey(intl);

    const buildingId =
      detail.building && detail.building.unicorn && detail.building.unicorn.id
        ? detail.building.unicorn.id
        : null;

    const stockId = this.getStockId(detail);
    const status =
      detail.status && detail.status[langKey] ? detail.status[langKey] : "---";

    const stockType =
      detail.stockType && detail.stockType[langKey]
        ? detail.stockType[langKey]
        : "---";
    const source =
      detail.source && detail.source[langKey] ? detail.source[langKey] : "---";
    const haveUpdateHistory = detail.updateHistory && detail.updateHistory.length > 0;

    // get the range of hands
    let maxHand = !isNaN(parseInt(detail.currentHands))
      ? parseInt(detail.currentHands)
      : 1;
    let ranges = [...Array(maxHand).keys()]
      .map((v) => v + 1)
      .map((v) => {
        return { value: parseInt(v), label: parseInt(v) };
      });

    const initialValues = {
      uploadTo: "Unit",
      type: 0,
      photoContent: "interior",
    };

    const handController = (
      <DetailHandController
        ranges={ranges}
        hand={hand}
        handleChange={this.handleHandChange}
        status={status}
        usageOrType={stockType}
        source={source}
        mongoid={detail._id}
        stockid={stockId}
        favoriteStockIds={this.props.favoriteStockIds}
      />
    );

    return (
      <div className={classes.root}>
        <MuiThemeProvider theme={theme}>
          <AppBar position="static" className={classes.bar}>
            <Tabs
              value={value}
              onChange={this.handleChange}
              variant="fullWidth"
              aria-label="full width tabs example"
              centered
            >
              <Tab
                label={<FormattedMessage id="stock.general" />}
                {...a11yProps(0)}
                value={0}
              />
              {haveUpdateHistory && <Tab
                label={<FormattedMessage id="stock.updatehistory" />}
                {...a11yProps(1)}
                value={1}
              />}
              <Tab
                label={<FormattedMessage id="stock.saleskit" />}
                className={classes.grey}
                {...a11yProps(2)}
                value={2}
              />
              {/*<Tab label="Upload Media" {...a11yProps(3)}*/}
              {/*     icon={<StandardSvg className={classes.uploadIcon} imgClass={classes.uploadIconSvg} src={uploadSvg} />}*/}
              {/*     className={classes.tabAlwaysActive}*/}
              {/*     onClick={this.handleClickOpenDialog} />*/}
            </Tabs>
          </AppBar>
        </MuiThemeProvider>
        {/*<div>**debug** hand: {hand}</div>*/}
        <TabPanel value={value} index={0}>
          <StockDetailGeneral handController={handController} hand={hand} />
        </TabPanel>
        {haveUpdateHistory && <TabPanel value={value} index={1}>
          <UpdateHistory handController={handController} hand={hand} />
        </TabPanel>}
        <TabPanel value={value} index={2}>
          <Media
            handleClickOpenDialog={this.handleClickOpenDialog}
            deleteDialogCallback={this.callbackAfterUpload}
          />
        </TabPanel>
        <UploadMediaDialog
          initialValues={initialValues}
          dialogOpen={dialogOpen}
          handleCloseDialog={this.handleCloseDialog}
          buildingId={buildingId}
          stockId={stockId}
          callback={this.callbackAfterUpload}
        />
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  userInfo:
    state.auth &&
      state.auth.user &&
      state.auth.user.login &&
      state.auth.user.login.info
      ? state.auth.user.login.info
      : {},
  detail: state.stock.detail ? state.stock.detail : {},
  unlockfinished: state.stocklist.unlockfinished
    ? state.stocklist.unlockfinished
    : false,
  favoriteStockIds: state.stock.favoriteStockIds ? state.stock.favoriteStockIds : [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    listStockMedia: (...args) => dispatch(listStockMedia(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(Stock)));
