export const LIST_STOCK_MEDIA_QUERY = `
query($sid: ID!, $empId: String) {
  stock(id: $sid) {
    id
    video: media(type:["video", "embedded_script"], empId: $empId) {
      id
      type
      tags
      markByWWW
      scoring 
      manualOffline  
      employeeId
      availableStartTime
      availableEndTime
      createdDate
      lastModified
      approval
      filename
      description
      originalFilename
      processing
      status
      ... on Video {
        youtubeId
        youtubeMrId
        youtubeHkpId
        youkuId
        vimeoId
        mediumRoot
        thumbnail
      }
      ... on EmbeddedScript {
        script
        thumbnail
      }
    }
    kolVideo: media(type:["kol_video"], empId: $empId) {
      id
      type
      tags
      markByWWW
      scoring 
      manualOffline 
      employeeId
      availableStartTime
      availableEndTime
      createdDate
      lastModified
      approval
      filename
      description
      originalFilename
      processing
      status
      ... on KolVideo {
        mediumRoot
        thumbnail
        characteristicZh
        characteristicEn
        buildingName
        address
        propertyRefId
        buildingId
        vimeoId
        operator {
          eName
        }
      }
    }
    panorama: media(type:"panorama_photo", empId: $empId) {
      id
      type
      tags
      markByWWW
      scoring 
      manualOffline 
      employeeId
      availableStartTime
      availableEndTime
      createdDate
      approval
      status
      ... on PanoramaPhoto {
        mediumRoot
        panoramaContent
      }
    }
    photo: media(type:"photo", empId: $empId) {
      id
      type
      tags
      markByWWW
      scoring 
      manualOffline 
      employeeId
      availableStartTime
      availableEndTime
      createdDate
      filename
      description
      originalFilename
      approval
      status
      ... on Photo {
        mediumRoot
        photoContent
      }
    }
    document: media(type: "document", empId: $empId) {
      id
      type
      tags
      markByWWW
      scoring 
      manualOffline 
      employeeId
      createdDate
      lastModified
      approval
      description
      processing
      status
      ...on Document {
        filename
        originalFilename
        mediumRoot
        documentContent
      }
    }
    virtualTour: media(type:"virtual_tour", empId: $empId) {
      id
      type
      tags
      markByWWW
      scoring 
      manualOffline 
      employeeId
      availableStartTime
      availableEndTime
      createdDate
      approval
      status
      ... on VirtualTour {
        virtualTourId
        thumbnail
      }
    }
  }
}
`;

export const QUERY_UNIT_VIEWS = `
query {
  unitViews {
    _id
    nameEn
    nameZh
    sbu
    abbr
  }
}
`;

export const QUERY_BUSINESS_OPTIONS = `
query(
  $name: String
  $limit: Int
) {
  business (name: $name, limit: $limit){
    _id
    nameEn
    nameZh
  }
}`;

export const QUERY_DECORATIONS = `
query {
  decorations {
    _id
    nameEn
    nameZh
    sbu
    abbr
  }
}
`;

export const QUERY_POSSESSIONS = `
query {
  possessions {
    _id
    nameEn
    nameZh
  }
}
`;

export const QUERY_CURRENT_STATES = `
  query {
    currentState {
      _id
      nameEn
      nameZh
    }
  }`;

export const STOCK_AI_TEXT_QUERY = `
  query(
    $stockId: ID!,
    $customerObj: String,
    $mediaUse: String,
    $copywritingLanguage: String,
    $useOfTone: String,
    $useOfToneType: String,
    $emoji: String,
    $specialRequirements: String,
    $from: String
  ) {
    stockAiTextUrl(
      stockId: $stockId,
      customerObj: $customerObj,
      mediaUse: $mediaUse,
      copywritingLanguage: $copywritingLanguage,
      useOfTone: $useOfTone,
      useOfToneType: $useOfToneType,
      emoji: $emoji,
      specialRequirements: $specialRequirements,
      from: $from
    )
  }
`

export const LIST_MARK_WWW_QUERY = `
  query($stockId:[ID]!){
    markWWWList(stockId:$stockId){
      _id
      stockId
      isDeleted
      markedCount
      markWWWUsers {
        emp_id
        emp_name
        teamCode
        isDeleted
        isEaa
        lastUpdateDate
      }
    }
  }
`;
