import React from "react";
import { Field } from "redux-form";
import { FormattedMessage } from "react-intl";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import ShowHiddenCheckBox from "./ShowHiddenCheckBox";

export const minValue = (min, msg) => (field) =>
  field && field.value && field.value < min ? msg : undefined;

export const maxValue = (max, msg) => (field) =>
  field && field.value && field.value > max ? msg : undefined;

export const number = (msg) => (field) =>
  field && field.value && isNaN(Number(field.value)) ? msg : undefined;

function createMinvalue(min) {
  return minValue(min, <FormattedMessage id="search.form.invalidinput" />);
}
function createMaxvalue(max) {
  return maxValue(max, <FormattedMessage id="search.form.invalidinput" />);
}

const numberValidate = number(
  <FormattedMessage id="search.form.invalidinput" />,
);

const styles = {};

function InputWithCheckBoxCustom(props) {
  const {
    classes,
    className,
    name,
    label,
    changeFieldValue,
    renderComponent,
    min,
    max,
    checkboxProps,
    checkboxInFront,
    checkboxXs = 4,
    jsonField,
    isShowField,
    aligntoLabel,
    bottomCheckbox,
    fakeCheckbox,
    fakeCheckboxEle,
    ...custom
  } = props;

  const [validators, setValidators] = React.useState([]);

  React.useEffect(() => {
    const validationArray = [];
    if (min || min === 0) validationArray.push(createMinvalue(min));
    if (max || max === 0) validationArray.push(createMaxvalue(max));
    if (custom.type === "number") validationArray.push(numberValidate);

    setValidators(validationArray);
  }, []);

  return (
    <Grid container className={className}>
      {fakeCheckbox && (
        fakeCheckboxEle ||
        <ShowHiddenCheckBox
          input={{
            value: { isShow: true },
          }}
          changeFieldValue={() => {}}
          aligntoLabel={aligntoLabel}
          bottomCheckbox={bottomCheckbox}
          disabled
        />
      )}
      {!fakeCheckbox && checkboxInFront && (
        <Field
          name={name}
          jsonField={isShowField ? isShowField : jsonField ? jsonField + "IsShow" : undefined}
          component={ShowHiddenCheckBox}
          changeFieldValue={changeFieldValue}
          aligntoLabel={aligntoLabel}
          bottomCheckbox={bottomCheckbox}
          {...checkboxProps}
        />
      )}
      <Grid item xs={12 - checkboxXs} style={{ margin: "auto" }}>
        <Field
          name={name}
          jsonField={jsonField ? jsonField : undefined}
          validate={validators}
          label={label}
          component={renderComponent}
          aligntoLabel={aligntoLabel}
          fullWidth
          {...custom}
        />
      </Grid>
      {!fakeCheckbox && !checkboxInFront && (
        <Grid item xs={checkboxXs}>
          <Field
            name={name}
            jsonField={isShowField ? isShowField : jsonField ? jsonField + "IsShow" : undefined}
            component={ShowHiddenCheckBox}
            changeFieldValue={changeFieldValue}
            {...checkboxProps}
          />
        </Grid>
      )}
    </Grid>
  );
}

export default withStyles(styles)(InputWithCheckBoxCustom);
