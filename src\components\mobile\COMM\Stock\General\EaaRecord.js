import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import EaaRecordBox from "./EaaRecordBox";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import { convertCurrency, numberComma } from "../../../../../helper/generalHelper";
import { injectIntl } from "react-intl";

const styles = (theme) => ({
  root: {
    padding: "1vh 0",
  },
  small: {
    fontSize: ".777em",
  },
  notFound: {},
});

class EaaRecord extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;

    const saleData =
      detail.propertyAdvertisements && detail.propertyAdvertisements.saleData
        ? detail.propertyAdvertisements.saleData
        : {};
    const rentData =
      detail.propertyAdvertisements && detail.propertyAdvertisements.rentData
        ? detail.propertyAdvertisements.rentData
        : {};
    let dataArr = [rentData, saleData];

    let itemArr = [];
    for (let i = 0; i < dataArr.length; i++) {
      let minTotal = dataArr[i].minTotal
        ? convertCurrency(dataArr[i].minTotal)
        : "";
      let minAverage = dataArr[i].minAverage ? (
        <span className={classes.small}>
          {" "}
          @{numberComma(dataArr[i].minAverage)}
        </span>
      ) : (
        ""
      );
      let minPrice =
        minTotal && minAverage ? (
          <>
            {minTotal}
            {minAverage}
          </>
        ) : (
          minTotal || minAverage
        );
      let maxTotal = dataArr[i].maxTotal
        ? convertCurrency(dataArr[i].maxTotal)
        : "";
      let maxAverage = dataArr[i].maxAverage ? (
        <span className={classes.small}>
          {" "}
          @{numberComma(dataArr[i].maxAverage)}
        </span>
      ) : (
        ""
      );
      let maxPrice =
        maxTotal && maxAverage ? (
          <>
            {maxTotal}
            {maxAverage}
          </>
        ) : (
          maxTotal || maxAverage
        );
      let price =
        minPrice && maxPrice ? (
          <>
            {minPrice} - {maxPrice}
          </>
        ) : (
          minPrice || maxPrice || "---"
        );

      let minDate = dataArr[i].minDate || "";
      let maxDate = dataArr[i].maxDate || "";
      let date =
        minDate && maxDate ? (
          <span>
            {minDate} - {maxDate}
          </span>
        ) : (
          minDate || maxDate || "---"
        );

      let consultantName =
        dataArr[i].consultant && dataArr[i].consultant.name_en
          ? dataArr[i].consultant.name_en
          : "---";
      let consultantDept =
        dataArr[i].consultant && dataArr[i].consultant.dept_code
          ? dataArr[i].consultant.dept_code
          : "";
      let consultant =
        consultantName + (consultantDept ? " (" + consultantDept + ")" : "");

      itemArr.push({
        type: i === 0 ? "rent" : "price",
        price,
        date,
        consultant,
      });
    }

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.eaarecord",
          })}
        >
          {itemArr.map((v, i) => (
            <EaaRecordBox
              {...v}
              key={i}
            />
          ))}
          {dataArr.length === 0 && <div className={classes.notFound}>---</div>}
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(EaaRecord));
