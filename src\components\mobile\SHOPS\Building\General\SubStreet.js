import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import { getLangKey } from "../../../../../helper/generalHelper";
import FieldVal from "../../../../common/FieldVal";
import { injectIntl } from "react-intl";

const styles = (theme) => ({
  gridContent: {
    padding: "1vw 2vw",
  },
});

class SubStreet extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langkey = getLangKey(intl);

    let streets = detail.streets ? detail.streets.slice(1) : [];
    if (streets.length === 0) streets.push({});
    streets = streets.map(v => {
      let number = v.streetNo || "";
      let streetName = v[langkey] || "---";
      return number + " " + streetName;
    });

    return (
      <DetailBoxSection
        text={intl.formatMessage({
          id: "building.substreet",
        })}
        expandable={true}
        isExpanding={true}
      >
        <Grid container spacing={2} className={classes.gridContent}>
          {streets.map((v, i) => (
            <Grid item xs={12} key={i}>
              <FieldVal field={intl.formatMessage({ id: "building.substreet" }) + " " + (i + 1)}>
                {v}
              </FieldVal>
            </Grid>
          ))}
        </Grid>
      </DetailBoxSection>
    );
  }
}

export default withStyles(styles)(injectIntl(SubStreet));
