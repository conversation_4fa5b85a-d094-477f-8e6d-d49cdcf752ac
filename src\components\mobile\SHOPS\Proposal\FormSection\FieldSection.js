import React, { useMemo, useState, useEffect, useRef } from "react";
import { connect } from "react-redux";
import { Field, FieldArray } from "redux-form";
import { injectIntl } from "react-intl";
import _ from "lodash";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import { Grid, IconButton } from "@material-ui/core";
import EmojiPicker from "emoji-picker-react";
import SentimentSatisfiedAltIcon from '@material-ui/icons/SentimentSatisfiedAlt';
import ProposalFormPaper from "../../../../common/ProposalFormPaper";
import MuiSelectArrayOutput from "../../../../common/MuiSelectArrayOutput";
import EditableNormalAndBottomPrice from "../../../../common/EditableNormalAndBottomPrice";
import InlineTextField from "../../../../common/InlineTextField";
import InlineTextInput from "../../../../common/InlineTextInput";
import InlineTextInputField from "../../../../common/InlineTextInputField";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import InputWithCheckBox from "../../../../common/InputWithCheckBox";
import InputWithCheckBoxCustom from "../../../../common/InputWithCheckBoxCustom";
import ProposalFeeField from "../../../../common/ProposalFeeField";
import ProposalTenantField from "../../../../common/ProposalTenantField";
import FtInField from "../../../../common/FtInField";
import AreaInputItems from "../../../../common/AreaInputItems";
import ShowHiddenCheckBox from "../../../../common/ShowHiddenCheckBox";
import {
  getLangKey,
  numberComma,
  paresFloorShopNo,
} from "../../../../../helper/generalHelper";
import {
  createOptions,
  getStreetTypeOptions,
  hasIsShowTrue,
} from "../../../../Saleskit/helpers";
import MediaView from "../../../../Media/MediaView";
import StockIdField from "../../../../Saleskit/StockIdField";
import InclusiveFeeGroup from "../../../../common/InclusiveFeeGroup";
import PillCheckBox from "../../../../common/PillCheckBox";

// We can inject some CSS into the DOM.
const styles = {
  section: {
    paddingTop: "1vh",
  },
  right: {
    textAlign: "right",
  },
  twoLevelItem: {
    marginBottom: "1vh",
    "& > *:not(:first-child)": {
      color: "rgba(255, 255, 255, 0.75)",
    },
    padding: "0 4px",
    borderRadius: 4,
    color: "#fff",
  },
  rentItem: {
    backgroundColor: "rgba(0, 197, 197, .75)",
  },
  priceItem: {
    backgroundColor: "rgba(232, 0, 0, .75)",
  },
  greyItem: {
    backgroundColor: "rgba(132, 132, 132, .1)",
  },
  rentItemNoValue: {
    backgroundColor: "rgba(140, 190, 190, 0.2)",
  },
  priceItemNoValue: {
    backgroundColor: "rgba(200, 170, 170, 0.2)",
  },
  checkboxNoPadding: {
    padding: 0,
  },
  hidden: {
    visibility: "hidden",
  },
  areaInfo: {
    margin: "1vh 0",
  },
  totalAreaRow: {
    paddingTop: ".5vh",
    marginTop: ".5vh",
    borderTop: "1px solid #777",
  },
  remarksFieldMargin: {
    marginBottom: 3,
  },
};

function FieldSection({
  detail,
  classes,
  intl,
  changeFieldValue,
  type,
  formState,
  possessions,
  currentStates,
  stockId,
  isListProposal,
  mode,
  children,
  mediaData,
  defaultExpand=false,
}) {
  const { stock, media } = formState;

  const stockRef = React.useRef(stock);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const customTitleInputRef = useRef(null);
  React.useEffect(() => {
    stockRef.current = stock;
  }, [stock]);

  const langKey = getLangKey(intl);

  const stockData = useMemo(
    () =>
      isListProposal && stockId
        ? _.find(detail, (s) => s._id === stockId)
        : _.head(detail),
    [],
  );

  let priceContainer = classes.priceItem; // TODO: need grey color?
  let leaseContainer = classes.rentItem;
  const hasPrice =
    parseFloat(_.get(stock, "avgPrice[0].value", 0)) !== 0 ||
    parseFloat(_.get(stock, "totalPrice[0].value", 0)) !== 0;
  const hasRent =
    parseFloat(_.get(stock, "avgRent[0].value", 0)) !== 0 ||
    parseFloat(_.get(stock, "totalRent[0].value", 0)) !== 0;

  const disablePriceBox = type === "Lease";
  const disableRentBox = type === "Sale";

  const streetTypeOptions = getStreetTypeOptions(
    intl,
    _.get(stockData, "streets", []),
  );

  const possessionOptions = useMemo(
    () => createOptions(intl.locale, possessions),
    [],
  );

  const currentStateOptions = useMemo(
    () => createOptions(intl.locale, currentStates),
    [],
  );

  const inputToDisplayFee = (v) =>
    v && parseFloat(v) !== 0 ? "$" + numberComma(v, 2) : "---";
  const inputToDisplayYield = (v) =>
    v && parseFloat(v) !== 0 ? numberComma(v, 2) + "%" : "---";
  /* const inputToDisplayTitle = (v) => v || "---";

  // streetName and streetNo are not fields (not editable)
  const streetType = !isNaN(parseInt(_.get(stock, "streetType")))
    ? parseInt(_.get(stock, "streetType"))
    : 0;
  const streetName =
    _.get(stockData, `streets.${streetType}.street.${langKey}`) || "";
  const streetNo = _.get(stockData, `streets.${streetType}.number`) || "";
  const floor = _.get(stockData, "floor") || "---";
  const floorInChinese = _.get(stockData, "floorInChinese") || "---";
  const unit = _.get(stockData, "unit") || "---"; */

  const handleStreetTypeChange = (v) => {
    const streetType = !isNaN(parseInt(v.value)) ? parseInt(v.value) : 0;
    const streetName =
      stockData?.streets?.[streetType]?.street?.[langKey] || "";
    const streetNo = stockData?.streets?.[streetType]?.number || "";

    if (intl.locale === "zh")
      changeFieldValue("general.proposalName", `${streetName}${streetNo}號`);
    else changeFieldValue("general.proposalName", `${streetNo} ${streetName}`);
  };

  const namePrefix = isListProposal ? `stocks.${stockId}.` : "";

  const handleChangeIsNego = (field) => (e) => {
    changeFieldValue(`${namePrefix}stock.total${field}[0].isNego`, e.target.checked);
  };

  useEffect(() => {
    const areas = _.get(stock, "areas");
    const grossSum = areas.reduce((acc, item) => acc + Number(item.gross), 0);
    const netSum = areas.reduce((acc, item) => acc + Number(item.net), 0);
    changeFieldValue(`${namePrefix}stock.area[0]`, {
      ...stock[`area`][0],
      gross: grossSum,
      net: netSum,
    });
  }, [_.get(stock, "areas")]);

  /** `AWS-3829` disabled for `usage` `Mix (with residential)` OR `Domestic Use` */
  const isResidential = React.useMemo(() => {
    const usageNameEn = _.get(stockData, "usage.nameEn") || "";
    if (usageNameEn) {
      const regex = /Mix\s*\(with residential\)|Domestic Use/;
      return regex.test(usageNameEn);
    }
    return false;
  }, [_.get(stockData, "usage.nameEn", "")]);

  const usageCheckboxDisableds = React.useMemo(() => {
    return {
      grossDisabled: isResidential,
      netDisabled: isResidential,
    };
  }, [isResidential]);

  /* React.useEffect(() => {
    if (isResidential) {
      changeFieldValue(`${namePrefix}stock.area[0]`, {
        ...stockRef.current[`area`][0],
        grossIsShow: false,
        netIsShow: false,
      });

      const areas = (_.get(stockRef.current, "areas") || []).map((item) => {
        return { ...item, grossIsShow: false, netIsShow: false, };
      });
      changeFieldValue(`${namePrefix}stock.areas`, areas);
    }
  }, [isResidential, changeFieldValue]); */

  React.useEffect(() => {
    const includedFeeObj = stock.includedFee[0];
    const { rates, gRent, managementFee, acFee } = stock;
    includedFeeObj.rates = rates[0].paidBy === 'Paid By Landlord';
    includedFeeObj.gRent = gRent[0].paidBy === 'Paid By Landlord';
    includedFeeObj.managementFee = managementFee[0].paidBy === 'Paid By Landlord';
    includedFeeObj.acFee = acFee[0].paidBy === 'Paid By Landlord';
    stock.allInclusive = [rates[0], gRent[0], managementFee[0]].every(({ paidBy }) => paidBy === 'Paid By Landlord');
    if (!stock.allInclusive && [rates[0], gRent[0], managementFee[0]].some(({ paidBy }) => paidBy === 'Paid By Tenant')) {
      stock.allInclusive = false;
    }
  }, [stock.rates[0].paidBy, stock.gRent[0].paidBy, stock.managementFee[0].paidBy, stock.acFee[0].paidBy]);

  React.useEffect(() => {
    // 检查customTitle的value是否有值
    const customTitleValue = _.get(stock, "customTitle.value");
    if (customTitleValue) {
      // 如果有值则设置isShow为true
      changeFieldValue(`${namePrefix}stock.customTitle.isShow`, true);
    }
  }, [_.get(stock, "customTitle.value")]);

  const onEmojiClick = (emojiData) => {
    const { selectionStart, selectionEnd } = customTitleInputRef.current;
    const currentTitle = _.get(stock, "customTitle.value") || "";
    // 在光标位置插入表情
    const newTitle = currentTitle.substring(0, selectionStart) + 
                     emojiData.emoji + 
                     currentTitle.substring(selectionEnd);
    changeFieldValue(`${namePrefix}stock.customTitle.value`, newTitle);
    setShowEmojiPicker(false);
  }

  return (
    <ProposalFormPaper>
      <DetailBoxSection
        text={intl.formatMessage({
          id: "proposal.form.stockinfo",
        })}
        expandable
        isExpanding={true}
      >
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <StockIdField stockId={_.get(stock, "stockId", "")} />
          </Grid>
          {mode === "indv" && (
          <>
          <Grid item xs={12}>
            <InputWithCheckBoxCustom
              name={`${namePrefix}stock.termRemarks.nameZh`}
              className={classes.remarksFieldMargin}
              label={intl.formatMessage({
                id: "proposal.form.chiRemarks",
              })}
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              fakeCheckbox
              renderComponent={InlineTextInputField}
              multiline
              fullWidth
            />
          </Grid>
          <Grid item xs={12}>
            <InputWithCheckBoxCustom
              name={`${namePrefix}stock.termRemarks.nameEn`}
              className={classes.remarksFieldMargin}
              label={intl.formatMessage({
                id: "proposal.form.engRemarks",
              })}
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              fakeCheckbox
              renderComponent={InlineTextInputField}
              multiline
              fullWidth
            />
          </Grid>
          <Grid item xs={12}>
            <InputWithCheckBoxCustom
              name={`${namePrefix}stock.customTitle`}
              label={
                <div onClick={e => e.preventDefault()} style={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
                  <span>{intl.formatMessage({id: "proposal.form.customtitle",})}</span>
                  {
                    <IconButton
                      size="small"
                      style={{
                        width: 48,
                        borderRadius: 18,
                        border: '1px solid gray',
                      }}
                      onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                    >
                      <SentimentSatisfiedAltIcon style={{ fontSize: 18, color: 'gray' }} />
                    </IconButton>
                  }
                </div>
              }
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              changeFieldValue={changeFieldValue}
              renderComponent={InlineTextField}
              fullWidth
              inputProps={{
                ref: customTitleInputRef,
              }}
            />
            {showEmojiPicker && (
              <div style={{ position: 'absolute', zIndex: 1000 }}>
                <EmojiPicker onEmojiClick={onEmojiClick} searchDisabled={true} skinTonesDisabled={true} previewConfig={{ showPreview: false }} />
              </div>
            )}
          </Grid>
          </>
          )}
          {!isListProposal && (
            <Grid item xs={6}>
              <InputWithCheckBoxCustom
                name="stock.streetType"
                label={intl.formatMessage({
                  id: "proposal.form.streettype",
                })}
                checkboxInFront
                aligntoLabel
                checkboxXs={2}
                fakeCheckbox
                renderComponent={MuiSelectArrayOutput}
                ranges={streetTypeOptions}
                isArrayOutput={false}
                extraHandleChange={handleStreetTypeChange}
                fullWidth
              />
            </Grid>
          )}
        </Grid>

        {/* <Grid container spacing={1}>
          <ShowHiddenCheckBox
            className={classes.hidden}
            input={{
              value: { isShow: true },
            }}
            changeFieldValue={() => {}}
            aligntoLabel={true}
            disabled
          />
          <Grid item xs={11} style={{ margin: "auto" }}>
            {intl.locale === "zh" ? (
              <div>
                {_.get(stock, "customDistrict")} {streetName} {streetNo}號
              </div>
            ) : (
              <div>
                {streetNo} {streetName}, {_.get(stock, "customDistrict")}
              </div>
            )}
            <div>{paresFloorShopNo(floor, floorInChinese, unit, intl)}</div>
          </Grid>
        </Grid> */}

        <Grid container spacing={1}>
          <Grid item xs={12}>
            <InputWithCheckBoxCustom
              name={`${namePrefix}stock.customAddressZh`}
              className={classes.remarksFieldMargin}
              label={intl.formatMessage({
                id: "proposal.form.chiAddress",
              })}
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              fakeCheckbox
              renderComponent={InlineTextInputField}
              multiline
              fullWidth
            />
          </Grid>
          <Grid item xs={12}>
            <InputWithCheckBoxCustom
              name={`${namePrefix}stock.customAddressEn`}
              className={classes.remarksFieldMargin}
              label={intl.formatMessage({
                id: "proposal.form.engAddress",
              })}
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              fakeCheckbox
              renderComponent={InlineTextInputField}
              multiline
              fullWidth
            />
          </Grid>
        </Grid>
        {/* building info */}
        {/* <div>
          <FieldArray
            name={`${namePrefix}stock.customBuilding`}
            component={InputWithCheckBox}
            renderComponent={InlineTextInput}
            changeFieldValue={changeFieldValue}
            checkboxProps={{
              className: classes.checkboxNoPadding,
            }}
            checkboxInFront
            checkboxXs={1}
            disabled
            noBottomBorder
          />
        </div> */}

        {/* basic info 3 */}
        <div className={classes.areaInfo}>
          <FieldArray
            name={`${namePrefix}stock.areas`}
            component={AreaInputItems}
            changeFieldValue={changeFieldValue}
            formFields={_.get(formState, "stock.areas") || []}
            props={usageCheckboxDisableds}
          />
          <FieldArray
            className={classes.totalAreaRow}
            name={`${namePrefix}stock.area`}
            component={AreaInputItems}
            changeFieldValue={changeFieldValue}
            formFields={_.get(formState, "stock.area") || []}
            props={usageCheckboxDisableds}
          />
        </div>

        {/* prcie rent box */}
        <div className={classes.right}>
          <div
            className={clsx(
              classes.twoLevelItem,
              leaseContainer,
              (disableRentBox) && classes.rentItemNoValue,
            )}
          >
            <EditableNormalAndBottomPrice
              avgDecimal={2}
              label="Rent"
              changeFieldValue={changeFieldValue}
              formFields={stock}
              fieldNamePrefix={namePrefix}
              disabled={disableRentBox}
              suggested
            />
          </div>

          <div style={{ marginBottom: "1vh" }}>
          <Field
            name={`${namePrefix}stock.totalRent[0].isNego`}
            component={PillCheckBox}
            input={{
              value: _.get(stock, "totalRent[0].isNego", false),
              onChange: handleChangeIsNego("Rent"),
            }}
            text={intl.formatMessage({ id: "stock.negotiable" })}
            disabled={disableRentBox}
          />
          </div>

          <div
            className={clsx(
              classes.twoLevelItem,
              priceContainer,
              (disablePriceBox) && classes.priceItemNoValue,
            )}
          >
            <EditableNormalAndBottomPrice
              label="Price"
              changeFieldValue={changeFieldValue}
              formFields={stock}
              fieldNamePrefix={namePrefix}
              disabled={disablePriceBox}
              updateYield
              suggested
            />
          </div>

          <div style={{ marginBottom: "1vh" }}>
          <Field
            name={`${namePrefix}stock.totalPrice[0].isNego`}
            component={PillCheckBox}
            input={{
              value: _.get(stock, "totalPrice[0].isNego", false),
              onChange: handleChangeIsNego("Price"),
            }}
            text={intl.formatMessage({ id: "stock.negotiable" })}
            disabled={disablePriceBox}
          />
          </div>
        </div>

        {/* unit section */}
        <Grid container spacing={1}>
          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.stockType`}
              label={intl.formatMessage({
                id: "stock.stocktype",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              disabled
              noBottomBorder
            />
          </Grid>
          <Grid item xs={6}>
          </Grid>
          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.possession`}
              label={intl.formatMessage({
                id: "proposal.form.possession",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={MuiSelectArrayOutput}
              changeFieldValue={changeFieldValue}
              ranges={possessionOptions}
              isArrayOutput={false}
            />
          </Grid>

          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.currentState`}
              label={intl.formatMessage({
                id: "search.form.currentState",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={MuiSelectArrayOutput}
              changeFieldValue={changeFieldValue}
              ranges={currentStateOptions}
              isArrayOutput={false}
            />
          </Grid>

          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.entranceWidth`}
              label={intl.formatMessage({
                id: "proposal.form.entranceWidth",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={FtInField}
              changeFieldValue={changeFieldValue}
            />
          </Grid>

          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.ceilingHeight`}
              label={intl.formatMessage({
                id: "proposal.form.ceilingHeight",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={FtInField}
              changeFieldValue={changeFieldValue}
            />
          </Grid>

          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.unitDepth`}
              label={intl.formatMessage({
                id: "stock.unitdepth",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={FtInField}
              changeFieldValue={changeFieldValue}
            />
          </Grid>

          <Grid item xs={6}>
            <FieldArray
              name={`${namePrefix}stock.yield`}
              label={intl.formatMessage({
                id: "stock.yield",
              })}
              component={InputWithCheckBox}
              checkboxInFront
              aligntoLabel
              checkboxXs={2}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              inputToDisplay={inputToDisplayYield}
              type="number"
              min={0}
              disabled
              noBottomBorder
            />
          </Grid>
        </Grid>

        <Grid container spacing={1}>
          <Grid item xs={12}>
            <FieldArray
              name={`${namePrefix}stock.managementFee`}
              label={intl.formatMessage({
                id: "stock.mgtfee",
              })}
              component={ProposalFeeField}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              inputToDisplay={inputToDisplayFee}
              type="number"
              min={0}
              included={
                _.get(stock, "allInclusive") ||
                _.get(stock, "includedFee[0].managementFee")
              }
              formState={formState}
              isListProposal={isListProposal}
              stockId={stockId}
            />
          </Grid>
          <Grid item xs={12}>
            <FieldArray
              name={`${namePrefix}stock.gRent`}
              label={intl.formatMessage({
                id: "stock.grent",
              })}
              component={ProposalFeeField}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              inputToDisplay={inputToDisplayFee}
              type="number"
              min={0}
              included={
                _.get(stock, "allInclusive") ||
                _.get(stock, "includedFee[0].gRent")
              }
              formState={formState}
              isListProposal={isListProposal}
              stockId={stockId}
            />
          </Grid>

          <Grid item xs={12}>
            <FieldArray
              name={`${namePrefix}stock.rates`}
              label={intl.formatMessage({
                id: "stock.rates",
              })}
              component={ProposalFeeField}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              inputToDisplay={inputToDisplayFee}
              type="number"
              min={0}
              included={
                _.get(stock, "allInclusive") ||
                _.get(stock, "includedFee[0].rates")
              }
              formState={formState}
              isListProposal={isListProposal}
              stockId={stockId}
            />
          </Grid>

          <Grid item xs={12}>
            <FieldArray
              name={`${namePrefix}stock.acFee`}
              label={intl.formatMessage({
                id: "stock.airconditioningfee",
              })}
              component={ProposalFeeField}
              renderComponent={InlineTextField}
              changeFieldValue={changeFieldValue}
              inputToDisplay={inputToDisplayFee}
              type="number"
              min={0}
              included={
                _.get(stock, "allInclusive") ||
                _.get(stock, "includedFee[0].acFee")
              }
              formState={formState}
              isListProposal={isListProposal}
              stockId={stockId}
            />
          </Grid>
          <Grid item xs={12}>
            <InclusiveFeeGroup
              fieldNamePrefix={namePrefix}
              includedFee={_.get(stock, "includedFee[0]")}
              allInclusive={_.get(stock, "allInclusive")}
              changeFieldValue={changeFieldValue}
            />
          </Grid>
        </Grid>

        <Grid container spacing={1} style={{ marginTop: 15 }}>
          <Grid item xs={12}>
            <InputWithCheckBoxCustom
              name={`${namePrefix}stock.remarks`}
              className={classes.remarksFieldMargin}
              label={intl.formatMessage({
                id: "proposal.form.remarks",
              })}
              checkboxInFront
              aligntoLabel
              checkboxXs={1}
              fakeCheckbox
              renderComponent={InlineTextInputField}
              multiline
              fullWidth
            />
          </Grid>
        </Grid>
      </DetailBoxSection>

      <div style={{paddingTop: "1vh"}}>
      <DetailBoxSection
        text={intl.formatMessage({
          id: "proposal.form.tenant",
        })}
        expandable
      >
        <Grid container spacing={1}>
          <Grid item xs={12}>
            <FieldArray
              name={`${namePrefix}stock.currentTenants`}
              component={ProposalTenantField}
              status={"currentTenants"}
              changeFieldValue={(...args) => {
                changeFieldValue(...args);
                if (isListProposal && hasIsShowTrue(args[1])) {
                  changeFieldValue(`general.showTenancy[0].isShow`, true);
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={1}>
          <Grid item xs={12}>
            <FieldArray
              name={`${namePrefix}stock.advanceTenants`}
              component={ProposalTenantField}
              status={"advanceTenants"}
              changeFieldValue={(...args) => {
                changeFieldValue(...args);
                if (isListProposal && hasIsShowTrue(args[1])) {
                  changeFieldValue(`general.showTenancy[0].isShow`, true);
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={1}>
          <Grid item xs={12}>
            <FieldArray
              name={`${namePrefix}stock.previousTenants`}
              component={ProposalTenantField}
              status={"previousTenants"}
              changeFieldValue={(...args) => {
                changeFieldValue(...args);
                if (isListProposal && hasIsShowTrue(args[1])) {
                  changeFieldValue(`general.showTenancy[0].isShow`, true);
                }
              }}
            />
          </Grid>
        </Grid>
        <Grid container spacing={1}>
          <Grid item xs={12}>
            <FieldArray
              name={`${namePrefix}stock.formerTenants`}
              component={ProposalTenantField}
              status={"formerTenants"}
              changeFieldValue={(...args) => {
                changeFieldValue(...args);
                if (isListProposal && hasIsShowTrue(args[1])) {
                  changeFieldValue(`general.showTenancy[0].isShow`, true);
                }
              }}
            />
          </Grid>
        </Grid>
      </DetailBoxSection>
      </div>

      <MediaView
        fieldNamePrefix={namePrefix}
        stockMedia={_.get(mediaData, "stock")}
        buildingMedia={_.get(mediaData, "building")}
        streetMedia={_.get(mediaData, "street")}
        isProposal
        stockId={stockId}
        coordinates={_.pick(media, ["lat", "lng"])}
        defaultExpand={defaultExpand}
        isListProposal={mode === "list"}
      />

      {children}
    </ProposalFormPaper>
  );
}

const mapStateToProps = (state) => ({
  form: state.form ? state.form : {},
  detail: _.get(state, "stock.detail") || [],
  possessions: state.stock.possessions,
  currentStates: state.stock.currentStates,
});

export default connect(mapStateToProps)(
  withStyles(styles)(injectIntl(FieldSection)),
);
