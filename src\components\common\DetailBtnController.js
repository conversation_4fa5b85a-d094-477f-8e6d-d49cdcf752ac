import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import Stroke from "./Stroke";
import FavoriteButton from "./FavoriteButton";
import SelectFieldArrayOutput from "./SelectFieldArrayOutput";
import { injectIntl } from "react-intl";
import MarkStockButton from "./MarkStockButton";
import aiTextIcon from "../../files/icons/aiText.svg"
import StandardSvg from "./StandardSvg";
import Link from "../../components/Link";
import {
  enableBatchTwo
} from "../../config";

// We can inject some CSS into the DOM.
const styles = {
  status: {
    fontSize: "1.5em",
    fontWeight: "700",
    textAlign: "right"
  },
  flex: {
    display: "flex",
  },
  strokeHidden: {
    visibility: "collapse"
  },
  myfavoriteIcon: {
    padding: "0 1vw"
  },
  btnBox: {
    display: "flex",
    alignItems: "center",
    "& > div:last-child": {
      padding: "0 1vw",
      "& > img": {
        width: "30px",
        height: "30px"
      }
    },
    "& svg": {
      width: "30px",
      height: "30px"
    },

  }
};

function DetailBtnController(props) {
  const {
    classes,
    status,
    mongoid,
    stockid,
    favoriteStockIds,
    markStockIds,
    intl,
  } = props;

  return (
    <div className={classes.root}>
      <div className={classes.btnBox}>
        {favoriteStockIds && mongoid && stockid && (
          <FavoriteButton
            favoriteStockIds={favoriteStockIds}
            mongoid={mongoid}
            stockid={stockid}
            isStockDetailPage={true}
            className={classes.myfavoriteIcon}
          />
        )}
        {markStockIds && mongoid && stockid && (
          <MarkStockButton checked={markStockIds.includes(mongoid)} stockId={mongoid} />
        )}
        <Link to={"/aiText/" + mongoid}>
          <StandardSvg src={aiTextIcon} style={{marginLeft: "6px"}}></StandardSvg>
        </Link>
      </div>
    </div>
  );
}

DetailBtnController.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
};

export default withStyles(styles)(injectIntl(DetailBtnController));
