import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { createStyles, withStyles } from "@material-ui/styles";
import { injectIntl } from "react-intl";
import Grid from "@material-ui/core/Grid";
import Photo from "./Photo";
import Embedded from "./Embedded";
import Document from "./Document";
import VideoThumbnail from "./VideoThumbnail";
import ProposalControlRow from "./ProposalControlRow";
import Pagination from '@material-ui/lab/Pagination';
import { addActivityLog } from "@/actions/log";
import Typography from "@material-ui/core/Typography";
import { dateFormatter } from "@/helper/generalHelper";

const styles = createStyles({
  paginationContainer: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: '16px'
  },
  mediaItemContainer: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    marginBottom: '8px',
    backgroundColor: '#fff',
    borderRadius: '4px',
    overflow: 'hidden'
  },
  // 列表视图样式
  listViewContainer: {
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    marginBottom: '8px',
    backgroundColor: '#fff',
    // borderRadius: '4px',
    overflow: 'hidden'
  },
  listViewImageContainer: {
    width: '110px',
    minWidth: '110px',
    height: '110px',
    position: 'relative',
  },
  listViewContentContainer: {
    flex: 1,
    padding: '0 0 0 12px',
    display: 'flex',
    flexDirection: 'column',
    width: 0,
  },
  listViewInfo: {
    flex: 1,
    // marginBottom: '8px',
    color: "#000",
  },
  listViewButtons: {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '8px',
  },
  // 文本样式
  textEllipsis: {
    color: '#000',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    width: '100%',
    display: 'block',
    fontSize: "14px",
  }
});

class Media extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    media: PropTypes.array,
    openLightboxAndSetMediaId: PropTypes.func,
    isProposal: PropTypes.bool,
    deletable: PropTypes.bool,
    handleOpenDeleteMediaDialog: PropTypes.func,
    isListProposal: PropTypes.bool,
    viewType: PropTypes.oneOf(['grid', 'list']),
    renderCustomButton: PropTypes.func,
  };

  state = {
    page: 1,
    itemsPerPage: 3 * 10 // 每页显示个30项目(3x10网格)
  };

  handlePageChange = (event, value) => {
    this.setState({ page: value });
  };

  handleAddActivityLog = (media) => {
    this.props.addActivityLog("media", "read", media);
  }

  renderMediaItemForList = (v) => {
    const {
      deletable,
      handleOpenDeleteMediaDialog,
      openLightboxAndSetMediaId,
      isProposal,
      mediaPath,
      fields,
      isListProposal,
      renderCustomButton,
    } = this.props;

    if (v.type === "document") {
      return (
        <Document
          record={v}
          deletable={deletable}
          handleOpenDeleteMediaDialog={handleOpenDeleteMediaDialog}
          handleOpenPopup={() => {
            this.handleAddActivityLog(v);
          }}
        />
      );
    }

    if (v.type === "photo") {
      return (
        <Photo
          photo={v}
          handleOpenLightbox={() => {
            this.handleAddActivityLog(v);
            openLightboxAndSetMediaId(v.id)
          }}
        />
      );
    }

    if (v.type === "video") {
      return (
        <VideoThumbnail
          video={v}
          deletable={deletable}
          handleOpenDeleteMediaDialog={handleOpenDeleteMediaDialog}
          handleOpenPopup={() => {
            this.handleAddActivityLog(v);
          }}
          renderCustomButton={renderCustomButton}
        />
      );
    }

    if (v.type === "kol_video") {
      return (
        <VideoThumbnail
          video={v}
          deletable={deletable}
          handleOpenDeleteMediaDialog={handleOpenDeleteMediaDialog}
          handleOpenPopup={() => {
            this.handleAddActivityLog(v);
          }}
          renderCustomButton={renderCustomButton}
        />
      );
    }

    if (v.type === "embedded_script") {
      return (
        <Embedded
          script={v}
          handleOpenLightbox={() => {
            this.handleAddActivityLog(v);
          }}
        />
      );
    }

    return null;
  }

  renderListMediaItem = (v) => {
    const { classes, intl } = this.props;

    const {
      createdDate,
      employee,
    } = v;

    return (
      <>
        <div className={classes.listViewContainer} key={v.id}>
          <div className={classes.listViewImageContainer}>
            {this.renderMediaItemForList(v)}
          </div>
          <div className={classes.listViewContentContainer}>
            <div className={classes.listViewInfo}>
              <Typography className={classes.textEllipsis}>
                {intl.locale === 'zh' ? (v.stockAddress?.zh || v.buildingAddress?.zh) : (v.stockAddress?.en || v.buildingAddress?.en)}
              </Typography>
              <Typography className={classes.textEllipsis}>
                {v.type === "photo" ? "Photo" : v.type === "video" ? "Video" : v.type === "kol_video" ? "KOL Video" : "Document"}
              </Typography>
              <Typography className={classes.textEllipsis}>
                {v.description || v.originalFilename}
              </Typography>
              <Typography className={classes.textEllipsis}>
                {dateFormatter(v.createdDate || "")} {employee?.branchId} {intl.locale === 'zh' ? employee?.cName : employee?.eName}
              </Typography>
            </div>
            <div className={classes.listViewButtons}>
              {v.renderCustomButtons && v.renderCustomButtons()}
            </div>
          </div>
        </div>

        <hr style={{ margin: "0 0 8px 0" }} />
      </>
    );
  }

  render() {
    const {
      classes,
      media,
      openLightboxAndSetMediaId,
      isProposal,
      deletable,
      handleOpenDeleteMediaDialog,
      mediaPath,
      fields,
      isListProposal,
      viewType = 'grid', // 默认使用网格视图
      renderCustomButton,
    } = this.props;
    const { page, itemsPerPage } = this.state;

    // 计算当前页的媒体项
    const startIndex = (page - 1) * itemsPerPage;
    const currentPageMedia = media.slice(startIndex, startIndex + itemsPerPage);
    const totalPages = Math.ceil(media.length / itemsPerPage);

    // 根据 viewType 决定使用网格视图还是列表视图
    if (viewType === 'list') {
      return (
        <>
          {currentPageMedia.map((v) => this.renderListMediaItem(v))}

          {totalPages > 1 && (
            <div className={classes.paginationContainer}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={this.handlePageChange}
                color="primary"
                size="small"
              />
            </div>
          )}
        </>
      );
    }

    // 网格视图（默认）
    return (
      <>
      <Grid container spacing={1}>
        {currentPageMedia.map((v, i) => (
          <Grid item xs={4} key={v.id}>
            <div className={classes.mediaItemContainer}>
            {v.type === "document" && (
              <>
                <Document
                  record={v}
                  deletable={deletable}
                  handleOpenDeleteMediaDialog={handleOpenDeleteMediaDialog}
                  handleOpenPopup={() => {
                    this.handleAddActivityLog(v);
                  }}
                />
                {isProposal && (
                  <ProposalControlRow
                    mediaPath={mediaPath}
                    fields={fields}
                    mediaId={v.id}
                    media={v}
                    isPhoto
                    isListProposal={isListProposal}
                  />
                )}
              </>
            )}
            {v.type === "photo" && (
              <>
                <Photo
                  photo={v}
                  handleOpenLightbox={() => {
                    this.handleAddActivityLog(v);
                    openLightboxAndSetMediaId(v.id)
                  }}
                />
                {isProposal && (
                  <ProposalControlRow
                    mediaPath={mediaPath}
                    fields={fields}
                    mediaId={v.id}
                    media={v}
                    isPhoto
                    isListProposal={isListProposal}
                  />
                )}
              </>
            )}
            {v.type === "video" && (
              <>
                {isProposal && (
                  <ProposalControlRow
                    mediaPath={mediaPath}
                    fields={fields}
                    mediaId={v.id}
                    isListProposal={isListProposal}
                  />
                )}
                <VideoThumbnail
                  video={v}
                  deletable={deletable}
                  handleOpenDeleteMediaDialog={handleOpenDeleteMediaDialog}
                  handleOpenPopup={() => {
                    this.handleAddActivityLog(v);
                  }}
                  renderCustomButton={renderCustomButton}
                />
              </>
            )}
            {v.type === "kol_video" && (
              <>
                {isProposal && (
                  <ProposalControlRow
                    mediaPath={mediaPath}
                    fields={fields}
                    mediaId={v.id}
                  />
                )}
                <VideoThumbnail
                  video={v}
                  deletable={deletable}
                  handleOpenDeleteMediaDialog={handleOpenDeleteMediaDialog}
                  handleOpenPopup={() => {
                    this.handleAddActivityLog(v);
                  }}
                  renderCustomButton={renderCustomButton}
                />
              </>
            )}
            {v.type === "embedded_script" && (
              <>
                {isProposal && (
                  <ProposalControlRow
                    mediaPath={mediaPath}
                    fields={fields}
                    mediaId={v.id}
                    isListProposal={isListProposal}
                  />
                )}
                <Embedded
                  script={v}
                  handleOpenLightbox={() => {
                    this.handleAddActivityLog(v);
                  }}
                />
              </>
            )}
            {v.renderCustomButtons && v.renderCustomButtons()}
            </div>
          </Grid>
        ))}
      </Grid>

      {totalPages > 1 && (
        <div className={classes.paginationContainer}>
          <Pagination
            count={totalPages}
            page={page}
            onChange={this.handlePageChange}
            color="primary"
            size="small"
          />
        </div>
      )}
      </>
    );
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    addActivityLog: (...args) => dispatch(addActivityLog(...args)),
  };
};

export default connect(
  null,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(Media)));
