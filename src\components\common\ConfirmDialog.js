import React from "react";
import { injectIntl } from "react-intl";
import PropTypes from "prop-types";

import Dialog from "./Dialog";
import DialogFrame from "./DialogFrame";

function ConfirmDialog({
  open,
  onOkCallback,
  onCancelCallback,
  handleClose,
  children,
  okMsg,
  cancelMsg,
  buttonMainProps,
  intl,
  ...dialogProps
}) {
  const handleOk = () => {
    onOkCallback();
    handleClose();
  };

  const handleCancel = () => {
    onCancelCallback && onCancelCallback();
    handleClose();
  };

  const okText = okMsg || intl.formatMessage({ id: "common.ok" });
  const cancelText = cancelMsg || intl.formatMessage({ id: "common.cancel" });

  return (
    <Dialog open={open} handleClose={handleCancel} fullWidth {...dialogProps}>
      <DialogFrame
        buttonBack={cancelText}
        buttonMain={onOkCallback ? okText : null}
        handleBack={handleCancel}
        handleMain={handleOk}
        buttonMainProps={buttonMainProps}
      >
        {children}
      </DialogFrame>
    </Dialog>
  );
}

ConfirmDialog.defaultProps = {
  okMsg: null,
  cancelMsg: null,
  buttonMainProps: null,
  onCancelCallback: null,
  onOkCallback: null,
};

ConfirmDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onOkCallback: PropTypes.func,
  handleClose: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  okMsg: PropTypes.string,
  cancelMsg: PropTypes.string,
  buttonMainProps: PropTypes.object,
  intl: PropTypes.object.isRequired,
};

export default injectIntl(ConfirmDialog);
