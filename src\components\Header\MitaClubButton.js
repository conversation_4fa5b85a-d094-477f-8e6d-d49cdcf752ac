import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import FormButton from "../common/FormButton";

const styles = {
  root: {
    fontSize: "1.1em",
    backgroundColor: "#B79046",
    boxShadow: "none",
    minWidth: "126px",
    "&:hover": {
      color: "#B79046"
    },
  }
};

function mitaClubButton(props) {
  const { classes, children, className, ...other } = props;

  return (
    <FormButton className={clsx(classes.root, className)} {...other}>
      {children}
    </FormButton>
  );
}

mitaClubButton.propTypes = {
  children: PropTypes.node,
  classes: PropTypes.object.isRequired,
  className: PropTypes.string
};

export default withStyles(styles)(mitaClubButton);
