export const LIST_TRANSACTIONS_QUERY = `query ($limit: Int, $offset: Int, $sorter: [transactionSorter], $building: [ID], $district: [ID], $type: [String], $floorMin: Int, $floorMax: Int, $floorLeft: String, $floorRight: String, $totalAreaMin: Int, $totalAreaMax: Int, $shopAreaMin: Int, $shopAreaMax: Int, $priceMinTotal: Int, $priceMaxTotal: Int, $priceMinAvg: Int, $priceMaxAvg: Int, $rentMinTotal: Int, $rentMaxTotal: Int, $rentMinAvg: Int, $rentMaxAvg: Int, $dateMin: String, $dateMax: String, $isDeleted: Boolean, $unicornId: [Int], $dataSource: [String], $street: [ID], $stock: [ID]) {
  transactions(limit: $limit, offset: $offset, sort: $sorter, building: $building, district: $district, type: $type, floorMin: $floorMin, floorMax: $floorMax, floorLeft: $floorLeft, floorRight: $floorRight, totalAreaMin: $totalAreaMin, totalAreaMax: $totalAreaMax, shopAreaMin: $shopAreaMin, shopAreaMax: $shopAreaMax, priceMinTotal: $priceMinTotal, priceMaxTotal: $priceMaxTotal, priceMinAvg: $priceMinAvg, priceMaxAvg: $priceMaxAvg, rentMinTotal: $rentMinTotal, rentMaxTotal: $rentMaxTotal, rentMinAvg: $rentMinAvg, rentMaxAvg: $rentMaxAvg, dateMin: $dateMin, dateMax: $dateMax, isDeleted: $isDeleted, unicornId: $unicornId, dataSource: $dataSource, street: $street, stock: $stock) {
    _id
    type
    date
    tx_nature
    street {
      no
      en
      zh
    }
    district {
      en
      zh
    }
    building {
      en
      zh
    }
    floor {
      en
      zh
    }
    unit
    area {
      value
      type
      shopno
      areaName
    }
    sell
    ft_sell
    data_source {
      en
      zh
    }
    street {
      en
    }
    agent {
      branch_code
      sales
    }
    last_update {
      datetime
      by {
        name_en
      }
    }
    tenancy {
      rent
      ft_rent
    }
  }
}
  `;
