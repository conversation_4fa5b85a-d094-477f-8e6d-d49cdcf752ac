export const getMediaTypeOptions = (intl) => [
  {
    value: "",
    label: intl.formatMessage({
      id: "search.form.all",
    }),
  },
  {
    value: "video",
    label: intl.formatMessage({
      id: "stock.video",
    }),
  },
  {
    value: "kol_video",
    label: intl.formatMessage({
      id: "stock.kol",
    }),
  },
  {
    value: "layout",
    label: intl.formatMessage({
      id: "stock.photo.layout",
    }),
  },
  {
    value: "interior",
    label: intl.formatMessage({
      id: "stock.photo.interior",
    }),
  },
  {
    value: "plan",
    label: intl.formatMessage({
      id: "stock.photo.plan",
    }),
  },
  {
    value: "lobby",
    label: intl.formatMessage({
      id: "stock.photo.lobby",
    }),
  },
  {
    value: "lift",
    label: intl.formatMessage({
      id: "stock.photo.lift",
    }),
  },
  {
    value: "entrance",
    label: intl.formatMessage({
      id: "stock.photo.entrance",
    }),
  },
  {
    value: "positionPlan",
    label: intl.formatMessage({
      id: "stock.photo.positionplan",
    }),
  },
  {
    value: "floorPlan",
    label: intl.formatMessage({
      id: "stock.photo.floorplan",
    }),
  },
  {
    value: "areaPlan",
    label: intl.formatMessage({
      id: "stock.photo.areaplan",
    }),
  },
  {
    value: "buildingInfo",
    label: intl.formatMessage({
      id: "stock.photo.buildinginfo",
    }),
  },
  {
    value: "gfPhoto",
    label: intl.formatMessage({
      id: "stock.photo.gfphoto",
    }),
  },
  {
    value: "OZP",
    label: intl.formatMessage({
      id: "stock.photo.ozp",
    }),
  },
];
