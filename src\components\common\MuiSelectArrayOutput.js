import React from "react";
import { makeStyles } from "@material-ui/core/styles";
import clsx from "clsx";
import Select from "@material-ui/core/Select";
import InputLabel from "@material-ui/core/InputLabel";
import MenuItem from "@material-ui/core/MenuItem";
import FormControl from "@material-ui/core/FormControl";
import FormHelperText from "@material-ui/core/FormHelperText";

const useStyles = makeStyles((theme) => ({
  root: {
    minWidth: 100,
  },
  selectAlignCheckbox: {
    "label + &": {
      marginTop: 20,
    },
  },
  labelAlignCheckbox: {
    fontSize: 18,
    lineHeight: 1.5,
  }
}));

function MuiSelectArrayOutput(props) {
  const {
    label,
    classname,
    ranges,
    input,
    selectedData,
    setSelectedData,
    isArrayOutput = true,
    aligntoLabel,
    extraHandleChange,
    jsonField = "value",
    ...custom
  } = props;
  const { value, name, onChange, onBlur } = input;
  const { touched, invalid, error } = props.meta;

  let val = null;
  let isJson = false;
  if (selectedData) {
    val = selectedData.value;
  } else if (value !== null && value !== undefined) {
    val = isArrayOutput ? value[0] : value;
    if (typeof val === "object") {
      isJson = true;
      val = val[jsonField];
    }
  }
  if (!val) val = ranges[0].value;

  const classes = useStyles();

  function handleChange(e) {
    const newValueJson = ranges && ranges.filter(v => v.value === e.target.value)[0]
      ? ranges.filter(v => v.value === e.target.value)[0]
      : null;
    if (extraHandleChange) extraHandleChange(newValueJson);
    if (setSelectedData) setSelectedData(name, newValueJson);
    if (isArrayOutput) {
      if (newValueJson != null && newValueJson.value !== "") {
        onChange([newValueJson.value]);
      } else {
        onChange([]);
      }
    } else if (isJson) {
      if (newValueJson != null && newValueJson.value !== "") {
        onChange({ ...input.value, [jsonField]: newValueJson.value });
      } else {
        onChange({ ...input.value, [jsonField]: "" });
      }
    } else {
      if (newValueJson != null && newValueJson.value !== "") {
        onChange(newValueJson.value);
      } else {
        onChange("");
      }
    }
  }

  return (
    <FormControl className={clsx(classes.root, classname)} error={touched && invalid} {...custom}>
      {label && <InputLabel className={aligntoLabel ? classes.labelAlignCheckbox : undefined} shrink id={name + "-label"}>
        {label}
      </InputLabel>}
      <Select
        labelId={name + "-label"}
        id={name || null}
        {...input}
        value={val}
        onChange={handleChange}
        onBlur={e => onBlur(undefined)}
        displayEmpty
        className={aligntoLabel ? classes.selectAlignCheckbox : undefined}
      >
        {ranges.map(option => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </Select>
      <FormHelperText>{touched && error}</FormHelperText>
    </FormControl>
  );
}

export default MuiSelectArrayOutput;
