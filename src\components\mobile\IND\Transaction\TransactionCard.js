import React from "react";
import PropTypes from "prop-types";
import { withStyles, createStyles } from "@material-ui/styles";
import { numberComma, convertCurrency, paresFloorUnit } from "../../../../helper/generalHelper";
import VaryingVal from "../../../common/VaryingVal";
import { FormattedMessage, injectIntl } from "react-intl";

const styles = createStyles((theme) => ({
  root: {
    borderRadius: 4,
    padding: 8,
    backgroundColor: "#FFF",
  },
  sourceRow: {
    fontSize: ".875em",
    color: "#777",
    marginBottom: ".5vh",
    display: "flex",
    justifyContent: "space-between",
  },
  unitRow: {
    fontSize: "1.125em",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  },
  content: {
    fontSize: "1.125em",
    marginBottom: ".5vh",
  },
  pricetype: {
    fontSize: "1.125em",
    fontWeight: "700",
    color: "#000",
  },
  priceRow: {
    fontSize: "1.125em",
    textAlign: "right",
  },
  val: {
    display: "inline-flex",
  },
  alignToright: {
    textAlign: "right",
  },
  cardPriceRow: {
    padding: "0 4px",
    borderRadius: 4,
    backgroundColor: "rgba(140, 190, 190, 0.2)",
  },
  cardRentRow: {
    padding: "0 4px",
    borderRadius: 4,
    backgroundColor: "rgba(200, 170, 170, 0.2)",
  },
}));

class TransactionCard extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
    className: PropTypes.string,
  };

  render() {
    const { classes, detail, className, intl } = this.props;
    const sourceLangKey = intl.locale === "zh" ? "zh" : "en";

    const isStockDetail = this.props.isStockDetail
      ? this.props.isStockDetail
      : false;

    const date = detail.date + " " || "";
    const source =
      detail.data_source && detail.data_source[sourceLangKey]
        ? detail.data_source[sourceLangKey]
        : "";
    const unit = detail.unit || "---";
    const floor = detail.floor || "---";

    const building = detail.building ? detail.building : null;
    const buildingnameEn = building && building.en ? building.en : "---";
    const buildingnameZh = building && building.zh ? building.zh : "---";
    const district =
      detail.district && detail.district.en ? ", " + detail.district.en : "";
    let area =
      detail.area && detail.area.value ? numberComma(detail.area.value) : null;
    area = area
      ? intl.locale == "zh"
        ? (area = intl.formatMessage({ id: "search.card.gross" }) + " " + area)
        : (area = area + " " + intl.formatMessage({ id: "search.card.gross" }))
      : "---";

    const priceType =
      detail.type === "Sale"
        ? intl.formatMessage({
            id: "search.status.sold",
          })
        : intl.formatMessage({
            id: "search.status.leased",
          });
    const priceTotal = detail.sell
      ? " " + "$" + numberComma(detail.sell)
      : "";
    const price =
      detail.type === "Sale"
        ? detail.ft_sell
          ? numberComma(detail.ft_sell)
          : ""
        : detail.tenancy && detail.tenancy.ft_rent
        ? numberComma(detail.tenancy.ft_rent, 2)
        : "";
    const priceText = price ? " @" + price : "";

    return (
      <div className={`${classes.root} ${className}`}>
        <div className={classes.sourceRow}>
          <span>
            {date}
            {source}
          </span>
          <div className={classes.pricetype}>{priceType}</div>
        </div>
        <div className={classes.unitRow}>
          <div>
            {paresFloorUnit(floor, unit, intl)}
          </div>
          {isStockDetail && (
            <span className={`${classes.alignToright}`}>{area}</span>
          )}
        </div>

        <div className={classes.content}>
          {buildingnameEn}
          {district}
          <div>{buildingnameZh}</div>
        </div>

        {isStockDetail ? null : (
          <div className={`${classes.content} ${classes.alignToright}`}>
            {area}
          </div>
        )}

        <div className={classes.priceRow}>
          {priceTotal !== "" && (
            <VaryingVal
              className={`${classes.val} ${
                isStockDetail && classes.cardPriceRow
              }`}
              label={intl.formatMessage({
                id: "search.common.price",
              })}
            >
              <span>
                {priceTotal} {priceText}
              </span>
            </VaryingVal>
          )}

          {priceTotal == "" && (
            <VaryingVal
              className={`${classes.val} ${
                isStockDetail && classes.cardRentRow
              }`}
              label={intl.formatMessage({
                id: "search.common.rent",
              })}
            >
              <span>{priceText}</span>
            </VaryingVal>
          )}
        </div>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(TransactionCard));
