import React, { useMemo } from "react";
import { Grid } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import { injectIntl } from "react-intl";
import PropTypes from "prop-types";
import { Field } from "redux-form";

import SelectMultiInput from "@/components/common/SelectMultiInput";
import TextInput from "@/components/common/TextInput";
import UnderlineSingleSelect from "@/components/common/UnderlineSingleSelect";
import { FILM_TYPES, MEMORIAL_KEY } from "@/constants/landsearch";

const useStyles = makeStyles({
  section: {
    margin: "5px 0",
  },
  multiInputContainer: {
    marginLeft: "-8px !important",
    marginRight: "-8px !important",
  },
  remarks: {
    color: "#555555",
    fontSize: "13px",
  },
});
function Condition({ intl }) {
  const classes = useStyles();

  const memorialKeys = useMemo(
    () =>
      MEMORIAL_KEY.map((item) => ({
        value: item.value,
        label: item[intl.locale],
      })),
    [intl.locale],
  );
  const filmTypes = useMemo(
    () =>
      FILM_TYPES.map((item) => ({
        value: item.value,
        label: item[intl.locale],
      })),
    [intl.locale],
  );

  return (
    <Grid item container direction="column" className={classes.section}>
      <SelectMultiInput
        label={intl.formatMessage({ id: "stock.applySearch.conditionalNo" })}
        selectName="conditionKey"
        selectXs={5}
        selectPosition="left"
        options={memorialKeys}
        spacing={2}
        InputComponent={TextInput}
        inputProps={{
          name: "conditionValue",
        }}
        required
      />

      <Grid item className={classes.section}>
        <Field
          name="filmType"
          label={intl.formatMessage({ id: "stock.applySearch.filmType" })}
          component={UnderlineSingleSelect}
          type="select-multiple"
          options={filmTypes}
        />
      </Grid>
    </Grid>
  );
}

Condition.propTypes = {
  intl: PropTypes.object.isRequired,
};

export default injectIntl(Condition);
