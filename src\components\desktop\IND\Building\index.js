import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { FormattedMessage, injectIntl } from "react-intl";
import { withStyles } from "@material-ui/styles";
import AppBar from "@material-ui/core/AppBar";
import Tabs from "@material-ui/core/Tabs";
import Tab from "@material-ui/core/Tab";
import Typography from "@material-ui/core/Typography";
import Cookies from "universal-cookie";

import Media from "./Media";
import BuildingDetailGeneral from "./General";
import { MuiThemeProvider, createMuiTheme } from "@material-ui/core/styles";

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <Typography
      component="div"
      role="tabpanel"
      hidden={value !== index}
      id={`full-width-tabpanel-${index}`}
      aria-labelledby={`full-width-tab-${index}`}
      {...other}
    >
      <div>{children}</div>
    </Typography>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.any.isRequired,
  value: PropTypes.any.isRequired
};

function a11yProps(index) {
  return {
    id: `full-width-tab-${index}`,
    "aria-controls": `full-width-tabpanel-${index}`
  };
}

const styles = theme => ({
  root: {
    flexGrow: 1,
    height: 250
  },
  bar: {
    // height: 57,
    boxShadow: "none",
    backgroundColor: "rgba(0, 0, 0, .6)",
    position: "relative",
    top: 0,
    left: 0,
    zIndex: 900
  },
  tabAlwaysActive: {
    minHeight: 57,
    paddingTop: 6,
    backgroundColor: "#33CCCC"
  },
  uploadIcon: {
    width: 42,
    height: 30
  },
  uploadIconSvg: {
    maxWidth: 42,
    maxHeight: 30
  },
  buildingId: {
    display: "none"
  }
});

const theme = createMuiTheme({
  overrides: {
    MuiTabs: {
      root: {
        minHeight: 36
      },
      indicator: {
        height: 0,
        backgroundColor: "#33CCCC"
      }
    },
    MuiTab: {
      root: {
        minHeight: 36,
        fontSize: "0.875em",
        lineHeight: 1.2,
        textTransform: "none",
        "&$selected": {
          backgroundColor: "#FFF",
          color: "rgba(0, 0, 0, .6)"
        }
      },
      textColorInherit: {
        color: "#FFF",
        opacity: 1
      },
      wrapper: {
        "&& > *:first-child": {
          marginBottom: 0
        }
      }
    }
  }
});

class Building extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      value: 0
    };
  }

  componentDidUpdate() {
    const { detail, headerRef, intl } = this.props;
    const id = detail.unicorn && detail.unicorn.id ? detail.unicorn.id : null;

    if (id)
      headerRef.current.changeTitle(
        intl.formatMessage({ id: "building.header.id" }) + id
      );
  }

  handleChange = (event, value) => {
    this.setState({ value });
  };

  render() {
    const { classes, detail } = this.props;
    const { value } = this.state;
    const id = detail.unicorn && detail.unicorn.id ? detail.unicorn.id : null;
    const cookies = new Cookies();
    cookies.set("selectedBuilding", id);

    return (
      <div className={classes.root}>
        <MuiThemeProvider theme={theme}>
          <AppBar position="static" className={classes.bar}>
            <Tabs
              value={value}
              onChange={this.handleChange}
              variant="fullWidth"
              aria-label="full width tabs example"
              centered
            >
              <Tab
                label={<FormattedMessage id="stock.general" />}
                {...a11yProps(0)}
              />
              <Tab
                label={<FormattedMessage id="stock.media" />}
                {...a11yProps(1)}
              />

              {/*<Tab label="Upload Media" {...a11yProps(2)}*/}
              {/*     icon={<StandardSvg className={classes.uploadIcon} imgClass={classes.uploadIconSvg} src={uploadSvg} />}*/}
              {/*     className={classes.tabAlwaysActive} />*/}
            </Tabs>
          </AppBar>
        </MuiThemeProvider>
        <TabPanel value={value} index={0}>
          <BuildingDetailGeneral />
        </TabPanel>
        <TabPanel value={value} index={1}>
          <Media />
        </TabPanel>
        <div className={classes.buildingId} id="buildingId">
          {id}
        </div>
      </div>
    );
  }
}

const mapStateToProps = state => ({
  detail: state.building.detail ? state.building.detail : {}
});

export default connect(mapStateToProps)(
  withStyles(styles)(injectIntl(Building))
);
