import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import DetailTabPanelFrame from "../../../../common/DetailTabPanelFrame";
import BuildingMain from "./BuildingMain";
import BasicInfo from "./BasicInfo";
import SubStreet from "./SubStreet";

const styles = (theme) => ({});

class General extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    listing: PropTypes.bool,
    detail: PropTypes.object,
  };

  constructor(props) {
    super(props);
  }

  render() {
    const { classes, detail, listed, listing } = this.props;

    const hasData = Object.keys(detail).length > 0;

    return (
      <DetailTabPanelFrame
        hasData={hasData}
        listing={listing}
        listed={listed}
        notFoundText="Building not found"
      >
        <BuildingMain detail={detail} />
        <BasicInfo detail={detail} />
        <SubStreet detail={detail} />
      </DetailTabPanelFrame>
    );
  }
}

const mapStateToProps = (state) => ({
  detail: state.building.detail ? state.building.detail : {},
  listed: state.building.listedDetail ? state.building.listedDetail : false,
  listing: state.building.listingDetail ? state.building.listingDetail : false,
});

export default connect(mapStateToProps)(withStyles(styles)(General));
