import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import { injectIntl } from "react-intl";
import ContactInfoBox from "./ContactInfoBox";

const styles = theme => ({
  root: {
    padding: "1vh 0"
  },
  notFound: {}
});

class ContactInfo extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;
    const titleLangKey = intl.locale === "zh" ? "titleZh" : "title";

    const mongoId = detail._id ? detail._id : null;
    const stockId =
      detail.unicorn && Number.isInteger(detail.unicorn.id)
        ? detail.unicorn.id
        : null;

    const company =
      detail.contact && detail.contact.holdingCompany
        ? detail.contact.holdingCompany
        : null;
    const companyId =
      detail.contact && detail.contact.holdingCompanyId
        ? detail.contact.holdingCompanyId
        : null;
    const people =
      detail.contact && detail.contact.person ? detail.contact.person : [];
    let contactInfo = [];
    for (let i = 0; i < people.length; i++) {
      if (!people[i]) continue;
      let contactName = people[i].name;
      let contactTitle = people[i][titleLangKey];
      let phones = people[i].phones || [];

      contactInfo.push({
        company,
        companyId,
        contactName,
        contactTitle,
        phones,
        mongoId,
        stockId,
      });
    }
    if (contactInfo.length === 0) contactInfo = [{}];

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.contact"
          })}
        >
          {contactInfo.map((v, i) => (
            <ContactInfoBox
              {...v}
              key={i}
            />
          ))}
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(ContactInfo));
