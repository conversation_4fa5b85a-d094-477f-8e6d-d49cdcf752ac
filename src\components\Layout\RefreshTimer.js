import React, { useState, useEffect } from "react";
import { connect } from "react-redux";
import { injectIntl } from "react-intl";
import _ from "lodash";
import { fetchRefreshToken } from "@/actions/auth";
import { refreshBeforeTimeoutInterval } from "@/config";

const RefreshTimer = ((props) => {
  const { fetchRefreshToken } = props;

  const [isMounted, setIsMounted] = useState(true);
  const [refreshAt, setRefreshAt] = useState(null);

  useEffect(() => {
    if (isMounted && typeof window !== 'undefined') {
      setRefreshAt(localStorage.getItem("casAccessTokenExpiresIn"));
    }
    return () => {
      setIsMounted(false);
    }
  }, [isMounted]);

  // Monitor localStorage changes for casAccessTokenExpiresIn
  useEffect(() => {
    const handleStorageChange = (event) => {
      setRefreshAt(localStorage.getItem("casAccessTokenExpiresIn"));
    };
    window.addEventListener("storage", handleStorageChange);
    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  useEffect(() => {
    if (!_.isNull(refreshAt)) {
      const timeout = parseInt(refreshAt) - refreshBeforeTimeoutInterval -  new Date();
      const refreshToken = localStorage.getItem("casRefreshToken");
      if (timeout < 0) fetchRefreshToken(refreshToken);
      const timeoutId = window.setTimeout(() => {
        fetchRefreshToken(refreshToken);
      }, timeout);
      return () => {
        window.clearTimeout(timeoutId);
      };
    }
  }, [refreshAt]);

  return null;
});

const mapStateToProps = (state) => {
  return {};
};

const mapDispatchToProps = (dispatch) => {
  return {
    fetchRefreshToken: (...args) => dispatch(fetchRefreshToken(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)((injectIntl(RefreshTimer)));
