/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-2016 Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import {
    GraphQLSchema as Schema,
    GraphQLObjectType as ObjectType,
  } from 'graphql';
  import MediumType from './types/MediumType';
  import VideoType from './types/VideoType';
  import PhotoType from './types/PhotoType';
  import PanoramaPhotoType from './types/PanoramaPhotoType';
  import DocumentType from './types/DocumentType';
  import VirtualTourType from './types/VirtualTourType';
  
  import { media, medium } from './queries/medium';
  import { tags } from './queries/tag';
  import { stock, stocks } from './queries/stock';
  import { building, buildings } from './queries/building';
  import buildingStock from './queries/buildingStock';
  import people from './queries/people';
  import branches from './queries/branches';
  
  import mutationTagMedium from './mutations/tagMedium';
  import mutationUntagMedia from './mutations/untagMedia';
  import mutationCreateMedium from './mutations/createMedium';
  import mutationUpdateMedium from './mutations/updateMedium';
  import mutationRemoveMedium from './mutations/removeMedium';
  import mutationScheduleProcessImages from './mutations/scheduleProcessImages';
  
  const schema = new Schema({
    types: [
      MediumType,
      VideoType,
      PhotoType,
      PanoramaPhotoType,
      DocumentType,
      VirtualTourType,
    ],
    query: new ObjectType({
      name: 'Query',
      fields: {
        medium,
        media,
        tags,
        stock,
        stocks,
        building,
        buildings,
        buildingStock,
        people,
        branches,
      },
    }),
    mutation: new ObjectType({
      name: 'Mutation',
      fields: {
        tagMedium: mutationTagMedium,
        untagMedia: mutationUntagMedia,
        createMedium: mutationCreateMedium,
        updateMedium: mutationUpdateMedium,
        removeMedium: mutationRemoveMedium,
        scheduleProcessImages: mutationScheduleProcessImages,
      },
    }),
  });
  
  export default schema;
  