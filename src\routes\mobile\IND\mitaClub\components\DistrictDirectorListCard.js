import React from "react";
import { Box, makeStyles, Paper } from "@material-ui/core";
import { convertCurrency } from "../../../../../helper/generalHelper";

const useDataTableSectionStyles = makeStyles((theme) => ({
  "@global": {
    "tr, td": {
      wordBreak: "break-all",
      whiteSpace: "normal",
    },
  },
  container: {
    // display: "flex",
    // alignItems: "center",
    // justifyContent: "space-between",
    height: "32px",
    // gap: "16px",
    // padding: "0 16px",
  },
  englishName: {
    // width: "30%",
    flex: 3,
    // "width": "86px",
    "height": "18px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "14px",
    "lineHeight": "18px",
    "letterSpacing": "0.02em",
    "color": "#222222",
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
    width: "100px",
    paddingLeft: 16,
    paddingRight: 8,
  },
  accumulatedSalesAmount: {
    flex: 2.2,
    // "width": "64px",
    "height": "18px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "14px",
    "lineHeight": "18px",
    "textAlign": "right",
    "letterSpacing": "0.02em",
    "color": "#222222",
    width: "85px",
    paddingLeft: 8,
    paddingRight: 8,
  },
  accumulatedSalesCase: {
    flex: 1.7,
    // "width": "48px",
    "height": "18px",
    "fontFamily": "'Microsoft JhengHei UI'",
    "fontStyle": "normal",
    "fontWeight": 700,
    "fontSize": "14px",
    "lineHeight": "18px",
    "textAlign": "right",
    "letterSpacing": "0.02em",
    "color": "#222222",
    width: "50px",
    paddingLeft: 8,
    paddingRight: 16,
  },
}));

/**
 * @typedef {Object} DataTableSectionProps
 * @property {boolean} isHeader
 * @property {string=} EnglishName
 * @property {number | string} AccumulatedSalesAmount
 * @property {number | string} AccumulatedSalesCase
 * @property {React.CSSProperties=} style
 * 
 * @param {DataTableSectionProps} props 
 */
const DataTableSection = (props) => {
  const {
    isHeader = false,
    EnglishName = "",
    style = {},
  } = props;
  const classes = useDataTableSectionStyles();

  const AccumulatedSalesAmount = isHeader ? props.AccumulatedSalesAmount : (+props.AccumulatedSalesAmount || 0);
  const AccumulatedSalesCase = isHeader ? props.AccumulatedSalesCase : (+props.AccumulatedSalesCase || 0);

  if (isHeader) {
    return (
      <tr style={style} className={classes.container}>
        <td className={classes.englishName} style={{ fontSize: 15 }}>{EnglishName}</td>
        <td className={classes.accumulatedSalesAmount} style={{ fontSize: 15 }}>{AccumulatedSalesAmount}</td>
        <td className={classes.accumulatedSalesCase} style={{ fontSize: 15 }}>{AccumulatedSalesCase}</td>
      </tr>
    );
  }
  return (
    <tr style={style} className={classes.container}>
      <td className={classes.englishName}>{EnglishName}</td>
      <td className={classes.accumulatedSalesAmount}>{`$${convertCurrency(AccumulatedSalesAmount)}`}</td>
      <td className={classes.accumulatedSalesCase}>{`${AccumulatedSalesCase}單`}</td>
    </tr>
  );
};

/**
 * @param {object} props
 * @param {Array<any>} props.districtDirectorList 
 * @returns 
 */
const DistrictDirectorListCard = (props) => {
  return (
    <Box sx={{ display: "flex", flexDirection: "column" }}>
      <Paper elevation={3}>
        <div
          style={{
            "padding": "16px 0",
            "flex": "none",
            overflowX: "auto",
          }}
        >
          <table style={{ width: "100%", minWidth: "235px", borderCollapse: "collapse", tableLayout: "fixed" }}>
            <thead>
              <DataTableSection
                isHeader={true}
                EnglishName="區董"
                AccumulatedSalesAmount="團隊業績"
                AccumulatedSalesCase="總單數"
                style={{ backgroundColor: "#F8F8F8" }}
              />
            </thead>
            <tbody>
              {props.districtDirectorList.map((data, index) => (
                <DataTableSection
                  key={`DataTableSection-row-${data.EnglishName || index}`}
                  style={index % 2 === 1 ? { backgroundColor: "#F8F8F8" } : {}}
                  {...data}
                />
              ))}
            </tbody>
          </table>
        </div>
      </Paper>
    </Box>
  );
};

export default DistrictDirectorListCard;
