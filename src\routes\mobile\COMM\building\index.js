/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import Layout from "../../../../components/Layout";

const title = "Building Detail";

async function action({ store, params }) {
  const { auth } = store.getState();
  if (!auth.user) {
    return { redirect: "/login" };
  } else if (auth.user.status == "false") {
    return { redirect: "/login" };
  }

  const Building = await require.ensure(
    [],
    require => require("./Building").default,
    "building"
  );

  let headerRef = React.createRef();

  return {
    chunks: ["building"],
    title,
    component: (
      <Layout headerRef={headerRef} backToListStep={2}>
        <Building buildingId={params} headerRef={headerRef} />
      </Layout>
    )
  };
}

export default action;
