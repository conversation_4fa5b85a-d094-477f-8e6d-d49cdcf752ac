import React from "react";
import Layout from "../../../../components/Layout";
import { FormattedMessage } from "react-intl";

const title = "My Favorite";

async function action({ store, params, query }) {
  const { auth } = store.getState();
  if (!auth.user) {
    return { redirect: "/login" };
  } else if (auth.user.authorized == false) {
    return { redirect: "/login" };
  }

  const MyFavoriteStocklist = await require.ensure(
    [],
    require => require("./MyFavoriteStocklist").default,
    "myFavorite"
  );

  return {
    chunks: ["myFavorite"],
    title,
    component: (
      <Layout
        path="result"
        header={<FormattedMessage id="home.myfavorite" />}
        isAdvanced={false}
      >
        <MyFavoriteStocklist />
      </Layout>
    )
  };
}

export default action;
