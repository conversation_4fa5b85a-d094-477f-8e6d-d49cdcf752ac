import fetch from "node-fetch";
import config from "../config";

export const addMarkStock = async (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    const body = {
      emp_id: req.body.emp_id,
      stockid: req.body.stockid,
      mongoid: req.body.mongoid,
      sbu: config.sbu,
    };

    try {
      const resp = await fetch(`${config.api.icimsgeneral}/addMarkStock`, {
        method: "POST",
        body: JSON.stringify(body),
        headers: {
          "content-type": "application/json",
        },
      });
      const data = await resp.json();
      res.send(data);
    } catch (e) {
      console.log(e);
      return {};
    }
  }
};

export const listMarkStock = async (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    const body = {
      emp_id: req.body.emp_id,
      sbu: config.sbu,
    };

    try {
      const resp = await fetch(`${config.api.icimsgeneral}/listMarkStock`, {
        method: "POST",
        body: JSON.stringify(body),
        headers: {
          "content-type": "application/json",
        },
      });
      const data = await resp.json();
      res.send(data);
    } catch (e) {
      console.log(e);
      return {};
    }
  }
};

export const removeMarkStock = async (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    const body = {
      emp_id: req.body.emp_id,
      sbu: config.sbu,
    };

    try {
      const resp = await fetch(`${config.api.icimsgeneral}/removeMarkStock`, {
        method: "POST",
        body: JSON.stringify(body),
        headers: {
          "content-type": "application/json",
        },
      });
      const data = await resp.json();
      res.send(data);
    } catch (e) {
      console.log(e);
      return {};
    }
  }
};
