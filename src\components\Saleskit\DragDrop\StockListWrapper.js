import React, { useState, useEffect, memo } from "react";
import PropTypes from "prop-types";
import _ from "lodash";
import { connect } from "react-redux";
import { injectIntl } from "react-intl";
import { DragDropContext, Droppable } from "react-beautiful-dnd";

import StockListItem from "./StockListItem";
import { getFullAddress, getStockFloorType, langKey, setTitleColor } from "../helpers";
import { changeCurrentStock } from "../../../actions/stock";
import { sbu } from "../../../config";

function StockListWrapper({
  detail,
  currentStock,
  selectStock,
  stockMedia,
  buildingMedia,
  streetMedia,
  formState,
  FormComponent,
  mode,
  changeFieldValue,
  ordering,
  handleOrderChange,
  initialValues,
  intl,
}) {
  const { stocks, order } = formState;
  const [expanded, setExpanded] = useState(order.length === 1 ? order[0] : "");

  const handleExpand = (id) => (event, isExpanded) => {
    if (ordering) return;
    setExpanded(isExpanded ? id : "");
    selectStock(id);
  };

  const handleSelect = (id) => () => {
    if (ordering) return;
    setExpanded("");
    selectStock(id);
  };

  const parseStockAddress = ({ unit, floor, building }) => {
    let addressStr = "";
    const nameZhEn = langKey(intl.locale, "name");
    if (intl.locale === "en") {
      addressStr = `${addressStr}${
        unit ? `${unit === "WF" ? "" : "Unit "}${unit}, ` : ""
      }`;
      addressStr = `${addressStr}${
        floor
          ? `${floor}${
              !floor.includes("/F") && floor !== "W/B" && floor !== "WB"
                ? "/F"
                : ""
            }${building && ","} `
          : ""
      }`;
      addressStr = `${addressStr}${
        building ? `${_.get(building, nameZhEn, "")}` : ""
      }`;
    } else {
      addressStr = `${addressStr}${
        building
          ? `${_.get(building, `district.${nameZhEn}`, "")} ${_.get(
              building,
              nameZhEn,
              "",
            )} `
          : ""
      }`;

      if (floor && floor.trim() !== "") {
        let floorStr = "";
        if (floor.includes("/F")) {
          floorStr = floor.replace("/F", "樓");
        } else if (floor === "W/B" || floor === "WB") {
          floorStr = "全幢";
        } else {
          floorStr = `${floor}樓`;
        }
        addressStr = `${addressStr}${floorStr}`;
      }

      if (unit && unit.trim() !== "") {
        let unitStr = "";
        if (unit === "WF") {
          unitStr = "全層";
        } else {
          unitStr = `${unit}室`;
        }
        addressStr = `${addressStr}${unitStr}`;
      }
    }
    return addressStr;
  };

  const handleValueChange = (id) => (field, value) =>
    changeFieldValue(`stocks.${id}.${field}`, value);

  const onDragEnd = (result) => {
    if (!result.destination) return;

    const srcIdx = result.source.index;
    const destIdx = result.destination.index;

    if (destIdx === srcIdx) return;
    handleOrderChange(srcIdx, destIdx);
  };

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId="stockList">
        {(droppableProvided) => (
          <div
            ref={droppableProvided.innerRef}
            {...droppableProvided.droppableProps}
          >
            {order.map((id, idx) => {
              const stockForm = _.get(stocks, id);
              const stock = _.find(detail, (s) => s._id === id);
              const stockUnicornId = _.get(stock, "unicorn.id");
              const buildingUnicornId = _.get(stock, "building.unicorn.id");
              const streetUnicornId = _.get(stock, "street.street.unicorn.id");

              const stockMediaData = stockUnicornId
                ? _.get(
                    _.find(
                      stockMedia,
                      (m) => m.id === stockUnicornId.toString(),
                    ),
                    "data",
                    {},
                  )
                : {};
              const buildingMediaData = buildingUnicornId
                ? _.get(
                    _.find(
                      buildingMedia,
                      (m) => m.id === buildingUnicornId.toString(),
                    ),
                    "data",
                    {},
                  )
                : {};
              const streetMediaData = streetUnicornId
                ? _.get(
                    _.find(
                      streetMedia,
                      (m) => m.id === streetUnicornId.toString(),
                    ),
                    "data",
                    {},
                  )
                : {};
              const floorNum = parseInt(_.get(stock, "floor"), 10);
              // const floorType = !_.isNaN(floorNum)
              //   ? "Actual Floor"
              //   : getStockFloorType(
              //       _.get(stock, "building.floors") || [],
              //       floorNum,
              //     );
              const floorType = "Actual Floor";

              return (
                <StockListItem
                  key={id}
                  stockId={id}
                  idx={idx}
                  expanded={expanded === id}
                  handleExpand={handleExpand(id)}
                  // title={parseStockAddress(stock)}
                  title={sbu === "SHOPS" ? getFullAddress(intl, stock, floorType, undefined, undefined, true) : getFullAddress(intl, stock, floorType)}
                  titleColor={setTitleColor(stock)}
                  show={_.get(stockForm, "stock.show")}
                  changeFieldValue={handleValueChange(id)}
                  selected={currentStock === id}
                  handleSelect={handleSelect(id)}
                  ordering={ordering}
                >
                  <FormComponent
                    type={_.get(formState, "general.type")}
                    formState={stockForm}
                    changeFieldValue={changeFieldValue}
                    mediaData={{
                      stock: stockMediaData,
                      building: buildingMediaData,
                      street: streetMediaData,
                    }}
                    stockId={id}
                    isListProposal
                    mode={mode}
                    stockDetail={idx === 0 ? stock : null}
                    initialValues={initialValues}
                    defaultExpand
                  />
                </StockListItem>
              );
            })}
            {droppableProvided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
}

StockListWrapper.propTypes = {
  detail: PropTypes.array.isRequired,
  currentStock: PropTypes.string.isRequired,
  stockMedia: PropTypes.array.isRequired,
  buildingMedia: PropTypes.array.isRequired,
  streetMedia: PropTypes.array.isRequired,
  formState: PropTypes.object.isRequired,
  FormComponent: PropTypes.func.isRequired,
  mode: PropTypes.string.isRequired,
  changeFieldValue: PropTypes.func.isRequired,
  selectStock: PropTypes.func.isRequired,
  ordering: PropTypes.bool.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  detail: _.get(state, "stock.detail", []),
  currentStock: _.get(state, "stock.currentDetail", ""),
  stockMedia: _.get(state, "stock.media", []),
  buildingMedia: _.get(state, "building.media", []),
  streetMedia: _.get(state, "street.media", []),
});

const mapDispatchToProps = (dispatch) => ({
  selectStock: (id) => dispatch(changeCurrentStock(id)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(memo(StockListWrapper)));
