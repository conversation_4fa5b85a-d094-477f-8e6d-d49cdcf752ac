export const CREATE_LIST_PROPOSAL_QUERY = `
    mutation($listProposal: CreateListProposalArguments!, $bypassCode: String!) {
        createListProposal(listProposal: $listProposal, bypassCode: $bypassCode)
    } 
`;

export const REMOVE_LIST_PROPOSAL_QUERY = `
  mutation($hash: String!, $salesmanId: String!, $bypassCode: String!) {
    removeListProposal(hash: $hash, salesmanId: $salesmanId, bypassCode: $bypassCode)
  }
`;
