import Cookies from "universal-cookie";
import _ from "lodash";
import {
  LIST_STOCK_START,
  LIST_STOCK_SUCCESS,
  LIST_STOCK_ERROR,
  LIST_STOCKMEDIA_START,
  LIST_STOCKMEDIA_SUCCESS,
  LIST_STOCKMEDIA_ERROR,
  LIST_M<PERSON>FAVORITE_SUCCESS,
  LIST_MYFAVORITE_ERROR,
  TOGGLE_MYFAVORITE_SUCCESS,
  TOGG<PERSON>_MYFAVORITE_ERROR,
  LIST_LANDSEARCH_SUCCESS,
  LIST_LANDSEARCH_ERROR,
  LIST_LANDSEARCH_PDF_SUCCESS,
  LIST_LANDSEARCH_PDF_ERROR,
  <PERSON><PERSON><PERSON>_STOCK,
  <PERSON><PERSON><PERSON>_CURRENT_STOCK,
  QUERY_UNIT_VIEWS_SUCCESS,
  QUERY_BUSINESS_SUCCESS,
  QUERY_UNIT_VIEWS_ERROR,
  QUERY_DECORATIONS_SUCCESS,
  QUERY_DECORATIONS_ERROR,
  QUERY_POSSESSIONS_SUCCESS,
  QUERY_POSSESSIONS_ERROR,
  QUERY_CURRENT_STATES_SUCCESS,
  QUERY_CURRENT_STATES_ERROR,
  LIST_MARK_STOCK_SUCCESS,
  LIST_MARK_STOCK_ERROR,
  TOGGLE_MARK_STOCK_SUCCESS,
  TOGGLE_MARK_STOCK_ERROR,
  REMOVE_MARK_STOCK_SUCCESS,
  REMOVE_MARK_STOCK_ERROR,
  UPDATE_STOCK_MEDIA_BY_INDEX,
  UPDATE_STOCK_MEDIA_LIST,
  LIST_MARK_WWW_QUERY_SUCCESS,
  LIST_MARK_WWW_QUERY_ERROR,
} from "../constants/stock";

const cookies = new Cookies();
const initialState = {
  listed: false,
  listedMedia: false,
  valid: false,
  selectedStock: cookies.get("selectedStock"),

  unitViews: [],
  businessOptions: [],
  decorations: [],
  possessions: [],
  currentStates: [],

  markedWWW:null,
  markedWWWError:null,
};

export default function stock(state = initialState, action) {
  switch (action.type) {
    case LIST_STOCK_START:
      return {
        ...state,
        listing: true,
        listed: false,
        selectedStock: action.payload,
        detail: [],
        media: [],
      };
    case LIST_STOCK_SUCCESS:
      return {
        ...state,
        listing: false,
        listed: true,
        detail: action.payload.data.data.stocks
          ? action.payload.data.data.stocks
          : null,
        currentDetail: _.get(action.payload.data.data.stocks, "[0]._id", ""),
      };
    case LIST_STOCK_ERROR:
      return {
        ...state,
        listing: false,
        listed: false,
        error: action.payload.error,
      };

    case LIST_STOCKMEDIA_START:
      return {
        ...state,
        listingMedia: true,
        listedMedia: false,
      };
    case LIST_STOCKMEDIA_SUCCESS:
      return {
        ...state,
        listingMedia: false,
        listedMedia: true,
        media: action.payload.data,
      };
    case LIST_STOCKMEDIA_ERROR:
      return {
        ...state,
        listingMedia: false,
        listedMedia: false,
        error: action.payload.error,
      };
    case UPDATE_STOCK_MEDIA_BY_INDEX:
      const { index, mediaIndex, mediaType, replace = false, media = {} } = action.payload;
      if (index === -1 || mediaIndex === -1) {
        return state;
      }
      if (state.media?.[index]?.data?.[mediaType]?.[mediaIndex]) {
        const newMedia = JSON.parse(JSON.stringify(state.media));
        const newMedias = newMedia[index].data[mediaType] || [];

        newMedias[mediaIndex] = replace ? media : { ...newMedias[mediaIndex], ...media };
        return {
          ...state,
          media: newMedia,
        };
      }
      return state;
    case CLEAR_STOCK:
      return {
        ...state,
        detail: [],
        currentDetail: "",
        listing: false,
        listed: false,
        listingMedia: false,
        listedMedia: false,
        selectedStock: null,
        media: [],
        error: null,
      };

    case LIST_MARK_STOCK_SUCCESS:
      return {
        ...state,
        markStockIds: action.payload.data.markStockIds,
      };
    case LIST_MARK_STOCK_ERROR:
      return {
        ...state,
        error: action.payload.error,
      };
    case TOGGLE_MARK_STOCK_SUCCESS: {
      return {
        ...state,
        markStockIds: action.payload.data.deleted
          ? _.pull(
              state.markStockIds.slice(),
              action.payload.data.deleted.stockid,
            )
          : state.markStockIds.concat(action.payload.stockid),
      };
    }
    case REMOVE_MARK_STOCK_SUCCESS:
      return {
        ...state,
        markStockIds: [],
      };
    case REMOVE_MARK_STOCK_ERROR:
      return {
        ...state,
        error: action.payload.error,
      };
    case LIST_MYFAVORITE_SUCCESS:
      return {
        ...state,
        favoriteStockIds: action.payload.data.favoriteStockIds,
      };
    case LIST_MYFAVORITE_ERROR:
      return { ...state, error: action.payload.error };
    case TOGGLE_MYFAVORITE_SUCCESS:
      return {
        ...state,
        favoriteStockIds: action.payload.data.deleted
          ? _.pull(
              state.favoriteStockIds.slice(),
              action.payload.data.deleted.mongoid,
            )
          : state.favoriteStockIds.concat(action.payload.mongoid),
      };
    case LIST_LANDSEARCH_SUCCESS: {
      if (state.detail.length === 0) return { ...state };
      const tempDetail = state.detail;
      const stockIdx = _.findIndex(
        tempDetail,
        (stock) => stock._id === state.currentDetail,
      );
      _.set(tempDetail, `[${stockIdx}].landSearch`, action.payload.data.data);
      return {
        ...state,
        detail: tempDetail,
      };
    }
    case LIST_LANDSEARCH_ERROR:
      return { ...state, error: action.payload.error };
    case LIST_LANDSEARCH_PDF_SUCCESS: {
      const landSearchRefNo = Object.keys(action.payload.data.data)[0];
      return {
        ...state,
        landSearchPdfList: {
          ...state.landSearchPdfList,
          [landSearchRefNo]:
            _.map(action.payload.data.data[landSearchRefNo], v => v.split(
              "/msearch-api/api",
            )[1])
        },
      };
    }
    case LIST_LANDSEARCH_PDF_ERROR:
      return { ...state, error: action.payload.error };
    case TOGGLE_MARK_STOCK_ERROR:
      return {
        ...state,
        error: action.payload.error,
      };
    case TOGGLE_MYFAVORITE_ERROR:
      return { ...state, error: action.payload.error };
    case CHANGE_CURRENT_STOCK:
      return {
        ...state,
        currentDetail: action.payload.current,
      };
    case QUERY_UNIT_VIEWS_SUCCESS:
      return {
        ...state,
        unitViews: action.payload.data.unitViews,
      };
    case QUERY_BUSINESS_SUCCESS:
      return {
        ...state,
        businessOptions: action.payload.data.business,
      };
    case QUERY_UNIT_VIEWS_ERROR:
      return {
        ...state,
        error: action.payload.error,
      };
    case QUERY_DECORATIONS_SUCCESS:
      return {
        ...state,
        decorations: action.payload.data.decorations,
      };
    case QUERY_DECORATIONS_ERROR:
      return {
        ...state,
        error: action.payload.error,
      };
    case QUERY_POSSESSIONS_SUCCESS:
      return {
        ...state,
        possessions: action.payload.data.possessions,
      };
    case QUERY_POSSESSIONS_ERROR:
      return {
        ...state,
        error: action.payload.error,
      };
    case QUERY_CURRENT_STATES_SUCCESS:
      return {
        ...state,
        currentStates: action.payload.data.currentState,
      };
    case QUERY_CURRENT_STATES_ERROR:
      return {
        ...state,
        error: action.payload.error,
      };
    case UPDATE_STOCK_MEDIA_LIST:
      if (state.media && state.media.length > 0) {
        const { mediaType, mediaList } = action.payload;
        const newMedia = JSON.parse(JSON.stringify(state.media));
        
        if (newMedia[0] && newMedia[0].data) {
          newMedia[0].data = {
            ...newMedia[0].data,
            [mediaType]: mediaList
          };
          
          return {
            ...state,
            media: newMedia,
            listedMedia: true
          };
        }
      }
      return state;
    case LIST_MARK_WWW_QUERY_SUCCESS:
      return {
        ...state,
        markedWWW: action.payload,
      };
    case LIST_MARK_WWW_QUERY_ERROR:
      return {
        ...state,
        markedWWWError: action.payload,
      };
    default:
      return state;
  }
}
