import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import { injectIntl } from "react-intl";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import FieldVal from "../../../../common/FieldVal";

const styles = theme => ({
  root: {
    padding: "2vw"
  }
});

class BasicInfo extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;

    const buildingUsage = detail.buildingUsage || "---";
    const buildingGrade = detail.buildingGrade || "---";
    const isCloseToMTR =
      detail.isCloseToMTR === true
        ? "Yes"
        : detail.isCloseToMTR === false
        ? "No"
        : "---";
    const completionDate = detail.completionDate || "---";
    const managementFee =
      detail.management && detail.management.fee
        ? "$" + detail.management.fee + "/SqFt"
        : "---";
    const feeIncludeACBool = detail.management
      ? detail.management.isIncludeAirCondCharge
      : null;
    const feeIncludeAC =
      feeIncludeACBool === true
        ? "Include"
        : feeIncludeACBool === false
        ? "Exclude"
        : "---";
    const developers = detail.developers || [];
    let developer = developers.length === 0 ? "---" : "";
    developers.forEach((v, i) => {
      developer += (i > 0 ? ", " : "") + (v.nameEn || "---");
    });
    const managementCompany =
      detail.managementCompany &&
      detail.managementCompany.managementCompany &&
      detail.managementCompany.managementCompany.nameEn
        ? detail.managementCompany.managementCompany.nameEn
        : "---";
    const contactName =
      detail.managementCompany && detail.managementCompany.contactName
        ? detail.managementCompany.contactName
        : "---";
    const contactPhones =
      detail.managementCompany && detail.managementCompany.contactPhones
        ? detail.managementCompany.contactPhones
        : [];
    let phone = contactPhones.length === 0 ? "---" : "";
    contactPhones.forEach((v, i) => {
      phone += (i > 0 ? " / " : "") + v;
    });
    let generalMapping = {
      [intl.formatMessage({
        id: "stock.usage"
      })]: { value: buildingUsage, xs: 6 },
      [intl.formatMessage({
        id: "search.form.grade"
      })]: { value: buildingGrade, xs: 6 },
      [intl.formatMessage({
        id: "building.mtr"
      })]: { value: isCloseToMTR, xs: 6 },
      [intl.formatMessage({
        id: "building.competition"
      })]: { value: completionDate, xs: 6 },
      [intl.formatMessage({
        id: "stock.mgtfee"
      })]: { value: managementFee, xs: 6 },
      [intl.formatMessage({
        id: "building.accharge"
      })]: { value: feeIncludeAC, xs: 6 },
      [intl.formatMessage({
        id: "building.developer"
      })]: { value: developer, xs: 12 },
      [intl.formatMessage({
        id: "building.mgtcompany"
      })]: { value: managementCompany, xs: 12 },
      [intl.formatMessage({
        id: "stock.contact"
      })]: { value: contactName, xs: 12 },
      [intl.formatMessage({
        id: "building.phone"
      })]: { value: phone, xs: 12 }
    };

    return (
      <div className={classes.root}>
        <Grid container spacing={2}>
          {Object.keys(generalMapping).map((v, i) => (
            <Grid item xs={generalMapping[v].xs} key={v}>
              <FieldVal field={v} style={generalMapping[v].style}>
                {generalMapping[v].value}
              </FieldVal>
            </Grid>
          ))}
        </Grid>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(BasicInfo));
