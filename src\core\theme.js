import { createMuiTheme } from "@material-ui/core/styles";
import { sbu } from "../config";

let baseColor, secondbaseColor, portraitBorderColor;
switch (sbu) {
  case "COMM":
    baseColor = "rgb(43, 146, 212)";
    secondbaseColor = "rgb(32, 119, 173)";
    portraitBorderColor = "rgb(96, 190, 251)";
    break;
  case "IND":
    baseColor = "rgb(71, 51, 124)";
    secondbaseColor = "rgb(60, 34, 128)";
    portraitBorderColor = "rgb(140, 107, 214)";
    break;
  case "SHOPS":
    baseColor = "rgb(218, 78, 0)";
    secondbaseColor = "rgb(173, 61, 0)";
    portraitBorderColor = "rgb(253, 121, 0)";
    break;
  default:
    baseColor = "#2b92d4";
    secondbaseColor = "#2076AD";
    portraitBorderColor = "rgb(96, 190, 251)";
    break;
}

// Create a theme instance.
const theme = createMuiTheme({
  base: {
    backgroundColor: baseColor,
    secondbaseColor: secondbaseColor,
    portraitBorderColor: portraitBorderColor,
  },
  // overrides: {
  //   MuiInputLabel: {
  //     root: {
  //       color: "#7F7F7F",
  //       "&$focused": {
  //         color: "#000"
  //       }
  //     }
  //   },

  //   MuiOutlinedInput: {
  //     root: {
  //       "& $notchedOutline": {
  //         borderColor: "#FEE100",
  //         borderWidth: 2
  //       },
  //       "&$focused $notchedOutline": {
  //         borderColor: "#FEE100"
  //       },
  //       "&:hover $notchedOutline": {
  //         borderColor: "#FEE100"
  //       }
  //     }
  //   }
  // }

  // palette: {
  //   primary: {
  //     main: '#556cd6',
  //   },
  //   secondary: {
  //     main: '#19857b',
  //   },
  //   error: {
  //     main: red.A400,
  //   },
  //   background: {
  //     default: '#fff',
  //   },
  // },
});

export default theme;
