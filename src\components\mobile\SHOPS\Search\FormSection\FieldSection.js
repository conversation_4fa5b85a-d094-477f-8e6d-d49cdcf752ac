import React, { useState } from "react";
import _ from "lodash";
import { connect } from "react-redux";
import { Field, FieldArray, change } from "redux-form";
import {
  OutlinedInput,
  Grid,
  Collapse,
  InputAdornment,
} from "@material-ui/core";
import { injectIntl, FormattedMessage } from "react-intl";
import { withStyles } from "@material-ui/core/styles";
import DetailBoxSection from "@/components/common/DetailBoxSection";
import TextInputCombined from "@/components/common/TextInputCombined";
import ArrayPillCheckBox from "@/components/common/ArrayPillCheckBox";
import PillCheckBox from "@/components/common/PillCheckBox";
import PillButton from "@/components/common/PillButton";
import SelectFieldArrayOutput from "@/components/common/SelectFieldArrayOutput";
import ReactSelectCreatable from "@/components/common/ReactSelectCreatable";
import ChipsCheckBox from "@/components/common/ChipsCheckBox";
import AutoCompleteSelect from "@/components/common/AutoCompleteSelect";
import Search from "@/components/common/Search";
import { minValue, maxValue, number } from "@/core/formValidators";
import { listBuildings } from "@/actions/building";
import { streetSearch } from "@/actions/street";
import { listCompanies } from "@/actions/stocklist";
import { getLangKey } from "@/helper/generalHelper";
import { enableConsolidLandSearch } from "@/config";

// We can inject some CSS into the DOM.
const styles = {
  expendbutton: {
    textTransform: "none",
    display: "flex",
    marginLeft: "auto",
    color: "#33CCCC",
  },
  divider: {
    textAlign: "center",
    margin: "auto 0px",
  },
  Headertitle: {
    color: "rgba(0, 0, 0, 0.54)",
    fontSize: "1em",
    fontWeight: "400",
  },
  DetailBoxSectionContent: {
    paddingLeft: 0,
    paddingRight: 0,
    paddingBottom: "3vh",
  },
  sticky: {
    position: "-webkit-sticky" /* Safari */,
    position: "sticky",
    top: "52px",
  },
  sectionTitleBtnContainer: {
    "& > button": {
      marginLeft: "1vw",
      fontSize: 12,
    },
  },
  fieldsdivider: {
    marginTop: 8,
    marginBottom: 10,
    borderRadius: 0,
    "& input": {
      textAlign: "center",
      transform: "scale(1.6)",
    },
    "& .MuiOutlinedInput-notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23)",
      borderLeft: 0,
      borderRight: 0,
    },
    "&:hover .MuiOutlinedInput-notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23)",
    },
    "& $focused $notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23) !important",
    },
  },
  fieldMargin: {
    marginTop: 8,
    marginBottom: 10,
  },
  paddingSection: {
    paddingBottom: "3vh",
  },
};

const boolranges = [
  {
    value: "",
    label: "All",
  },
  {
    value: true,
    label: "True",
  },
  {
    value: false,
    label: "False",
  },
];

function getstatusOptions(intl) {
  const statusOptions = [
    {
      value: "Sale",
      label: intl.formatMessage({
        id: "search.status.sale",
      }),
    },
    {
      value: "Lease",
      label: intl.formatMessage({
        id: "search.status.lease",
      }),
    },
    {
      value: "Sale+Lease",
      label: intl.formatMessage({
        id: "search.status.salesandlease",
      }),
    },
    {
      value: "Leased",
      label: intl.formatMessage({
        id: "search.status.leased",
      }),
    },
    {
      value: "Sold",
      label: intl.formatMessage({
        id: "search.status.sold",
      }),
    },
    {
      value: "Search",
      label: intl.formatMessage({
        id: "search.status.se",
      }),
    },
    {
      value: "Cancel",
      label: intl.formatMessage({
        id: "search.status.cancel",
      }),
    },
    {
      value: "History",
      label: intl.formatMessage({
        id: "search.status.history",
      }),
    },
    {
      value: "Pending",
      label: intl.formatMessage({
        id: "search.status.pending",
      }),
    },
    {
      value: "Don't Call",
      label: intl.formatMessage({
        id: "search.status.dontcall",
      }),
    },
    {
      value: "CU",
      label: intl.formatMessage({
        id: "search.status.cu",
      }),
    },
  ];
  return statusOptions;
}

function getpropertyconditionOptions(intl) {
  const miscOptions = [
    {
      value: "isShoppingMallStock",
      label: intl.formatMessage({
        id: "search.form.shoppingmallstock",
      }),
    },
    {
      value: "isSingleSideStock",
      label: intl.formatMessage({
        id: "search.form.singlesidestock",
      }),
    },
    {
      value: "isFrontAndRearPortion",
      label: intl.formatMessage({
        id: "search.form.frontandrearportion",
      }),
    },
    {
      value: "isMortgagee",
      label: intl.formatMessage({
        id: "tips.stocktag.mortgagee",
      }),
    },
    {
      value: "haveSurveyorProposal",
      label: intl.formatMessage({
        id: "search.form.surveryorPP",
      }),
    },
    {
      value: "isSaleEquity",
      label: intl.formatMessage({
        id: "search.form.saleequity",
      }),
    },
    {
      value: "isHandOver",
      label: intl.formatMessage({
        id: "search.form.handover",
      }),
    },
    {
      value: "isConfirmorSales",
      label: intl.formatMessage({
        id: "search.form.confirmor",
      }),
    },
    {
      value: "wasSoleAgentForSale",
      label: intl.formatMessage({
        id: "search.form.soleagentsoleexpired",
      }),
    },
    {
      value: "wasSoleAgentForLease",
      label: intl.formatMessage({
        id: "search.form.soleagentrentexpired",
      }),
    },
    {
      value: "havePropertyAdvertisementsForSale",
      label: intl.formatMessage({
        id: "search.form.eaaBuy",
      }),
    },
    {
      value: "havePropertyAdvertisementsForLease",
      label: intl.formatMessage({
        id: "search.form.eaaRent",
      }),
    },
    {
      value: "havePropertyAdvertisementsExpire",
      label: intl.formatMessage({
        id: "search.form.eaaExpired",
      }),
    },
    {
      value: "isWithKey",
      label: intl.formatMessage({
        id: "search.form.withkey",
      }),
    },
    {
      value: "isWithoutKey",
      label: intl.formatMessage({
        id: "search.form.withoutkey",
      }),
    },
    {
      value: "pdfPP",
      label: intl.formatMessage({
        id: "search.form.pdfPP",
      }),
    },
    {
      value: "haveVR",
      label: intl.formatMessage({
        id: "search.form.vr",
      }),
    },
    {
      value: "haveStockVideo",
      label: intl.formatMessage({
        id: "search.form.haveStockVideo",
      }),
    },
    {
      value: "haveBuildingVideo",
      label: intl.formatMessage({
        id: "search.form.haveBuildingVideo",
      }),
    },
    {
      value: "havePropertyAdvertisements",
      label: intl.formatMessage({
        id: "search.form.eaa",
      }),
    },
    {
      value: "haveLandSearchDoc",
      label: intl.formatMessage({
        id: "search.form.landSearch",
      }),
    },
    {
      value: "isInWater",
      label: intl.formatMessage({
        id: "search.form.inwater",
      }),
    },
    {
      value: "isOutWater",
      label: intl.formatMessage({
        id: "search.form.outwater",
      }),
    },
    {
      value: "haveTownGas",
      label: intl.formatMessage({
        id: "search.form.towngas",
      }),
    },
  ];
  return miscOptions;
}

function getmarketableMapping(intl) {
  const marketableMapping = [
    {
      value: "isNew",
      label: intl.formatMessage({
        id: "search.new",
      }),
    },
    {
      value: "isMarketableForSale",
      label: intl.formatMessage({
        id: "search.marketable.sale",
      }),
    },
    {
      value: "isMarketableForLease",
      label: intl.formatMessage({
        id: "search.marketable.lease",
      }),
    },
    {
      value: "isSoleAgentForSale",
      label: intl.formatMessage({
        id: "search.form.soleagentsale",
      }),
    },
    {
      value: "isSoleAgentForLease",
      label: intl.formatMessage({
        id: "search.form.soleagentlease",
      }),
    },
    {
      value: "isWWW",
      label: intl.formatMessage({
        id: "stock.tag.www",
      }),
    },
  ];
  return marketableMapping;
}

function getcockloftMapping(intl) {
  const marketableMapping = [
    {
      value: "haveLegalCockloft",
      label: intl.formatMessage({
        id: "stock.cockloft.authorized",
      }),
    },
    {
      value: "haveCockloft",
      label: intl.formatMessage({
        id: "stock.cockloft.selfbuilt",
      }),
    },
    {
      value: "haveCockloftOrLegalCockloft",
      label: intl.formatMessage({
        id: "stock.cockloft.authorizedandselfbuilt",
      }),
    },
  ];
  return marketableMapping;
}

const unitM = {
  endAdornment: (
    <InputAdornment position="end">
      <span>M</span>
    </InputAdornment>
  ),
};

const unitK = {
  endAdornment: (
    <InputAdornment position="end">
      <span>K</span>
    </InputAdornment>
  ),
};

const unitFormatter = (value, unit) => {
  if (!value || value == null) return "";

  let unitValue;
  if (unit == "K") {
    unitValue = 1000;
  } else if (unit == "M") {
    unitValue = 1000000;
  } else {
    unitValue = 1;
  }
  return value / unitValue;
};

const formatMaxLength = (value, length) => {
  if (!value) {
    return value;
  }

  if (value.length <= length) {
    return `${value.slice(0, length)}`;
  }
  return `${value.slice(0, length)}`;
};

const formatUnitandMaxLength = (value, unit) => {
  if (!value) {
    return value;
  }

  value = value.replace(/[^0-9.]/g, "");

  let unitValue;
  if (unit == "K") unitValue = 1000;
  if (unit == "M") unitValue = 1000000;

  let number = value * unitValue;
  const roundnumber = number.toFixed();
  // return the value after rounding
  return `${roundnumber.toString()}`;
};

function createMinvalue(min) {
  return minValue(min, <FormattedMessage id="search.form.invalidinput" />);
}
function createMaxvalue(max) {
  return maxValue(max, <FormattedMessage id="search.form.invalidinput" />);
}
const numberValidate = number(
  <FormattedMessage id="search.form.invalidinput" />,
);
const minvaluezero = createMinvalue(0);
const maxvalue1 = createMaxvalue(200);
const maxvalue2 = createMaxvalue(999);
const maxvalue3 = createMaxvalue(9999);
const maxvalue4 = createMaxvalue(99999);
const maxvalue5 = createMaxvalue(9999999);
const maxvalue6 = createMaxvalue(99999999);
const maxvalue7 = createMaxvalue(999999999);
const maxvalue8 = createMaxvalue(9999999999);

function FieldSection(props) {
  const {
    classes,
    buildings,
    fieldhistory,
    streets,
    districts,
    possession,
    stockType,
    listBuildings,
    searchStreets,
    selectedData,
    setSelectedData,
    expanded,
    intl,
    changeForm,
    initialValues,
    companies,
    listCompanies,
    retainMarkStocks,
    onRetainMarkStocksClick,
  } = props;

  const parity = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "odd",
      label: intl.formatMessage({
        id: "search.form.odd",
      }),
    },
    {
      value: "even",
      label: intl.formatMessage({
        id: "search.form.even",
      }),
    },
  ];

  const boardstatus = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "已掛板",
      label: intl.formatMessage({
        id: "tips.stocktag.boardhanged",
      }),
    },
    {
      value: "未掛板",
      label: intl.formatMessage({
        id: "search.form.noboard",
      }),
    },
    {
      value: "不可掛板",
      label: intl.formatMessage({
        id: "search.form.canthang",
      }),
    },
    {
      value: "不適用",
      label: intl.formatMessage({
        id: "common.na",
      }),
    },
  ];

  const newsstatus = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "已貼海報",
      label: intl.formatMessage({
        id: "search.form.poster",
      }),
    },
    {
      value: "未登報紙",
      label: intl.formatMessage({
        id: "search.form.noadv",
      }),
    },
    {
      value: "不可貼海報",
      label: intl.formatMessage({
        id: "search.form.cantposter",
      }),
    },
    {
      value: "不可登報",
      label: intl.formatMessage({
        id: "search.form.cantadv",
      }),
    },
    {
      value: "不適用",
      label: intl.formatMessage({
        id: "common.na",
      }),
    },
  ];

  let priceType, rentType;

  if (initialValues.priceMinTotal || initialValues.priceMaxTotal)
    priceType = "Total";
  if (initialValues.priceMinAvg || initialValues.priceMaxAvg) priceType = "Avg";
  if (initialValues.rentMinTotal || initialValues.rentMaxTotal)
    rentType = "Avg";
  if (initialValues.rentMinAvg || initialValues.rentMaxAvg) rentType = "Avg";
  const [TogglePriceValue, setTogglePriceValue] = useState(
    priceType ? priceType : "Total",
  );
  const [ToggleRentValue, setToggleRentValue] = useState(
    rentType ? rentType : "Total",
  );

  const langkey = getLangKey(intl);
  const possessionselection = (possession || []).map((item) => ({
    value: item["nameEn"],
    label: item[langkey] !== "" ? item[langkey] : item["nameEn"],
  }));
  const stockTypeselection = (stockType || []).map((item) => ({
    value: item["nameEn"],
    label: item[langkey] !== "" ? item[langkey] : item["nameEn"],
  }));

  const selectAllStatus = () => {
    const statusValue = getstatusOptions(intl).map((v) => v.value);
    changeForm("status", statusValue);
  };

  const deselectAllStatus = (fieldName) => {
    changeForm(fieldName, []);
  };

  const singleSelectAllOptions = (options) => {
    const miscOptions = options.map((v) => v.value);
    for (let key in miscOptions) {
      changeForm(miscOptions[key], true);
    }
  };

  const singledeSelectAllOptions = (options) => {
    const miscOptions = options.map((v) => v.value);
    for (let key in miscOptions) {
      changeForm(miscOptions[key], false);
    }
  };

  const unicornIdIsSet = !_.isEmpty(_.get(selectedData, "unicornId"));

  return (
    <div>
      {!expanded ? (
        <div className={classes.sticky}>
          <Field
            name="streets"
            margin="normal"
            label={intl.formatMessage({
              id: "search.form.street",
            })}
            fullWidth
            component={AutoCompleteSelect}
            optionsdata={streets}
            history={fieldhistory}
            apiaction={searchStreets}
            selectedData={selectedData && selectedData.streets}
            setSelectedData={setSelectedData}
            placeholder={unicornIdIsSet && "---"}
            disabled={unicornIdIsSet}
            limit={9999}
          />
        </div>
      ) : (
        <Field
          name="streets"
          margin="normal"
          label={intl.formatMessage({
            id: "search.form.street",
          })}
          fullWidth
          component={AutoCompleteSelect}
          optionsdata={streets}
          apiaction={searchStreets}
          selectedData={selectedData && selectedData.streets}
          setSelectedData={setSelectedData}
          customInputProps={{
            className: classes.fieldMargin,
          }}
          placeholder={unicornIdIsSet && "---"}
          disabled={unicornIdIsSet}
          limit={9999}
        />
      )}

      {/* <Field
          name="building"
          type="text"
          margin="normal"
          label="Building"
          component={TextInput}
          fullWidth
          variant="outlined"
        /> */}

      <Collapse in={expanded}>
        <div className={classes.paddingSection}>
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="streetsNoMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.form.streetnumber",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 4)}
                  compPosition={"left"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="streetsNoMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue3]}
                  parse={(value) => formatMaxLength(value, 4)}
                  compPosition={"right"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={12}>
              <Field
                name="parity"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.header.parity",
                })}
                ranges={unicornIdIsSet ? [{ value: "", label: "---" }] : parity}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                selectedData={selectedData && selectedData.parity}
                setSelectedData={setSelectedData}
                disabled={unicornIdIsSet}
              />
            </Grid>
          </Grid>

          <Field
            name="district"
            margin="normal"
            label={intl.formatMessage({
              id: "search.form.district",
            })}
            fullWidth
            component={Search}
            searchItems={districts}
            selectedData={selectedData && selectedData.district}
            setSelectedData={setSelectedData}
            customInputProps={{
              className: classes.fieldMargin,
            }}
            placeholder={unicornIdIsSet && "---"}
            disabled={unicornIdIsSet}
          />

          <Field
            name="buildingSourcesId"
            margin="normal"
            label={intl.formatMessage({
              id: "search.form.building",
            })}
            fullWidth
            component={AutoCompleteSelect}
            optionsdata={buildings}
            apiaction={listBuildings}
            selectedData={selectedData && selectedData.buildingSourcesId}
            setSelectedData={setSelectedData}
            customInputProps={{
              className: classes.fieldMargin,
            }}
            placeholder={unicornIdIsSet && "---"}
            disabled={unicornIdIsSet}
          />
        </div>

        {/* marketable */}
        <DetailBoxSection
          text={intl.formatMessage({
            id: "search.header.marketable",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
          customRight={
            <div className={classes.sectionTitleBtnContainer}>
              <PillButton
                onClick={() =>
                  singleSelectAllOptions(getmarketableMapping(intl))
                }
                disabled={unicornIdIsSet}
              >
                {intl.formatMessage({
                  id: "search.form.all",
                })}
              </PillButton>
              <PillButton
                onClick={() =>
                  singledeSelectAllOptions(getmarketableMapping(intl))
                }
                disabled={unicornIdIsSet}
              >
                {intl.formatMessage({
                  id: "search.form.none",
                })}
              </PillButton>
            </div>
          }
        >
          <Grid container spacing={1}>
            {getmarketableMapping(intl).map((v, i) => {
              return (
                <Grid key={i} item xs={4}>
                  <div>
                    <Field
                      name={v.value}
                      text={v.label}
                      component={PillCheckBox}
                      disabled={unicornIdIsSet}
                    />
                  </div>
                </Grid>
              );
            })}
          </Grid>
        </DetailBoxSection>

        {/* status */}
        <DetailBoxSection
          text={intl.formatMessage({
            id: "search.header.status",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
          customRight={
            <div className={classes.sectionTitleBtnContainer}>
              <PillButton onClick={selectAllStatus} disabled={unicornIdIsSet}>
                {intl.formatMessage({
                  id: "search.form.all",
                })}
              </PillButton>
              <PillButton
                onClick={() => deselectAllStatus("status")}
                disabled={unicornIdIsSet}
              >
                {intl.formatMessage({
                  id: "search.form.none",
                })}
              </PillButton>
            </div>
          }
        >
          <FieldArray
            name="status"
            component={ArrayPillCheckBox}
            options={getstatusOptions(intl)}
            disabled={unicornIdIsSet}
          />
        </DetailBoxSection>

        {/* cockloft */}
        <DetailBoxSection
          text={intl.formatMessage({
            id: "stock.cockloft",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
          customRight={
            <div className={classes.sectionTitleBtnContainer}>
              <PillButton
                onClick={() => singleSelectAllOptions(getcockloftMapping(intl))}
                disabled={unicornIdIsSet}
              >
                {intl.formatMessage({
                  id: "search.form.all",
                })}
              </PillButton>
              <PillButton
                onClick={() =>
                  singledeSelectAllOptions(getcockloftMapping(intl))
                }
                disabled={unicornIdIsSet}
              >
                {intl.formatMessage({
                  id: "search.form.none",
                })}
              </PillButton>
            </div>
          }
        >
          <Grid container spacing={1}>
            {getcockloftMapping(intl).map((v, i) => {
              return (
                <Grid key={i} item xs={4}>
                  <div>
                    <Field
                      name={v.value}
                      text={v.label}
                      component={PillCheckBox}
                      disabled={unicornIdIsSet}
                    />
                  </div>
                </Grid>
              );
            })}
          </Grid>
        </DetailBoxSection>

        <DetailBoxSection
          text={intl.formatMessage({
            id: "search.header.stock",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
        >
          {/* area */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="areaMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.area",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"left"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="areaMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue8]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"right"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          {/* area for shop */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="areaForShopMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "stock.shoparea",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"left"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="areaForShopMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue8]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"right"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          {/* price */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"priceMin" + TogglePriceValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  label={intl.formatMessage({
                    id: "search.common.price",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  validate={
                    TogglePriceValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue8]
                      : [minvaluezero]
                  }
                  InputProps={TogglePriceValue === "Total" ? unitM : null}
                  format={(value) => {
                    let formattedvalue;
                    if (TogglePriceValue === "Total") {
                      formattedvalue = unitFormatter(value, "M");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (TogglePriceValue === "Total") {
                      return formatUnitandMaxLength(value, "M");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"priceMax" + TogglePriceValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  validate={
                    TogglePriceValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue8]
                      : [minvaluezero, maxvalue8]
                  }
                  InputProps={TogglePriceValue == "Total" && unitM}
                  format={(value) => {
                    let formattedvalue;
                    if (TogglePriceValue === "Total") {
                      formattedvalue = unitFormatter(value, "M");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (TogglePriceValue === "Total") {
                      return formatUnitandMaxLength(value, "M");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                  // rightcomp={
                  //   <Switch
                  //     value={TogglePriceValue}
                  //     textL={intl.formatMessage({
                  //       id: "search.common.pricetotal",
                  //     })}
                  //     textR={intl.formatMessage({
                  //       id: "search.common.priceavg",
                  //     })}
                  //     valleft="Total"
                  //     valright="Avg"
                  //     onChange={onChangePriceTotalAvg}
                  //   />
                  // }
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          {/* rent */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"rentMin" + ToggleRentValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  label={intl.formatMessage({
                    id: "search.common.rent",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  validate={
                    ToggleRentValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue6]
                      : [minvaluezero]
                  }
                  InputProps={ToggleRentValue === "Total" && unitK}
                  format={(value) => {
                    let formattedvalue;
                    if (ToggleRentValue === "Total") {
                      formattedvalue = unitFormatter(value, "K");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (ToggleRentValue === "Total") {
                      return formatUnitandMaxLength(value, "K");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"rentMax" + ToggleRentValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  validate={
                    ToggleRentValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue6]
                      : [minvaluezero, maxvalue8]
                  }
                  InputProps={ToggleRentValue === "Total" && unitK}
                  format={(value) => {
                    let formattedvalue;
                    if (ToggleRentValue === "Total") {
                      formattedvalue = unitFormatter(value, "K");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (ToggleRentValue === "Total") {
                      return formatUnitandMaxLength(value, "K");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                  // rightcomp={
                  //   <Switch
                  //     value={ToggleRentValue}
                  //     textL={intl.formatMessage({
                  //       id: "search.common.renttotal",
                  //     })}
                  //     textR={intl.formatMessage({
                  //       id: "search.common.rentavg",
                  //     })}
                  //     valleft="Total"
                  //     valright="Avg"
                  //     onChange={onChangeRentTotalAvg}
                  //   />
                  // }
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          {/* yield */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="yieldMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "stock.yield",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"left"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="yieldMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue8]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"right"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          <Grid container>
            <Grid item xs={12}>
              <Field
                name="stockType"
                margin="normal"
                component={Search}
                valueField="value"
                labelField="label"
                label={intl.formatMessage({
                  id: "search.form.type",
                })}
                fullWidth
                searchItems={stockTypeselection}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                selectedData={selectedData && selectedData.stockType}
                setSelectedData={setSelectedData}
                placeholder={unicornIdIsSet && "---"}
                disabled={unicornIdIsSet}
              />
            </Grid>
          </Grid>

          {/* board  */}
          <Grid container>
            <Grid item xs={12}>
              <Field
                name="boardStatus"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.board",
                })}
                ranges={
                  unicornIdIsSet ? [{ value: "", label: "---" }] : boardstatus
                }
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                disabled={unicornIdIsSet}
              />
            </Grid>
          </Grid>

          {/* promote news  */}
          <Grid container>
            <Grid item xs={12}>
              <Field
                name="newsStatus"
                component={SelectFieldArrayOutput}
                label={intl.formatMessage({
                  id: "search.form.news",
                })}
                ranges={
                  unicornIdIsSet ? [{ value: "", label: "---" }] : newsstatus
                }
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                disabled={unicornIdIsSet}
              />
            </Grid>
          </Grid>

          {/* possession */}
          <Grid container>
            <Grid item xs={12}>
              <Field
                name="possession"
                margin="normal"
                component={Search}
                valueField="value"
                labelField="label"
                label={intl.formatMessage({
                  id: "search.form.possession",
                })}
                fullWidth
                searchItems={possessionselection}
                selectedData={selectedData && selectedData.possession}
                setSelectedData={setSelectedData}
                customInputProps={{
                  className: classes.fieldMargin,
                }}
                placeholder={unicornIdIsSet && "---"}
                disabled={unicornIdIsSet}
              />
            </Grid>
          </Grid>
        </DetailBoxSection>

        <DetailBoxSection
          text={intl.formatMessage({
            id: "search.header.miscellaneous",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
          customRight={
            <div className={classes.sectionTitleBtnContainer}>
              <PillButton
                onClick={() =>
                  singleSelectAllOptions(getpropertyconditionOptions(intl))
                }
                disabled={unicornIdIsSet}
              >
                {intl.formatMessage({
                  id: "search.form.all",
                })}
              </PillButton>
              <PillButton
                onClick={() =>
                  singledeSelectAllOptions(getpropertyconditionOptions(intl))
                }
                disabled={unicornIdIsSet}
              >
                {intl.formatMessage({
                  id: "search.form.none",
                })}
              </PillButton>
            </div>
          }
        >
          <div className={classes.paddingSection}>
            <Grid container spacing={1}>
              {getpropertyconditionOptions(intl).map((v, i) => {
                return (
                  <Grid key={i} item xs={4}>
                    <div>
                      <Field
                        name={v.value}
                        text={v.label}
                        component={PillCheckBox}
                        disabled={unicornIdIsSet}
                      />
                    </div>
                  </Grid>
                );
              })}
            </Grid>
          </div>

          {/* create date */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="createDateMin"
                  type={unicornIdIsSet ? "text" : "date"}
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.createdate",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="createDateMax"
                  type={unicornIdIsSet ? "text" : "date"}
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          {/* last update date */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="lastUpdateDateMin"
                  type={unicornIdIsSet ? "text" : "date"}
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.lastupdatedate",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="lastUpdateDateMax"
                  type={unicornIdIsSet ? "text" : "date"}
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          {/* price change date */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="priceChangeDateMin"
                  type={unicornIdIsSet ? "text" : "date"}
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.form.pricechange",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="priceChangeDateMax"
                  type={unicornIdIsSet ? "text" : "date"}
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          {/* tenancy expiry date */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="tenancyExpireDateMin"
                  type={unicornIdIsSet ? "text" : "date"}
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "stock.tenancyperiod",
                  })}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.min",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="tenancyExpireDateMax"
                  type={unicornIdIsSet ? "text" : "date"}
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={
                    unicornIdIsSet
                      ? "---"
                      : intl.formatMessage({
                          id: "search.form.common.max",
                        })
                  }
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  disabled={unicornIdIsSet}
                />
              </div>
            </Grid>
          </Grid>

          <Grid item xs={12}>
            <Field
              name="unicornId"
              margin="normal"
              label={intl.formatMessage({
                id: "search.header.stockid",
              })}
              fullWidth
              component={ReactSelectCreatable}
              optionsdata={[]}
              customInputProps={{
                className: classes.fieldMargin,
              }}
              selectedData={selectedData && selectedData.unicornId}
              setSelectedData={setSelectedData}
              autoCreateOnBlur
            />
          </Grid>
        </DetailBoxSection>

        {enableConsolidLandSearch == "true" && (
          <DetailBoxSection
            text={intl.formatMessage({
              id: "search.header.consolidate",
            })}
            titleClass={classes.Headertitle}
            contentClass={classes.DetailBoxSectionContent}
          >
            <Grid container>
              <Grid item xs={6}>
                <Field
                  name="isVendor"
                  label={intl.formatMessage({ id: "search.form.owner" })}
                  component={ChipsCheckBox}
                  disabled={unicornIdIsSet}
                />
              </Grid>
              <Grid item xs={6}>
                <Field
                  name="isCurrent"
                  label={intl.formatMessage(
                    { id: "stock.tenant" },
                    {
                      status: intl.formatMessage({
                        id: "stock.currenttenancy",
                      }),
                    },
                  )}
                  component={ChipsCheckBox}
                  disabled={unicornIdIsSet}
                />
              </Grid>
              <Grid item xs={6}>
                <Field
                  name="isPrevious"
                  label={intl.formatMessage(
                    { id: "stock.tenant" },
                    {
                      status: intl.formatMessage({
                        id: "stock.previoustenancy",
                      }),
                    },
                  )}
                  component={ChipsCheckBox}
                  disabled={unicornIdIsSet}
                />
              </Grid>
              <Grid item xs={6}>
                <Field
                  name="isFormer"
                  label={intl.formatMessage(
                    { id: "stock.tenant" },
                    {
                      status: intl.formatMessage({ id: "stock.formertenancy" }),
                    },
                  )}
                  component={ChipsCheckBox}
                  disabled={unicornIdIsSet}
                />
              </Grid>
              <Grid item xs={6}>
                <Field
                  name="isAdvance"
                  label={intl.formatMessage(
                    { id: "stock.tenant" },
                    {
                      status: intl.formatMessage({
                        id: "stock.advancetenancy",
                      }),
                    },
                  )}
                  component={ChipsCheckBox}
                  disabled={unicornIdIsSet}
                />
              </Grid>
              <Grid item xs={6}>
                <Field
                  name="isPrivacy"
                  label={intl.formatMessage({ id: "search.form.privacylimit" })}
                  component={ChipsCheckBox}
                  disabled={unicornIdIsSet}
                />
              </Grid>

              <Grid item xs={12}>
                <Field
                  name="contactsPerson"
                  margin="normal"
                  label={intl.formatMessage({
                    id: "stock.contact",
                  })}
                  fullWidth
                  component={ReactSelectCreatable}
                  optionsdata={[]}
                  customInputProps={{
                    className: classes.fieldMargin,
                  }}
                  selectedData={selectedData && selectedData.contactsPerson}
                  setSelectedData={setSelectedData}
                  placeholder={unicornIdIsSet && "---"}
                  disabled={unicornIdIsSet}
                />
              </Grid>
              <Grid item xs={12}>
                <Field
                  name="contactsCompany"
                  margin="normal"
                  label={intl.formatMessage({
                    id: "search.form.company",
                  })}
                  fullWidth
                  component={AutoCompleteSelect}
                  optionsdata={companies}
                  apiaction={listCompanies}
                  customInputProps={{
                    className: classes.fieldMargin,
                  }}
                  selectedData={selectedData && selectedData.contactsCompany}
                  setSelectedData={setSelectedData}
                  showZhEnLabel={true}
                  placeholder={unicornIdIsSet && "---"}
                  disabled={unicornIdIsSet}
                />
              </Grid>
              <Grid item xs={12}>
                <Field
                  name="contactsPhone"
                  margin="normal"
                  label={intl.formatMessage({
                    id: "building.phone",
                  })}
                  fullWidth
                  component={ReactSelectCreatable}
                  optionsdata={[]}
                  customInputProps={{
                    className: classes.fieldMargin,
                  }}
                  selectedData={selectedData && selectedData.contactsPhone}
                  setSelectedData={setSelectedData}
                  placeholder={unicornIdIsSet && "---"}
                  disabled={unicornIdIsSet}
                />
              </Grid>
              <Grid item xs={12}>
                <Field
                  name="contactsEmail"
                  margin="normal"
                  label={intl.formatMessage({
                    id: "search.form.email",
                  })}
                  fullWidth
                  component={ReactSelectCreatable}
                  optionsdata={[]}
                  customInputProps={{
                    className: classes.fieldMargin,
                  }}
                  selectedData={selectedData && selectedData.contactsEmail}
                  setSelectedData={setSelectedData}
                  placeholder={unicornIdIsSet && "---"}
                  disabled={unicornIdIsSet}
                />
              </Grid>
              <Grid item xs={12}>
                <Field
                  // name="retainMarkStocks"
                  label={intl.formatMessage({
                    id: "search.checkbox.retainMarkedRecord",
                  })}
                  component={ChipsCheckBox}
                  input={{
                    value: retainMarkStocks,
                    onChange: onRetainMarkStocksClick,
                  }}
                />
              </Grid>
            </Grid>
          </DetailBoxSection>
        )}

        {props.children}
      </Collapse>
    </div>
  );
}

const mapStateToProps = (state) => ({
  buildings: state.building.buildings || [],
  fieldhistory: state.stocklist.fieldhistory || [],
  queryvariables: state.stocklist.queryvariables
    ? state.stocklist.queryvariables
    : {},
  listing: state.building.listing ? state.building.listing : false,
  listed: state.building.listed ? state.building.listed : false,
  // streets: state.street.streets ? state.street.streets : [],
  streets: _.get(state, "street.streetSearch") || [],
  districts: state.district.districts ? state.district.districts : [],
  possession: state.stocklist.possession ? state.stocklist.possession : [],
  stockType: state.stocklist.stockType ? state.stocklist.stockType : [],
  companies: state.stocklist.companies ? state.stocklist.companies : [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    listBuildings: (graphqlvariable) => {
      dispatch(listBuildings(graphqlvariable));
    },
    // listStreets: (graphqlvariable) => dispatch(listStreets(graphqlvariable)),
    searchStreets: (variables) => dispatch(streetSearch(variables)),
    listCompanies: (graphqlvariable) =>
      dispatch(listCompanies(graphqlvariable)),
    changeForm: (field, val) => dispatch(change("searchForm", field, val)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(FieldSection)));
