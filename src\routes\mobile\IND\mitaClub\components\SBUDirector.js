import React from "react";
import { connect } from "react-redux";
import { Box, makeStyles } from "@material-ui/core";
import { convertCurrency } from "@/helper/generalHelper";
import {
  getEmployeesMitaclubManagerInfo,
  getMitaclubEmpsByTeamCode,
  queryMitaClubAgentsWithTeam,
  queryMitaClubDistrictDirectorList,
  queryMitaClubDistrictManager,
  queryMitaClubManagerList,
} from "@/actions/employee";
import DistrictDirectorListCard from "./DistrictDirectorListCard";
import { AGENT_TARGET, MANAGER_TARGET } from "../MitaClubPage";
import QualificationCard from "./QualificationCard";
import TeamDetailsCard from "./TeamDetailsCard";
import GoldCard from "./GoldCard";
import _ from "lodash";

const qualificationLevels = [
  {
    title: "首 1 名最高盈利",
  },
];

const useStyles = makeStyles((theme) => ({
  "@global": {
    body: {
      margin: 0,
      padding: 0,
      height: "100vh",
      backgroundColor: "#F5F5F5",
    }
  },
  root: {
    // paddingBottom: "32px",
  },
  contentStack: {
    display: "flex",
    flexDirection: "column",
    width: "100%",
    gap: theme.spacing(3),
    // marginTop: "-85px",
  },
  teamDetailsCardTeamName: {
    // width: 0,
    // flex: 1,
    "&, & > span": {
      overflow: "hidden",
      textOverflow: "ellipsis",
      whiteSpace: "nowrap",
    },
  },
}));

function CardBox(props) {
  const classes = useStyles();

  const performanceData = React.useMemo(() => {
    const result = {
      AccumulatedSalesAmount: 0,
      AccumulatedSalesCase: 0,
      ExecutionDate: "",
    };
    if (props.mitaclubAgent) {
      result.AccumulatedSalesAmount = +props.mitaclubAgent.AccumulatedSalesAmount || 0;
      result.AccumulatedSalesCase = +props.mitaclubAgent.AccumulatedSalesCase || 0;
      result.ExecutionDate = props.mitaclubAgent.ExecutionDate || "";
    }
    return result;
  }, [props.mitaclubAgent]);

  const metricsValue = React.useMemo(() => {
    return convertCurrency(+performanceData.AccumulatedSalesAmount || 0)
  }, [performanceData]);

  const districtDirectorList = React.useMemo(() => {
    const { mitaclubAgent = {}, mitaclubTeams = [] } = props;
    return mitaclubTeams.filter((row) => !(row.EmpId === mitaclubAgent.EmpId));
  }, [props.mitaclubAgent, props.mitaclubTeams]);

  const districtDirectorWithManagersAndAgentsData = React.useMemo(() => {
    const {
      mitaclubAgent = {},
      mitaclubManagers = [],
      mitaclubTeams = [],
      mitaclubAgentsWithTeam = [],
    } = props;
    const clearMitaclubTeams = mitaclubTeams
      .filter((row) => !(row.EmpId === mitaclubAgent.EmpId))
      .sort((a, b) => +b.AccumulatedSalesAmount - +a.AccumulatedSalesAmount);

    return clearMitaclubTeams.map((sbuDirector = {}) => {
      const { TeamCode = [] } = sbuDirector;
      const teamCodeSet = new Set(TeamCode);

      const managers = mitaclubManagers
        .filter(m => teamCodeSet.has(m.TeamCode))
        .sort((a, b) => +b.AccumulatedSalesAmount - +a.AccumulatedSalesAmount)
        .map((m) => {
          const agents = _.orderBy(
            _.filter(mitaclubAgentsWithTeam, { TeamCode: m.TeamCode }),
            (row) => parseFloat(row.AccumulatedSalesAmount),
            "desc"
          );
          return { ...m, agents };
        });

      return {
        ...sbuDirector,
        managers,
      }
    });
  }, [props.mitaclubAgent, props.mitaclubAgentsWithTeam, props.mitaclubTeams, props.mitaclubManagers]);

  const getSummaryOfTeam = (teamCode, rows, isManager = false) => {
    const mitaclubDistrictDirectors = districtDirectorWithManagersAndAgentsData;

    const target = isManager ? MANAGER_TARGET : AGENT_TARGET;
    if (isManager) {
      const platinumCount = _.size(_.filter(rows, row => parseFloat(row.AccumulatedSalesAmount) >= MANAGER_TARGET[1]));
      const basicCount = _.size(_.filter(rows, row => parseFloat(row.AccumulatedSalesAmount) < MANAGER_TARGET[1]));
      const totalSales = _.sumBy(rows, row => parseFloat(row.AccumulatedSalesAmount));
      const totalCases = _.sumBy(rows, row => parseFloat(row.AccumulatedSalesCase));
      return { platinumCount, basicCount, totalSales, totalCases };
    }
    const diamondCount = _.size(_.filter(rows, row => row.TeamCode === teamCode && parseFloat(row.AccumulatedSalesAmount) >= target[1]));
    const platinumCount = _.size(_.filter(
      rows,
      row => row.TeamCode === teamCode
        && parseFloat(row.AccumulatedSalesAmount) >= target[0]
        && parseFloat(row.AccumulatedSalesAmount) < target[1])
    );
    const basicCount = _.size(_.filter(rows, row => row.TeamCode === teamCode && parseFloat(row.AccumulatedSalesAmount) < target[0]));
    const totalSales = _.find(mitaclubDistrictDirectors, { TeamCode: teamCode })?.AccumulatedSalesAmount || 0;
    const totalCases = _.find(mitaclubDistrictDirectors, { TeamCode: teamCode })?.AccumulatedSalesCase || 0;
    return { diamondCount, platinumCount, basicCount, totalSales, totalCases };
  };

  return (
    <Box style={{ padding: "0 12px" }}>
      <Box className={classes.contentStack}>
        {/* 业绩卡片 */}
        <GoldCard
          title={"你的年度團隊業績"}
          updateDate={performanceData.ExecutionDate}
          currentPerformance={(+performanceData.AccumulatedSalesAmount || 0)}
          metricsValue={metricsValue}
        />

        {/* 资格卡片 */}
        <QualificationCard
          titleType={"會長"}
          qualificationLevels={qualificationLevels}
        />

        {!districtDirectorList?.length ? null : <DistrictDirectorListCard
          districtDirectorList={districtDirectorList}
        />}

        {districtDirectorWithManagersAndAgentsData.map((districtDirector) => {
          const managers = districtDirector.managers?.map(m => ({ ...m, EnglishName: m.TeamCode }));
          if (!managers?.length) {
            return null;
          }

          return (
            <>
              <TeamDetailsCard
                key={districtDirector.englishName}
                hideIcon
                hideProgressBar
                agents={managers}
                teamInfo={districtDirector}
                teamName={(
                  <div className={classes.teamDetailsCardTeamName}>
                    <div style={{ display: "flex", alignItems: "center", gap: 4, width: "100%" }}>
                      <span style={{ fontSize: 14 }}>{districtDirector.EnglishName}</span>
                      <span style={{ fontSize: 15 }}>區董</span>
                    </div>
                    <span style={{ fontWeight: "bold", fontSize: 15 }}>
                      {districtDirector.managers.map(m => m.TeamCode).join(", ")}
                    </span>
                  </div>
                )}
                targetValues={[0, 0]}
              />

              {_.isArray(managers) && _.map(managers, (manager) => {
                const { agents: teamRowsForTeam } = manager;

                return (
                  <TeamDetailsCard
                    targetValues={AGENT_TARGET}
                    agents={teamRowsForTeam}
                  />
                );
              })}
            </>
          )
        })}

      </Box>
    </Box>
  );
}

function SBUDirector(props) {
  const classes = useStyles();

  React.useEffect(() => {
    if (props.employee?.emp_id) {
      const { emp_id } = props.employee;
      props.queryMitaClubDistrictManager(emp_id);
    }
  }, [props.employee, props.queryMitaClubDistrictManager]);

  React.useEffect(() => {
    const { mitaclubAgent } = props;
    if (mitaclubAgent) {
      if (_.isArray(mitaclubAgent.TeamCode)) {
        // if is sbu director, load district directors data
        props.queryMitaClubDistrictDirectorList({ teamCode: mitaclubAgent.TeamCode });
        // if is director, load agents from corresponding teams
        props.queryMitaClubManagerList({ teamCode: mitaclubAgent.TeamCode });
        props.queryMitaClubAgentsWithTeam({ teamCode: mitaclubAgent.TeamCode });
      }
    }
  }, [props.mitaclubAgent]);

  return (
    <Box className={classes.root}>
      <CardBox
        mitaclubInfo={props.mitaclubInfo}
        mitaclubAgentMap={props.mitaclubAgentMap}
        mitaclubManagerInfo={props.mitaclubManagerInfo}

        mitaclubAgent={props.mitaclubAgent}
        mitaclubTeams={props.mitaclubTeams}
        mitaclubManagers={props.mitaclubManagers}
        mitaclubAgentsWithTeam={props.mitaclubAgentsWithTeam}
      />
    </Box>
  );
}

const mapStateToProps = (state) => ({
  mitaclubInfo: _.get(state, "employee.mitaclubInfo") || [],
  mitaclubAgentMap: _.get(state, "employee.mitaclubAgentMap"),
  mitaclubManagerInfo: _.get(state, "employee.mitaclubManagerInfo") || [],

  mitaclubAgent: _.get(state, "employee.mitaclubAgent") || {},
  mitaclubTeams: _.get(state, "employee.mitaclubTeams") || [],
  mitaclubManagers: _.get(state, "employee.mitaclubManagers") || [],
  mitaclubAgentsWithTeam: _.get(state, "employee.mitaclubAgentsWithTeam") || [],
});

const mapDispatchToProps = (dispatch) => ({
  getMitaclubEmpsByTeamCode: (teamCode) => dispatch(getMitaclubEmpsByTeamCode({ teamCode })),
  getEmployeesMitaclubManagerInfo: (teamCode = []) => dispatch(getEmployeesMitaclubManagerInfo({ teamCode })),

  queryMitaClubDistrictDirectorList: (params = { tempCode: [] }) => dispatch(queryMitaClubDistrictDirectorList(params)),
  queryMitaClubDistrictManager: (empId = "") => dispatch(queryMitaClubDistrictManager({ empId })),
  queryMitaClubManagerList: (params = { tempCode: [] }) => dispatch(queryMitaClubManagerList(params)),
  queryMitaClubAgentsWithTeam: (params = { tempCode: [] }) => dispatch(queryMitaClubAgentsWithTeam(params)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(SBUDirector);
