import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import FieldVal from "../../../../common/FieldVal";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import { convertCurrency, getLangKey, numberComma } from "../../../../../helper/generalHelper";
import TenancyRecordBox from "./TenancyRecordBox";
import { injectIntl } from "react-intl";

const styles = (theme) => ({
  root: {
    padding: "1vh 0",
  },
  gridContent: {
    padding: "1vw 2vw",
  },
  notFound: {
    padding: "1vw 2vw",
    borderRadius: 4,
    backgroundColor: "#F6F6F6",
  },
  contactHeader: {
    paddingTop: 8,
  },
});

class Tenancy extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langKey = getLangKey(intl);

    const rentFreeMapping = {
      "可商議": intl.formatMessage({ id: "stock.negotiable" }),
      "無資料": intl.formatMessage({ id: "stock.nodata" }),
      "有": intl.formatMessage({ id: "stock.yes" }),
      "無": intl.formatMessage({ id: "stock.no" }),
    };

    const optionToRenewTypeMapping = {
      "可商議": intl.formatMessage({ id: "stock.negotiable" }),
      "無資料": intl.formatMessage({ id: "stock.nodata" }),
      "生約": intl.formatMessage({ id: "stock.tenancy.option" }),
      "死約": intl.formatMessage({ id: "stock.tenancy.fixed" }),
    };

    const termMapping = {
      "生約": intl.formatMessage({ id: "stock.tenancy.option" }),
      "死約": intl.formatMessage({ id: "stock.tenancy.fixed" }),
      "不詳": intl.formatMessage({ id: "stock.tenancy.unknown" }),
    };

    const feeTypeMapping = {
      "無資料": intl.formatMessage({ id: "stock.nodata" }),
      "按比率": intl.formatMessage({ id: "stock.tenancy.proportion" }),
      "按市值": intl.formatMessage({ id: "stock.tenancy.marketrate" }),
      "固定": intl.formatMessage({ id: "stock.tenancy.fix" }),
      "不高於": intl.formatMessage({ id: "stock.tenancy.notgreater" }),
      "不低於": intl.formatMessage({ id: "stock.tenancy.notless" }),
    };

    const mongoId = detail._id ? detail._id : null;
    const stockId =
      detail.unicorn && Number.isInteger(detail.unicorn.id)
        ? detail.unicorn.id
        : null;

    const tenancyData = detail.tenancy || {};
    const rentFree =
      tenancyData.rentFree && rentFreeMapping[tenancyData.rentFree]
        ? rentFreeMapping[tenancyData.rentFree]
        : "---";
    const freePeriodDay =
      tenancyData.freePeriod && tenancyData.freePeriod.days
        ? tenancyData.freePeriod.days + intl.formatMessage({ id: "stock.days" })
        : "";
    const freePeriodMonth =
      tenancyData.freePeriod && tenancyData.freePeriod.months
        ? tenancyData.freePeriod.months + intl.formatMessage({ id: "stock.months" })
        : "";
    const freePeriod =
      freePeriodDay || freePeriodMonth
        ? freePeriodMonth + " " + freePeriodDay
        : "---";
    const leasePeriodYear =
      tenancyData.leasePeriod && tenancyData.leasePeriod.years
        ? tenancyData.leasePeriod.years + intl.formatMessage({ id: "stock.years" })
        : "";
    const leasePeriodMonth =
      tenancyData.leasePeriod && tenancyData.leasePeriod.months
        ? tenancyData.leasePeriod.months + intl.formatMessage({ id: "stock.months" })
        : "";
    const leasePeriod =
      leasePeriodYear || leasePeriodMonth
        ? leasePeriodYear + " " + leasePeriodMonth
        : "---";
    const leasePeriodDescription =
      tenancyData.leasePeriod && tenancyData.leasePeriod.description
        ? tenancyData.leasePeriod.description
        : "---";
    const depositInMonth =
      tenancyData.depositInMonth
        ? tenancyData.depositInMonth + intl.formatMessage({ id: "stock.months" })
        : "---";
    let optionToRenews = tenancyData.optionToRenews || [];
    optionToRenews = optionToRenews.slice(0, 2).map(v => {
      let years = v.years ? v.years + intl.formatMessage({ id: "stock.years" }) : "";
      let months = v.months ? v.months + intl.formatMessage({ id: "stock.months" }) : "";
      let type = v.type && optionToRenewTypeMapping[v.type] ? " (" + optionToRenewTypeMapping[v.type] + ")" : "";
      return years || months || type ? years + " " + months + type : "---";
    });
    let renewFees = tenancyData.renewFees || [];
    renewFees = renewFees.slice(0, 2).map(v => {
      let type = v.type && feeTypeMapping[v.type] ? feeTypeMapping[v.type] + " " : "";
      let amount = v.amount ? "$" + numberComma(v.amount) : "";
      let rate = v.rate ? v.rate + "%" : "";
      let rentDisplay = (amount || rate || type) ? type + (amount || rate) : "";
      return rentDisplay.trim() || "---";
    });
    const prepay =
      tenancyData.prepaidInMonth
        ? tenancyData.prepaidInMonth + intl.formatMessage({ id: "stock.months" })
        : "---";

    const tenancyRecords = detail.tenancyRecords ? detail.tenancyRecords : [];
    let advTenants = [];
    let currTenants = [];
    let prevTenants = [];
    let formerTenants = [];

    for (let i = 0; i < tenancyRecords.length; i++) {
      if (tenancyRecords[i].deleted) continue;
      let tenant = tenancyRecords[i].tenant;
      if (!tenant) continue;

      const status = tenancyRecords[i].status
        ? tenancyRecords[i].status
        : "Former";

      let tenantNameEn = tenant.nameEn;
      let tenantNameZh = tenant.nameZh;
      let tenantId = tenant.tenantCompanyId;
      let floor = tenancyRecords[i].floor || "---";
      let shopNumber = tenancyRecords[i].shopNumber || "---";
      let area =
        tenancyRecords[i].area && tenancyRecords[i].area.total
          ? tenancyRecords[i].area.total.toString()
          : "---";
      let minDate =
        tenancyRecords[i].expiry && tenancyRecords[i].expiry.minDate
          ? tenancyRecords[i].expiry.minDate
          : "";
      let maxDate =
        tenancyRecords[i].expiry && tenancyRecords[i].expiry.maxDate
          ? tenancyRecords[i].expiry.maxDate
          : "";
      let date =
        minDate && maxDate ? minDate + " - " + maxDate : minDate || maxDate || "---";
      let rent =
        tenancyRecords[i].rentalFee
          ? "$" + numberComma(tenancyRecords[i].rentalFee)
          : "---";
      let options = tenancyRecords[i].extends || [];
      options = options.slice(0, 3).map(v => {
        let minDate = v.minDate || "";
        let maxDate = v.maxDate || "";
        let type = v.rent && v.rent.type && feeTypeMapping[v.rent.type] ? feeTypeMapping[v.rent.type] + " " : "";
        let amount = v.rent && v.rent.amount ? "$" + numberComma(v.rent.amount) : "";
        let rate = v.rent && v.rent.rate ? v.rent.rate + "%" : "";
        let term = v.rent && v.rent.term && termMapping[v.rent.term] ? " (" + termMapping[v.rent.term] + ")" : "";
        let rentDisplay = (type || amount || rate || term) ? type + (amount || rate) + term : "";
        rentDisplay = rentDisplay.trim() || "---";
        return {
          period: minDate && maxDate ? minDate + " - " + maxDate : minDate || maxDate || "---",
          rent: rentDisplay,
        };
      });
      let tenantCompany = tenant.company;
      let tenantCompanyId = tenant.companyId;
      let business =
        tenant.business && tenant.business[langKey]
          ? tenant.business[langKey]
          : "---";
      let phones = tenant.phones || [];

      let contactPersons = tenant.contact;
      if (!tenant.contact || tenant.contact.length === 0)
        contactPersons = [{}];
      for (let j = 0; j < contactPersons.length; j++) {
        let contactName = contactPersons[j].name;
        let contactTitle = contactPersons[j].title;

        let item = {
          status,
          tenantEn: tenantNameEn,
          tenantZh: tenantNameZh,
          tenantId: tenantId,
          floor,
          shopNumber,
          area,
          tenancyPeriod: date,
          rent: rent,
          options,
          company: tenantCompany,
          companyId: tenantCompanyId,
          business,
          contactName,
          contactTitle,
          phones,
          mongoId,
          stockId,
        };

        if (status === "Advance") advTenants.push(item);
        if (status === "Current") currTenants.push(item);
        if (status === "Previous") prevTenants.push(item);
        if (status === "Former") formerTenants.push(item);
      }
    }

    if (advTenants.length === 0) advTenants = [{ status: "Advance" }];
    if (currTenants.length === 0) currTenants = [{ status: "Current" }];
    if (prevTenants.length === 0) prevTenants = [{ status: "Previous" }];
    if (formerTenants.length === 0) formerTenants = [{ status: "Former" }];

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.tenancy",
          })}
        >

          <DetailBoxSection noStrike={true}>
            {advTenants.reverse().map((v, i) => (
              <TenancyRecordBox
                {...v}
                key={i}
              />
            ))}
          </DetailBoxSection>

          <DetailBoxSection noStrike={true}>
            {currTenants.reverse().map((v, i) => (
              <TenancyRecordBox
                {...v}
                key={i}
              />
            ))}
          </DetailBoxSection>

          <DetailBoxSection noStrike={true}>
            {prevTenants.reverse().map((v, i) => (
              <TenancyRecordBox
                {...v}
                key={i}
              />
            ))}
          </DetailBoxSection>

          <DetailBoxSection noStrike={true}>
            {formerTenants.reverse().map((v, i) => (
              <TenancyRecordBox
                {...v}
                key={i}
              />
            ))}
          </DetailBoxSection>

          <DetailBoxSection noStrike={true}>
            <Grid container spacing={2} className={classes.gridContent}>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.rentfree",
                  })}
                >
                  {rentFree}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.freeperiod",
                  })}
                >
                  {freePeriod}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.leaseperiod",
                  })}
                >
                  {leasePeriod}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.leaseperiodfreetext",
                  })}
                >
                  {leasePeriodDescription}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.tenancy.option1",
                  })}
                >
                  {optionToRenews[0] || "---"}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.tenancy.rent1",
                  })}
                >
                  {renewFees[0] || "---"}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.tenancy.option2",
                  })}
                >
                  {optionToRenews[1] || "---"}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.tenancy.rent2",
                  })}
                >
                  {renewFees[1] || "---"}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.deposit",
                  })}
                >
                  {depositInMonth}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.prepay",
                  })}
                >
                  {prepay}
                </FieldVal>
              </Grid>
            </Grid>
          </DetailBoxSection>

        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(Tenancy));
