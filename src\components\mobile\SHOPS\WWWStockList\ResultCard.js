import React from "react";
import {connect} from "react-redux";
import {createStyles, withStyles} from "@material-ui/core/styles";
import Grid from "@material-ui/core/Grid";
import clsx from "clsx";
import {injectIntl} from "react-intl";
import _ from "lodash";

import DetailBoxTitle from "../../../common/DetailBoxTitle";
import DetailBoxSection from "../../../common/DetailBoxSection";
import VaryingVal from "../../../common/VaryingVal";
import GridSection from "../../../common/GridSection";
import {convertCurrency, numberComma, paresFloorUnit,} from "../../../../helper/generalHelper";
import history from "@/core/history";
import WWWStockTag from "@/components/common/WWWStockTag";
import WWWScoreIcon from "@/components/common/WWWStockScoreIcon";
import UnlockDialogItem from "@/components/common/UnlockDialogItem";

const styles = createStyles((theme) => ({
  root: {
    padding: "1vw 2vw",
    backgroundColor: "rgba(255, 255, 255, .6)",
    borderTop: "0px solid #fff",
    boxShadow: "none",
  },
  unlockedRoot: {
    padding: "1vw 2vw",
    backgroundColor: "rgba(255, 255, 200, .6)",
    borderTop: "0px solid #fff",
    boxShadow: "none",
  },
  priceRow: {
    display: "flex",
    justifyContent: "space-between",
    textAlign: "right",
    padding: "1vh 0 0.5vh 0",
  },
  commonItem: {
    display: "flex",
    fontSize: "1.5em",
    padding: "0 4px",
    borderRadius: 4,
    color: "#fff",
  },
  rentItem: {
    backgroundColor: "rgba(0, 197, 197, .75)",
  },
  priceItem: {
    backgroundColor: "rgba(232, 0, 0, .75)",
  },
  greyItem: {
    backgroundColor: "rgba(132, 132, 132, .1)",
  },
  rentItemNoValue: {
    backgroundColor: "rgba(140, 190, 190, 0.2)",
  },
  priceItemNoValue: {
    backgroundColor: "rgba(200, 170, 170, 0.2)",
  },
  fieldItem: {
    fontSize: "1.175em",
  },
  status: {
    fontWeight: 700,
    display: "inline-flex",
  },
  link: {
    textDecoration: "none",
    color: "#000",
  },
  DetailBoxSectionContent: {
    padding: 0,
  },
  totalItem: {
    display: "block",
    width: "100%",
    fontSize: "0.75em",
  },
  tagContainer: {
    padding: "0",
  },
  exposureRate: {
    color: "gray",
    padding: "5px 0px"
  }
}));



class ResultCard extends React.Component {

  constructor() {
    super();
    this.state = {
      dialogOpen: false,
    };
  }

  goStockDetail = (stockId) => {
    // console.log("click", stockId)
    history.push("/stock?mode=indv&ids=" + encodeURIComponent(JSON.stringify(stockId)));
  };

  handleClickOpenDialog = (purpose) => {
    this.setState({ dialogOpen: true });
  };

  handleCloseDialog = () => {
    this.setState({ dialogOpen: false });
  };

  formatPrice = (askingPrice, askingTotalPrice) => {

  }

  formatRent = (askingRent, askingTotalRent) => {

  }

  formatArea = (area, areaForShop) => {
    if (!area && !areaForShop ) {
      return "---";
    }
    return `${numberComma(areaForShop) || "---"} / ${numberComma(area) || "---"}`
  }

  render() {
    const {
      classes,
      result,
      intl,
      uid,
      isUnlocked,
    } = this.props;
    // console.log("result:", result);

    const stockId = _.get(result, 'stockId', '');
    const stockBasicInfo = _.get(result, 'stockBasicInfo', {});
    const agents = _.get(result, 'agents', []);
    const wwwChannelFull = _.get(result, 'wwwChannelFull', {});
    const unicornId = _.get(stockBasicInfo, 'unicorn.stock', '');
    const buildingNameEn = _.get(stockBasicInfo, 'building.nameEn', '---');
    const buildingNameZh = _.get(stockBasicInfo, 'building.nameZh', '---');

    const districtAbbr = _.get(stockBasicInfo, 'district.abbr', '---');
    const districtZh = _.get(stockBasicInfo, 'district.nameZh', '---');

    const floor = _.get(stockBasicInfo, 'floor.input', '---');
    const unit = _.get(stockBasicInfo, 'unit', '---');

    const buildingRowEn = `${buildingNameEn}, ${districtAbbr}`;
    const buildingRowZh = `${buildingNameZh}, ${districtZh}`;

    const [titleUp, titleDown] = intl.locale === "zh"
      ? [buildingRowZh, buildingRowEn]
      : [buildingRowEn, buildingRowZh];

    const isOnline = _.get(result, 'isOnline', false);
    const wwwScoreTotal = _.get(result, 'wwwScoreTotal', 0);

    const sumAgentScore = _.sumBy(_.get(result, 'agents'), 'scoreTotal');
    const agent = _.find(agents, {empId: uid});
    const exposureRate = Math.round(_.get(agent, 'scoreTotal') / sumAgentScore * 100) || 0;

    const askingPrice = _.get(stockBasicInfo, 'askingPrice.average', 0);
    const askingTotalPrice = _.get(stockBasicInfo, 'askingPrice.total', 0);
    const askingPriceType = _.get(stockBasicInfo, 'askingPrice.trend', null);

    const askingRent = _.get(stockBasicInfo, 'askingRent.average', 0);
    const askingTotalRent = _.get(stockBasicInfo, 'askingRent.total', 0);
    const askingRentType = _.get(stockBasicInfo, 'askingRent.trend', null);
    const statusDisplay = _.get(stockBasicInfo, intl.locale === "zh" ? "statusZh" : "status", '---');
    const area = _.get(result, 'stockBasicInfo.area');
    const areaForShop = _.get(result, 'stockBasicInfo.areaForShop');
    const areaDisplay = this.formatArea(area, areaForShop);

    return (
      <div>
        <div
          onClick={() => isUnlocked ? this.goStockDetail([stockId]) : this.handleClickOpenDialog()}
          className={classes.link}
        >
          <GridSection className={isUnlocked ? classes.unlockedRoot : classes.root}>
            <DetailBoxTitle
              subtitle={<span>{paresFloorUnit(floor, unit, intl)}</span>}
              rightIcon={
                <div className={classes.status}>
                  <WWWScoreIcon
                    isOnline={isOnline}
                    score={wwwScoreTotal}
                    isCurrentAgent={true}
                  />
                </div>
              }
            />
            <DetailBoxTitle
              subtitle={titleUp}
              rightIcon={<span className={classes.fieldItem}>{statusDisplay}</span>}
            />

            <DetailBoxTitle
              subtitle={titleDown}
              rightIcon={<span className={classes.fieldItem}>{areaDisplay}</span>}
            />

            <DetailBoxSection
              contentClass={classes.DetailBoxSectionContent}
              noStrike={true}
            >
              <Grid container spacing={1} className={classes.priceRow}>
                <Grid item xs={6}>
                  <VaryingVal
                    className={clsx(
                      classes.commonItem,
                      classes.priceItem,
                      askingPrice <= 0 && classes.priceItemNoValue,
                    )}
                    type={askingPriceType}
                  >
                    <span>
                      {askingPrice > 0
                        ? `@${numberComma(askingPrice, 0)}`
                        : "---"}
                      {askingPrice !== 0 && askingTotalPrice !== 0 ? (
                        <span className={classes.totalItem}>
                          ${convertCurrency(askingTotalPrice, undefined, {decimals: 3})}
                        </span>
                      ) : (
                        <span className={classes.totalItem}>---</span>
                      )}
                    </span>
                  </VaryingVal>
                </Grid>

                <Grid item xs={6}>
                  <VaryingVal
                    className={clsx(
                      classes.commonItem,
                      classes.rentItem,
                      askingRent <= 0 && classes.rentItemNoValue,
                    )}
                    type={askingRentType}
                    // label={"Rent"}
                  >
                    <span>
                      {askingRent > 0 ? `@${numberComma(askingRent, 2)}` : "---"}
                      {askingRent !== 0 && askingTotalRent !== 0 ? (
                        <span className={classes.totalItem}>
                          ${convertCurrency(askingTotalRent, undefined, {decimals: 3})}
                        </span>
                      ) : (
                        <span className={classes.totalItem}>---</span>
                      )}
                    </span>
                  </VaryingVal>
                </Grid>
              </Grid>

              <div className={classes.exposureRate}>
                {intl.formatMessage({id: "wwwStock.exposureRate"})}
                <span style={{color: "black", marginLeft: 5}}>{exposureRate}%</span>
              </div>

              <div className={classes.tagContainer}>
                <WWWStockTag
                  uid={uid}
                  agent={agent}
                  wwwChannelFull={wwwChannelFull}
                />
              </div>

            </DetailBoxSection>

          </GridSection>
        </div>

        <UnlockDialogItem
          dialogOpen={this.state.dialogOpen}
          handleCloseDialog={this.handleCloseDialog}
          stockid={stockId}
          stockunicornid={unicornId}
        />
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  uid: state.auth.user.login.info.emp_id,
});

const mapDispatchToProps = (dispatch) => {
  return {
    // unlockStock: (id) => dispatch(unlockStock(id)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(ResultCard)));
