import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import { getLangKey, convertCurrency, convertNewlineToBr } from "../../../../../helper/generalHelper";
import ItemWithIcon from "../../../../common/ItemWithIcon";
import { injectIntl } from "react-intl";

const styles = (theme) => ({
  root: {
    fontSize: "1.125em",
  },
  small: {
    fontSize: ".777em",
  },
  notFound: {},
  flexRow: {
    display: "flex",
    alignItems: "center",
  },
  flexOne: {
    flex: 1,
  },
});

class UpdateHistoryMain extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, hand, intl } = this.props;
    const langKey = getLangKey(intl);

    const updateHistory = detail.updateHistory ? detail.updateHistory : [];
    let currHandArr = [];
    for (let i = 0; i < updateHistory.length; i++) {
      if (updateHistory[i].deleted) continue;
      if (
        isNaN(parseInt(updateHistory[i].hands)) ||
        parseInt(updateHistory[i].hands) !== hand
        && hand !== -1
      )
        continue;

      let date = updateHistory[i].datetime
        ? updateHistory[i].datetime.slice(0, 10)
        : "";
      let employeeName =
        updateHistory[i].employee && updateHistory[i].employee.name_en
          ? updateHistory[i].employee.name_en
          : "---";
      let employeeDept =
        updateHistory[i].employee && updateHistory[i].employee.dept_code
          ? updateHistory[i].employee.dept_code
          : "";
      let remarks =
        updateHistory[i].description
          ? convertNewlineToBr(updateHistory[i].description)
          : "---";
      let status =
        updateHistory[i].status && updateHistory[i].status[langKey]
          ? updateHistory[i].status[langKey]
          : "---";
      let askingPrice =
        updateHistory[i].askingPrice && updateHistory[i].askingPrice.total
          ? "$" + convertCurrency(updateHistory[i].askingPrice.total)
          : "---";
      let askingRent =
        updateHistory[i].askingRent && updateHistory[i].askingRent.total
          ? "$" + convertCurrency(updateHistory[i].askingRent.total)
          : "---";

      let textContent = (
        <>
          <div>{date}</div>
          <div className={classes.flexRow}>
            <div className={classes.flexOne}>
              {employeeName + (employeeDept ? " (" + employeeDept + ")" : "")}
            </div>
            <div>
              {intl.formatMessage({
                id: "search.common.price",
              })}{" "}
              {askingPrice}
            </div>
          </div>
          <div className={classes.flexRow}>
            <div className={classes.flexOne}>{status}</div>
            <div>
              {intl.formatMessage({
                id: "search.common.rent",
              })}{" "}
              {askingRent}
            </div>
          </div>
          <div>{remarks}</div>
        </>
      );
      let item = {
        textContent,
        date,
      };

      let j;
      for (j = 0; j < currHandArr.length; j++) {
        if (date < currHandArr[j].date) break;
      }
      currHandArr.splice(j, 0, item);
    }

    return (
      <div className={classes.root}>
        {currHandArr.reverse().map((v, i) => (
          <ItemWithIcon textContent={v.textContent} key={i} />
        ))}
        {currHandArr.length === 0 && (
          <div className={classes.notFound}>
            {intl.formatMessage({
              id: "stock.noupdatehistory",
            })}
          </div>
        )}
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(UpdateHistoryMain));
