import fetch from "node-fetch";
import config from "../config";

const versionText = (config.mode === "production") ? '' : ' (測試版)';

export async function sendSMS(token, phone) {
  const content = {
    company: config.notificationApi.company,
    receiver: phone,
    subject: `MSearch 搵盤易${versionText}`,
    message: `MSearch 搵盤易${versionText} 登入 PIN: ${token}`,
    method: "sms",
    notifyUrl: null,
    requestFrom: "ICIMS"
  };
  console.log(content);
  sendNotification(content);
}

export function sendappNotification(token, emp_id) {
  const content = {
    company: config.notificationApi.company,
    receiver: emp_id,
    subject: `MSearch 搵盤易${versionText}`,
    message: `MSearch 搵盤易${versionText} 登入 PIN: ${token}`,
    method: "app",
    notifyUrl: null,
    requestFrom: "ICIMS"
  };
  sendNotification(content);
}

async function sendNotification(content) {
  try {
    const resp = await fetch(config.notificationApi.url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(content)
    });
    if (resp.status !== 200) throw new Error(resp.statusText);
  } catch (e) {
    console.log(e);
  }
}
