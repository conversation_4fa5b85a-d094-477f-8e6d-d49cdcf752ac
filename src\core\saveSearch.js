import { ObjectID } from "mongodb";
import mongodb from "../data/mongodb";

function resJson(res, status, error, data) {
  res.json({
    status,
    errors: error ? [{ message: error }] : null,
    data
  });
  res.end();
}

export const saveSearch = (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    if (!req.body || !req.body.name) {
      resJson(res, 300, "Empty name is not allowed");
      return;
    }

    const userInfo =
      req.user && req.user.login && req.user.login.info
        ? req.user.login.info
        : {};

    const query = {
      emp_id: userInfo.emp_id,
      name: req.body.name,
    };
    const col = mongodb.db.collection("saved_search");
    col.findOneAndUpdate(query, {
      $set: { query: req.body.query },
      $setOnInsert: { createDate: new Date() }
    },  {
      upsert: true,
      new: true,
      returnDocument: "after",
    }, function (err, r) {
      if (err) {
        resJson(res, 300, "Failed to save record");
      } else if (r.upsertedCount > 0) {
        resJson(res, 200, null, r.upsertedId);
      } else {
        resJson(res, 201, null, r.value);
      }
    });
  }
};

export const deleteSearch = (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    const userInfo =
      req.user && req.user.login && req.user.login.info
        ? req.user.login.info
        : {};

    const col = mongodb.db.collection("saved_search");
    col.deleteOne({
      _id: new ObjectID(req.body._id),
      emp_id: userInfo.emp_id,
    }, function (err, r) {
      if (err) {
        resJson(res, 300, "Failed to delete record");
      } else {
        resJson(res, 200, null, r.result);
      }
    });
  }
};

export const listSearch = (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    const userInfo =
      req.user && req.user.login && req.user.login.info
        ? req.user.login.info
        : {};

    const query = {
      emp_id: userInfo.emp_id,
    };

    const pipeline = [
      { $match: query },
      { $project: { name: 1, query: 1 } }
    ]

    const col = mongodb.db.collection("saved_search");

    col.aggregate(pipeline).toArray((err, docs) => {
      resJson(res, 200, null, docs);
    });
  }
};
