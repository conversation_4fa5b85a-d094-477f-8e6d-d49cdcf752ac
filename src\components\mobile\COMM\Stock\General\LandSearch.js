import React, { useState, useEffect } from "react";
import { connect } from "react-redux";
import { makeStyles } from "@material-ui/core/styles";
import { injectIntl } from "react-intl";
import Grid from "@material-ui/core/Grid";
import PropTypes from "prop-types";

import DetailBoxSection from "../../../../common/DetailBoxSection";
import { listLandSearch } from "../../../../../actions/stock";
import LandSearchCard from "../../../../common/LandSearchCard";
import ItemCount from "../../../../common/ItemCount";

const useStyles = makeStyles({
  card: {
    padding: 0,
    backgroundColor: "transparent",
  },
  noData: {
    fontSize: "1.125em",
    paddingLeft: "2vw",
  },
});

function LandSearch({ detail, listLandSearch, intl }) {
  const classes = useStyles();
  const [isExpanding, setIsExpanding] = useState(false);
  const [landSearchData, setLandSearchData] = useState([]);

  useEffect(() => {
    const landSearchId = detail.unicorn && detail.unicorn.landSearch;

    if (landSearchId && !_.has(detail, "landSearch")) {
      listLandSearch(landSearchId).then(() => {
        if (detail.landSearch) {
          // 先过滤数据，然后按create_date降序排序
          const filteredData = detail.landSearch
            .filter(item => item.list.length > 0)
            .sort((a, b) => {
              // 转换日期字符串为Date对象并比较
              return new Date(b.create_date) - new Date(a.create_date);
            });
          setLandSearchData(filteredData);
        }
      });
    }
  }, [detail]);

  const handleChange = (value) => {
    // get the value returned by child component
    setIsExpanding(value);
  };

  return (
    <div className={classes.root}>
      <DetailBoxSection
        expandable
        isExpanding={isExpanding}
        callback={handleChange}
        text={intl.formatMessage({ id: "stock.landsearch" })}
      >
        {landSearchData && landSearchData.length > 0 ? (
          <div>
            <ItemCount
              count={
                landSearchData.filter((data) => data.list.length > 0).length
              }
              messageid="landSearch.list.count"
            />
            <Grid container spacing={1}>
              {landSearchData.map((v, i) => {
                return (
                  <Grid item xs={12} key={i}>
                    {v.list.length > 0 && (
                      <LandSearchCard
                        className={classes.card}
                        isStockDetail
                        detail={detail}
                        landSearch={v}
                      />
                    )}
                  </Grid>
                );
              })}
            </Grid>
          </div>
        ) : (
          <div className={classes.noData}>
            {intl.formatMessage({ id: "stock.landsearch.null" })}
          </div>
        )}
      </DetailBoxSection>
    </div>
  );
}

LandSearch.propTypes = {
  detail: PropTypes.object.isRequired,
  listLandSearch: PropTypes.func.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapDispatchToProps = (dispatch) => {
  return {
    listLandSearch: (args) => dispatch(listLandSearch(args)),
  };
};

export default connect(null, mapDispatchToProps)(injectIntl(LandSearch));
