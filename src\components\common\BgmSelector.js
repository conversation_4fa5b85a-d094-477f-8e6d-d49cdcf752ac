import React, { Component } from "react";
import PropTypes from "prop-types";
import { createStyles, withStyles } from "@material-ui/styles";
import { injectIntl } from "react-intl";
import axios from "axios";
import clsx from "clsx";

import Dialog from "@material-ui/core/Dialog";
import DialogContent from "@material-ui/core/DialogContent";
import DialogActions from "@material-ui/core/DialogActions";
import Button from "@material-ui/core/Button";
import IconButton from "@material-ui/core/IconButton";
import PlayArrowIcon from "@material-ui/icons/PlayArrow";
import PauseIcon from "@material-ui/icons/Pause";
import LibraryMusicIcon from '@material-ui/icons/LibraryMusic';
import TextField from "@material-ui/core/TextField";
import CircularProgress from '@material-ui/core/CircularProgress';

const styles = createStyles((theme) => ({
  bgmListItem: {
    display: "flex",
    alignItems: "center",
    padding: "10px",
    borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
    backgroundColor: "#005F5F",
    color: "#fff",
    "&:hover": {
      backgroundColor: "#004444",
    },
    cursor: "pointer",
  },
  bgmListItemSelected: {
    backgroundColor: "#004444",
  },
  bgmName: {
    flexGrow: 1,
    marginLeft: "10px",
    overflowWrap: "anywhere",
  },
  playIcon: {
    color: "#fff",
  },
  bgmListContainer: {
    maxHeight: "60vh",
    overflowY: "auto",
    backgroundColor: "#005F5F",
    padding: 0,
  },
  bgmDialog: {
    "& .MuiDialog-paper": {
      backgroundColor: "#005F5F",
      color: "#fff",
      minWidth: "300px",
      margin: 0,
      borderRadius: 0,
    },
  },
  bgmDialogActions: {
    backgroundColor: "#005F5F",
    justifyContent: "space-between",
    padding: 0,
    margin: 0,
  },
  bgmButton: {
    backgroundColor: "#00C974",
    color: "#fff",
    borderRadius: 4,
    width: "50%",
    margin: 10,
    "&:hover": {
      backgroundColor: "#00B868",
    },
  },
  musicIcon: {
    marginRight: "8px",
    backgroundColor: "#333",
    borderRadius: "4px",
    padding: "4px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  playButtonContainer: {
    marginLeft: "auto",
    backgroundColor: "lightgray",
    borderRadius: "18px",
  },
  textField: {
    width: "100%",
    "& .MuiFormLabel-asterisk": {
      color: "red",
    },
    "& .MuiFormHelperText-root": {
      marginTop: 0,
      color: "rgba(0,0,0,.8)",
    },
  },
  loadingContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    padding: "20px",
    minHeight: "150px",
  },
  loadingText: {
    marginLeft: "10px",
    color: "#fff",
  },
  whiteProgress: {
    color: "#FFF",
  },
}));

class BgmSelector extends Component {
  constructor(props) {
    super(props);
    this.state = {
      bgmDialogOpen: false,
      bgmList: [],
      currentPlayingBgm: null,
      loading: false,
    };
    this.audioRef = React.createRef();
  }

  componentDidMount() {
    this.fetchBgmList();
  }

  componentWillUnmount() {
    // 停止当前播放的音乐
    if (this.audioRef.current) {
      this.audioRef.current.pause();
    }
  }

  fetchBgmList = async () => {
    try {
      this.setState({ loading: true });
      const response = await axios.post('/media/graphql', {
        query: `
          query {
            bgmList {
              name
              path
              size
              type
              lastModified
              duration
            }
          }
        `
      });
      
      if (response.data && response.data.data && response.data.data.bgmList) {
        this.setState({ 
          bgmList: response.data.data.bgmList,
          loading: false
        });
      } else {
        this.setState({ loading: false });
      }
    } catch (error) {
      console.error('获取背景音乐列表失败:', error);
      this.setState({ loading: false });
    }
  }

  openBgmDialog = () => {
    this.setState({ bgmDialogOpen: true });
    // 如果列表为空，重新加载
    if (this.state.bgmList.length === 0) {
      this.fetchBgmList();
    }
  };

  closeBgmDialog = () => {
    this.setState({ bgmDialogOpen: false });
    // 停止当前播放的音乐
    if (this.state.currentPlayingBgm && this.audioRef.current) {
      this.audioRef.current.pause();
      this.setState({ currentPlayingBgm: null });
    }
  };

  playBgm = (bgm) => {
    if (this.state.currentPlayingBgm === bgm.path) {
      // 如果点击的是当前正在播放的音乐，则暂停
      if (this.audioRef.current) {
        this.audioRef.current.pause();
        this.setState({ currentPlayingBgm: null });
      }
    } else {
      // 播放新的音乐
      if (this.audioRef.current) {
        this.audioRef.current.src = bgm.path;
        this.audioRef.current.play();
        this.setState({ currentPlayingBgm: bgm.path });
      }
    }
  };

  selectBgm = (bgm) => {
    const { onChange } = this.props;
    
    if (onChange) {
      onChange(bgm);
    }
    
    // 停止当前播放的音乐
    if (this.audioRef.current) {
      this.audioRef.current.pause();
      this.setState({ currentPlayingBgm: null });
    }
  };

  render() {
    const { classes, intl, value, label } = this.props;
    const { bgmDialogOpen, bgmList, currentPlayingBgm, loading } = this.state;
    const selectedBgmName = value ? (typeof value === "string" ? value : value.name) : "";

    return (
      <div>
        <TextField
          className={classes.textField}
          variant="outlined"
          margin="normal"
          label={label || intl.formatMessage({ id: "stock.kol.bgm" })}
          onClick={this.openBgmDialog}
          value={selectedBgmName}
          InputLabelProps={{
            shrink: true,
          }}
          InputProps={{
            readOnly: true,
            startAdornment: selectedBgmName ? (
              <span style={{ fontSize: "24px" }}>♫</span>
            ) : null,
          }}
        />

        {/* 背景音乐弹窗 */}
        <Dialog
          open={bgmDialogOpen}
          onClose={this.closeBgmDialog}
          className={classes.bgmDialog}
          maxWidth="md"
          fullWidth
        >
          <DialogContent className={classes.bgmListContainer}>
            {loading ? (
              <div className={classes.loadingContainer}>
                <CircularProgress size={80} className={classes.whiteProgress}/>
              </div>
            ) : bgmList.length > 0 ? (
              bgmList.map((bgm, index) => (
                <div
                  key={index}
                  className={clsx(classes.bgmListItem, {
                    [classes.bgmListItemSelected]: selectedBgmName === bgm.name,
                  })}
                  onClick={() => this.selectBgm(bgm)}
                >
                  <div className={classes.musicIcon}>
                    <LibraryMusicIcon />
                  </div>
                  <div className={classes.bgmName}>{bgm.name}</div>
                  <div className={classes.playButtonContainer}>
                    <IconButton size="small" className={classes.playIcon} onClick={(e) => {
                      e.stopPropagation();
                      this.selectBgm(bgm);
                      this.playBgm(bgm);
                    }}>
                      {currentPlayingBgm === bgm.path ? <PauseIcon /> : <PlayArrowIcon />}
                    </IconButton>
                  </div>
                </div>
              ))
            ) : (
              <div className={classes.loadingContainer}>
                <span className={classes.loadingText}>
                  NO BGM DATA
                </span>
              </div>
            )}
          </DialogContent>
          <DialogActions className={classes.bgmDialogActions}>
            <Button onClick={this.closeBgmDialog} className={classes.bgmButton}>
              {intl.formatMessage({ id: "common.cancel" })}
            </Button>
            <Button onClick={this.closeBgmDialog} className={classes.bgmButton}>
              {intl.formatMessage({ id: "common.ok" })}
            </Button>
          </DialogActions>
        </Dialog>

        {/* 音频播放器 */}
        <audio ref={this.audioRef} style={{ display: "none" }} />
      </div>
    );
  }
}

BgmSelector.propTypes = {
  classes: PropTypes.object.isRequired,
  intl: PropTypes.object.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  onChange: PropTypes.func.isRequired,
  label: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
};

export default withStyles(styles)(injectIntl(BgmSelector)); 