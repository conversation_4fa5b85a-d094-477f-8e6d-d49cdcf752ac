import React, { memo, useEffect, useState } from "react";
import PropTypes from "prop-types";
import _ from "lodash";
import { makeStyles } from "@material-ui/styles";
import { connect } from "react-redux";
import { injectIntl } from "react-intl";

import DetailBoxSection from "../../../../common/DetailBoxSection";
import { getDeniedCalls } from "../../../../../actions/stock";
import ContactInfoBox from "../../../../common/ContactInfoBox";

const useStyles = makeStyles({
  root: {
    padding: "1vh 0",
  },
  notFound: {},
});

function ContactInfo({ detail, hand, getDeniedCalls, intl }) {
  const classes = useStyles();

  const [contactInfo, setContactInfo] = useState([]);
  const [deniedCalls, setDeniedCalls] = useState(null);

  const mongoId = _.get(detail, "_id");
  const stockId = Number.isInteger(_.get(detail, "unicorn.id") ?? "")
    ? _.get(detail, "unicorn.id")
    : null;

  useEffect(() => {
    const vendors = _.get(detail, "vendors") || [];
    if (!_.isEmpty(vendors)) {
      const handsInfo = detail.handsInfo;
      const vendorsOfSelectedHand = vendors.filter(v =>
        handsInfo.find(v2 => v2._id === v.handsId)?.hands === hand
      );
      setContactInfo(vendorsOfSelectedHand);
    }
  }, [detail, hand]);

  useEffect(() => {
    if (!_.isEmpty(contactInfo) && _.isNull(deniedCalls)) {
      getDeniedCalls(_.map(contactInfo, "phones")).then((res) =>
        setDeniedCalls(res),
      );
    }
  }, [contactInfo]);

  return (
    <div className={classes.root}>
      <DetailBoxSection
        expandable
        text={intl.formatMessage({
          id: "stock.contact",
        })}
      >
        {contactInfo.map((v, i) => (
          <ContactInfoBox contact={v} mongoId={mongoId} stockId={stockId} key={i} deniedCalls={deniedCalls} />
        ))}
      </DetailBoxSection>
    </div>
  );
}

ContactInfo.propTypes = {
  detail: PropTypes.object,
  getDeniedCalls: PropTypes.func.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapDispatchToProps = (dispatch) => ({
  getDeniedCalls: (phoneNos) => dispatch(getDeniedCalls(phoneNos)),
});

export default connect(null, mapDispatchToProps)(memo(injectIntl(ContactInfo)));
