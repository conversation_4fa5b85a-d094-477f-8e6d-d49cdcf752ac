import {
  CLEAR_PROGRESSING_MEDIUM,
  CLEAR_PROGRESSING_MEDIUM_BY_ID,
  CREATE_MEDIUM_START,
  CREATE_MEDIUM_SUCCESS,
  CREATE_MEDIUM_ERROR,
  DELETE_MEDIUM_START,
  DELETE_MEDIUM_SUCCESS,
  DELETE_MEDIUM_ERROR,
  UPDATE_MEDIUM_START,
  UPDATE_MEDIUM_SUCCESS,
  UPDATE_MEDIUM_ERROR,
  LIST_APPROVE_MEDIA_START,
  LIST_APPROVE_MEDIA_SUCCESS,
  LIST_APPROVE_MEDIA_ERROR,
  CLEAR_APPROVE_MEDIA
} from "../constants/medium";

import { uploadRequestTimeout, media } from "../config";

export function clearProgressingMedium() {
  return (dispatch) => dispatch({ type: CLEAR_PROGRESSING_MEDIUM });
}

export function clearProgressingMediumById(id) {
  return (dispatch) => dispatch({ type: CLEAR_PROGRESSING_MEDIUM_BY_ID, payload: { id } });
}


/**
 * 检查KOL视频是否符合要求（时长小于3分钟、格式正确、宽高比正确）
 * @param {string} mediaType
 * @param {File} file
 * @returns {Promise<{isValid: boolean, error?: string}>}
 */
function isKolVideoValid(mediaType, file) {
  const typeStringZh = mediaType === 'kol_video' ? 'KOL' : '';
  const typeStirngEn = mediaType === 'kol_video' ? 'KOL video' : 'Video'
  if ((mediaType === 'kol_video' || mediaType === 'video') && file) {
    return new Promise((resolve) => {
      // 检查文件类型
      const acceptableFormats = ['video/mp4', 'video/quicktime'];
      if (!file || !acceptableFormats.includes(file.type)) {
        resolve({
          isValid: false,
          error: `KOL Video file does not meet the requirements|${typeStringZh}影片格式不为MP4或MOV，上傳失敗|${typeStirngEn} cannot not be uploaded becuase of wrong file type. (MP4, MOV)`
        });
        return;
      }

      // 创建视频元素来获取视频时长和宽高
      const video = document.createElement('video');
      video.preload = 'metadata';

      video.onloadedmetadata = () => {
        window.URL.revokeObjectURL(video.src);

        // 检查视频时长是否小于30秒或大于10分钟
        if (video.duration > media.kolVideoDurationLimitSeconds || video.duration < 30) {
          resolve({
            isValid: false,
            error: `KOL Video file does not meet the requirements|${typeStringZh}影片時長不符合最少30秒最多10分鐘的限制，上載失敗|${typeStirngEn} cannot be uploaded because of outranged length (min. 30 sec and max. 10 mins)`
          });
          return;
        }

        // 检查视频宽高比
        const width = video.videoWidth;
        const height = video.videoHeight;
        if (!validateAspectRatio(width, height)) {
          resolve({
            isValid: false,
            error: `KOL Video file does not meet the requirements|${typeStringZh}影片比例不符合16:9的限制，上載失敗|${typeStirngEn} cannot be uploaded because of invalid ratio (required: 16:9)`
          });
          return;
        }

        if (video.videoHeight < 720) {
          resolve({
            isValid: false,
            error: `KOL Video file does not meet the requirements|${typeStringZh}影片解析度不符合最低720P的限制，上載失敗|${typeStirngEn} cannot be uploaded because resolution is lower than 720P`
          });
          return;
        }

        resolve({ isValid: true });
      };

      video.onerror = () => {
        window.URL.revokeObjectURL(video.src);
      //   resolve({
      //     isValid: false,
      //     error: "KOL Video file does not meet the requirements|无法读取视频文件|Unable to read the video file"
      //   });
      };

      video.src = URL.createObjectURL(file);
    });
  }
  return Promise.resolve({ isValid: true });
}

/**
 * 验证视频宽高比是否接近16:9
 * @param {number} width 视频宽度
 * @param {number} height 视频高度
 * @returns {boolean} 是否符合要求的宽高比
 */
function validateAspectRatio(width, height) {
  const aspectRatio = width / height;
  const target169Ratio = 16 / 9;
  // 允许一定的误差范围
  if (Math.abs(aspectRatio - target169Ratio) > 0.1) {
    return false;
  }
  return true;
}

export function createMedium(
  medium,
  /** @type {[File, File]} - [mediaFile, thumbnail] */
  file,
  uuid,
  allowDuplicateFilename = false
) {
  return async (dispatch, getState, { fetch, universalRequest }) => {
    const startTime = new Date().getTime();

    dispatch({
      type: CREATE_MEDIUM_START,
      payload: {
        ...medium,
        filename: file[0].name,
        filesize: file[0].size,
        startTime,
        uuid,
      },
    });

    const isKolVideoValidResult = await isKolVideoValid(medium.type, file[0]);
    if (!isKolVideoValidResult.isValid) {
      dispatch({
        type: CREATE_MEDIUM_ERROR,
        payload: {
          error: { message: isKolVideoValidResult.error },
          startTime,
        },
      });
      return;
    }

    try {
      const payload = new FormData();
      if (file[0]) {
        payload.append('medium', file[0]);
      }
      if (file[1]) {
        payload.append('thumbnail', file[1]);
      }
      payload.append(
        "variables",
        JSON.stringify({
          medium,
          allowDuplicateFilename,
          uuid,
        })
      );
      const { data, errors } = await universalRequest(
        "/createMedium",
        {
          method: "post",
          body: payload,
          // credentials: 'include',
        },
        {
          timeoutMS: parseInt(String(uploadRequestTimeout)),
        }
      );

      if (errors) throw new Error(errors[0].message);

      localStorage.removeItem(`${medium.stockId}_KOLFileInfo`);
      dispatch({
        type: CREATE_MEDIUM_SUCCESS,
        payload: {
          medium: data,
          startTime,
        },
      });
    } catch (err) {
      localStorage.setItem(`${medium.stockId}_KOLFileInfo`, JSON.stringify(medium));
      const message = err?.message || "";
      dispatch({
        type: CREATE_MEDIUM_ERROR,
        payload: {
          err,
          error: { message },
          startTime,
        },
      });
      // throw new Error(error);
    }
  };
}

export function updateMedium(media) {
  return async (dispatch, getState, { fetch, universalRequest }) => {
    const startTime = new Date().getTime();

    dispatch({
      type: UPDATE_MEDIUM_START,
      payload: { ...media, startTime },
    });

    try {
      const payload = JSON.stringify(media);
      const { data, errors } = await universalRequest(
        "/updateMedium",
        {
          method: "POST",
          headers: {
            "content-type": "application/json",
          },
          body: payload,
          // credentials: 'include',
        },
        {
          timeoutMS: parseInt(String(uploadRequestTimeout)),
        }
      );

      if (errors) throw new Error(errors[0].message);

      dispatch({
        type: UPDATE_MEDIUM_SUCCESS,
        payload: {
          ...media,
        },
      });
    } catch (err) {
      const message = err?.message || "";
      dispatch({
        type: UPDATE_MEDIUM_ERROR,
        payload: {
          id: media.id,
          err,
          error: { message },
        },
      });
    }
  };
};

export function deleteMedium(mediaid) {
  return async (dispatch, getState, { fetch, universalRequest }) => {
    const startTime = new Date().getTime();

    dispatch({
      type: DELETE_MEDIUM_START,
      payload: { id: mediaid, startTime, operation: "delete" },
    });

    try {
      const payload = JSON.stringify({
        id: mediaid,
      });
      const { data, errors } = await universalRequest(
        "/removeMedium",
        {
          method: "POST",
          headers: {
            "content-type": "application/json",
          },
          body: payload,
          // credentials: 'include',
        },
        {
          timeoutMS: parseInt(String(uploadRequestTimeout)),
        }
      );

      if (errors) throw new Error(errors[0].message);

      dispatch({
        type: DELETE_MEDIUM_SUCCESS,
        payload: {
          medium: data,
          startTime,
          operation: "delete",
        },
      });
    } catch (err) {
      // 不能直接將 error 傳入 payload, 否則 stringify 后格式不對
      const message = err?.message || "";
      dispatch({
        type: DELETE_MEDIUM_ERROR,
        payload: {
          err,
          error: { message },
          startTime,
          operation: "delete",
        },
      });
      // throw new Error(error);
    }
  };
}

export function getMedium(id, customQuery = null) {
  return async (dispatch, getState, { universalRequest }) => {
    try {
      const query = customQuery || `
        query($id: ID!) {
          medium(id: $id) {
            id
            type
            tags
            status
            scoring
            markByWWW
            manualOffline
            sqsProcessing
          }
        }
      `;

      const payload = JSON.stringify({
        query,
        variables: { id },
      });

      const options = {
        headers: {
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      };

      const { data, errors } = await universalRequest(
        "/media/graphql",
        {
          method: "POST",
          body: payload,
          ...options,
        }
      );

      if (errors) throw new Error(errors[0].message);
      
      return data.medium;
    } catch (err) {
      console.error("Failed to get medium:", err);
      return null;
    }
  };
}

// 新增：批量获取媒体数据
export function getMediumByIds(mediaIds) {
  return async (dispatch, getState, { universalRequest }) => {
    try {
      // 如果没有媒体ID，直接返回空数组
      if (!mediaIds || mediaIds.length === 0) {
        return [];
      }

      console.log('批量获取媒体数据，媒体ID列表:', mediaIds);

      const query = `
        query($ids: [ID!]!) {
          media(ids: $ids) {
            id
            type
            status
            scoring
            markByWWW
            manualOffline
            sqsProcessing
          }
        }
      `;

      const payload = JSON.stringify({
        query,
        variables: { ids: mediaIds },
      });

      const options = {
        headers: {
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      };

      const { data, errors } = await universalRequest(
        "/media/graphql",
        {
          method: "POST",
          body: payload,
          ...options,
        }
      );

      if (errors) throw new Error(errors[0].message);

      const result = data.media || [];
      console.log('批量获取媒体数据结果:', result);
      return result;
    } catch (err) {
      console.error("Failed to get media by type:", err);
      return [];
    }
  };
}

// export function createMedium(medium, file, uuid, allowDuplicateFilename = false) {
//   return async (dispatch, getState, { fetch, api }) => {
//     const startTime = new Date().getTime();
//
//     dispatch({
//       type: CREATE_MEDIUM_START,
//       payload: {
//         ...medium,
//         filename: file.name,
//         filesize: file.size,
//         startTime,
//         uuid,
//       },
//     });
//
//     try {
//       const payload = new FormData();
//       payload.append('medium', file);
//       payload.append('query', `
//           mutation($medium: MediumCreate!, $allowDuplicateFilename: Boolean) {
//             createMedium(medium: $medium, allowDuplicateFilename: $allowDuplicateFilename) {
//               id
//             }
//           }
//       `);
//       payload.append('variables', JSON.stringify({
//         medium,
//         allowDuplicateFilename,
//       }));
//       const resp = await fetch(
//         api.media,
//         {
//           method: 'post',
//           body: payload,
//           credentials: 'include',
//         }
//       );
//       if (resp.status !== 200) throw new Error(resp.statusText);
//       const { data, errors } = await resp.json();
//       if (errors) throw new Error(errors[0].message);
//       if (!data.createMedium) throw new Error('No response from GraphQL API');
//
//       dispatch({
//         type: CREATE_MEDIUM_SUCCESS,
//         payload: {
//           medium: data.createMedium,
//           startTime,
//         },
//       });
//     } catch (error) {
//       dispatch({
//         type: CREATE_MEDIUM_ERROR,
//         payload: {
//           error,
//           startTime,
//         },
//       });
//       // throw new Error(error);
//     }
//   };
// }

export function listApproveMedia(variables) {
  return async (dispatch, getState, { fetch, graphqlRequest, api, getQuery, delay, universalRequest }) => {
    dispatch({
      type: LIST_APPROVE_MEDIA_START,
      payload: {
        variables
      },
      checkrefreshToken: true
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      };

      const queryLogin = await getQuery("LIST_EMPLOYEES_QUERY");
      const loginEmployee = await universalRequest("/employee/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: queryLogin,
          variables: { emp_id: [getState().auth.user.login.info.emp_id] }
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });
      if (loginEmployee.errors) {
        throw new Error(loginEmployee.errors[0].message);
      }

      const currentEmployee = loginEmployee?.data?.employees?.[0] || null;

      const gradeUnderManager = {
        "SALES MANAGER": ['S7'],
        "DISTRICT MANAGER": ['S4', 'S5', 'S6', 'S7']
      };

      let temasEmployeeIds = null;
      if (["DISTRICT MANAGER", "SALES MANAGER"].includes(currentEmployee?.auth?.role)){
        const query = await getQuery("LIST_TEAMS_EMPLOYEES_QUERY");
        const temasEmployees = await universalRequest("/employee/graphql", {
          method: "POST",
          body: JSON.stringify({
            query,
            variables: {
              team: currentEmployee.belongingTeams,
              grade_id: gradeUnderManager[currentEmployee?.auth?.role],
              emp_type: ['FL']
            }
          }),
          ...options
        }).catch((error) => {
          return {
            errors: error,
          };
        });
        if (temasEmployees.errors) {
          throw new Error(temasEmployees.errors[0].message);
        }
        temasEmployeeIds = temasEmployees.data.employees.map(obj => obj.emp_id) || [];
        temasEmployeeIds.push(currentEmployee.emp_id);
      }

      const query = `query($approval: String${temasEmployeeIds ? ", $employees: [String]" : ""}) {
        media (approval: $approval${temasEmployeeIds ? ", employees: $employees" : ""}) {
          id
          type
          tags
          status
          employeeId
          employee {
            id
            branchId
            cName
            eName
            cTitle
            eTitle
            licence
            phone
            email
            sex
          }
          processing
          approval
          createdDate
          lastModified
          description
          stockId
          stockMongodbId
          stockAddress {
            zh
            en
          }
          buildingId
          buildingMongodbId
          buildingAddress {
            zh
            en
          }
          streetId
          ...on Photo {
            filename
            originalFilename
            mediumRoot
            photoContent
          }
          ...on Document {
            filename
            originalFilename
            mediumRoot
            documentContent
          }
          ...on Video {
            youtubeId
            youtubeMrId
            youtubeHkpId
            youkuId
            filename
            originalFilename
            mediumRoot
            thumbnail
          }
          ... on EmbeddedScript {
            script
            thumbnail
            __typename
          }
          ... on KolVideo {
            filename
            originalFilename
            mediumRoot
            thumbnail
            description
            characteristicZh
            characteristicEn
            buildingName
            address
            propertyRefId
            vimeoId
          }
          ... on VirtualTour {
            filename
            originalFilename
            virtualTourId
            thumbnail
            sceneID
          }
        }
      }`;

      const resp = await universalRequest("/media/graphql", {
        method: "POST",
        body: JSON.stringify({
          query: query,
          variables: {
            approval: variables.approval,
            ...(temasEmployeeIds ? { employees: temasEmployeeIds } : {})
          },
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (resp.errors) {
        throw new Error(resp.errors[0].message);
      }

      const { data } = resp;

      dispatch({
        type: LIST_APPROVE_MEDIA_SUCCESS,
        payload: {
          approveMediaList: data.media || []
        }
      });
    } catch (error) {
      dispatch({
        type: LIST_APPROVE_MEDIA_ERROR,
        payload: {
          error
        }
      });
    }
  };
}

export function clearApproveMedia() {
  return async dispatch => {
    dispatch({
      type: CLEAR_APPROVE_MEDIA
    });
  };
}
