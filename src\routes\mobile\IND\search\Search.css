/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

.root {
  padding: 1vw 3vw;
  background-color: #fff;
  padding-bottom: 8vh;
}

.expandedroot {
  padding: 1vw 3vw;
  background-color: #fff;
  position: -webkit-sticky; /* Safari */
  position: sticky;
  top: 52px;
  z-index: 999;
  display: none;
}

.card {
  padding: 1vw 3vw;
}

.container {
  margin: 0 auto;
  padding: 20px 0;
  max-width: var(--max-content-width);
}
