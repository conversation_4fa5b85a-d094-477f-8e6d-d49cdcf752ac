import _ from "lodash";
import React, { useEffect, useMemo } from "react";
import { connect } from "react-redux";
import PropTypes from "prop-types";
import { injectIntl } from "react-intl";

import ProposalForm from "../../../Saleskit/Forms/ProposalForm";
import {
  clearCreateProposal,
  createProposal,
} from "../../../../actions/proposal";
import FieldSection from "./FormSection/FieldSection";
import langFile from "../../../../lang/IND/messages";
import {
  findZhEnFromOptions,
  getContainers,
  getDefaultRemarks,
  getFloorTypeInfoFromLangFile,
  getProposalName,
  getStockFloorType,
  langKey,
  parseMedia,
  parseTenancyRecords,
  titleMapping,
  getFullAddress,
  getAreaType,
  getOriginalfloorTypeEnValue,
  getNotNullIsShow,
  mergeTenants,
} from "../../../Saleskit/helpers";
import { sbu } from "../../../../config";

function Individual({
  detail,
  currentStock,
  media,
  buildingMedia,
  stockUnitViews,
  stockDecorations,
  stockPossessions,
  stockCurrentStates,
  reCreatePPStocks,
  createDialogOpen,
  closeDialog,
  createProposal,
  clearCreateProposal,
  intl,
}) {
  useEffect(() => () => clearCreateProposal(), []);

  const stock = useMemo(
    () => _.find(detail, ["_id", currentStock]),
    [detail, currentStock],
  );

  const initializeFormState = () => {
    // avoid initiialize form if stock is still undefined
    if (!stock || !media || !buildingMedia) return {};
    const [ppHistoryContent] = reCreatePPStocks;

    // determine stock type
    let type = _.get(ppHistoryContent, "type") || "SaleAndLease";
    // switch (stock?.status?.nameEn) {
    //   case "Lease":
    //     type = "Lease";
    //     break;
    //   case "Sale+Lease":
    //     type = "SaleAndLease";
    //     break;
    //   default:
    // }

    const stockMediaData = _.find(
      media,
      (m) => m.id === _.get(stock, "unicorn.id").toString(),
    );
    const buildingMediaData = _.find(
      buildingMedia,
      (m) => m.id === _.get(stock, "building.unicorn.id", "").toString(),
    );

    const mainPhoto = []
      .concat(
        _.get(buildingMediaData, "data.photo", []),
      )
      .find((m) => m?.tags?.indexOf("main") >= 0);

    const termRemarks = _.get(ppHistoryContent, "termRemarks") || getDefaultRemarks(type);

    const possibleFeeType = ["/SqFt", "/Qtr", "/Month", "/SY"];
    const possiblePaidBy = ["Paid By Tenant", "Paid By Landlord"];

    const areas = _(_.get(stock, "area.sizes", [])).reduce(
      (areaObj, item) => {
        if (!Object.keys(areaObj).includes(item.type)) return areaObj;
        return { ...areaObj, [item.type]: item.value || 0 };
      },
      { GROSS: 0, NET: 0, SALEABLE: 0, LETTABLE: 0 },
    );

    const getAreaValues = () => {
      const areaGross = _.get(ppHistoryContent, "areaGross.value", _.get(areas, "GROSS")) || 0;
      const areaNet = _.get(ppHistoryContent, "areaNet.value", _.get(areas, "NET")) || 0;
      const areaSaleable = _.get(ppHistoryContent, "areaSaleable.value", _.get(areas, "SALEABLE")) || 0;
      const areaLettable = _.get(ppHistoryContent, "areaLettable.value", _.get(areas, "LETTABLE")) || 0;

      const areaGrossIsShow = _.get(ppHistoryContent, "areaGross.isShow", !!areaGross) || false;
      const areaNetIsShow = _.get(ppHistoryContent, "areaNet.isShow", !!areaNet) || false;
      const areaSaleableIsShow = _.get(ppHistoryContent, "areaSaleable.isShow", !!areaSaleable) || false;
      const areaLettableIsShow = _.get(ppHistoryContent, "areaLettable.isShow", !!areaLettable) || false;

      const areaValues = {
        areaGross: [{ value: areaGross, isShow: areaGrossIsShow }],
        areaNet: [{ value: areaNet, isShow: areaNetIsShow }],
        areaSaleable: [{ value: areaSaleable, isShow: areaSaleableIsShow }],
        areaLettable: [{ value: areaLettable, isShow: areaLettableIsShow }],
      };

      return {
        ...areaValues,
      }
    };

    const { areaGross, areaLettable, areaNet, areaSaleable } = getAreaValues();

    const containers = getContainers(stock).map((v) => ({
      value: v[langKey(intl.locale, "name")] || "---",
      haveLoadingBay: v.haveLoadingBay,
      isShow: true,
    }));

    const floor = _.get(stock, "floor") || "";
    const floorType = "Actual Floor";
    // const floorType = !_.isNaN(parseInt(floor, 10))
    //   ? "Actual Floor"
    //   : getStockFloorType(
    //       _.get(stock, "building.floors") || [],
    //       parseInt(floor, 10),
    //     );

    const floors = _.get(stock, "building.floors") || [];
    const floorTmp = !isNaN(parseInt(_.get(stock, "floor")))
      ? parseInt(_.get(stock, "floor"))
      : _.get(stock, "floor");
    const correspondingFloor = _.find(floors, v => {
      if (!v.name || typeof v.name !== "string") return;

      if (typeof floorTmp === "number") {
        const min = parseInt(v.name.split("-")[0]);
        const max = parseInt(v.name.split("-")[1]);
        if (!isNaN(min) && !isNaN(max)) {
          if (min <= floorTmp && max >= floorTmp) return true;
        }

        const num = parseInt(v.name);
        if (!isNaN(num)) {
          if (num === floorTmp) return true;
        }
      }

      if (typeof floorTmp === "string" && floorTmp !== "") {
        if (v.name === floorTmp) return true;
      }
    });

    const managementFeeTemp = _.get(stock, "managementFee.number") ? _.get(stock, "managementFee.number") : correspondingFloor?.managementFee ? correspondingFloor?.managementFee : parseFloat(_.get(stock, `building.management.fee`)?.replace('@', '') || 0);

    const currentTenants = mergeTenants(
      parseTenancyRecords(_.get(stock, "tenancyRecords", []), intl, "Current"),
      ppHistoryContent?.currentTenants || []
    );

    const managementFeeUnit = _.get(ppHistoryContent, "managementFee.unit") ||
    (_.indexOf(possibleFeeType, _.get(stock, "managementFee.type")) >
      -1
      ? _.get(stock, "managementFee.type", "")
      : ""); 

    const totalMonthlyMgtFee = managementFeeUnit === "/SqFt" ? parseFloat(managementFeeTemp) * parseFloat(_.get(areaGross, "[0].value", 0)) : 0;

    // initial proposal form
    const form = {
      // stock values
      stock: {
        stockId: _.get(stock, "unicorn.id"),
        avgPrice: [
          {
            value: _.get(ppHistoryContent, "avgPrice.value") || _.get(stock, "askingPrice.average", 0),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "avgPrice.isShow"), type === "Sale" || type === "SaleAndLease"),
          },
        ],
        totalPrice: [
          {
            value: _.get(ppHistoryContent, "totalPrice.value") || _.get(stock, "askingPrice.total", 0),
            isShow: ppHistoryContent ? _.get(ppHistoryContent, "totalPrice.isShow") || false : (type === "Sale" || type === "SaleAndLease"),
            isNego: ppHistoryContent ? _.get(ppHistoryContent, "totalPrice.isNego") || false : false,
          },
        ],
        bottomAvgPrice: [
          {
            value: _.get(ppHistoryContent, "bottomAvgPrice.value") || _.get(
              _.find(_.get(stock, "askingPrice.details", []), [
                "type",
                "average_bottom",
              ]),
              "value",
              0,
            ),
            isShow: _.get(ppHistoryContent, "bottomAvgPrice.isShow") || false,
          }
        ],
        bottomTotalPrice: [
          {
            value: _.get(ppHistoryContent, "bottomTotalPrice.value") || _.get(
              _.find(_.get(stock, "askingPrice.details", []), [
                "type",
                "total_bottom",
              ]),
              "value",
              0,
            ),
            isShow: _.get(ppHistoryContent, "bottomTotalPrice.value") || false
          }
        ],
        avgRent: [
          {
            value: _.get(ppHistoryContent, "avgRent.value") || _.get(stock, "askingRent.average", 0),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "avgRent.isShow"), (type === "Lease" || type === "SaleAndLease")),
          },
        ],
        totalRent: [
          {
            value: _.get(ppHistoryContent, "totalRent.value") || _.get(stock, "askingRent.total", 0),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "totalRent.isShow"), (type === "Lease" || type === "SaleAndLease")),
            isNego: _.get(ppHistoryContent, "totalRent.isNego") || false,
          },
        ],
        bottomAvgRent: [
          {
            value: _.get(ppHistoryContent, "bottomAvgRent.value") || _.get(
              _.find(_.get(stock, "askingRent.details", []), [
                "type",
                "average_bottom",
              ]),
              "value",
              0,
            ),
            isShow: _.get(ppHistoryContent, "bottomAvgRent.isShow") || false,
          }
        ],
        bottomTotalRent: [
          {
            value: _.get(ppHistoryContent, "bottomTotalRent.value") || _.get(
              _.find(_.get(stock, "askingRent.details", []), [
                "type",
                "total_bottom",
              ]),
              "value",
              0,
            ),
            isShow: _.get(ppHistoryContent, "bottomTotalRent.isShow") || false,
          }
        ],
        stockType: [
          {
            value: _.get(ppHistoryContent, `stockType.value.${langKey(intl.locale, "name")}`) || _.get(stock, `stockType.${langKey(intl.locale, "name")}`),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "stockType.isShow"), true),
          },
        ],
        floor: [
          {
            value: _.get(ppHistoryContent, `floor.value`) || floor,
            isShow: ppHistoryContent ? getNotNullIsShow(_.get(ppHistoryContent, `floor.isShow`), true) : true,
          },
        ],
        unit: [
          {
            value: _.get(ppHistoryContent, `unit.value`) || _.get(stock, "unit", ""),
            isShow: ppHistoryContent ? getNotNullIsShow(_.get(ppHistoryContent, `unit.isShow`), false) : true,
          },
        ],
        customAddressZh: _.get(ppHistoryContent, "customAddressZh") || getFullAddress({ ...intl, locale: "zh" }, stock),
        customAddressEn: _.get(ppHistoryContent, "customAddressEn") || getFullAddress({ ...intl, locale: "en" }, stock),
        customBuilding: {
          value: _.get(ppHistoryContent, `customBuilding.value`) || _.get(
            stock,
            `building.${langKey(intl.locale, "name")}`,
            "---",
          ),
          isShow: getNotNullIsShow(_.get(ppHistoryContent, `customBuilding.isShow`), true),
        },
        customStreet: _.get(ppHistoryContent, `customStreet`) || _.get(
          stock,
          `building.street.street.${langKey(intl.locale, "name")}`,
          "",
        ),
        customStreetNo: _.get(ppHistoryContent, `customStreetNo`) || _.get(stock, "building.street.number", ""),
        customDistrict: _.get(ppHistoryContent, `customDistrict`) || _.get(
          stock,
          `building.district.${langKey(intl.locale, "name")}`,
          "---",
        ),
        areaEfficiency: [
          {
            value: _.get(ppHistoryContent, `areaEfficiency.value`) || _.get(stock, "area.efficiency", 0),
            isShow: _.get(ppHistoryContent, `areaEfficiency.isShow`) || false,
          },
        ],
        areaGross,
        areaNet,
        areaSaleable,
        areaLettable,
        possession: [
          {
            value: _.get(ppHistoryContent?.possession?.value, "nameEn") || _.get(stock, `possession.nameEn`, ""),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "possession.isShow"), !!_.get(stock, `possession.nameEn`)),
          },
        ],
        ceilingHeight: [
          {
            ft: _.get(ppHistoryContent, "ceilingHeight.ft") || _.get(stock, "ceilingHeight.ft") || "",
            in: _.get(ppHistoryContent, "ceilingHeight.in") || _.get(stock, "ceilingHeight.in") || "",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "ceilingHeight.isShow"), true),
          },
        ],
        managementFee: [
          {
            value: _.get(ppHistoryContent, "managementFee.value") || managementFeeTemp,
            unit: managementFeeUnit,
            paidBy: _.get(ppHistoryContent, "managementFee.paidBy") ||
              (_.indexOf(possiblePaidBy, _.get(stock, "managementFee.paidBy")) >
                -1
                ? _.get(stock, "managementFee.paidBy", "")
                : ""),
            paidByIsShow: ppHistoryContent ? _.get(ppHistoryContent, "managementFee.paidByIsShow", _.get(ppHistoryContent, `includedFee.managementFee`)) || false
              : _.get(stock, "managementFee.paidBy", "") === "Paid By Landlord",
            isShow: ppHistoryContent ? getNotNullIsShow(_.get(ppHistoryContent, `managementFee.isShow`), false) : !!managementFeeTemp,
            totalMonthlyMgtFee: _.get(ppHistoryContent, "managementFee.totalMonthlyMgtFee", Number.isInteger(totalMonthlyMgtFee) ? totalMonthlyMgtFee : Math.round(totalMonthlyMgtFee + 0.5)),
            totalMonthlyMgtFeeIsShow: getNotNullIsShow(_.get(ppHistoryContent, `managementFee.totalMonthlyMgtFeeIsShow`),
              _.get(stock, "managementFee.totalMonthlyMgtFeeIsShow", false) || false),
          },
        ],
        gRent: [
          {
            value: _.get(ppHistoryContent, "gRent.value") || _.get(stock, "governmentRent.number") || 0,
            paidBy: _.get(ppHistoryContent, "gRent.paidBy") || _.get(stock, "governmentRent.paidBy") || "",
            paidByIsShow: getNotNullIsShow(_.get(ppHistoryContent, "gRent.paidByIsShow"),
              _.get(ppHistoryContent, `includedFee.gRent`, _.get(stock, "governmentRent.paidBy", "") === "Paid By Landlord")),
            unit: _.get(ppHistoryContent, "gRent.unit") || _.get(stock, "governmentRent.type") || "",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "gRent.isShow", !!_.get(stock, "governmentRent.number") || false)),
          }
        ],
        rates: [
          {
            value: _.get(ppHistoryContent, "rates.value", _.get(stock, "rates.number") || 0),
            paidBy: _.get(ppHistoryContent, "rates.paidBy", _.get(stock, "rates.paidBy")) || "",
            paidByIsShow: getNotNullIsShow(_.get(ppHistoryContent, "rates.paidByIsShow"),
              _.get(ppHistoryContent, `includedFee.rates`, _.get(stock, "rates.paidBy", "") === "Paid By Landlord")),
            unit: _.get(ppHistoryContent, "rates.unit") || _.get(stock, "rates.type") || "",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "rates.isShow"), false),
          }
        ],
        acFee: [
          {
            value: _.get(ppHistoryContent, "acFee.value", _.get(stock, "airConditioningFee.number") || 0),
            paidBy: _.get(ppHistoryContent, "acFee.paidBy") ||
              _.get(stock, "airConditioningFee.paidBy") || "",
            paidByIsShow: getNotNullIsShow(_.get(ppHistoryContent, "acFee.paidByIsShow"),
              _.get(ppHistoryContent, `includedFee.acFee`, _.get(stock, "airConditioningFee.paidBy", "") === "Paid By Landlord")),
            unit: _.get(ppHistoryContent, "acFee.unit") || _.get(stock, "airConditioningFee.type") || "",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "acFee.isShow"), !!_.get(stock, "airCond.fee", false)),
          }
        ],
        currentState: [
          {
            value: _.get(ppHistoryContent, "currentState.value.nameEn") || _.get(stock, `currentState.nameEn`, ""),
            isShow: _.get(ppHistoryContent, "currentState.isShow") || false,
          }
        ],
        areaTerrace: [
          {
            value: _.get(ppHistoryContent, "areaTerrace.value", _.get(stock, "area.terrace")),
            isShow: _.get(ppHistoryContent, "areaTerrace.isShow") || false,
          }
        ],
        areaRoof: [
          {
            value: _.get(ppHistoryContent, "areaRoof.value", _.get(stock, "area.roof")),
            isShow: _.get(ppHistoryContent, "areaRoof.isShow") || false,
          }
        ],
        floorLoading: [
          {
            value: _.get(ppHistoryContent, "floorLoading.value", _.get(stock, "floorLoading")),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "floorLoading.isShow", true)),
          }
        ],
        decoration: [
          {
            value: _.get(ppHistoryContent, "decoration.value.nameEn", _.get(stock, `decoration.nameEn`)),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "decoration.isShow"), !!_.get(stock, `decoration.nameEn`)),
          },
        ],
        unitView: [
          {
            value: _.get(ppHistoryContent, "unitView.value.nameEn", _.get(stock, `unitView.nameEn`)),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "unitView.isShow"), !!_.get(stock, `unitView.nameEn`)),
          },
        ],
        availability: [
          {
            value: _.get(ppHistoryContent, "availability.value", _.get(stock, "availability")),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "availability.isShow"), !!_.get(stock, "availability")),
          },
        ],
        remarks: _.get(ppHistoryContent, "remarks") || "",
        yield: [
          {
            value: _.get(ppHistoryContent, "yield.value", _.get(stock, "yield") || 0),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "yield.isShow"), !!_.get(stock, "yield")),
          },
        ],
        usage: [
          {
            value: _.get(ppHistoryContent, `building.usage.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.buildingUsage.${langKey(intl.locale, "name")}`,
              ) || "---",
            isShow: _.get(ppHistoryContent, "building.usage.isShow") || false,//!!_.get(
            //   stock,
            //   `building.buildingUsage.${langKey(intl.locale, "name")}`,
            // ),
          },
        ],
        title: [
          {
            value: _.get(ppHistoryContent, `building.title.value.${langKey(intl.locale)}`) ||
              _.get(
                _.get(titleMapping, _.get(stock, "building.title", ""), {}),
                langKey(intl.locale, "name"),
              ) || "---",
            isShow: _.get(ppHistoryContent, "building.title.isShow") || false,
          },
        ],
        inTakeDate: [
          {
            value: _.get(ppHistoryContent, "building.inTakeDate.value") ||
              _.defaultTo(_.get(stock, "building.completionDate"), "---"),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "building.inTakeDate.isShow"), true), //!!_.get(stock, "building.completionDate"),
          },
        ],
        managementCompany: [
          {
            value: _.get(ppHistoryContent, `building.managementCompany.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.managementCompany.managementCompany.${langKey(
                  intl.locale,
                  "name",
                )}`,
              ) || "---",
            isShow: _.get(ppHistoryContent, "building.managementCompany.isShow") || false,
          },
        ],
        transport: [
          {
            value: _.get(ppHistoryContent, `building.transport.value.${langKey(intl.locale)}`) ||
              _.get(stock, `building.${langKey(intl.locale, "transport")}`) ||
              "---",
            isShow: _.get(ppHistoryContent, "building.transport.isShow") || false,
          },
        ],
        includedFee: [
          {
            managementFee: getNotNullIsShow(_.get(ppHistoryContent, "includedFee.managementFee"),
              _.get(stock, "managementFee.paidBy", "") === "Paid By Landlord"),
            rates: getNotNullIsShow(_.get(ppHistoryContent, "includedFee.rates"),
              _.get(stock, "rates.paidBy", "") === "Paid By Landlord"),
            gRent: getNotNullIsShow(_.get(ppHistoryContent, "includedFee.gRent"),
              _.get(stock, "governmentRent.paidBy", "") === "Paid By Landlord"),
            acFee: getNotNullIsShow(_.get(ppHistoryContent, "includedFee.acFee"),
              _.get(stock, "airConditioningFee.paidBy", "") === "Paid By Landlord"),
          },
        ],
        allInclusive: getNotNullIsShow(_.get(ppHistoryContent, "allInclusive"), _.every(
          [
            _.get(stock, "managementFee.paidBy", "") === "Paid By Landlord",
            _.get(stock, "rates.paidBy", "") === "Paid By Landlord",
            _.get(stock, "governmentRent.paidBy", "") === "Paid By Landlord",
          ],
          Boolean,
        )),
        passengerLift: [
          {
            value: _.get(ppHistoryContent, `building.passengerLift.value.${langKey(intl.locale)}`) ||
              _.get(
                _.find(_.get(stock, "building.lifts"), {
                  type: "Passenger",
                }),
                "quantity",
              ) || "---",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "building.passengerLift.isShow"), true),
          },
        ],
        cargoLift: [
          {
            value: _.get(ppHistoryContent, `building.cargoLift.value.${langKey(intl.locale)}`) ||
              _.get(
                _.find(_.get(stock, "building.lifts"), {
                  type: "Cargo",
                }),
                "quantity",
              ) || "---",
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "building.cargoLift.isShow"), true),
          },
        ],
        containers: _.get(ppHistoryContent, "building.containers", containers),
        airConditioningType: [
          {
            value: _.get(ppHistoryContent, `building.airConditioningType.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.airConditioning.type.${langKey(intl.locale, "name")}`,
              ) || "---",
            isShow: _.get(ppHistoryContent, "building.airConditioningType.isShow") || false,
          },
        ],
        airConditioningOpeningTime: [
          {
            value: _.get(ppHistoryContent, `building.airConditioningOpeningTime.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.airConditioning.openingTime.${langKey(intl.locale, "name")}`,
              ) || "---",
            isShow: _.get(ppHistoryContent, "building.airConditioningOpeningTime.isShow") || false,
          },
        ],
        airConditioningExtraCharges: [
          {
            value: _.get(ppHistoryContent, `building.airConditioningExtraCharges.value.${langKey(intl.locale)}`) ||
              _.get(
                stock,
                `building.airConditioning.extraCharges.${langKey(intl.locale, "name")}`,
              ) || "---",
            isShow: _.get(ppHistoryContent, "building.airConditioningExtraCharges.isShow") || false,
          },
        ],
        currentTenants,
        floorType: getOriginalfloorTypeEnValue(_.get(ppHistoryContent, `floorType.${langKey('en')}`, floorType)),
      },
      general: {
        type,
        multiImg: ppHistoryContent ? _.get(ppHistoryContent, "multiImg") : "FOUR",
        showEmployeePhoto: [
          {
            value: intl.formatMessage({
              id: "proposal.form.showemployeephoto",
            }),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "showEmployeePhoto"), true),
          },
        ],
        showContact: [
          {
            value: intl.formatMessage({
              id: "proposal.form.showcontact",
            }),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "showContact"), true),
          },
        ],
        exactFloor: [
          {
            value: intl.formatMessage({
              id: "proposal.form.exactFloor",
            }),
            isShow: getNotNullIsShow(_.get(ppHistoryContent, "exactFloor"), true),
          },
        ],
        showUnit: [
          {
            value: intl.formatMessage({
              id: `proposal.form.showUnit`,
            }),
            isShow: true,
          },
        ],
        proposalName: _.get(ppHistoryContent, "proposalName") || getProposalName(intl, stock, floorType),
        companyTitle: _.get(ppHistoryContent, "companyTitle") || "midlandici",
        lang: _.get(ppHistoryContent, "lang") || "CHI",
        customTitle: {
          value: _.get(ppHistoryContent, "customTitle.value") || "",
          isShow: _.get(ppHistoryContent, "customTitle.isShow") || false,
        },
        termRemarks,
      },
      media: {
        stockMedia: !ppHistoryContent ? [] : _.map(_.get(ppHistoryContent, "photos", []), (p) => _.get(p, "id")) || []
          .concat(media?.photo || [], media?.video || [])
          ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
          ?.map((v) => v.id),
        buildingMedia: !ppHistoryContent ? [] : []
          .concat(buildingMedia?.photo || [], buildingMedia?.video || [])
          ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
          ?.map((v) => v.id),
        googleMapPhoto: ppHistoryContent && _.get(ppHistoryContent, "googleMap.isPP", false) ? ["map"] : [],
        selectedMedia: ppHistoryContent && _.get(ppHistoryContent, "googleMap.isPP", false) ? ["map"] : [],
        mainPhoto: _.get(ppHistoryContent, "mainPhoto.id") || _.get(mainPhoto, "id", "map"),
        main1Photo: _.get(ppHistoryContent, "main1Photo.id") || "",
        lat: _.get(ppHistoryContent, "lat", _.get(stock, "building.coordinates.latitude")),
        lng: _.get(ppHistoryContent, "lng", _.get(stock, "building.coordinates.longitude")),
        govLat: _.get(ppHistoryContent, "govLat", _.get(stock, "building.coordinates.latitude")),
        govLng: _.get(ppHistoryContent, "govLng", _.get(stock, "building.coordinates.longitude")),
        attachmentMultiImgConfig: _.reduce(_.get(ppHistoryContent, "photos", []), (result, photo) => {
          if (!photo?.id || (photo.id === "map" && !_.get(ppHistoryContent, "googleMap.isPP", false))) {
            return result;
          }
          result[photo.id] = photo.multiImg;
          return result;
        }, {}),
      },
    };
    return form;
  };

  // const formatMediaFormValue = (mediaValue) => {
  //   // handle media state...
  //   const stockMediaData = _.get(
  //     media.find((m) => m.id === _.get(stock, "unicorn.id").toString()),
  //     "data",
  //     {},
  //   );
  //   const buildingMediaData = _.get(
  //     buildingMedia.find(
  //       (m) => m.id === _.get(stock, "building.unicorn.id", "").toString(),
  //     ),
  //     "data",
  //     {},
  //   );
  //   const mainPhoto =
  //     // stockMediaData?.photo?.find((m) => m.tags && m.tags.includes("main")) ||
  //     buildingMediaData?.photo?.find(
  //       (m) => m.tags && m.tags.includes("main"),
  //     ) || null;

  //   return {
  //     googleMap: {
  //       isPP: !!(_.get(mediaValue, "googleMapPhoto", []).length > 0),
  //       isMain1: _.get(mediaValue, "main1Photo") === "map",
  //       isMain2: false,
  //     },
  //     photos: _.concat(
  //       [],
  //       _.map(_.get(mediaValue, "stockMedia", []), (id) =>
  //         parseMedia(stockMediaData.photo, id),
  //       ),
  //       _.map(_.get(mediaValue, "buildingMedia", []), (id) =>
  //         parseMedia(buildingMediaData.photo, id),
  //       ),
  //     ),
  //     main1Photo:
  //       _.get(mediaValue, "main1Photo") !== "map"
  //         ? parseMedia(
  //             _.concat([], stockMediaData.photo, buildingMediaData.photo),
  //             _.get(mediaValue, "main1Photo"),
  //           )
  //         : null,
  //     mainPhoto: mainPhoto
  //       ? _.pick(mainPhoto, ["id", "mediumRoot", "photoContent"])
  //       : null,
  //     videos: [],
  //     lat: mediaValue.lat,
  //     lng: mediaValue.lng,
  //   };
  // };

  // const handleSubmit = (values) => {
  //   const { stock: stockValue, media: mediaValue, general } = values;

  //   const containers = getContainers(stock);

  //   // handle stock states ...
  //   const stockVariables = {
  //     ...stockValue,
  //     ...general,
  //     stockMongoId: _.get(stock, "_id"),
  //     floor: _.get(stockValue, "floor[0]"),
  //     unit: _.get(stockValue, "unit[0]"),
  //     avgPrice: {
  //       ..._.get(stockValue, "avgPrice[0]"),
  //       value: _.get(stockValue, "avgPrice[0].value", 0),
  //     },
  //     totalPrice: {
  //       ..._.get(stockValue, "totalPrice[0]"),
  //       value: _.get(stockValue, "totalPrice[0].value", 0),
  //     },
  //     avgRent: {
  //       ..._.get(stockValue, "avgRent[0]"),
  //       value: _.get(stockValue, "avgRent[0].value", 0),
  //     },
  //     totalRent: {
  //       ..._.get(stockValue, "totalRent[0]"),
  //       value: _.get(stockValue, "totalRent[0].value", 0),
  //     },
  //     areaEfficiency: _.get(stockValue, "areaEfficiency[0]"),
  //     areaGross: _.get(stockValue, "areaGross[0]"),
  //     areaNet: _.get(stockValue, "areaNet[0]"),
  //     areaSaleable: _.get(stockValue, "areaSaleable[0]"),
  //     areaLettable: _.get(stockValue, "areaLettable[0]"),
  //     possession: {
  //       value: _.defaultTo(
  //         findZhEnFromOptions(
  //           stockPossessions,
  //           _.get(stockValue, "possession[0].value"),
  //         ),
  //         null,
  //       ),
  //       isShow: _.get(stockValue, "possession[0].isShow"),
  //     },
  //     decoration: {
  //       value: _.defaultTo(
  //         findZhEnFromOptions(
  //           stockDecorations,
  //           _.get(stockValue, "decoration[0].value"),
  //         ),
  //         null,
  //       ),
  //       isShow: _.get(stockValue, "decoration[0].isShow"),
  //     },
  //     unitView: {
  //       value: _.defaultTo(
  //         findZhEnFromOptions(
  //           stockUnitViews,
  //           _.get(stockValue, "unitView[0].value"),
  //         ),
  //         null,
  //       ),
  //       isShow: _.get(stockValue, "unitView[0].isShow"),
  //     },
  //     usage: {
  //       ..._.get(stockValue, "usage[0]", {}),
  //       value: {
  //         nameZh: _.get(stock, "building.buildingUsage.nameZh", ""),
  //         nameEn: _.get(stock, "building.buildingUsage.nameEn", ""),
  //       },
  //     },
  //     title: {
  //       ..._.get(stockValue, "title[0]", {}),
  //       value: {
  //         nameZh: _.get(
  //           _.get(titleMapping, _.get(stock, "building.title", ""), {}),
  //           "nameZh",
  //           "---",
  //         ),
  //         nameEn: _.get(
  //           _.get(titleMapping, _.get(stock, "building.title", ""), {}),
  //           "nameEn",
  //           "---",
  //         ),
  //       },
  //     },
  //     managementFee: _.get(stockValue, "managementFee[0]"),
  //     availability: _.get(stockValue, "availability[0]"),
  //     managementCompany: {
  //       ..._.get(stockValue, "managementCompany[0]", {}),
  //       value: {
  //         nameZh: _.get(
  //           stock,
  //           "building.managementCompany.managementCompany.nameZh",
  //           "---",
  //         ),
  //         nameEn: _.get(
  //           stock,
  //           "building.managementCompany.managementCompany.nameEn",
  //           "---",
  //         ),
  //       },
  //     },
  //     transport: {
  //       ..._.get(stockValue, "transport[0]", {}),
  //       value: {
  //         nameZh: _.get(stock, `building.transportZh`, "---"),
  //         nameEn: _.get(stock, `building.transportEn`, "---"),
  //       },
  //     },
  //     passengerLift: {
  //       ..._.get(stockValue, "passengerLift[0]", {}),
  //       value: {
  //         nameZh: _.get(stockValue, "passengerLift[0].value", "---"),
  //         nameEn: _.get(stockValue, "passengerLift[0].value", "---"),
  //       },
  //     },
  //     cargoLift: {
  //       ..._.get(stockValue, "cargoLift[0]", {}),
  //       value: {
  //         nameZh: _.get(stockValue, "cargoLift[0].value") || "---",
  //         nameEn: _.get(stockValue, "cargoLift[0].value") || "---",
  //       },
  //     },
  //     containers: _(_.get(stockValue, "containers", [])).map((v, i) => ({
  //       ...v,
  //       value: containers[i],
  //     })),
  //     airConditioningType: {
  //       ..._.get(stockValue, "airConditioningType[0]", {}),
  //       value: {
  //         nameZh: _.get(stock, "building.airConditioning.type.nameZh", "---"),
  //         nameEn: _.get(stock, "building.airConditioning.type.nameEn", "---"),
  //       },
  //     },
  //     currentTenants: stockValue?.currentTenants?.map((v) => ({
  //       tenant: {
  //         ...(_.get(v, "tenant") || {}),
  //         isShow: v.tenantIsShow,
  //       },
  //       rentalFee: {
  //         value: v.rentalFee || 0,
  //         isShow: v.rentalFeeIsShow,
  //       },
  //       period: {
  //         ...(_.get(v, "period") || {}),
  //         isShow: v.periodIsShow,
  //       },
  //       tenancy: {
  //         ...(_.get(v, "tenancy") || {}),
  //         isShow: v.tenancyIsShow,
  //       },
  //     })),
  //     stockType: {
  //       ..._.get(stockValue, "stockType[0]"),
  //       value: {
  //         ..._.pick(_.get(stock, "stockType"), ["nameEn", "nameZh"]),
  //       },
  //     },
  //     ceilingHeight: {
  //       isShow: _.get(stockValue, "ceilingHeight[0].isShow") || false,
  //       ft: parseInt(_.get(stockValue, "ceilingHeight[0].ft", 0), 10) || 0,
  //       in: parseInt(_.get(stockValue, "ceilingHeight[0].in", 0), 10) || 0,
  //     },
  //     includedFee: _.get(stockValue, "includedFee[0]"),
  //     inTakeDate: _.get(stockValue, "inTakeDate[0]"),
  //     // yield: _.get(stockValue, "yield[0]"),
  //     yield: {
  //       ..._.get(stockValue, "yield[0]", {}),
  //       value: parseFloat(_.get(stockValue, "yield[0].value") || "0"),
  //     },
  //     hideEmployeePhoto: _.get(general, "hideEmployeePhoto[0].isShow"),
  //     hideContact: _.get(general, "hideContact[0].isShow"),
  //     floorType: getFloorTypeInfoFromLangFile(langFile, stockValue?.floorType),
  //     districtNameZh: _.get(stock, "building.district.nameZh", ""),
  //     districtNameEn: _.get(stock, "building.district.nameEn", ""),
  //     streetNameZh: _.get(stock, "building.street.street.nameZh", ""),
  //     streetNameEn: _.get(stock, "building.street.street.nameEn", ""),
  //     streetNo: _.get(stock, "building.street.number", ""),
  //     buildingNameZh: _.get(stock, "building.nameZh", ""),
  //     buildingNameEn: _.get(stock, "building.nameEn", ""),
  //     buildingDistrictNameZh: _.get(stock, "building.district.nameZh", ""),
  //     buildingDistrictNameEn: _.get(stock, "building.district.nameEn", ""),
  //     isSoleagent: !!(
  //       stock.soleagent &&
  //       stock.soleagent.periodStart != null &&
  //       stock.soleagent.periodEnd != null
  //     ),
  //     isBuildingFieldsAllHide: !(
  //       _.get(stockValue, "usage[0].isShow") ||
  //       _.get(stockValue, "title[0].isShow") ||
  //       _.get(stockValue, "inTakeDate[0].isShow") ||
  //       _.get(stockValue, "managementCompany[0].isShow") ||
  //       _.get(stockValue, "transport[0].isShow") ||
  //       _.get(stockValue, "passengerLift[0].isShow") ||
  //       _.get(stockValue, "cargoLift[0].isShow") ||
  //       _.get(stockValue, "airConditioningType[0].isShow")
  //     ),
  //     sbu,
  //   };

  //   const mediaVariables = formatMediaFormValue(mediaValue);

  //   createProposal({ ...stockVariables, ...mediaVariables });
  // };

  return (
    <ProposalForm
      onSubmit={createProposal}
      stockId={_.get(stock, "unicorn.id")}
      buildingId={_.get(stock, "building.unicorn.id")}
      initialValues={initializeFormState()}
      FormComponent={FieldSection}
      createDialogOpen={createDialogOpen}
      closeDialog={closeDialog}
    />
  );
}

Individual.propTypes = {
  detail: PropTypes.array.isRequired,
  currentStock: PropTypes.string.isRequired,
  stockUnitViews: PropTypes.array.isRequired,
  stockDecorations: PropTypes.array.isRequired,
  stockPossessions: PropTypes.array.isRequired,
  stockCurrentStates: PropTypes.array.isRequired,
  media: PropTypes.array.isRequired,
  buildingMedia: PropTypes.array.isRequired,
  createDialogOpen: PropTypes.bool.isRequired,
  closeDialog: PropTypes.func.isRequired,
  createProposal: PropTypes.func.isRequired,
  clearCreateProposal: PropTypes.func.isRequired,
  intl: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  detail: _.get(state, "stock.detail") || [],
  currentStock: _.get(state, "stock.currentDetail") || "",
  stockUnitViews: _.get(state, "stock.unitViews") || [],
  stockDecorations: _.get(state, "stock.decorations", []),
  stockPossessions: _.get(state, "stock.possessions", []),
  stockCurrentStates: _.get(state, "stock.currentStates", []),
  media: _.get(state, "stock.media", []),
  buildingMedia: _.get(state, "building.media", []),
  reCreatePPStocks: _.get(state, "proposal.reCreatePPStocks", []),
});

const mapDispatchToProps = (dispatch) => ({
  createProposal: () => dispatch(createProposal()),
  clearCreateProposal: () => dispatch(clearCreateProposal()),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(Individual));
