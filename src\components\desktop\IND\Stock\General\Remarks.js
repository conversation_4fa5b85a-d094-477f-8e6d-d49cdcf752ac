import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import FieldVal from "../../../../common/FieldVal";
import { injectIntl } from "react-intl";
import { getLangKey } from "../../../../../helper/generalHelper";

const styles = theme => ({
  root: {
    padding: "1vh 0"
  },
  gridContent: {
    padding: "1vw 2vw"
  }
});

class Remarks extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langKey = getLangKey(intl);

    let remarkMapping = {
      ceilingHeight: intl.formatMessage({
        id: "stock.ceilingheight"
      }),
      entranceWidth: intl.formatMessage({
        id: "stock.entrancewidth"
      }),
      airConditioningType: intl.formatMessage({
        id: "stock.airconditioningtype"
      }),
      floorLoading: intl.formatMessage({
        id: "stock.floorloading"
      }),
    };
    const remarks = detail.remarks || {};
    // some remarks are not in detail.remarks
    const ceilingHeightFt = detail.ceilingHeight && detail.ceilingHeight.ft ? detail.ceilingHeight.ft + "'" : "";
    const ceilingHeightIn = detail.ceilingHeight && detail.ceilingHeight.in ? detail.ceilingHeight.in + "\"" : "";
    const entranceWidthFt = detail.entranceWidth && detail.entranceWidth.ft ? detail.entranceWidth.ft + "'" : "";
    const entranceWidthIn = detail.entranceWidth && detail.entranceWidth.in ? detail.entranceWidth.in + "\"" : "";
    const extraRemarks = {
      ceilingHeight: ceilingHeightFt && ceilingHeightIn ? ceilingHeightFt + " " + ceilingHeightIn : ceilingHeightFt || ceilingHeightIn || "---",
      entranceWidth: entranceWidthFt && entranceWidthIn ? entranceWidthFt + " " + entranceWidthIn : entranceWidthFt || entranceWidthIn || "---",
      airConditioningType: detail.airConditioningType && detail.airConditioningType[langKey] ? detail.airConditioningType[langKey] : "---",
      floorLoading: detail.floorLoading ? detail.floorLoading + " " + intl.formatMessage({ id: "common.lbpersqft" }) : "---",
    };

    return (
      <div className={classes.root}>
        <DetailBoxSection
          text={intl.formatMessage({
            id: "stock.remarks"
          })}
          expandable={true}
        >
          <Grid container spacing={2} className={classes.gridContent}>
            {Object.keys(remarkMapping).map((v, i) => (
              <Grid item xs={6} key={v}>
                <FieldVal field={remarkMapping[v]}>
                  {remarks[v] || extraRemarks[v] || "---"}
                </FieldVal>
              </Grid>
            ))}
          </Grid>
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(Remarks));
