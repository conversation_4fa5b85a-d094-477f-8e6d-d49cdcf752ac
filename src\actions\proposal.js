import { getFormValues } from "redux-form";
import _ from "lodash";

import {
  parseFormToListPP,
  parseFormToProposal,
} from "@/components/Saleskit/helpers";
import {
  CLEAR_CREATE_PROPOSAL,
  CREATE_PROPOSAL_START,
  CREATE_PROPOSAL_SUCCESS,
  CREATE_PROPOSAL_ERROR,
  CLEAR_PROPOSALS,
  LIST_PROPOSALS_START,
  LIST_MORE_PROPOSALS_START,
  LIST_PROPOSALS_SUCCESS,
  LIST_PROPOSALS_NULL_SUCCESS,
  LIST_PROPOSALS_ERROR,
  REMOVE_PROPOSAL_START,
  REMOVE_PROPOSAL_SUCCESS,
  REMOVE_PROPOSAL_ERROR,
  GET_PROPOSALCOUNT_SUCCESS,
  GET_PROPOSALCOUNT_ERROR,
  CREATE_PREVIEW_ERROR,
  CLEAR_CREATED_PP_PREVIEW,
  CREATE_PP_PREVIEW_SUCCESS,
} from "../constants/proposal";
import { link, sbu } from "@/config";
import { getDisplayStockId } from "../helper/generalHelper";
import { gtagHandler, addActivityLog } from "./log";

export const clearCreatedPPPerview = () => ({
  type: CLEAR_CREATED_PP_PREVIEW,
});

export const createPPPreviewSuccess = (perviewUrl, width, height) => ({
  type: CREATE_PP_PREVIEW_SUCCESS,
  payload: {
    perviewUrl: perviewUrl,
    width: width,
    height: height,
  },
});

export const clearCreateProposal = () => ({
  type: CLEAR_CREATE_PROPOSAL,
});

export const createProposal =
  () =>
  async (dispatch, getState, { fetch, universalRequest, takeLog }) => {
    const state = await getState();
    const form = getFormValues("proposal")(state);

    dispatch({
      type: CREATE_PROPOSAL_START,
    });

    try {
      const proposal = parseFormToProposal(form, {
        ..._.pick(_.get(state, "stock"), [
          "possessions",
          "unitViews",
          "decorations",
          "media",
          "currentStates",
          "currentDetail",
        ]),
        stocks: _.get(state, "stock.detail") || [],
        buildingMedia: _.get(state, "building.media") || [],
        streetMedia: _.get(state, "street.media") || [],
      });

      const { data, errors } = await universalRequest("/createProposal", {
        method: "POST",
        headers: {
          "content-type": "application/json",
          "CAS-Authorization": localStorage.getItem("casAccessToken"), //getState().auth.user.casAccessToken,
        },
        body: JSON.stringify({ proposal }),
        // credentials: 'include',
      });

      dispatch(addActivityLog("proposal", "create", proposal));

      if (errors) throw new Error(errors[0].message);

      // take log for creating pdf successfully
      const msg = JSON.stringify({
        action: "create-proposal",
        status: "success",
        parameters: proposal,
      });
      await takeLog(state.auth.user.login.info.emp_id, msg, fetch, true);
      // window.dataLayer.push({ firstLoginSuccess: false });

      dispatch(gtagHandler("Create Proposal", {
        StockID: getDisplayStockId(_.get(proposal, "stockId")),
      }));

      dispatch({
        type: CREATE_PROPOSAL_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: CREATE_PROPOSAL_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };

export const createPreview =
  (type) =>
  async (dispatch, getState, { universalRequest }) => {
    const state = await getState();
    const form = getFormValues("proposal")(state);

    try {
      const parseFormFunc =
        type === "proposal" ? parseFormToProposal : parseFormToListPP;

      const postData = {
        empId: _.get(state, "auth.user.login.info.emp_id"),
        type,
        [type]: parseFormFunc(form, {
          ..._.pick(_.get(state, "stock"), [
            "possessions",
            "unitViews",
            "decorations",
            "media",
            "currentStates",
            "currentDetail",
          ]),
          stocks: _.get(state, "stock.detail") || [],
          buildingMedia: _.get(state, "building.media") || [],
          streetMedia: _.get(state, "street.media") || [],
        }),
      }

      const { data, errors } = await universalRequest("/createPreview", {
        method: "POST",
        headers: {
          "content-type": "application/json",
        },
        body: JSON.stringify({
          ...postData
        }),
      });

      dispatch(addActivityLog("proposal.preview", "read", postData));

      if (errors) throw new Error(errors[0].message);
      if (_.isNil(_.get(data, "createPreview")))
        throw new Error("Failed to create preview.");
      // const isIndividualPP = type === "proposal" || type === "offerLetter" || _.get(state, "proposal.form.general.mode", "indv") === "indv";
      // dispatch(
      //   createPPPreviewSuccess(
      //     `${link.previewProposal}/${_.get(data, "createPreview")}`,
      //     //`${link.previewPDFProposal}/${_.get(data, "createPreview")}?mode=${type}`,
      //     isIndividualPP ? 1020 : 1438,
      //     isIndividualPP ? 900 : 900
      //   ),
      // );
      // window
      //   .open(
      //     `${link.previewProposal}/${_.get(data, "createPreview")}`,
      //     // "targetWindow",
      //     // `status=no,resizable=yes,${
      //     //   isIndividualPP ? "width=1020,height=1414" : "width=1434,height=1000"
      //     // }`,
      //   )
      //   .focus();
      const previewUrl = `${link.previewProposal}/${_.get(data, "createPreview")}`;
      const a = document.createElement('a');
      a.href = previewUrl;
      a.target = '_blank';
      a.rel = 'noopener noreferrer';
      a.click();
    } catch (error) {
      dispatch({
        type: CREATE_PREVIEW_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };

export const clearProposals = () => ({
  type: CLEAR_PROPOSALS,
});

export const listProposals =
  (variables, isFetchingMore) =>
  async (dispatch, getState, { fetch, universalRequest, delay }) => {
    if (isFetchingMore) {
      dispatch({
        type: LIST_MORE_PROPOSALS_START,
        payload: {
          variables,
        },
      });
    } else {
      dispatch({
        type: LIST_PROPOSALS_START,
        payload: {
          variables,
        },
        checkrefreshToken: true,
      });
    }

    dispatch(addActivityLog("proposal.search", "read", { ...variables }));

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const payload = JSON.stringify({ variables });
      const { data, errors } = await universalRequest("/listProposal", {
        method: "POST",
        headers: {
          "content-type": "application/json",
        },
        body: payload,
        // credentials: 'include',
      });

      if (data.proposals && data.proposals.length > 0) {
        dispatch({
          type: LIST_PROPOSALS_SUCCESS,
          payload: {
            data,
          },
        });
      } else {
        dispatch({
          type: LIST_PROPOSALS_NULL_SUCCESS,
          payload: {
            data,
          },
        });
      }
    } catch (error) {
      dispatch({
        type: LIST_PROPOSALS_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };

export const getProposalCount =
  () =>
  async (dispatch, getState, { universalRequest }) => {
    const { emp_id } = getState().auth.user.login.info;
    try {
      const data = await universalRequest("/getProposalCount", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ emp_id }),
      });

      dispatch({
        type: GET_PROPOSALCOUNT_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: GET_PROPOSALCOUNT_ERROR,
        payload: { error: error.message },
      });
      // throw error;
    }
  };

export const removeProposal =
  (variables) =>
  async (dispatch, getState, { fetch, universalRequest, delay }) => {
    dispatch({
      type: REMOVE_PROPOSAL_START,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const payload = JSON.stringify({ variables });
      const { data, errors } = await universalRequest("/removeProposal", {
        method: "POST",
        headers: {
          "content-type": "application/json",
        },
        body: payload,
        // credentials: 'include',
      });

      if (errors) throw new Error(errors[0].message);
      dispatch({
        type: REMOVE_PROPOSAL_SUCCESS,
      });
    } catch (error) {
      dispatch({
        type: REMOVE_PROPOSAL_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };
