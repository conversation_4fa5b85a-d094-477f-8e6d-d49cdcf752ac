import React from "react";
import { withStyles } from "@material-ui/styles";
import clsx from "clsx";
import Checkbox from "@material-ui/core/Checkbox";

const styles = {
  wrapper: {
    position: "relative",
  },
  checkbox: {
    display: "block",
    "&.Mui-checked:not(.Mui-disabled)": {
      color: "#13CE66",
    },
  },
  bk: {
    padding: 9,
    position: props => props.bottomCheckbox ? "inherit" : "absolute",
    "& div": {
      width: 18,
      height: 18,
      borderRadius: 3,
      backgroundColor: "#FFF",
      margin: 3,
    },
  },
  nopadding: {
    padding: 0,
  },
};

const ShowHiddenCheckBox = (props) => {
  const {
    classes,
    className,
    jsonField = "isShow",
    aligntoLabel = false,
    changeFieldValue,
    bottomCheckbox = false,
    ...custom
  } = props;
  const { value, name, onChange, onBlur, ...otherProps } = props.input;

  const handleonChange = (event) => {
    // event.preventDefault();
    changeFieldValue(name, { ...value, [jsonField]: !value[jsonField] });
  };

  return (
    <div className={classes.wrapper}>
      <div
        className={clsx(
          classes.bk,
          className,
          aligntoLabel ? classes.nopadding : "",
        )}
      >
        <div />
      </div>
      <Checkbox
        className={clsx(
          classes.checkbox,
          className,
          aligntoLabel ? classes.nopadding : "",
        )}
        checked={value[jsonField]}
        onChange={handleonChange}
        {...custom}
      />
    </div>
  );
};

export default withStyles(styles)(ShowHiddenCheckBox);
