import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import FieldVal from "../../../../common/FieldVal";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import { convertCurrency, getLangKey } from "../../../../../helper/generalHelper";
import { injectIntl } from "react-intl";
import TenancyRecordBox from "./TenancyRecordBox";

const styles = (theme) => ({
  root: {
    padding: "1vh 0",
  },
  gridContent: {
    padding: "1vw 2vw",
  },
  notFound: {
    padding: "1vw 2vw",
    borderRadius: 4,
    backgroundColor: "#F6F6F6",
  },
  contactHeader: {
    paddingTop: 8,
  },
});

class Tenancy extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;
    const rentFreeLangKey = intl.locale === "zh" ? "rentFreeZh" : "rentFree";

    const breakLeaseMapping = {
      true: intl.formatMessage({ id: "common.yes" }),
      false: intl.formatMessage({ id: "common.no" }),
    };

    const mongoId = detail._id ? detail._id : null;
    const stockId =
      detail.unicorn && Number.isInteger(detail.unicorn.id)
        ? detail.unicorn.id
        : null;

    const tenancyData = detail.tenancy || {};
    const rentFree = tenancyData[rentFreeLangKey] || "---";
    const freePeriodDay =
      tenancyData.freePeriod && tenancyData.freePeriod.days
        ? tenancyData.freePeriod.days + " Days"
        : "";
    const freePeriodMonth =
      tenancyData.freePeriod && tenancyData.freePeriod.months
        ? tenancyData.freePeriod.months + " Months"
        : "";
    const freePeriod =
      freePeriodDay || freePeriodMonth
        ? freePeriodMonth + " " + freePeriodDay
        : "---";
    const leasePeriodYear =
      tenancyData.leasePeriod && tenancyData.leasePeriod.years
        ? tenancyData.leasePeriod.years + " Years"
        : "";
    const leasePeriodMonth =
      tenancyData.leasePeriod && tenancyData.leasePeriod.months
        ? tenancyData.leasePeriod.months + " Months"
        : "";
    const leasePeriod =
      leasePeriodYear || leasePeriodMonth
        ? leasePeriodYear + " " + leasePeriodMonth
        : "---";
    const depositInMonth = tenancyData.depositInMonth
      ? tenancyData.depositInMonth + " Months"
      : "---";
    const optionYear = tenancyData.leaseOptions && tenancyData.leaseOptions[0] && tenancyData.leaseOptions[0].years
      ? tenancyData.leaseOptions[0].years + " Years"
      : "---";
    const newRental =
      tenancyData.leaseOptions && tenancyData.leaseOptions[0] && tenancyData.leaseOptions[0].rent && tenancyData.leaseOptions[0].rent.amount
        ? convertCurrency(tenancyData.leaseOptions[0].rent.amount)
        : "---";
    const premium = tenancyData.premium
      ? convertCurrency(tenancyData.premium)
      : "---";
    const breakLease =
      typeof tenancyData.breakLease === "boolean"
        ? breakLeaseMapping[tenancyData.breakLease.toString()]
        : "---";

    const tenancyRecords = detail.tenancyRecords ? detail.tenancyRecords : [];
    let currTenants = [];
    let prevTenants = [];

    for (let i = 0; i < tenancyRecords.length; i++) {
      if (tenancyRecords[i].deleted) continue;

      const status = tenancyRecords[i].status
        ? tenancyRecords[i].status
        : "Previous";
      let statustoDisplay;
      if (status === "Current")
        statustoDisplay = intl.formatMessage({
          id: "stock.currenttenancy",
        });
      if (status === "Previous")
        statustoDisplay = intl.formatMessage({
          id: "stock.previoustenancy",
        });

      let minDate =
        tenancyRecords[i].expiry && tenancyRecords[i].expiry.minDate
          ? tenancyRecords[i].expiry.minDate
          : "";
      let maxDate =
        tenancyRecords[i].expiry && tenancyRecords[i].expiry.maxDate
          ? tenancyRecords[i].expiry.maxDate
          : "";
      let date =
        minDate && maxDate ? minDate + " - " + maxDate : minDate || maxDate || "---";

      let item = {
        status,
        persons: tenancyRecords[i].tenants || [],
        tenancyPeriod: date,
        mongoId,
        stockId,
      };

      if (status === "Current") currTenants.push(item);
      if (status === "Previous") prevTenants.push(item);
    }

    if(currTenants.length === 0) currTenants = [{ status: "Current" }];
    if(prevTenants.length === 0) prevTenants = [{ status: "Previous" }];

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.tenancy",
          })}
        >
          <DetailBoxSection noStrike={true}>
            <Grid container spacing={2} className={classes.gridContent}>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.rentfree",
                  })}
                >
                  {rentFree}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.freeperiod",
                  })}
                >
                  {freePeriod}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.leaseperiod",
                  })}
                >
                  {leasePeriod}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.deposit",
                  })}
                >
                  {depositInMonth}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.option",
                  })}
                >
                  {optionYear}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.newrental",
                  })}
                >
                  {newRental}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.premium",
                  })}
                >
                  {premium}
                </FieldVal>
              </Grid>
              <Grid item xs={6}>
                <FieldVal
                  field={intl.formatMessage({
                    id: "stock.breaklease",
                  })}
                >
                  {breakLease}
                </FieldVal>
              </Grid>
            </Grid>
          </DetailBoxSection>

          <DetailBoxSection noStrike={true}>
            {currTenants.reverse().map((v, i) => (
              <TenancyRecordBox
                {...v}
                key={i}
              />
            ))}
          </DetailBoxSection>

          <DetailBoxSection noStrike={true}>
            {prevTenants.reverse().map((v, i) => (
              <TenancyRecordBox
                {...v}
                key={i}
              />
            ))}
          </DetailBoxSection>
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(Tenancy));
