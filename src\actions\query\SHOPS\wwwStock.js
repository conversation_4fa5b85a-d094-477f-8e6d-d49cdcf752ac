export const LIST_WWW_STOCK_QUERY = `
      query ($empId: String, $stockId: String, $limit: Int, $offset: Int, $sort: [wwwSorter],)
      {
        wwwStockListCount(empId: $empId, stockId: $stockId)
        wwwStockList(empId: $empId, stockId: $stockId, limit: $limit, offset: $offset, sort: $sort) {
          _id
          stockId
          agents {
            empId
            empDept
            empName
            scoreTotal
            score {
              eaaOwner
              soleAgent
              photo
              video
              kolVideo
            }
            eaaOwner
            soleAgent
            video
            videoId
            kolVideo
            kolVideoId
            photo
            photoIds
          }
          isOnline
          wwwScore {
            eaaOwner
            soleagent
            stockInformation
            newStock
            video
            kolVideo
            photo
          }
          wwwScoreTotal
          stockBasicInfo {
            _id
            status
            statusZh
            unicorn {
              stock
            }
            district {
              abbr
              nameEn
              nameZh
            }
            building {
              nameEn
              nameZh
            }
            floor {
              input
            }
            stockType
            stockTypeZh
            unit
            askingPrice {
              total
              average
              trend
            }
            askingRent {
              total
              average
              trend
            }
            netArea
            lettableArea
            saleableArea
            grossArea
            searchArea
            searchAreaType
            areaForShop
            area
          }
          mediaCutoffDate
          lastUpdateDate
          wwwChannelFull {
            eaaOwner
            soleagent
            video
            kolVideo
            photo
          }
        }
      }`;



export const DESC_WWW_STOCK_QUERY = `
  query ($empId: String! ,$stockId: String!){
    wwwStockDesc(empId: $empId, stockId: $stockId) {
      _id
      stockId
      empId
      www_desc_chi
      www_desc_eng
    }
  }`;


export const UPDATE_WWW_STOCK_QUERY = `
  mutation($wwwStock: WWWStockUpdate!)  {
    updateWWWStock(wwwStock: $wwwStock) {
      _id
      stockId
      mediaCutoffDate
    }
  }`

export const UPDATE_WWW_STOCK_DESC_QUERY = `
  mutation($wwwStockDesc: WWWStockDescUpdate!)  {
    updateWWWStockDesc( wwwStockDesc: $wwwStockDesc) {
      _id
      stockId
      www_desc_chi
      www_desc_eng
    }
  }`
