import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import InputBase from "@material-ui/core/InputBase";

// We can inject some CSS into the DOM.
const styles = {
  root: {},
  inputBase: {
    fontSize: "1em",
    lineHeight: 1.5,
  },
  input: {
    height: "auto",
    minHeight: "1.375em",
    padding: "2px 0",
  },
  inner: {
    position: "relative",
  },
  inputLineHover: {
    "&:hover > div:first-child": {
      borderBottom: "2px solid rgba(0, 0, 0, 0.87)",
    },
  },
  baseLine: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    transition: "border-bottom-color 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms",
    borderBottom: "1px solid rgba(0, 0, 0, 0.42)",
    pointerEvents: "none",
  },
  inputLine: {
    transform: "scaleX(0)",
    transition: "transform 200ms cubic-bezier(0.0, 0, 0.2, 1) 0ms",
    borderBottom: "2px solid #3F51B5",
  },
  inputting: {
    transform: "scaleX(1)",
  },
  hidden: {
    display: "none",
  },
  display: {
    width: "100%",
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
  },
  errorLine: {
    borderBottom: "2px solid #f44336",
  },
  errorText: {
    color: "#f44336",
    fontSize: "0.75em",
    marginTop: "3px",
    lineHeight: "1.66",
    letterSpacing: "0.03333em",
  },
};

function InlineTextInput(props) {
  const {
    classes,
    className,
    placeholder,
    input,
    meta: { touched, invalid, error },
    customInputProps = {},
    inputToDisplay = (v) => v,
    jsonField = "value",
    extraHandleChange,
    disabled,
    noBottomBorder,
    aligntoLabel,
    ...custom
  } = props;
  const [inputting, setInputting] = React.useState(false);
  const inputEl = React.useRef(null);

  const changeToDisplayMode = () => {
    setInputting(false);
  };
  const changeToInputMode = () => {
    if (props.disabled) return;

    setInputting(true);
    setTimeout(() => {
      inputEl.current.focus();
    }, 10);
  };

  const handleBlur = (e) => {
    e.preventDefault();
    changeToDisplayMode();
    input.onBlur();
  };

  const handleClick = (e) => {
    changeToInputMode();
  };

  const handleTouchStart = (e) => {
    changeToInputMode();
  };

  const handleOnChange = (e) => {
    let value = e.target.value;
    if (custom?.label?.includes("totalPrice") || custom?.label?.includes("bottomTotalPrice") || custom?.label?.includes("suggestedTotalPrice")) {
      value = value * 1000000;
    }
    if (custom?.label?.includes("totalRent") || custom?.label?.includes("bottomTotalRent") || custom?.label?.includes("suggestedTotalRent")) {
      value = value * 1000;
    }
    if (extraHandleChange) extraHandleChange(value);
    if (typeof input.value === "object")
      input.onChange({ ...input.value, [jsonField]: value });
    else input.onChange(value);
  };

  let value =
    typeof input.value === "object" ? input.value[jsonField] : input.value;

  if (custom?.label?.includes("totalPrice") || custom?.label?.includes("bottomTotalPrice") || custom?.label?.includes("suggestedTotalPrice")) {
    value = value / 1000000;
  }
  if (custom?.label?.includes("totalRent") || custom?.label?.includes("bottomTotalRent") || custom?.label?.includes("suggestedTotalRent")) {
    value = value / 1000;
  }
  if (typeof value === "undefined") value = "";

  return (
    <div className={clsx(classes.root, className)}>
      <div
        className={clsx(
          classes.inner,
          !noBottomBorder && !disabled ? classes.inputLineHover : "",
        )}
      >
        <div className={!noBottomBorder ? clsx(classes.baseLine) : undefined} />
        <div
          className={
            !noBottomBorder && !disabled
              ? clsx(
                  classes.baseLine,
                  classes.inputLine,
                  inputting ? classes.inputting : "",
                )
              : undefined
          }
        />
        <div
          className={
            touched && invalid
              ? clsx(classes.baseLine, classes.errorLine)
              : undefined
          }
        />
        <InputBase
          fullWidth
          className={clsx(classes.inputBase, !inputting ? classes.hidden : "")}
          placeholder={placeholder}
          error={touched && invalid}
          inputProps={{
            ...customInputProps,
            className: clsx(classes.input, customInputProps.className),
          }}
          {...input}
          value={value}
          onChange={handleOnChange}
          inputRef={inputEl}
          onBlur={handleBlur}
          disabled={disabled}
          {...custom}
        />

        {!inputting && (
          <div
            {...customInputProps}
            className={clsx(
              classes.display,
              classes.input,
              customInputProps.className,
            )}
            onClick={handleClick}
            onTouchStart={handleTouchStart}
          >
            {custom?.startAdornmentText && <span>{custom?.startAdornmentText}</span>}
            {custom?.multiline ? <pre style={{whiteSpace: 'pre-wrap', font: 'inherit', margin: 0}}>{value}</pre> : <span>{inputToDisplay(value)}</span>}
            {custom?.endAdornmentText && <span>{custom?.endAdornmentText}</span>}
          </div>
        )}
      </div>
      {touched && error && <div className={classes.errorText}>{error}</div>}
    </div>
  );
}

InlineTextInput.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  placeholder: PropTypes.string,
  input: PropTypes.object.isRequired,
  meta: PropTypes.object,
  customInputProps: PropTypes.object,
  inputToDisplay: PropTypes.func,
  jsonField: PropTypes.string,
};

export default withStyles(styles)(InlineTextInput);
