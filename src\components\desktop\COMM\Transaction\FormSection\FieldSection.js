import React, { useState } from "react";
import { connect } from "react-redux";
import { Field, FieldArray, change } from "redux-form";
import { injectIntl, FormattedMessage } from "react-intl";
import { withStyles } from "@material-ui/core/styles";
import OutlinedInput from "@material-ui/core/OutlinedInput";
import InputAdornment from "@material-ui/core/InputAdornment";
import Grid from "@material-ui/core/Grid";
import Collapse from "@material-ui/core/Collapse";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import ArrayPillCheckBox from "../../../../common/ArrayPillCheckBox";
import SelectFieldArrayOutput from "../../../../common/SelectFieldArrayOutput";
import TextInputCombined from "../../../../common/TextInputCombined";
import AutoCompleteSelect from "../../../../common/AutoCompleteSelect";
import TextArrayInput from "../../../../common/TextArrayInput";
import Search from "../../../../common/Search";
import Switch from "../../../../common/Switch";
import { minValue, maxValue, number } from "../../../../../core/formValidators";
import { listBuildings } from "../../../../../actions/building";
import { sbu } from "../../../../../config";

// We can inject some CSS into the DOM.
const styles = {
  divider: {
    textAlign: "center",
    margin: "auto 0px",
  },
  Headertitle: {
    color: "rgba(0, 0, 0, 0.54)",
    fontSize: "1em",
    fontWeight: "400",
  },
  DetailBoxSectionContent: {
    paddingLeft: 0,
    paddingRight: 0,
    paddingBottom: "3vh",
  },
  sticky: {
    position: "-webkit-sticky" /* Safari */,
    position: "sticky",
    top: "52px",
  },
  fieldsdivider: {
    marginTop: 8,
    marginBottom: 10,
    borderRadius: 0,
    "& input": {
      textAlign: "center",
      transform: "scale(1.6)",
    },
    "& .MuiOutlinedInput-notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23)",
      borderLeft: 0,
      borderRight: 0,
    },
    "&:hover .MuiOutlinedInput-notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23)",
    },
    "& $focused $notchedOutline": {
      borderWidth: 1,
      borderColor: "rgba(0, 0, 0, 0.23) !important",
    },
  },
  fieldMargin: {
    marginTop: 8,
    marginBottom: 10,
  },
  paddingSection: {
    paddingBottom: "3vh",
  },
};

function createMinvalue(min) {
  return minValue(min, <FormattedMessage id="search.form.invalidinput" />);
}
function createMaxvalue(max) {
  return maxValue(max, <FormattedMessage id="search.form.invalidinput" />);
}
const numberValidate = number(
  <FormattedMessage id="search.form.invalidinput" />
);
const minvaluezero = createMinvalue(0);
const maxvalue1 = createMaxvalue(200);
const maxvalue2 = createMaxvalue(999);
const maxvalue3 = createMaxvalue(9999);
const maxvalue4 = createMaxvalue(99999);
const maxvalue5 = createMaxvalue(9999999);
const maxvalue6 = createMaxvalue(99999999);
const maxvalue7 = createMaxvalue(999999999);
const maxvalue8 = createMaxvalue(9999999999);

const unitM = {
  endAdornment: (
    <InputAdornment position="end">
      <span>M</span>
    </InputAdornment>
  ),
};

const unitK = {
  endAdornment: (
    <InputAdornment position="end">
      <span>K</span>
    </InputAdornment>
  ),
};

const unitFormatter = (value, unit) => {
  if (!value || value == null) return "";

  let unitValue;
  if (unit == "K") {
    unitValue = 1000;
  } else if (unit == "M") {
    unitValue = 1000000;
  } else {
    unitValue = 1;
  }
  return value / unitValue;
};

const formatMaxLength = (value, length) => {
  if (!value) {
    return value;
  }

  if (value.length <= length) {
    return `${value.slice(0, length)}`;
  }
  return `${value.slice(0, length)}`;
};

function FieldSection(props) {
  const {
    classes,
    buildings,
    districts,
    listBuildings,
    selectedData,
    setSelectedData,
    expanded,
    intl,
    changeForm,
    initialValues,
  } = props;

  const datasources = [
    {
      value: "",
      label: intl.formatMessage({
        id: "search.form.all",
      }),
    },
    {
      value: "Real",
      label: intl.formatMessage({
        id: "stock.transaction.real",
      }),
    },
    {
      value: "Market Info",
      label: intl.formatMessage({
        id: "stock.transaction.marketinfo",
      }),
    },
  ];

  const statusOptions = [
    {
      value: "Sale",
      label: intl.formatMessage({
        id: "search.status.sold",
      }),
    },
    {
      value: "Lease",
      label: intl.formatMessage({
        id: "search.status.leased",
      }),
    },
  ];

  let priceType, rentType;

  if (initialValues.priceMinTotal || initialValues.priceMaxTotal)
    priceType = "Total";
  if (initialValues.priceMinAvg || initialValues.priceMaxAvg) priceType = "Avg";
  if (initialValues.rentMinTotal || initialValues.rentMaxTotal)
    rentType = "Avg";
  if (initialValues.rentMinAvg || initialValues.rentMaxAvg) rentType = "Avg";

  const [TogglePriceValue, setTogglePriceValue] = useState(
    priceType ? priceType : "Total"
  );

  const [ToggleRentValue, setToggleRentValue] = useState(
    rentType ? rentType : "Total"
  );

  const onChangePriceTotalAvg = () => {
    if (TogglePriceValue === "Total") {
      changeForm("priceMinTotal", "");
      changeForm("priceMaxTotal", "");
      setTogglePriceValue("Avg");
    } else if (TogglePriceValue === "Avg") {
      changeForm("priceMinAvg", "");
      changeForm("priceMaxAvg", "");
      setTogglePriceValue("Total");
    }
  };

  const onChangeRentTotalAvg = () => {
    if (ToggleRentValue === "Total") {
      changeForm("rentMinTotal", "");
      changeForm("rentMaxTotal", "");
      setToggleRentValue("Avg");
    } else if (ToggleRentValue === "Avg") {
      changeForm("rentMinAvg", "");
      changeForm("rentMaxAvg", "");
      setToggleRentValue("Total");
    }
  };

  const formatUnitandMaxLength = (value, unit) => {
    if (!value) {
      return value;
    }

    value = value.replace(/[^0-9.]/g, "");

    let unitValue;
    if (unit == "K") unitValue = 1000;
    if (unit == "M") unitValue = 1000000;

    let number = value * unitValue;
    const roundnumber = number.toFixed();
    // return the value after rounding
    return `${roundnumber.toString()}`;
  };

  return (
    <div>
      {!expanded ? (
        <div className={classes.sticky}>
          <Field
            name="building"
            margin="normal"
            label={intl.formatMessage({
              id: "search.form.building",
            })}
            fullWidth
            component={AutoCompleteSelect}
            optionsdata={buildings}
            apiaction={listBuildings}
            selectedData={selectedData && selectedData.building}
            setSelectedData={setSelectedData}
          />
        </div>
      ) : (
        <Field
          name="building"
          margin="normal"
          label={intl.formatMessage({
            id: "search.form.building",
          })}
          fullWidth
          component={AutoCompleteSelect}
          optionsdata={buildings}
          apiaction={listBuildings}
          selectedData={selectedData && selectedData.building}
          setSelectedData={setSelectedData}
          customInputProps={{
            className: classes.fieldMargin,
          }}
        />
      )}

      <Collapse in={expanded}>
        <div className={classes.paddingSection}>
          <Field
            name="district"
            margin="normal"
            label={intl.formatMessage({
              id: "search.form.district",
            })}
            fullWidth
            component={Search}
            searchItems={districts}
            selectedData={selectedData && selectedData.district}
            setSelectedData={setSelectedData}
            customInputProps={{
              className: classes.fieldMargin,
            }}
          />
          {/*  date */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="dateMin"
                  type="date"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.date",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="dateMax"
                  type="date"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>
        </div>

        <DetailBoxSection
          text={intl.formatMessage({
            id: "search.header.status",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
        >
          <FieldArray
            name="type"
            component={ArrayPillCheckBox}
            options={statusOptions}
          />
        </DetailBoxSection>

        <DetailBoxSection
          text={intl.formatMessage({
            id: "search.form.building",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
        >
          {/* floor number */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="floorMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.floor",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 4)}
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="floorMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue3]}
                  parse={(value) => formatMaxLength(value, 4)}
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>
        </DetailBoxSection>

        <DetailBoxSection
          text={intl.formatMessage({
            id: "search.header.stock",
          })}
          titleClass={classes.Headertitle}
          contentClass={classes.DetailBoxSectionContent}
        >
          {/* data source */}
          {sbu == "IND" && (
            <Grid container>
              <Grid item xs={12}>
                <Field
                  name="dataSource"
                  component={SelectFieldArrayOutput}
                  isarr
                  label={intl.formatMessage({
                    id: "search.form.datasource",
                  })}
                  ranges={datasources}
                  customInputProps={{
                    className: classes.fieldMargin,
                  }}
                />
              </Grid>
            </Grid>
          )}

          {/* area */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name="areaMin"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  label={intl.formatMessage({
                    id: "search.common.area",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"left"}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name="areaMax"
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  validate={[minvaluezero, maxvalue8]}
                  parse={(value) => formatMaxLength(value, 10)}
                  compPosition={"right"}
                />
              </div>
            </Grid>
          </Grid>

          {/* price */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"priceMin" + TogglePriceValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  label={intl.formatMessage({
                    id: "search.common.price",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  validate={
                    TogglePriceValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue8]
                      : [minvaluezero]
                  }
                  InputProps={TogglePriceValue === "Total" ? unitM : null}
                  format={(value) => {
                    let formattedvalue;
                    if (TogglePriceValue === "Total") {
                      formattedvalue = unitFormatter(value, "M");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (TogglePriceValue === "Total") {
                      return formatUnitandMaxLength(value, "M");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"priceMax" + TogglePriceValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  validate={
                    TogglePriceValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue8]
                      : [minvaluezero, maxvalue8]
                  }
                  InputProps={TogglePriceValue == "Total" && unitM}
                  format={(value) => {
                    let formattedvalue;
                    if (TogglePriceValue === "Total") {
                      formattedvalue = unitFormatter(value, "M");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (TogglePriceValue === "Total") {
                      return formatUnitandMaxLength(value, "M");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                  rightcomp={
                    <Switch
                      value={TogglePriceValue}
                      textL={intl.formatMessage({
                        id: "search.common.pricetotal",
                      })}
                      textR={intl.formatMessage({
                        id: "search.common.priceavg",
                      })}
                      valleft="Total"
                      valright="Avg"
                      onChange={onChangePriceTotalAvg}
                    />
                  }
                />
              </div>
            </Grid>
          </Grid>

          {/* rent */}
          <Grid container>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"rentMin" + ToggleRentValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  label={intl.formatMessage({
                    id: "search.common.rent",
                  })}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.min",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"left"}
                  validate={
                    ToggleRentValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue6]
                      : [minvaluezero]
                  }
                  InputProps={ToggleRentValue === "Total" && unitK}
                  format={(value) => {
                    let formattedvalue;
                    if (ToggleRentValue === "Total") {
                      formattedvalue = unitFormatter(value, "K");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (ToggleRentValue === "Total") {
                      return formatUnitandMaxLength(value, "K");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                />
              </div>
            </Grid>
            <Grid item xs={2}>
              <OutlinedInput
                disabled
                readOnly={true}
                defaultValue="|"
                variant="outlined"
                className={classes.fieldsdivider}
              />
            </Grid>
            <Grid item xs={5}>
              <div>
                <Field
                  name={"rentMax" + ToggleRentValue}
                  type="number"
                  margin="normal"
                  className={classes.fieldMargin}
                  inputProps={{ step: "any" }}
                  placeholder={intl.formatMessage({
                    id: "search.form.common.max",
                  })}
                  component={TextInputCombined}
                  fullWidth
                  variant="outlined"
                  compPosition={"right"}
                  validate={
                    ToggleRentValue === "Total"
                      ? [numberValidate, minvaluezero, maxvalue6]
                      : [minvaluezero, maxvalue8]
                  }
                  InputProps={ToggleRentValue === "Total" && unitK}
                  format={(value) => {
                    let formattedvalue;
                    if (ToggleRentValue === "Total") {
                      formattedvalue = unitFormatter(value, "K");
                    } else {
                      formattedvalue = unitFormatter(value);
                    }
                    return formattedvalue;
                  }}
                  parse={(value) => {
                    if (ToggleRentValue === "Total") {
                      return formatUnitandMaxLength(value, "K");
                    } else {
                      return formatMaxLength(value, 10);
                    }
                  }}
                  rightcomp={
                    <Switch
                      value={ToggleRentValue}
                      textL={intl.formatMessage({
                        id: "search.common.renttotal",
                      })}
                      textR={intl.formatMessage({
                        id: "search.common.rentavg",
                      })}
                      valleft="Total"
                      valright="Avg"
                      onChange={onChangeRentTotalAvg}
                    />
                  }
                />
              </div>
            </Grid>
          </Grid>

          <Field
            name="unicornId"
            type="number"
            margin="normal"
            className={classes.fieldMargin}
            label={intl.formatMessage({
              id: "search.header.stockid",
            })}
            component={TextArrayInput}
            fullWidth
            validate={[numberValidate, minvaluezero, maxvalue5]}
            variant="outlined"
          />
        </DetailBoxSection>
        {props.children}
      </Collapse>
    </div>
  );
}

const mapStateToProps = (state) => ({
  buildings: state.building.buildings || [],
  districts: state.district.districts ? state.district.districts : [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    listBuildings: (graphqlvariable) =>
      dispatch(listBuildings(graphqlvariable)),
    changeForm: (field, val) => dispatch(change("searchForm", field, val)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(injectIntl(FieldSection)));
