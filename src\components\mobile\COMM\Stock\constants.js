import _ from "lodash";

const statusMap = {
  Leased: "search.status.leased",
  Selfuse: "search.status.selfuse",
  Pending: "search.status.pending",
  Search: "search.status.search",
  Tenanted: "search.status.tenanted",
  Sold: "search.status.sold",
  Lease: "search.status.lease",
  Sale: "search.status.sale",
  "Sale+Lease": "search.status.salesandlease",
};
export const getStatusDisplay = (status, intl) =>
  status && _.has(statusMap, status)
    ? intl.formatMessage({ id: _.get(statusMap, status) })
    : "---";
