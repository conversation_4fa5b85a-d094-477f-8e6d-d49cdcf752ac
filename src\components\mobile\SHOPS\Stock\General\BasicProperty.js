import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import FieldVal from "../../../../common/FieldVal";
import { getLangKey, numberComma } from "../../../../../helper/generalHelper";
import { injectIntl } from "react-intl";
import DetailBoxSection from "../../../../common/DetailBoxSection";

const styles = (theme) => ({
  root: {
    padding: "1vh 0",
  },
  lmrAlign: {
    paddingLeft: "2vw",
  },
});

class BasicProperty extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langKey = getLangKey(intl);

    const singleSideTypeMapping = {
      "Big": intl.formatMessage({ id: "stock.big" }),
      "Small": intl.formatMessage({ id: "stock.small" }),
    };
    const surveyorStatusMapping = {
      true: intl.formatMessage({ id: "common.yes" }),
      false: intl.formatMessage({ id: "common.no" }),
    };

    const stockType =
      detail.stockType && detail.stockType[langKey]
        ? detail.stockType[langKey]
        : "---";
    const ownerType =
      detail.ownerType && detail.ownerType[langKey]
        ? detail.ownerType[langKey]
        : "---";
    const possession =
      detail.possession && detail.possession[langKey]
        ? detail.possession[langKey]
        : "---";
    const availability = detail.availability || "---";
    const keyNumber = detail.keyNumber || "---";
    const yieldValue = detail.yield ? `${detail.yield}%` : "---";
    const facingTo = detail.facingTo || "---";
    const singleSideType =
      detail.singleSideType && singleSideTypeMapping[detail.singleSideType]
        ? singleSideTypeMapping[detail.singleSideType]
        : "---";
    const surveyorStatus =
      detail.surveyorProposal && surveyorStatusMapping[detail.surveyorProposal.status]
        ? surveyorStatusMapping[detail.surveyorProposal.status]
        : "---";
    const surveyorDate =
      detail.surveyorProposal && detail.surveyorProposal.date
        ? detail.surveyorProposal.date
        : "---";
    const areaSite =
      detail.area && detail.area.site && !isNaN(parseInt(detail.area.site))
        ? numberComma(detail.area.site)
        : "---";

    const stockTypeHeader = intl.formatMessage({
      id: "stock.stocktype",
    });
    const ownerTypeHeader = intl.formatMessage({
      id: "stock.ownertype",
    });
    const possessionHeader = intl.formatMessage({
      id: "stock.possession",
    });
    const availabilityHeader = intl.formatMessage({
      id: "stock.availability",
    });
    const keynumpwHeader = intl.formatMessage({
      id: "stock.keynumpw",
    });
    const yieldHeader = intl.formatMessage({
      id: "stock.yield",
    });
    const facingToHeader = intl.formatMessage({
      id: "stock.facingto",
    });
    const singleSideTypeHeader = intl.formatMessage({
      id: "stock.singlesidetype",
    });
    const surveyorStatusHeader = intl.formatMessage({
      id: "stock.surveyorproposalstatus",
    });
    const surveyorDateHeader = intl.formatMessage({
      id: "stock.surveyorproposaldate",
    });
    const areaSiteHeader = intl.formatMessage({
      id: "stock.area.site",
    });

    let generalMapping = {
      [possessionHeader]: { value: possession, xs: 6 },
      [availabilityHeader]: { value: availability, xs: 6 },
      [stockTypeHeader]: { value: stockType, xs: 6 },
      [ownerTypeHeader]: { value: ownerType, xs: 6 },
      [yieldHeader]: { value: yieldValue, xs: 6 },
      [facingToHeader]: { value: facingTo, xs: 6 },
      [singleSideTypeHeader]: { value: singleSideType, xs: 6 },
      [keynumpwHeader]: { value: keyNumber, xs: 6 },
      [surveyorStatusHeader]: { value: surveyorStatus, xs: 6 },
      [surveyorDateHeader]: { value: surveyorDate, xs: 6 },
      [areaSiteHeader]: { value: areaSite, xs: 6 },
    };

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.unit",
          })}
        >
          <Grid container spacing={2} className={classes.lmrAlign}>
            {Object.keys(generalMapping).map((v, i) => (
              <Grid item xs={generalMapping[v].xs} key={v}>
                <FieldVal field={v}>
                  {generalMapping[v].value}
                </FieldVal>
              </Grid>
            ))}
          </Grid>
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(BasicProperty));
