import React, { useEffect } from "react";
import { connect } from "react-redux";
import { reduxForm } from "redux-form";
import { makeStyles } from "@material-ui/core/styles";
import _ from "lodash";

import FieldSection from "./FieldSection";
import { updateQueryVariables } from "@/actions/company";

const useStyles = makeStyles({
  form: {
    position: "-webkit-sticky" /* Safari */,
    position: "sticky",
    top: 52,
    zIndex: 10,
    background: "#ffffff",
  },
});

let CompanySearchForm = ({ queryvariables, initialize }) => {
  const classes = useStyles();

  useEffect(() => {
    initialize({
      companyName: !_.isEmpty(_.get(queryvariables, "companyName") || [])
        ? queryvariables.companyName
        : [],
    });
  }, []);

  return (
    <form className={classes.form}>
      <FieldSection />
    </form>
  );
};

CompanySearchForm = reduxForm({
  form: "companySearch",
  enableReinitialize: true,
  keepDirtyOnReinitialize: true,

  onChange: (values, dispatch, props, previousValues) => {
    if (!_.isEqual(values.companyName, previousValues.companyName)) {
      dispatch(
        updateQueryVariables(null, null, {
          ...props.queryvariables,
          ...values,
          offset: 0,
        }),
      );
    }
  },
})(CompanySearchForm);

const mapStateToProps = (state) => ({
  queryvariables: state.company.queryvariables,
});

export default connect(mapStateToProps)(CompanySearchForm);
