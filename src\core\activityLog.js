import fetch from "node-fetch";
import config from "../config";

export const addActivityLog = async (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    try {
      const headers = {
        "Content-Type": req.headers["content-type"],
        "Authorization": req.headers["authorization"],
        "CAS-Authorization": req.headers["cas-authorization"]
      }
      const resp = await fetch(`${config.api.icimsgeneral}/addActivityLog`, {
        method: "POST",
        body: JSON.stringify(req.body),
        headers: headers
      });
      const data = await resp.json();
      res.send(data);
    } catch (e) {
      console.log(e);
      return {};
    }
  }
};
