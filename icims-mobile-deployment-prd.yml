apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: C:\Users\<USER>\kompose.exe convert
    kompose.version: 1.21.0 (992df58d8)
  creationTimestamp: null
  labels:
    io.kompose.service: ${SERVICE_NAME}
  name: ${SERVICE_NAME}
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: ${SERVICE_NAME}
  strategy:
    type: RollingUpdate
  template:
    metadata:
      annotations:
        kompose.cmd: C:\Users\<USER>\kompose.exe convert
        kompose.version: 1.21.0 (992df58d8)
      creationTimestamp: null
      labels:
        io.kompose.service: ${SERVICE_NAME}
    spec:
      hostAliases:
      - ip: "**********"
        hostnames:
        - "watermark.midland.com.hk"
      - ip: "**********"
        hostnames:
        - "mrmsweb.midland.com.hk"
      containers:
        - args:
            - start
          command:
            - /home/<USER>/entrypoint-eks.sh
          env:
            - name: BASE_URL
              value: ${BASE_URL}
            - name: DEPLOY_DOMAIN
              value: ${DEPLOY_DOMAIN}
            - name: ENABLE_TRANSLATION
              value: ${ENABLE_TRANSLATION}
            - name: LOCALE
              value: ${LOCALE}
            - name: MODE
              value: ${MODE}
            - name: NODE_ENV
              value: ${NODE_ENV}
            - name: PORT
              value: ${PORT}
            - name: PUBLIC_HOST
              value: ${PUBLIC_HOST}
            - name: REQUEST_TIMEOUT
              value: ${REQUEST_TIMEOUT}
            - name: SBU
              value: ${SBU}
            - name: DATABASE_URL
              value: ${DATABASE_URL}
            - name: DATABASE_NAME
              value: ${DATABASE_NAME}
            - name: DATABASE_TABLE
              value: ${DATABASE_TABLE}
            - name: SESSION_DATABASE_NAME
              value: ${SESSION_DATABASE_NAME}
            - name: API_CLIENT_URL
              value: ${API_CLIENT_URL}
            - name: API_DISTRICT
              value: ${API_DISTRICT}
            - name: API_STREET
              value: ${API_STREET}
            - name: API_BUILDING
              value: ${API_BUILDING}
            - name: API_STOCK
              value: ${API_STOCK}
            - name: API_MEDIA
              value: ${API_MEDIA}
            - name: API_TRANSACTION
              value: ${API_TRANSACTION}
            - name: API_SUPPLEMENT
              value: ${API_SUPPLEMENT}
            - name: API_EMPLOYEE
              value: ${API_EMPLOYEE}
            - name: API_SEARCH
              value: ${API_SEARCH}
            - name: API_WWW
              value: ${API_WWW}
            - name: API_ICIMS_GENERAL
              value: ${API_ICIMS_GENERAL}
            - name: API_LANDSEARCH_DETAILS
              value: ${API_LANDSEARCH_DETAILS}
            - name: API_CONTACT
              value: ${API_CONTACT}
            - name: API_STOCK_INTERNAL
              value: ${API_STOCK_INTERNAL}
            - name: API_EMPLOYEE_INTERNAL
              value: ${API_EMPLOYEE_INTERNAL}
            - name: API_CONTACT_INTERNAL
              value: ${API_CONTACT_INTERNAL}
            - name: API_PROPOSAL
              value: ${API_PROPOSAL}
            - name: API_S3_MEDIA_TO_LOCAL
              value: ${API_S3_MEDIA_TO_LOCAL}
            - name: API_CLOUDWATCH_LOG_URL
              value: ${API_CLOUDWATCH_LOG_URL}
            - name: API_CLOUDWATCH_LOG_INTERNAL_URL
              value: ${API_CLOUDWATCH_LOG_INTERNAL_URL}
            - name: API_NOTIFICATION_CENTER_URL
              value: ${API_NOTIFICATION_CENTER_URL}
            - name: NOTIFICATION_CENTER_COMPANY
              value: ${NOTIFICATION_CENTER_COMPANY}
            - name: NOTIFICATION_CENTER_REQUESTFROM
              value: ${NOTIFICATION_CENTER_REQUESTFROM}
            - name: IT_USERS
              value: ${IT_USERS}
            - name: BANNED_USERS
              value: ${BANNED_USERS}
            - name: MEDIA_BYPASS_CODE
              value: ${MEDIA_BYPASS_CODE}
            - name: AUTH_CENTRAL_HOST
              value: ${AUTH_CENTRAL_HOST}
            - name: APP_NOTIFI_BOARDCAST_URL
              value: ${APP_NOTIFI_BOARDCAST_URL}
            - name: OAUTH2_URL
              value: ${OAUTH2_URL}
            - name: CLIENT_ID
              value: ${CLIENT_ID}
            - name: CLIENT_SECRET
              value: ${CLIENT_SECRET}
            - name: PROVISION_KEY
              value: ${PROVISION_KEY}
            - name: AUTHENTICATED_USERID
              value: ${AUTHENTICATED_USERID}
            - name: GRANT_TYPE
              value: ${GRANT_TYPE}
            - name: SCOPE
              value: ${SCOPE}
            - name: GOOGLE_TRACKING_ID
              value: ${GOOGLE_TRACKING_ID}
            - name: GTAG_MANAGER_ID
              value: ${GTAG_MANAGER_ID}
            - name: REQUEST_TIMEOUT
              value: ${REQUEST_TIMEOUT}
            - name: UPLOAD_REQUEST_TIMEOUT
              value: ${UPLOAD_REQUEST_TIMEOUT}
            - name: LOG_GROUP
              value: ${LOG_GROUP}
            - name: LOCALE
              value: ${LOCALE}
            - name: ENABLE_TRANSLATION
              value: ${ENABLE_TRANSLATION}
            - name: REFRESH_GRANT_TYPE
              value: ${REFRESH_GRANT_TYPE}
            - name: REFRESH_TOKEN_TIMEOUT
              value: ${REFRESH_TOKEN_TIMEOUT}
            - name: BACKEND_KEY
              value: ${BACKEND_KEY}
            - name: RECAPTCHA_SITE_KEY
              value: ${RECAPTCHA_SITE_KEY}
            - name: RECAPTCHA_SECRET_KEY
              value: ${RECAPTCHA_SECRET_KEY}
            - name: RESENDPIN_COUNTER
              value: ${RESENDPIN_COUNTER}
            - name: UNLOCK_QUOTA
              value: ${UNLOCK_QUOTA}
            - name: PROPOSAL_QUOTA
              value: ${PROPOSAL_QUOTA}
            - name: PP_STOCK_QUOTA
              value: ${PP_STOCK_QUOTA}
            - name: PROPOSAL_BYPASS_CODE
              value: ${PROPOSAL_BYPASS_CODE}
            - name: ENABLE_PROPOSAL
              value: ${ENABLE_PROPOSAL}
            - name: API_LANDSEARCH
              value: ${API_LANDSEARCH}
            - name: API_LANDSEARCH_FILEPATH
              value: ${API_LANDSEARCH_FILEPATH}
            - name: API_LANDSEARCH_LOG
              value: ${API_LANDSEARCH_LOG}
            - name: ENABLE_CONSOLID_LANDSEARCH
              value: ${ENABLE_CONSOLID_LANDSEARCH}
            - name: ENABLE_DESKTOPVIEW
              value: ${ENABLE_DESKTOPVIEW}
            - name: ENABLE_BATCH_TWO
              value: ${ENABLE_BATCH_TWO}
            - name: ENABLE_WWW_SCORE
              value: ${ENABLE_WWW_SCORE}
            - name: SSO
              value: ${SSO}
            - name: CAS_ACCESS_TOKEN_HOST
              value: ${CAS_ACCESS_TOKEN_HOST}
            - name: CAS_CLIENT_SERVICE
              value: ${CAS_CLIENT_SERVICE}
            - name: CAS_REFRESH_TOKEN_HOST
              value: ${CAS_REFRESH_TOKEN_HOST}
            - name: CAS_PROFILE_HOST
              value: ${CAS_PROFILE_HOST}
            - name: CAS_LOGOUT_HOST
              value: ${CAS_LOGOUT_HOST}
            - name: CAS_AUTH_CODE_HOST
              value: ${CAS_AUTH_CODE_HOST}
            - name: CAS_CLIENT_ID
              value: ${CAS_CLIENT_ID}
            - name: CAS_CLIENT_SECRET
              value: ${CAS_CLIENT_SECRET}
            - name: REFRESH_BEFORE_TIMEOUT_INTERVAL
              value: ${REFRESH_BEFORE_TIMEOUT_INTERVAL}
            - name: GATEWAY
              value: ${GATEWAY}
            - name: PERMISSION_VIEW_STOCK_QUOTA
              value: ${PERMISSION_VIEW_STOCK_QUOTA}
            - name: FIRST_HAND_STOCK_API
              value: ${FIRST_HAND_STOCK_API}
            - name: M1_PUBLIC_KEY_NAME
              value: ${M1_PUBLIC_KEY_NAME}
            - name: WB_TYPE_ID
              value: ${WB_TYPE_ID}
            - name: KOL_VIDEO_DURATION_LIMIT_SECONDS
              value: ${KOL_VIDEO_DURATION_LIMIT_SECONDS}
          image: ${IMAGE_NAME}
          imagePullPolicy: Always
          name: ${SERVICE_NAME}
          ports:
            - containerPort: ${DEPLOY_PORT}
          resources:
             requests:
               cpu: 150m
          livenessProbe:
            tcpSocket:
              port: ${DEPLOY_PORT}
          readinessProbe:
            tcpSocket:
              port: ${DEPLOY_PORT}
            initialDelaySeconds: 5
            periodSeconds: 5
      restartPolicy: Always
      serviceAccountName: ""
      volumes: null
status: {}
