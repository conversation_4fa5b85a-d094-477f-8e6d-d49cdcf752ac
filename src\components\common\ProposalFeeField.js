import React from "react";
import { Field } from "redux-form";
import { injectIntl } from "react-intl";
import { withStyles } from "@material-ui/core/styles";
import Grid from "@material-ui/core/Grid";
import InputWithCheckBoxCustom from "./InputWithCheckBoxCustom";
import MuiSelectArrayOutput from "./MuiSelectArrayOutput";
import { sbu } from "../../config";

const styles = {
  hidden: {
    visibility: "hidden",
  },
};

function ProposalFeeField(props) {
  const {
    classes,
    fields,
    label,
    intl,
    formState,
    isListProposal,
    stockId,
    ...custom
  } = props;

  const unitOptions = [
    {
      value: "",
      label: "---",
    },
    {
      value: "/SqFt",
      label: "/" + intl.formatMessage({ id: "common.sqft" }),
    },
    {
      value: "/Qtr",
      label: "/" + intl.formatMessage({ id: "common.qtr" }),
    },
    {
      value: "/Month",
      label: "/" + intl.formatMessage({ id: "common.month" }),
    },
    {
      value: "/SY",
      label: "/" + intl.formatMessage({ id: "common.sy" }),
    },
  ];

  const paidByOptions = [
    {
      value: "",
      label: "---",
    },
    {
      value: "Paid By Tenant",
      label: intl.formatMessage({ id: "stock.paidbytenant" }),
    },
    {
      value: "Paid By Landlord",
      label: intl.formatMessage({ id: "stock.paidbylandlord" }),
    },
  ];
  const { stock } = formState;
  const namePrefix = isListProposal ? `stocks.${stockId}.` : "";
  const isManagermentFee = fields.name.includes("managementFee");
  const isFirstRender = React.useRef(true);

  React.useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    let area = 0;
    let managementFee = parseFloat(stock.managementFee[0].value) || 0;
    if (sbu === "SHOPS") {
      area = parseFloat(stock.area[0].gross) || 0;
    } else if (sbu === "IND") {
      area = parseFloat(stock.areaGross[0].value) || 0;
    } else if (sbu === "COMM") {
      area = parseFloat(stock.areaGross[0].value || stock.areaNet[0].value || stock.areaSaleable[0].value || stock.areaLettable[0].value) || 0;
    }
    const managementFeeValue = managementFee * area;
    if (managementFeeValue > 0) {
      custom.changeFieldValue(`${namePrefix}stock.managementFee[0]`, {
        ...stock[`managementFee`][0],
        totalMonthlyMgtFee: Number.isInteger(managementFeeValue) ? managementFeeValue : Math.round(managementFeeValue + 0.5),
      });
    } else {
      custom.changeFieldValue(`${namePrefix}stock.managementFee[0]`, {
        ...stock[`managementFee`][0],
        totalMonthlyMgtFee: 0,
      });
    }
    if (stock.managementFee[0].unit !== "/SqFt") {
      custom.changeFieldValue(`${namePrefix}stock.managementFee[0]`, {
        ...stock[`managementFee`][0],
        totalMonthlyMgtFee: 0,
        totalMonthlyMgtFeeIsShow: false,
      });
    }
  }, [
    stock.managementFee[0].value, 
    stock.managementFee[0].unit, 
    ...(sbu === "SHOPS" ? [stock.area[0].gross] : [stock.areaGross[0].value, stock.areaNet[0].value, stock.areaSaleable[0].value, stock.areaLettable[0].value]),
  ]);

  return (
    <div>
      {fields.map((item, index) => (
        <Grid container spacing={1} key={index}>
          <Grid item xs={6}>
            <InputWithCheckBoxCustom
              name={item}
              label={label}
              checkboxInFront={true}
              aligntoLabel={true}
              bottomCheckbox={sbu === "COMM" ? false : true}
              checkboxXs={2}
              {...custom}
            />
          </Grid>
          <Grid item xs={6}>
            <InputWithCheckBoxCustom
              name={item}
              label=" "
              renderComponent={MuiSelectArrayOutput}
              isArrayOutput={false}
              checkboxInFront={true}
              aligntoLabel={true}
              bottomCheckbox={true}
              checkboxXs={2}
              checkboxProps={{
                className: classes.hidden,
              }}
              jsonField="unit"
              ranges={unitOptions}
              fullWidth
            />
          </Grid>
          {isManagermentFee && stock.managementFee[0].unit === "/SqFt" && (
            <Grid item xs={12}>
              <InputWithCheckBoxCustom
                name={item}
                label={intl.formatMessage({ id: "stock.totalMonthlyMgtfee" })}
                checkboxInFront={true}
                aligntoLabel={true}
                bottomCheckbox={sbu === "COMM" ? false : true}
                checkboxXs={1}
                jsonField="totalMonthlyMgtFee"
                fullWidth
                type="number"
                {...custom}
              />
            </Grid>
          )}
          {sbu !== "COMM" &&(
          <Grid item xs={12}>
            <InputWithCheckBoxCustom
              name={item}
              // label=" "
              renderComponent={MuiSelectArrayOutput}
              isArrayOutput={false}
              checkboxInFront={true}
              aligntoLabel={true}
              bottomCheckbox={false}
              checkboxXs={1}
              // checkboxProps={{
              //   className: classes.hidden,
              // }}
              jsonField="paidBy"
              ranges={paidByOptions}
              fullWidth
              changeFieldValue={props.changeFieldValue}
            />
          </Grid>
          )}
        </Grid>
      ))}
    </div>
  );
}

export default withStyles(styles)(injectIntl(ProposalFeeField));