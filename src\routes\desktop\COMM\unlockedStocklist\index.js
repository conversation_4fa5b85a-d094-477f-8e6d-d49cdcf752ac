import React from "react";
import Layout from "../../../../components/Layout";
import { FormattedMessage } from "react-intl";

const title = "Viewed Stocks";

async function action({ store, params, query }) {
  const { auth } = store.getState();
  if (!auth.user) {
    return { redirect: "/login" };
  } else if (auth.user.authorized == false) {
    return { redirect: "/login" };
  }

  const UnlockedStocklist = await require.ensure(
    [],
    require => require("./UnlockedStocklist").default,
    "unlockedStocklist"
  );

  return {
    chunks: ["unlockedStocklist"],
    title,
    component: (
      <Layout
        path="result"
        header={<FormattedMessage id="home.viewedstock" />}
        isAdvanced={false}
      >
        <UnlockedStocklist />
      </Layout>
    )
  };
}

export default action;
