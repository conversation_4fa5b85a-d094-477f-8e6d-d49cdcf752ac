export const CLEAR_CREATE_PROPOSAL = "CLEAR_CREATE_PROPOSAL";
export const CREATE_PROPOSAL_START = "CREATE_PROPOSAL_START";
export const CREATE_PROPOSAL_SUCCESS = "CREATE_PROPOSAL_SUCCESS";
export const CREATE_PROPOSAL_ERROR = "CREATE_PROPOSAL_ERROR";

export const CREATE_PREVIEW_ERROR = "CREATE_PREVIEW_ERROR";
export const CREATE_PP_PREVIEW_SUCCESS = `CREATE_PP_PREVIEW_SUCCESS`;
export const CLEAR_CREATED_PP_PREVIEW = `CLEAR_CREATED_PP_PREVIEW`;

export const CLEAR_PROPOSALS = "CLEAR_PROPOSALS";

export const LIST_PROPOSALS_START = "LIST_PROPOSALS_START";
export const LIST_MORE_PROPOSALS_START = "LIST_MORE_PROPOSALS_START";
export const LIST_PROPOSALS_SUCCESS = "LIST_PROPOSALS_SUCCESS";
export const LIST_PROPOSALS_NULL_SUCCESS = "LIST_PROPOSALS_NULL_SUCCESS";
export const LIST_PROPOSALS_ERROR = "LIST_PROPOSALS_ERROR";

export const REMOVE_PROPOSAL_START = "REMOVE_PROPOSAL_START";
export const REMOVE_PROPOSAL_SUCCESS = "REMOVE_PROPOSAL_SUCCESS";
export const REMOVE_PROPOSAL_ERROR = "REMOVE_PROPOSAL_ERROR";

export const GET_PROPOSALCOUNT_SUCCESS = "GET_PROPOSALCOUNT_SUCCESS";
export const GET_PROPOSALCOUNT_ERROR = "GET_PROPOSALCOUNT_ERROR";

export const QUERY_PROPOSAL_RES_START = "QUERY_PROPOSAL_RES_START";
export const QUERY_PROPOSAL_RES_SUCCESS = "QUERY_PROPOSAL_RES_SUCCESS";
export const QUERY_PROPOSAL_RES_ERROR = "QUERY_PROPOSAL_ERROR";

export const FETCH_RECREATE_PP = `FETCH_RECREATE_PP`;
export const FETCH_RECREATE_PP_SUCCESS = `FETCH_RECREATE_PP_SUCCESS`;
/** 設置當前 `proposal` 是否為 `reCreate` */
export const SET_IS_RECREATE_PP = `SET_IS_RECREATE_PP`;
