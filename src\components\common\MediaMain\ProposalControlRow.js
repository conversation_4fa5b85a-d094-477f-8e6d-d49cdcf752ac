import React, {useEffect} from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { change, getFormValues } from "redux-form";
import { withStyles } from "@material-ui/styles";
import DescriptionIcon from "@material-ui/icons/Description";
import { injectIntl, FormattedMessage } from "react-intl";
import Checkbox from "@material-ui/core/Checkbox";
import { sbu } from "../../../config";
import TextField from "@material-ui/core/TextField";
import MenuItem from "@material-ui/core/MenuItem";
import {
  getMultiImgOptions
} from "../../Saleskit/helpers.js";
import _ from "lodash";

const styles = {
  root: {
    display: "flex",
    alignItems: "center",
    justifyContent: " space-between",
  },
  label: {
    color: "#777",
    fontSize: "0.875em",
    display: "flex",
    alignItems: "center",
  },
  labelIcon: {
    width: 20,
    height: 20,
    display: "block",
  },
  hide: {
    visibility: "hidden",
  },
  checkbox: {
    padding: "2px 0",
    "&.Mui-checked": {
      color: "#13CE66",
    },
  },
  box2: {
    width: '100%'
  }
};

class ProposalControlRow extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    fields: PropTypes.object,
    mediaId: PropTypes.string,
    isPhoto: PropTypes.bool,
    form: PropTypes.object,
    changeFieldValue: PropTypes.func.isRequired,
    media: PropTypes.object
  };

  render() {
    const multiImgOptions = getMultiImgOptions()
    const {
      classes,
      mediaId,
      fields,
      isPhoto = false,
      form,
      changeFieldValue,
      mediaPath = "",
      intl,
      media,
      isListProposal
    } = this.props;

    const main1Photo =
      _.get(form, `${mediaPath}mainPhoto`) ||
      // _.get(form, "media.main1Photo") ||
      "";
    const main2Photo =
      _.get(form, `${mediaPath}main1Photo`) ||
      // _.get(form, "media.main1Photo") ||
      "";
    const selectedMedia = _.cloneDeep(_.get(form, `${mediaPath}selectedMedia`, []))

    const fieldsArray = fields && fields.getAll() ? fields.getAll() : [];
    const targetPhotoContents = new Set(["arcCert", "areaPlan", "floorPlan", "plan", "positionPlan", "map", ...(sbu === "COMM" ? ["layout"] : [])]);

    const toggle = (event, option) => {
      if (fieldsArray.indexOf(option) === -1) {
        fields.push(option);
      } else if (fieldsArray.indexOf(option) !== -1) {
        fields.remove(fieldsArray.indexOf(option));
      }
      if (selectedMedia.indexOf(option) === -1) {
        selectedMedia.push(option);
      } else if (selectedMedia.indexOf(option) !== -1) {
        selectedMedia.splice(selectedMedia.indexOf(option),1);
      }
      changeFieldValue(`${mediaPath}selectedMedia`, selectedMedia)
      
      changeFieldValue(`${mediaPath}attachmentMultiImgConfig`, {
        ..._.get(form, `${mediaPath}attachmentMultiImgConfig`),
        [mediaId]: _.get(form, `${mediaPath}attachmentMultiImgConfig.${mediaId}`) ? null : ["map", "govMap"].includes(media.id) || targetPhotoContents.has(media.photoContent) ? 'ONE' : _.get(form, 'general.multiImg', 'ONE')
      })
    };

    const handleMain1Change = (e) => {
      console.log('[ e ] >',main1Photo, e.target.value)
      if (main1Photo === e.target.value) {
        changeFieldValue(`${mediaPath}mainPhoto`, null);
      } else {
        changeFieldValue(`${mediaPath}mainPhoto`, e.target.value);
      }
    };

    const handleMain2Change = (e) => {
      if (main2Photo === e.target.value) {
        changeFieldValue(`${mediaPath}main1Photo`, null);
      } else {
        changeFieldValue(`${mediaPath}main1Photo`, e.target.value);
      }
    };

    const multiImgChange = (e) => {
      changeFieldValue(`${mediaPath}attachmentMultiImgConfig`, {
        ..._.get(form, `${mediaPath}attachmentMultiImgConfig`),
        [mediaId]: e.target.value
      })
    }

    return (
      <div>
        <div className={classes.root}>
          <label
            className={`${classes.label} ${!isPhoto ? classes.hide : undefined}`}
          >
            <Checkbox
              className={classes.checkbox}
              value={mediaId}
              onChange={handleMain1Change}
              checked={main1Photo == mediaId}
            />
            <FormattedMessage id="proposal.form.media.mainPhoto" />
          </label>
          {!isListProposal && 
            <label
              className={`${classes.label} ${!isPhoto ? classes.hide : undefined}`}
            >
              <Checkbox
                className={classes.checkbox}
                value={mediaId}
                onChange={handleMain2Change}
                checked={main2Photo == mediaId}
              />
              <FormattedMessage id="proposal.form.media.sidePhoto" />
            </label>
          }
          <label
            className={`${classes.label} ${!isPhoto ? classes.hide : undefined}`}
          >
            <Checkbox
              className={classes.checkbox}
              value={mediaId}
              onChange={(event) => toggle(event, mediaId)}
              checked={!!_.get(form, `${mediaPath}attachmentMultiImgConfig.${mediaId}`)}
            />
            <DescriptionIcon className={classes.labelIcon} />
          </label>
          {/*{sbu !== "SHOPS" && (*/}
          {/*  <label className={`${classes.label} ${!isPhoto ? classes.hide : undefined}`}>*/}
          {/*    2*/}
          {/*    <Checkbox*/}
          {/*      className={classes.checkbox}*/}
          {/*      value={mediaId}*/}
          {/*      onChange={handleMain2Change}*/}
          {/*      checked={main2Photo === mediaId}*/}
          {/*    />*/}
          {/*  </label>*/}
          {/*)}*/}
          
        </div>
        {!!_.get(form, `${mediaPath}attachmentMultiImgConfig.${mediaId}`) && (
          <div>
            <TextField
              className={classes.box2}
              select
              label=""
              value={_.get(form, `${mediaPath}attachmentMultiImgConfig.${mediaId}`)}
              onChange={multiImgChange}
              helperText=""
            >
              {multiImgOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          </div>
        )}
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  form: getFormValues("proposal")(state),
});

const mapDispatchToProps = (dispatch) => {
  return {
    changeFieldValue: (field, value) =>
      dispatch(change("proposal", field, value)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(ProposalControlRow)));
