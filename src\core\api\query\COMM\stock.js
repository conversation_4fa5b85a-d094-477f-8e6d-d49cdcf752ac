export const LIST_STOCK_DETAIL_QUERY = `
query ($_id: [ID], $backendKey: String!) {
  stocks(_id: $_id, backendKey: $backendKey) {
    _id
    stockType {
      nameEn
      nameZh
      _id
    }
    unicorn {
      id
      landSearch
      landSearchDetail {
        search_ref_no
        address
        flat
        floor
      }
    }
    askingRent {
      total
      average
      trend
      details {
        value
        type
      }
    }
    askingPrice {
      total
      average
      trend
      details {
        value
        type
      }
    }
    unit
    floor
    status
    source
    possession
    currentState {
      nameZh
      nameEn
    }
    currentStateId
    availability
    managementFee
    isIncludeAirConditioning
    isIncludeManagementFee
    keyNumber
    rates
    isIncludeRates
    governmentRent
    isIncludeGovernmentRent
    completionDate
    inspection
    area {
      size
      efficiency
      sizeProving
      sizes {
        value
        type
      }
    }
    unitView {
      nameEn
      nameZh
    }
    decoration {
      nameEn
      nameZh
    }
    unitViews {
      nameEn
      nameZh
    }
    decorations {
      nameEn
      nameZh
    }
    building {
      _id
      nameEn
      nameZh
      unicorn {
        id
      }
      floors {
        type
        name
      }
      buildingUsage
      buildingUsageZh
      street {
        number
        street {
          nameEn
          nameZh
        }
      }
      district {
        nameEn
        nameZh
      }
      coordinates {
        latitude
        longitude
      }
      completionDate
      developers {
        nameZh
        nameEn
      }
      managementCompany {
        managementCompany {
          nameZh
          nameEn
        }
      }
      passengerLift
      haveCargoLift
      airConditioning {
        type
        typeZh
        desc
        openingTime
        extraCharges
      }
      transportZh
      transportEn
      title
      haveCarPark
    }
    facilities {
      conferenceRoom
      meetingRoom
      room
      utilityRoom
      storeRoom
      workstation
      windowBlinds
      computerRoom
      desk
    }
    remarks {
      quick
      surroundings
      landlordProvisions
      internal
    }
    recordOperation {
      createDate
      lastUpdateDate
      createBy {
        emp_id
        name_en
      }
      lastUpdateBy {
        emp_id
        name_en
      }
    }
    currentHands
    vendors {
      hands
      handsId
      contactsPerson {
        _id
        nameEn
        nameZh
        titleEn
        titleZh
        companies {
          _id
          nameEn
          nameZh
        }
        contact {
          phones {
            type
            number
            doNotContact
            privacy
          }
        }
      }
      contactsPersons {
        _id
        nameEn
        nameZh
        title
        remarks
        contact {
          phones {
            type
            number
            doNotContact
            privacy
          }
        }
      }
      contactsCompanies {
        _id
        nameEn
        nameZh
        remarks
        contact {
          phones {
            type
            number
            doNotContact
            privacy
          }
        }
      }
    }
    consultantShares {
      empId
      date
      remarks
      employee {
        name_en
      }
    }
    propertyAdvertisements {
      saleData {
        minTotal
        minAverage
        maxTotal
        maxAverage
        minDate
        maxDate
        consultant {
          name_en
          dept_code
        }
      }
      rentData {
        minTotal
        minAverage
        maxTotal
        maxAverage
        minDate
        maxDate
        consultant {
          name_en
          dept_code
        }
      }
    }
    tenancy {
      rentFree
      rentFreeZh
      freePeriod {
        days
        months
      }
      leasePeriod {
        months
        years
      }
      depositInMonth
      leaseOptions {
        years
        rent {
          amount
        }
      }
      premium
      breakLease
    }
    tenancyRecords {
      status
      deleted
      rentalFee
      tenants {
        isTenant
        contactsPerson {
          _id
          nameEn
          nameZh
          titleEn
          titleZh
          companies {
            _id
            nameEn
            nameZh
          }
          contact {
            phones {
              type
              number
              doNotContact
            }
          }
        }
        contactsPersons {
          _id
          nameEn
          nameZh
          title
          remarks
          isTenant
          contact {
            phones {
              type
              number
              doNotContact
            }
          }
        }
        contactsCompanies {
          _id
          nameEn
          nameZh
          remarks
          contact {
            phones {
              type
              number
              doNotContact
            }
          }
        }
      }
      expiry {
        minDate
        maxDate
      }
    }
    updateHistory {
      handsId
      isCancelled
      datetime
      employee {
        name_en
        dept_code
      }
      status {
        nameEn
        nameZh
      }
      askingPrice {
        average
      }
      askingRent {
        average
      }
      description
    }
    mortgagee {
      at
    }
    isSubDivide
    isCarPark
    isWWW
    saleEquity
    soleagent {
      periodStart
      periodEnd
      assignDate
      agents {
        emp_id
      }
    }
    inspectionZh
    possessionZh
    sourceZh
    handsInfo {
      _id
      hands
      isCancelled
    }
    photoUrl
    videoUrl
    vrUrl
    surveyorProposal {
      status
      date
    }
  }
}
`;
