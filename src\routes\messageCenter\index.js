/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import Layout from "../../components/Layout";
import { FormattedMessage } from "react-intl";

const title = "Public Message";

async function action({ store, params }) {
  const { auth } = store.getState();
  if (!auth.user) {
    return { redirect: "/login" };
  } else if (auth.user.authorized == false) {
    return { redirect: "/login" };
  }

  const MessageCenter = await require.ensure(
    [],
    require => require("./MessageCenter").default,
    "messageCenter"
  );
  return {
    chunks: ["messageCenter"],
    title,
    component: (
      <Layout
        header={<FormattedMessage id="home.publicmessage" />}
        hideSearchIcon={true}
        isSticky={true}
      >
        <MessageCenter />
      </Layout>
    )
  };
}

export default action;
