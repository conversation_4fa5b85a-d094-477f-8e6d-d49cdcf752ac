/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import _ from "lodash";
import InfiniteList from "../../../common/InfiniteList";
import TransactionCard from "./TransactionCard";
import {
  listTransactions,
  clearTransactions
} from "../../../../actions/transaction";
import LoadingOverlay from "../../../LoadingOverlay";

const defaultQuery = {
  limit: 25,
  offset: 0
};

const defaultSorter = [
  { field: "date", order: "DESC" },
];

const styles = theme => ({
  root: {
    padding: "1vw 2vw"
  },
  card: {
    marginBottom: "1vh"
  }
});

class List extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    transactions: PropTypes.array,
    listTransactions: PropTypes.func.isRequired,
    queryvariables: PropTypes.object,
    hasMore: PropTypes.bool,
  };

  constructor(props) {
    super(props);
    this.state = {
      queryvariables: this.props.queryvariablesFromUrl || {}
    };
  }

  fetchData = (variables, isMore) => {
    const selectedData = this.props.selectedDataFromUrl
    this.props.listTransactions(variables, isMore, selectedData);
  };

  fetchMoreData = () => {
    let variables = this.state.queryvariables;
    let newQuery = {
      ...variables,
      offset: variables.offset + 25
    };
    this.setState({ queryvariables: newQuery });
    this.fetchData(newQuery, true);
  };

  firstFetch = variables => {
    this.fetchData(variables, false);
  };

  checkQueryAndFetch = query => {
    let clone = query ? JSON.parse(JSON.stringify(query)) : defaultQuery;

    if (!clone.sorter || !Array.isArray(clone.sorter) || clone.sorter.length === 0)
      clone.sorter = defaultSorter;

    this.firstFetch(clone);
    this.updateQuery(clone);
  };

  componentDidMount() {
    if (this.props.isFirstFetch == true) {
      this.checkQueryAndFetch(this.props.queryvariablesFromUrl)
    } else {
      if (this.props.isFirstFetch && this.props.listed) {
        this.updateQuery(this.props.queryvariables);
      } else if (!this.props.listed) {
        // only the first time user enter this page, we do data fetch
        this.checkQueryAndFetch(this.props.queryvariablesFromUrl);
      }
      else {
        this.updateQuery(this.props.queryvariables);
      }
    }
  }

  componentDidUpdate(prevProps) {
    if (
      !_.isEqual(
        prevProps.queryvariablesFromUrl,
        this.props.queryvariablesFromUrl
      )
    ) {
      this.checkQueryAndFetch(this.props.queryvariablesFromUrl);
    }
  }

  updateQuery = newQuery => {
    this.setState({ queryvariables: newQuery });
  };

  render() {
    const {
      classes,
      listing,
      listed,
      transactions,
      hasMore,
      expanded
    } = this.props;
    const { queryvariables } = this.state;

    return (
      <div className={classes.root}>
        {listed && (
          <InfiniteList list={transactions} fetchMoreData={this.fetchMoreData} hasMore={hasMore}>
            {({ ...props }) =>
              <TransactionCard className={classes.card} {...props} />
            }
          </InfiniteList>
        )}
        {listing && <LoadingOverlay />}
      </div>
    );
  }
}

const mapStateToProps = state => ({
  transactions: state.transaction.transactions ? state.transaction.transactions : [],
  listed: state.transaction.listed ? state.transaction.listed : false,
  listing: state.transaction.listing ? state.transaction.listing : false,
  queryvariables: state.transaction.queryvariables ? state.transaction.queryvariables : {},
  hasMore: state.transaction.hasMore ? state.transaction.hasMore : false
});

const mapDispatchToProps = dispatch => {
  return {
    listTransactions: (...args) => dispatch(listTransactions(...args)),
    clearTransactions: () => dispatch(clearTransactions()),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(List));
