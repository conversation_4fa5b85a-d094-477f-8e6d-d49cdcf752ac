/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import InfiniteList from "../common/InfiniteList";
import MessageCard from "../common/MessageCard";
import {
  listMessages
} from "../../actions/messageCenter";

const styles = theme => ({
  card: {
    marginBottom: "1vh"
  }
});

class List extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    messages: PropTypes.array,
    listMessages: PropTypes.func.isRequired,
    queryvariables: PropTypes.object,
    hasMore: PropTypes.bool,
    messageType: PropTypes.string,
    
    privateQueryvariables: PropTypes.object,
    privateHasMore: PropTypes.bool,
  };

  fetchMoreData = () => {
    const {queryvariables, privateQueryvariables, messageType} = this.props
    let variables = messageType === 'private' ? privateQueryvariables : queryvariables;
    variables.offset += 25;
    this.props.listMessages(variables, true, messageType);
  };

  render() {
    const {
      classes,
      messages,
      hasMore,
      privateHasMore,
      messageType
    } = this.props;

    return (
      <div className={classes.root}>
        <InfiniteList list={messages} fetchMoreData={this.fetchMoreData} hasMore={messageType === 'private' ? privateHasMore : hasMore}>
          {({...props}) =>
            <MessageCard className={classes.card} {...props} />
          }
        </InfiniteList>
      </div>
    );
  }
}

const mapStateToProps = state => ({
  queryvariables: state.messageCenter.queryvariables ? state.messageCenter.queryvariables : {},
  hasMore: state.messageCenter.hasMore ? state.messageCenter.hasMore : false,
  
  privateQueryvariables: state.messageCenter.privateQueryvariables ? state.messageCenter.privateQueryvariables : {},
  privateHasMore: state.messageCenter.privateHasMore ? state.messageCenter.privateHasMore : false
});

const mapDispatchToProps = dispatch => {
  return {
    listMessages: (...args) => dispatch(listMessages(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withStyles(styles)(List));
