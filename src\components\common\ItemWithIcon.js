import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import FieldVal from "./FieldVal";
import Grid from "@material-ui/core/Grid";
import { injectIntl } from "react-intl";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    "&:not(:last-child)": {
      marginBottom: "1vh"
    }
  },
  doubleDash: {
    margin: "0 5vw"
  },
  text: {
    padding: "1vw 2vw",
    borderRadius: "4px",
    backgroundColor: "rgba(132, 132, 132, .1)"
  },
  textContent: {
    // fontSize: "1.125em"
  },
  contactName: {
    // fontSize: "1.125em"
  },
  contactContainer: {
    paddingTop: 8
  }
};

function ItemWithIcon(props) {
  const { classes, textContent, contactName, icon, key, intl } = props;
  return (
    <div className={classes.root} key={key}>
      <div className={classes.text}>
        <div className={classes.textContent}>{textContent}</div>

        {icon && Array.isArray(icon) && icon.length > 0 && (
          <Grid container className={classes.contactContainer} spacing={1}>
            <Grid item xs={12}>
              <FieldVal
                field={intl.formatMessage({
                  id: "stock.contact"
                })}
              />
            </Grid>

            <Grid item xs={12}>
              {contactName}
            </Grid>
            {icon.map((v, i) => (
              <Grid item xs={6} key={i}>
                {v}
              </Grid>
            ))}
          </Grid>
        )}
        {icon && !Array.isArray(icon) && (
          <Grid container>
            <Grid item xs={12}>
              {icon}
            </Grid>
          </Grid>
        )}
      </div>
    </div>
  );
}

ItemWithIcon.propTypes = {
  classes: PropTypes.object.isRequired,
  textContent: PropTypes.node,
  contactName: PropTypes.node,
  icon: PropTypes.oneOfType([PropTypes.node, PropTypes.arrayOf(PropTypes.node)])
};

export default withStyles(styles)(injectIntl(ItemWithIcon));
