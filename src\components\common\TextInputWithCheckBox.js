import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { withStyles } from "@material-ui/core/styles";
import TextField from "@material-ui/core/TextField";

// We can inject some CSS into the DOM.
const styles = {
    // root: {
    //   borderColor: "blue"
    // },
    // notchedOutline: {
    //   borderWidth: "1px",
    //   borderColor: "green !important"
    // }
};

function TextInputWithCheckBox(props) {
    const {
        classes,
        className,
        label,
        input,
        meta: { touched, invalid, error },
        ...custom
    } = props;

    const handleonBlur = (e) => {
        e.preventDefault();
        input.onBlur()
    }

    const handleonChange = (e) => {
        input.onChange({ ...input.value, value: e.target.value })
        input.onBlur()
    }

    return (
        <TextField
            className={clsx(classes.root, className)}
            label={label}
            placeholder={label}
            // helperText={error}
            error={invalid}
            helperText={error}
            InputLabelProps={{
                shrink: true
            }}
            {...input}
            onChange={handleonChange}
            onBlur={handleonBlur}
            value={input.value.value}
            {...custom}
        />
    );
}

export default withStyles(styles)(TextInputWithCheckBox);
