import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import FieldVal from "../../../../common/FieldVal";
import { getLangKey, numberComma } from "../../../../../helper/generalHelper";
import { injectIntl } from "react-intl";
import DetailBoxSection from "../../../../common/DetailBoxSection";

const styles = (theme) => ({
  root: {
    padding: "1vh 0",
  },
  lmrAlign: {
    paddingLeft: "2vw",
  },
});

class BasicProperty extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;
    const langKey = getLangKey(intl);

    const ownerTypeMapping = {
      "Single Owner": intl.formatMessage({ id: "search.form.singleowner" }),
      "Investor": intl.formatMessage({ id: "search.form.investor" }),
      "Strata Title": intl.formatMessage({ id: "search.form.stratatitle" }),
      "Cooperation": intl.formatMessage({ id: "search.form.cooperation" }),
      "Mortgagee": intl.formatMessage({ id: "search.form.mortgagee" }),
      "Developer": intl.formatMessage({ id: "search.form.developer" }),
      "HandOver": intl.formatMessage({ id: "search.form.handover" }),
    };

    const ownerType =
      detail.ownerType && ownerTypeMapping[detail.ownerType]
        ? ownerTypeMapping[detail.ownerType]
        : "---";
    const unitViewName =
      detail.unitView && detail.unitView[langKey]
        ? detail.unitView[langKey]
        : "---";
    const decoName =
      detail.decoration && detail.decoration[langKey]
        ? detail.decoration[langKey]
        : "---";
    const possession =
      detail.possession && detail.possession[langKey]
        ? detail.possession[langKey]
        : "---";
    const currentState = 
      detail.currentState && detail.currentState[langKey]
        ? detail.currentState[langKey]
        : "---";
    const availability = detail.availability || "---";
    const keyNumber = detail.keyNumber || "---";
    const inspection =
      detail.inspection &&
      detail.inspection.type &&
      detail.inspection.type[langKey]
        ? detail.inspection.type[langKey]
        : "---";
    const commission = detail.commission ? detail.commission : "---";
    const yieldValue = detail.yield ? `${detail.yield}%` : "---";
    const terrace =
      detail.area && detail.area.terrace
        ? numberComma(detail.area.terrace) + intl.formatMessage({ id: "common.sqft" })
        : "---";
    const roof =
      detail.area && detail.area.roof
        ? numberComma(detail.area.roof) + intl.formatMessage({ id: "common.sqft" })
        : "---";
    const cockloft =
      detail.area && detail.area.cockloft
        ? numberComma(detail.area.cockloft) + intl.formatMessage({ id: "common.sqft" })
        : "---";

    const ownerTypeHeader = intl.formatMessage({
      id: "stock.ownertype",
    });
    const possessionHeader = intl.formatMessage({
      id: "stock.possession",
    });
    const currentStateHeader = intl.formatMessage({
      id: "stock.currentState",
    });
    const viewHeader = intl.formatMessage({
      id: "stock.view",
    });
    const availabilityHeader = intl.formatMessage({
      id: "stock.availability",
    });
    const decorationHeader = intl.formatMessage({
      id: "stock.decoration",
    });
    const keynumpwHeader = intl.formatMessage({
      id: "stock.keynumpw",
    });
    const inspectionHeader = intl.formatMessage({
      id: "stock.inspection",
    });
    const commissionHeader = intl.formatMessage({
      id: "stock.commission",
    });
    const yieldHeader = intl.formatMessage({
      id: "stock.yield",
    });
    const terraceHeader = intl.formatMessage({
      id: "stock.terrace",
    });
    const roofHeader = intl.formatMessage({
      id: "stock.roof",
    });
    const cockloftHeader = intl.formatMessage({
      id: "stock.cockloft",
    });

    let generalMapping = {
      [possessionHeader]: { value: possession, xs: 6 },
      [currentStateHeader]: { value: currentState, xs: 6 },
      [viewHeader]: { value: unitViewName, xs: 6 },
      [availabilityHeader]: { value: availability, xs: 6 },
      [decorationHeader]: { value: decoName, xs: 6 },
      [keynumpwHeader]: { value: keyNumber, xs: 6 },
      [inspectionHeader]: { value: inspection, xs: 6 },
      [ownerTypeHeader]: { value: ownerType, xs: 6 },
      [commissionHeader]: { value: commission, xs: 6 },
      [yieldHeader]: { value: yieldValue, xs: 6 },
      [terraceHeader]: { value: terrace, xs: 6 },
      [roofHeader]: { value: roof, xs: 6 },
      [cockloftHeader]: { value: cockloft, xs: 6 },
    };

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          text={intl.formatMessage({
            id: "stock.unit",
          })}
        >
          <Grid container spacing={2} className={classes.lmrAlign}>
            {Object.keys(generalMapping).map((v, i) => (
              <Grid item xs={generalMapping[v].xs} key={v}>
                <FieldVal field={v}>
                  {/* <div
                    className={
                      generalMapping[v].className
                        ? generalMapping[v].className
                        : null
                    }
                  > */}
                  {generalMapping[v].value}
                  {/* </div> */}
                </FieldVal>
              </Grid>
            ))}
          </Grid>
        </DetailBoxSection>
      </div>
    );
  }
}

export default withStyles(styles)(injectIntl(BasicProperty));
