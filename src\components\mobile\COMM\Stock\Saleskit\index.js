import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import ButtonGroup from "@material-ui/core/ButtonGroup";
import { injectIntl, FormattedMessage } from "react-intl";
import DetailTabPanelFrame from "../../../../common/DetailTabPanelFrame";
import {
  changeCurrentStock,
  listStockMedia,
} from "../../../../../actions/stock";
import { getProposalCount } from "../../../../../actions/proposal";
import { listBuildingMedia } from "../../../../../actions/building";
import ProposalCreate from "./ProposalCreate";
import {
  getDisplayStockId,
  getLangKey,
} from "../../../../../helper/generalHelper";
import { generalProposalQuota } from "../../../../../config";
import history from "../../../../../core/history";
import <PERSON>Field from "../../../../common/SelectField";
import _ from "lodash";
import BottomButtons from "../../../../common/BottomButtons";

const styles = (theme) => ({
  wrapper: {
    backgroundColor: "#f5f5f5",
  },
  proposalWrapper: {
    minHeight: "calc(100vh - 88px - 2vw)",
  },
  listProposalWrapper: {
    minHeight: "calc(100vh - 168px - 2vw)",
  },
  fixBtnContainer: {
    width: "100%",
    zIndex: "999",
    position: "fixed",
    left: 0,
    bottom: 0,
  },
  fixBtn: {
    textTransform: "none",
    fontSize: "1.125em",
    height: "60px",
    minWidth: "auto",
    lineHeight: "1em",
  },
  sectionWrapper: {
    paddingTop: "1vh",
  },
  title: {
    color: "#EC1F26",
  },
  num: {
    backgroundColor: "#EC1F26",
  },
  link: {
    textDecoration: "none",
  },
  formContainer: {
    paddingBottom: "8vh",
  },
});

function Media({
  classes,
  media,
  listedMedia,
  listingMedia,
  buildingMedia,
  listedBuildingMedia,
  listingBuildingMedia,
  detail,
  currentDetail,
  listed: listedStockData,
  listing: listingStockData,
  listStockMedia,
  listBuildingMedia,
  getProposalCount,
  // handleClickOpenDialog,
  changeCurrentStock,
  userInfo,
  createdPdf,
  intl,
}) {
  const [createProposalDialogOpen, setCreateProposalDialogOpen] =
    useState(false);
  const [exceedQuota, setExceedQuota] = useState(false);

  const [currentStock, setCurrentStock] = useState({});
  const [isListProposal, setIsListProposal] = useState(false);

  useEffect(() => {
    if (history)
      setIsListProposal(history.location.pathname === "/listProposal");
  }, []);

  useEffect(() => {
    if (detail && detail.length > 0) {
      // listStockMediaAfterIdReturned();
      getProposalCount();
    }
  }, [detail]);

  useEffect(() => {
    if (detail.length > 0 && detail[currentDetail]) {
      setCurrentStock(detail[currentDetail]);
    }
  }, [detail, currentDetail]);

  const handleOpenCreateProposalDialog = () => {
    const proposalscount = createdPdf;
    if (proposalscount >= generalProposalQuota) {
      setExceedQuota(true);
    } else {
      setExceedQuota(false);
    }
    setCreateProposalDialogOpen(true);
    window.dataLayer.push({
      stockId: getDisplayStockId(currentStock.unicorn.id),
    });
  };

  const handleCloseCreateProposalDialog = () => {
    setCreateProposalDialogOpen(false);
  };

  const getAddress = ({ building }) => {
    const langKey = getLangKey(intl);
    return `${building.street.number || ""} ${
      building.street.street[langKey] || "---"
    }, ${building.district[langKey] || "---"}`;
  };

  const addressOptions = () =>
    detail.map((d) => ({
      value: d._id,
      label: getAddress(d),
    }));

  const handleAddressChange = (e) => {
    const { value } = e.target;
    const idx = detail.findIndex((d) => d._id === value);
    changeCurrentStock(idx);
  };

  const stockMediaData =
    media?.filter((m) => m.id === currentStock?.unicorn?.id?.toString())?.[0]
      ?.data || {};
  console.log(buildingMedia);
  const buildingMediaData =
    buildingMedia?.filter(
      (m) => m.id === currentStock?.building?.unicorn?.id?.toString(),
    )?.[0]?.data || {};

  const hasStockData = stockMediaData && Object.keys(stockMediaData).length > 0;
  const hasBuildingData =
    buildingMediaData && Object.keys(buildingMediaData).length > 0;

  const hasData = hasStockData || hasBuildingData;
  const listing = listingStockData || listingMedia || listingBuildingMedia;
  const listed = listedStockData && (listedMedia || listedBuildingMedia);

  return (
    <div className={classes.wrapper}>
      {currentStock &&
      Object.keys(currentStock).length > 0 &&
      isListProposal ? (
        <div>
          <SelectField
            label={intl.formatMessage({ id: "proposal.general.address" })}
            ranges={addressOptions()}
            input={{
              value: currentStock._id,
              onChange: handleAddressChange,
            }}
            meta={{}}
            variant="outlined"
            fullWidth
          />
        </div>
      ) : null}
      <DetailTabPanelFrame
        wrapperProps={{
          className: isListProposal
            ? classes.listProposalWrapper
            : classes.proposalWrapper,
        }}
        hasData={hasData}
        listing={listing}
        listed={listed}
        notFoundText="Stock media not found"
      >
        <div className={classes.formContainer}>
          <ProposalCreate
            createProposalDialogOpen={createProposalDialogOpen}
            exceedQuota={exceedQuota}
            handleCloseCreateProposalDialog={handleCloseCreateProposalDialog}
          />
        </div>

        <BottomButtons
          buttons={[
            {
              label: intl.formatMessage({ id: "proposal.createpdf" }),
              onClick: handleOpenCreateProposalDialog,
            },
          ]}
        />
      </DetailTabPanelFrame>
    </div>
  );
}

const mapStateToProps = (state) => ({
  userInfo: _.get(state, "auth.user.login.info"),
  media: state.stock.media ? state.stock.media : [],
  listedMedia: state.stock.listedMedia ? state.stock.listedMedia : false,
  listingMedia: state.stock.listingMedia ? state.stock.listingMedia : false,
  buildingMedia: state.building.media ? state.building.media : [],
  listedBuildingMedia: state.building.listedMedia
    ? state.building.listedMedia
    : false,
  listingBuildingMedia: state.building.listingMedia
    ? state.building.listingMedia
    : false,
  detail: state.stock.detail ? state.stock.detail : [],
  currentDetail: state.stock.currentDetail ? state.stock.currentDetail : 0,
  listing: state.stock.listing ? state.stock.listing : false,
  listed: state.stock.listed ? state.stock.listed : false,
  createdPdf: state.proposal.createdPdf ? state.proposal.createdPdf : 0,
});

const mapDispatchToProps = (dispatch) => {
  return {
    listStockMedia: (...args) => dispatch(listStockMedia(...args)),
    listBuildingMedia: (...args) => dispatch(listBuildingMedia(...args)),
    getProposalCount: () => dispatch(getProposalCount()),
    changeCurrentStock: (current) => dispatch(changeCurrentStock(current)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(Media)));
