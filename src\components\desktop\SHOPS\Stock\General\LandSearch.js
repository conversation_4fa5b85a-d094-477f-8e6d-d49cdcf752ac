import React from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import { injectIntl } from "react-intl";
import { listLandSearch } from "../../../../../actions/stock";
import LandSearchCard from "../../../../common/LandSearchCard";
import ItemCount from "../../../../common/ItemCount";

const styles = (theme) => ({
  card: {
    padding: 0,
    backgroundColor: "transparent",
  },
  noData: {
    fontSize: "1.125em",
    paddingLeft: "2vw",
  },
});

class LandSearch extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.state = {
      isExpanding: false,
    };
  }

  componentDidMount() {
    const { detail } = this.props;
    const landSearchId = detail.unicorn && detail.unicorn.landSearch;
    this.props.listLandSearch(landSearchId);
  }

  componentDidUpdate(prevProps) {}

  componentWillUnmount() {}

  handleChange = (value) => {
    // get the value returned by child component
    this.setState({ isExpanding: value });
  };

  render() {
    const { classes, detail, intl } = this.props;
    const landSearchData =
      detail.landSearch && detail.landSearch.filter((i) => i.list.length > 0);

    return (
      <div className={classes.root}>
        <DetailBoxSection
          expandable={true}
          isExpanding={this.state.isExpanding}
          callback={this.handleChange}
          text={intl.formatMessage({ id: "stock.landsearch" })}
        >
          {landSearchData && landSearchData.length > 0 ? (
            <div>
              <ItemCount
                count={
                  landSearchData.filter((data) => data.list.length > 0).length
                }
                messageid="landSearch.list.count"
              />
              <Grid container spacing={1}>
                {landSearchData.map((v, i) => {
                  return (
                    <Grid item xs={12} key={i}>
                      {v.list.length > 0 && (
                        <LandSearchCard
                          className={classes.card}
                          isStockDetail={true}
                          detail={detail}
                          landSearch={v}
                        />
                      )}
                    </Grid>
                  );
                })}
              </Grid>
            </div>
          ) : (
            <div className={classes.noData}>
              {intl.formatMessage({ id: "stock.landsearch.null" })}
            </div>
          )}
        </DetailBoxSection>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  transactions: state.transaction.transactions
    ? state.transaction.transactions
    : [],
});

const mapDispatchToProps = (dispatch) => {
  return {
    listLandSearch: (args) => dispatch(listLandSearch(args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(styles)(injectIntl(LandSearch)));
