import React from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { makeStyles } from "@material-ui/styles";
import Paper from "@material-ui/core/Paper";

const useStyles = makeStyles({
  root: {
    padding: props => props.isMobile ? "2vw" : "8px",
    borderTop: props => props.isMobile ? "5px solid #FEE100" : "none",
    marginBottom: props => props.isMobile ? "1.5vw" : 0,
  }
});

let GridSection = (props, context) => {
  const { children, className, ...other } = props;
  const { browserDetect } = context;
  const classes = useStyles({
    isMobile: browserDetect === "mobile",
  });

  return (
    <Paper
      className={clsx(classes.root, className)}
      elevation={browserDetect === "mobile" ? 1 : 0}
      {...other}
    >
      {children}
    </Paper>
  );
};

GridSection.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string
};

GridSection.contextTypes = {
  browserDetect: PropTypes.string,
};

export default GridSection;
