import React, { useEffect } from "react";
import { Field, reduxForm } from "redux-form";
import PropTypes from "prop-types";
import { Grid, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import _ from "lodash";
import { connect } from "react-redux";
import { injectIntl } from "react-intl";

import TextInput from "@/components/common/TextInput";
import {
  APPLY_COMPANYSEARCH_GUIDANCE_ROWS,
  APPLY_COMPANYSEARCH_PURPOSES_ROWS,
} from "./constants";
import BooleanCheckBox from "@/components/common/BooleanCheckBox";
import { applyCompanySearch, createCompany } from "@/actions/company";
import { addActivityLog } from "@/actions/log";

const useStyles = makeStyles({
  form: {
    padding: "7px 0",
  },
  subtext: {
    lineHeight: 1.2,
    color: "rgba(0,0,0,.75)",
    fontSize: 14,
  },
  purposeRow: {
    marginBottom: 15,
  },
  checkboxLabel: {
    "& .MuiFormControlLabel-label": {
      lineHeight: 1.2,
      color: "rgba(0,0,0,.75)",
      fontSize: 14,
    },
  },
});

const parseForm = (values, company) => {
  const parsedForm = {
    ...values,
    declarations: [
      ...(values.declaration1 ? ["1"] : []),
      ...(values.declaration2 ? ["2"] : []),
    ],
    companyId: _.get(company, '_id')
  };
  _.unset(parsedForm, "declaration1");
  _.unset(parsedForm, "declaration2");
  return parsedForm;
};

const validate = (values, props) => {
  const errors = {};
  const mustInput = {
    categoryMsgId: "company.applySearch.mustInput",
  };
  const atLeastInputOne = {
    categoryMsgId: "company.applySearch.atLeastInputOne",
  };
  const atLeastCheckOne = {
    categoryMsgId: "company.applySearch.atLeastCheckOne",
  };

  if (!(values.declaration1 || values.declaration2)) {
    errors.declaration1 = {
      ...atLeastCheckOne,
      fieldMsgId: "company.applySearch.declarations1",
    };
    errors.declaration2 = {
      ...atLeastCheckOne,
      fieldMsgId: "company.applySearch.declarations2",
    };
  }

  if (props.isListApply && !values.companyName) {
    errors.companyName = {
      ...mustInput,
      fieldMsgId: "company.applySearch.companyName",
    }
  }
  return errors;
};

let ApplyCompanySearchForm = ({ company, initialize, initialized, intl, isListApply }) => {
  const classes = useStyles();

  useEffect(() => {
    initialize({
      companyRegistrationNumber:
        _.get(company, "companyRegistrationNumber") || "",
      companyName:
        _.get(company, "companyNameEn") ||
        _.get(company, "companyNameZh") ||
        "",
      declaration1: false,
      declaration2: false,
    });
  }, []);

  const renderGuidance = () => (
    <div className={classes.subtext}>
      <div>查冊指引：</div>
      {APPLY_COMPANYSEARCH_GUIDANCE_ROWS.map((row, idx) => (
        <div key={`guidance-row-${idx}`}>{row}</div>
      ))}
    </div>
  );

  const renderPurposes = () => (
    <div className={classes.subtext}>
      <div>查冊目的﹕</div>
      {APPLY_COMPANYSEARCH_PURPOSES_ROWS.map((row, idx) => (
        <div key={`purpose-row-${idx}`} className={classes.purposeRow}>
          <div>
            ({idx + 1}) {row.zh}
          </div>
          <div>{row.en}</div>
        </div>
      ))}
    </div>
  );

  const renderDeclarations = () => (
    <div style={{ marginTop: 20 }}>
      <Typography variant="h5" style={{ margin: "10px 0" }}>
        聲明
      </Typography>
      <div className={classes.subtext}>
        <div>當本人將此表格提交至查冊資訊部，即表示</div>
        <Grid container style={{ margin: "10px 0" }}>
          <Grid item xs={12}>
            <Field
              name="declaration1"
              label="1. 本人確認已細讀和明白上述使用公司註冊處電子查冊服務的條款及條件和私隱聲明，並同意受其約束。"
              component={BooleanCheckBox}
              type="checkbox"
              className={classes.checkboxLabel}
            />
          </Grid>
          <Grid item xs={12}>
            <Field
              name="declaration2"
              label="2. 本人現提交上述有關查冊的目的聲明，本人明白，查冊所得個人資料只能作本人所聲明的用途。"
              component={BooleanCheckBox}
              type="checkbox"
              className={classes.checkboxLabel}
            />
          </Grid>
        </Grid>
        <div>
          公司註冊處表示，如果查冊目的是為了確定該公司、其董事或其他高級人員、或其前董事（如有的話）的詳情，可選該聲明內的第
          7 項
        </div>
      </div>
    </div>
  );

  return (
    initialized && (
      <form className={classes.form}>
        {renderGuidance()}

        <Grid container direction="column" style={{ margin: "20px 0" }}>
          <Grid item style={{ marginBottom: 10 }}>
            <Field
              name="companyName"
              component={TextInput}
              label={intl.formatMessage({
                id: "company.applySearch.companyName",
              })}
              showPlaceholder={false}
              helperText="* 請用大楷及全寫輸入, 如 COMPANY LIMITED"
              disabled={!isListApply}
            />
          </Grid>
          <Grid item>
            <Field
              name="companyRegistrationNumber"
              component={TextInput}
              label={intl.formatMessage({
                id: "company.applySearch.companyId",
              })}
              showPlaceholder={false}
              disabled={!isListApply}
            />
          </Grid>
        </Grid>

        {renderPurposes()}
        {renderDeclarations()}
      </form>
    )
  );
};

ApplyCompanySearchForm = reduxForm({
  form: "applyCompanySearch",
  validate: (values, props) => validate(values, props),

  onSubmit: async (values, dispatch, props) => {
    if(props.isListApply) {
      const data = await dispatch(createCompany({
        companyNameZh: values.companyName,
        companyNameEn: values.companyName
      }))
      const formData = parseForm(values, {_id: _.get(data, 'createCompanyBookContact._id')});
      dispatch(addActivityLog("company.applySearch", "create", formData));
      dispatch(applyCompanySearch(formData));
    }else{
      const formData = parseForm(values, props.company);
      dispatch(addActivityLog("company.applySearch", "create", formData));
      dispatch(applyCompanySearch(formData));
    }
  },
})(ApplyCompanySearchForm);

ApplyCompanySearchForm.propTypes = {
  company: PropTypes.object.isRequired,

  initialize: PropTypes.func,
  initialized: PropTypes.bool,

  intl: PropTypes.object.isRequired,
  isListApply: PropTypes.bool
};

const mapStateToProps = (state) => ({
  company: state.company.company,
});

export default connect(mapStateToProps)(injectIntl(ApplyCompanySearchForm));
