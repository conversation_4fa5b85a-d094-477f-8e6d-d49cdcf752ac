import React, { useState, useEffect, lazy, Suspense } from "react";
import PropTypes from "prop-types";
import { connect, useDispatch, useSelector } from "react-redux";
import _ from "lodash";
import { injectIntl } from "react-intl";

import DetailTabPanelFrame from "../common/DetailTabPanelFrame";
import ModeSelector from "./ModeSelector";
import {sbu, wholeBlockTypeId} from "../../config";
import LoadingOverlay from "../LoadingOverlay";
import { queryProposalRes } from "../../actions/stock";
import {
  clearCreateProposal,
  clearCreatedPPPerview,
  createPreview,
  getProposalCount,
} from "../../actions/proposal";
import { makeStyles } from "@material-ui/core/styles";
import { change, getFormValues } from 'redux-form';
import { getStockFloorType } from "../Saleskit/helpers";
import PerviewProposalDialog from "../common/PerviewProposalDialog";
import {PERMISSIONS} from "@/constants/auth";
import Dialog from "@/components/common/Dialog";
import { DialogContent } from "@material-ui/core";
import { FormattedMessage } from "react-intl";
import OneBtnDialog from "../common/OneBtnDialog";

const useStyles = makeStyles(() => ({
  greyBtn: {
    backgroundColor: "#626262",
    "&:hover, &:active": {
      backgroundColor: "#626262",
    }
  },
  errorText: {
    color: "#F44336",
    fontSize: "0.875em",
    textAlign: "center",
    marginTop: "2vh",
    maxHeight: "30vh",
    overflowY: "auto",
    overflowX: "hidden",
  }
}));

const formLoaders = {
  indv: {
    COMM: lazy(() => import("../mobile/COMM/Proposal/Individual")),
    IND: lazy(() => import("../mobile/IND/Proposal/Individual")),
    SHOPS: lazy(() => import("../mobile/SHOPS/Proposal/Individual")),
  },
  list: {
    COMM: lazy(() => import("../mobile/COMM/Proposal/List")),
    IND: lazy(() => import("../mobile/IND/Proposal/List")),
    SHOPS: lazy(() => import("../mobile/SHOPS/Proposal/List")),
  },
};

function Saleskit({
  hasData,
  listingData,
  listedData,
  multipleStocks,
  mode,
  getProposalCount,
  queryProposalRes,
  clearCreateProposal,
  clearCreatedPPPerview,
  intl,
  proposal,
  stock,
  createPreviewError,
}) {
  const dispatch = useDispatch();
  const classes = useStyles();

  const [type, setType] = useState(multipleStocks ? "list" : "list"); //只使用listpp
  const [tempMode, setTempMode] = useState(mode);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [floorBtnLabel, setFloorBtnLabel] = useState(true);
  const [unitBtnLabel, setUnitBtnLabel] = useState(true);
  const [wbPermissionDialog, setWbPermissionDialog] = useState(false);

  const perviewUrl = useSelector(state => state.proposal.perviewUrl);
  const width = useSelector(state => state.proposal.width);
  const height = useSelector(state => state.proposal.height);

  const permissions = useSelector(state => _.get(state, "employee.permissions"));
  const proposalProps = useSelector((state) => getFormValues('proposal')(state));
  const indvPPId = useSelector(state => _.get(state, "stock.currentDetail"));
  const stocks = useSelector(state => _.get(state, "stock.detail"));
  const { ALLOW_WHOLE_BLOCK_FOR_PP } = PERMISSIONS;

  useEffect(() => {
    queryProposalRes();
    getProposalCount();
  }, []);

  const allowWholeBlockForPP = () => {
    if (sbu !== "SHOPS") {
      return true;
    }
    let hasWbStock;
    if (type === "list") {
      // console.log("only selected:", _.find(proposalProps.stocks, o => o.stock.show ))
      // console.log("wb & selected:", _.find(proposalProps.stocks, o => o.stock.show &&  o.stock.stockTypeId === wholeBlockTypeId))
      hasWbStock = _.some(proposalProps.stocks, o => o.stock.show && o.stock.stockTypeId === wholeBlockTypeId)
    } else {
      // console.log("only selected:", _.find(stocks, o => o._id === indvPPId))
      // console.log("wb & selected:", _.find(stocks, o => o._id === indvPPId && o.stockTypeId === wholeBlockTypeId ))
      hasWbStock = _.some(stocks, o => o._id === indvPPId && o.stockTypeId === wholeBlockTypeId)
    }
    // console.log("permissions:", permissions[ALLOW_WHOLE_BLOCK_FOR_PP])
    return !(hasWbStock && !permissions[ALLOW_WHOLE_BLOCK_FOR_PP]);
  }

  const previewPPHandle = () => {
    if (!allowWholeBlockForPP()) {
      setWbPermissionDialog(true)
      return;
    }
    dispatch(createPreview(type === "indv" ? "proposal" : "listProposal"))
  }

  const createPPPDFHandle = () => {
    if (!allowWholeBlockForPP()) {
      setWbPermissionDialog(true)
      return;
    }
    setCreateDialogOpen(true)
  }

  const bottomButtons = [
    ...sbu !== "SHOPS" ? [
      // {
      //   className: classes.greyBtn,
      //   label: intl.formatMessage({ id: floorBtnLabel ? "proposal.setHML" : "proposal.exactFloor" }),
      //   onClick: () => {
      //     setFloorBtnLabel(!floorBtnLabel);
      //     if (type === "indv") {
      //       if (floorBtnLabel) {
      //         const floor = _.get(stock[0], "floor") || "";
      //         dispatch(change("proposal", "stock.floorType",
      //           getStockFloorType(_.get(stock[0], "building.floors") || [],
      //             floor)))
      //       } else
      //         dispatch(change("proposal", "stock.floorType", "Actual Floor"))
      //     } else if (type === "list") {
      //       if (floorBtnLabel) {
      //         _.forEach(proposal.stocks, (value, key) => {
      //           const currentStock = stock.filter((value) => {
      //             return value._id === key
      //           })
      //           const floor = _.get(currentStock[0], "floor") || "";
      //           dispatch(change("proposal", `stocks.${key}.stock.floorType`,
      //             getStockFloorType(_.get(currentStock[0], "building.floors") || [],
      //               floor)))
      //         })
      //       } else {
      //         _.forEach(proposal.stocks, (value, key) => {
      //           dispatch(change("proposal", `stocks.${key}.stock.floorType`, "Actual Floor"))
      //         })
      //       }
      //     }
      //   }
      // },
      // {
      //   className: classes.greyBtn,
      //   label: intl.formatMessage({ id: unitBtnLabel ? "proposal.hideUnit" : "proposal.showUnit" }),
      //   onClick: () => {
      //     setUnitBtnLabel(!unitBtnLabel);
      //     if (type === "indv")
      //       dispatch(change("proposal", "stock.unit[0].isShow", !unitBtnLabel))
      //     else if (type === "list") {
      //       _.forEach(proposal.stocks, (value, key) => {
      //         dispatch(change("proposal", `stocks.${key}.stock.unit[0].isShow`, !unitBtnLabel))
      //       })
      //     }
      //   }
      // },
    ] : [],
    {
      label: intl.formatMessage({ id: "proposal.preview" }),
      onClick: () => previewPPHandle()
    },
    {
      label: intl.formatMessage({ id: "proposal.createpdf" }),
      onClick: () => createPPPDFHandle(),
    },
  ];

  const FormComponent = formLoaders[type][sbu];
  return (
    <DetailTabPanelFrame
      hasData={hasData}
      listing={listingData}
      listed={listedData}
      notFoundText="Initialization failed. Please retry."
      showBg={false}
      bottomButtons={bottomButtons}
    >
      {/* {<ModeSelector mode={tempMode} setMode={setTempMode} />} */}

      <Suspense fallback={<LoadingOverlay />}>
        <FormComponent
          createDialogOpen={createDialogOpen}
          closeDialog={() => {
            setCreateDialogOpen(false);
            clearCreateProposal();
          }}
          mode={tempMode}
        />
        <OneBtnDialog
          open={!!createPreviewError}
          handleClose={() => {
            clearCreatedPPPerview();
          }}
          btn={intl.formatMessage({ id: "common.ok" })}
          btnHandleClick={() => {
            clearCreatedPPPerview();
          }}
        >
          <div className={classes.errorText}>
            {createPreviewError}
          </div>
        </OneBtnDialog>
        <PerviewProposalDialog
          open={!!perviewUrl}
          perviewUrl={perviewUrl}
          close={() => {
            clearCreatedPPPerview();
          }}
        />
      </Suspense>


      <Dialog
        open={wbPermissionDialog}
        handleClose={() =>
          setWbPermissionDialog(false)
        }
      >
        <DialogContent style={{ padding: "10px 20px" }}>
          <FormattedMessage id="proposal.whole.block.permission.error" />
        </DialogContent>
      </Dialog>

    </DetailTabPanelFrame>
  );
}

Saleskit.propTypes = {
  hasData: PropTypes.bool.isRequired,
  listingData: PropTypes.bool.isRequired,
  listedData: PropTypes.bool.isRequired,
  multipleStocks: PropTypes.bool.isRequired,
  mode: PropTypes.string,
  getProposalCount: PropTypes.func.isRequired,
  queryProposalRes: PropTypes.func.isRequired,
  clearCreateProposal: PropTypes.func.isRequired,
  clearCreatedPPPerview: PropTypes.func.isRequired,
  intl: PropTypes.object.isRequired,
  proposal: PropTypes.object.isRequired,
  stock: PropTypes.array.isRequired,
  createPreviewError: PropTypes.string,
};

const mapStateToProps = (state) => ({
  hasData:
    !_.isNil(_.get(state, "stock.media", [])) &&
    !_.isNil(_.get(state, "building.media", [])) &&
    (sbu === "SHOPS" ||
      (!_.isEmpty(_.get(state, "stock.unitViews", [])) &&
        !_.isEmpty(_.get(state, "stock.decorations", [])))) &&
    !_.isEmpty(_.get(state, "stock.possessions", [])) &&
    !_.isEmpty(_.get(state, "stock.currentStates", [])) &&
    _.get(state, "proposal.queryProposalResSuccess") &&
    !_.get(state, "proposal.queryProposalResError"),
  listingData: _.some(
    [
      _.get(state, "stock.listing"),
      _.get(state, "stock.listingMedia"),
      _.get(state, "building.listingMedia"),
      _.get(state, "proposal.queryingProposalRes"),
    ],
    Boolean,
  ),
  listedData:
    _.get(state, "stock.listed") &&
    _.get(state, "proposal.queryProposalResSuccess") &&
    !_.get(state, "proposal.queryingProposalRes") &&
    (_.get(state, "stock.listedMedia") || _.get(state, "building.listedMedia")),
  proposal: getFormValues("proposal")(state),
  stock: _.get(state, "stock.detail"),
  createPreviewError:
  _.get(state, "proposal.createPreviewError.message") ||
  _.get(state, "listProposal.createPreviewError.message"),
});

const mapDispatchToProps = (dispatch) => ({
  getProposalCount: () => dispatch(getProposalCount()),
  queryProposalRes: () => dispatch(queryProposalRes()),
  clearCreateProposal: () => dispatch(clearCreateProposal()),
  clearCreatedPPPerview: () => dispatch(clearCreatedPPPerview()),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(Saleskit));
