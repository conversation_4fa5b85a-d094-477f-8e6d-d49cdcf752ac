import {
  LIST_STREETS_START,
  LIST_STREETS_SUCCESS,
  LIST_STREETS_ERROR,
  C<PERSON>AR_STREETS,
  SEARCH_STREETS_START,
  SEARCH_STREETS_ERROR,
  SEARCH_STREETS_SUCCESS,
  LIST_STREET_MEDIA_START,
  LIST_STREET_MEDIA_SUCCESS,
  LIST_STREET_MEDIA_ERROR,
  UPDATE_STREET_MEDIA_BY_INDEX,
  UPDATE_STREET_MEDIA_LIST,
} from "../constants/street";
import { addActivityLog } from "./log";

export function listStreets(variables) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_STREETS_START,
      checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        }, //getState().auth.user.casAccessToken }
      };

      const query = await getQuery("LIST_STREETS_QUERY");

      const data = await universalRequest("/street/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({
        type: LIST_STREETS_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_STREETS_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };
}

export function streetSearch(variables) {
  return async (
    dispatch,
    getState,
    { graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: SEARCH_STREETS_START,
      checkrefreshToken: true,
    });

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          "Content-Type": "application/json",
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem("casAccessToken"),
        },
      };

      const query = await getQuery("SEARCH_STREETS_QUERY");

      const data = await universalRequest("/street/graphql", {
        method: "POST",
        body: JSON.stringify({
          query,
          variables
        }),
        ...options
      }).catch((error) => {
        return {
          errors: error,
        };
      });

      if (data.errors) {
        throw new Error(data.errors[0].message);
      }

      dispatch({ type: SEARCH_STREETS_SUCCESS, payload: { data } });
    } catch (e) {
      console.error(e);
      dispatch({
        type: SEARCH_STREETS_ERROR,
        payload: {
          error: e,
        },
      });
    }
  };
}

export function clearStreets() {
  return async (dispatch) => {
    dispatch({
      type: CLEAR_STREETS,
    });
  };
}

export function listStreetMedia(variables) {
  return async (
    dispatch,
    getState,
    { fetch, graphqlRequest, api, getQuery, delay, universalRequest },
  ) => {
    dispatch({
      type: LIST_STREET_MEDIA_START,
      checkrefreshToken: true,
    });

    dispatch(addActivityLog("media.search", "read", { ...variables, mediaType: "street" }));

    try {
      const { refreshing } = getState().auth;
      refreshing && (await delay(1500));

      const options = {
        headers: {
          Authorization: getState().auth.user.oauth,
          "CAS-Authorization": localStorage.getItem('casAccessToken'),//getState().auth.user.casAccessToken,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      };

      const query = await getQuery("LIST_STREET_MEDIA_QUERY");
      const { sid, empId } = variables;

      const promises = sid.map(id =>
        universalRequest("/media/graphql", {
          method: "POST",
          body: JSON.stringify({
            query,
            variables: { sid: id, empId },
          }),
          ...options
        }).then(res => {
          if (!res.errors) {
            return { id, data: res.data.street };
          }
          return null;
        }).catch(() => null)
      );
      const results = await Promise.all(promises);
      const data = results.filter(Boolean);

      dispatch({
        type: LIST_STREET_MEDIA_SUCCESS,
        payload: {
          data,
        },
      });
    } catch (error) {
      dispatch({
        type: LIST_STREET_MEDIA_ERROR,
        payload: {
          error,
        },
      });
      // throw new Error(error);
    }
  };
}

export function updateStreetMediaByIndex({ index = -1, mediaIndex = -1, mediaType = "photo", media }) {
  return async (
    dispatch,
  ) => {
    dispatch({
      type: UPDATE_STREET_MEDIA_BY_INDEX,
      payload: {
        index,
        mediaIndex,
        mediaType,
        media,
      },
    });
  };
}

export function updateStreetMediaList(mediaType, mediaList) {
  return {
    type: UPDATE_STREET_MEDIA_LIST,
    payload: {
      mediaType,
      mediaList
    }
  };
}
