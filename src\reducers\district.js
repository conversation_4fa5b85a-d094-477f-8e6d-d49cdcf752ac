import {
  LIST_DISTRICTS_START,
  LIST_DISTRICTS_SUCCESS,
  LIST_DISTRICTS_ERROR,
  CLEAR_DISTRICTS
} from "../constants/district";

const initialState = {
  listed: false,
  listing: false
};

export default function district(state = initialState, action) {
  switch (action.type) {
    case LIST_DISTRICTS_START:
      return {
        ...state,
        listed: false,
        listing: true,
        districts: null
      };
    case LIST_DISTRICTS_SUCCESS:
      return {
        ...state,
        listed: true,
        listing: false,
        districts: action.payload.data.data.districts
      };
    case LIST_DISTRICTS_ERROR:
      return {
        ...state,
        listed: false,
        listing: false,
        error: action.payload.error
      };
    case CLEAR_DISTRICTS:
      return {
        ...state,
        listed: false,
        listing: false,
        districts: null
      };
    default:
      return state;
  }
}
