import fetch from "node-fetch";
import _ from "lodash";
import moment from "moment";
import {
  api,
  backend<PERSON>ey,
  dmoToken,
  generalDailyQuota,
  sbu,
} from "../../config";
import mongodb from "../../data/mongodb";

function resJson(res, status, error, data) {
  res.json({
    status,
    errors: error ? [{ message: error }] : null,
    data,
  });
  res.end();
}

// eslint-disable-next-line import/prefer-default-export
export const listStockDetail = async (req, res, next, getQuery) => {
  if (!req.user) {
    next();
  } else {
    const stockIds = req.body.variables && req.body.variables._id;
    if (!stockIds) {
      resJson(res, 300, "Missing stock ID");
      return;
    }

    const emp_id = req.user.login.info.emp_id;
    const mongoQueryCount = {
      emp_id,
    };

    const mongoQuery = {
      stockid: { $in: Array.isArray(stockIds) ? stockIds : [stockIds] },
      emp_id,
    };

    // try {
    //   const mongoUnlockCount = await mongodb.db
    //     .collection("unlock")
    //     .find(mongoQueryCount)
    //     .count();
    //   if (mongoUnlockCount <= generalDailyQuota) {
    //     const mongoResult = await mongodb.db
    //       .collection("unlock")
    //       .find(mongoQuery)
    //       .toArray();
    //     if (!mongoResult || mongoResult.length === 0) {
    //       resJson(res, 300, "The querying stock has not been unlocked");
    //       return;
    //     }
    //   } else {
    //     resJson(res, 300, "The user has reached max daily quota");
    //     return;
    //   }
    // } catch (err) {
    //   console.log(err);
    //   resJson(res, 300, err.message);
    //   return;
    // }

    try {
      const query = await getQuery("LIST_STOCK_DETAIL_QUERY");
      const chunkSize = 5;
      const groupSize = 10;
      const chunks = [];
      const responses = [];
      for (let i = 0; i < stockIds.length; i += chunkSize) {
        chunks.push(stockIds.slice(i, i + chunkSize));
      }
      for (let i = 0; i < chunks.length; i += groupSize) {
        const groups = chunks.slice(i, i + groupSize);
        const promises = groups.map(async (group) => {
          const variables = {
            ...req.body.variables,
            _id: group,
            backendKey,
          };
          const body = {
            query,
            variables,
          };
          const response = await fetch(api.stockInternal, {
            method: "POST",
            headers: {
              Authorization: "Bearer rVEk4fDSvbVGCL93ANHto8LR0KQIIP6v"
            },
            body: JSON.stringify(body),
          });
          return response.json();
        });
        responses.push(...await Promise.all(promises));
      }
      const stocks = responses.flatMap(response => _.get(response, "data.stocks", []));
      const userData = await getEmployeeInfo(getQuery, emp_id);
      const filteredStocks = stocks.reduce((stockDetail, stock) => {
        const tempStock = stock;
        const hasDoNotContact = (v) =>
          v.contactsPersons?.filter(v =>
            v?.contact?.phones?.filter((v) => v.doNotContact).length > 0
          ).length > 0 ||
          v.contactsCompanies?.filter(v =>
            v?.contact?.phones?.filter((v) => v.doNotContact).length > 0
          ).length > 0;
        const removeDoNotContact = (v) => {
          if (v.contactsPersons) {
            v.contactsPersons = v.contactsPersons.map(v => {
              if (v?.contact?.phones) {
                v.contact.phones =
                  v.contact.phones.filter((v) => !v.doNotContact);
              }
              return v;
            })
          }
          if (v.contactsCompanies) {
            v.contactsCompanies = v.contactsCompanies.map(v => {
              if (v?.contact?.phones) {
                v.contact.phones =
                  v.contact.phones.filter((v) => !v.doNotContact);
              }
              return v;
            })
          }
          return v;
        };
        const removeAllContact = (v) => {
          if (v.contactsPersons) {
            v.contactsPersons.forEach((v) => {
              v.contact = null;
            });
          }
          if (v.contactsCompanies) {
            v.contactsCompanies.forEach((v) => {
              v.contact = null;
            });
          }
          return v;
        };
        const isSoleAgent = (v) => {
          // for COMM
          const today = moment().format("YYYY-MM-DD");
          const start = v?.soleagent?.periodStart;
          const end = v?.soleagent?.periodEnd;
          return start && end && start <= today && today <= end;
        };
        /**
         * for COMM, there are three cases that need to check the user's identity:
         * 1. has private contact 2. has private tenancyRecords 3. is sole agent stock
         *
         * for IND, 1. hide all phone numbers 2. hide update history
         *
         * for SHOPS, show all
         */
        if (
          (sbu === "COMM" &&
            tempStock?.vendors?.filter(hasDoNotContact).length > 0) ||
          (sbu === "COMM" &&
            tempStock?.tenancyRecords?.filter(
              (v) => v.tenants?.filter(hasDoNotContact).length > 0,
            ).length > 0) ||
          (sbu === "COMM" && isSoleAgent(tempStock))
        ) {
          if (userData.is_manager !== "1" && userData.emp_type !== "MK") {
            if (isSoleAgent(tempStock)) {
              let agentsId = tempStock.soleagent.agents.map((v) => v.emp_id);
              if (agentsId.indexOf(emp_id) < 0) {
                if (tempStock.vendors) delete tempStock.vendors;

                tempStock.tenancyRecords = (
                  tempStock.tenancyRecords || []
                ).map((v) => {
                  v.tenants = (v.tenants || []).map(removeDoNotContact);
                  return v;
                });
              }
            } else {
              /* tempStock.vendors = (tempStock.vendors || []).map(
                removeDoNotContact,
              ); */

              tempStock.tenancyRecords = (tempStock.tenancyRecords || []).map(
                (v) => {
                  v.tenants = (v.tenants || []).map(removeDoNotContact);
                  return v;
                },
              );
            }
          }
        } else if (sbu === "IND") {
          // for IND, hide all phone numbers
          // tempStock.vendors = (tempStock.vendors || []).map(removeAllContact);
          tempStock.vendors = [];
          //tempStock.vendors = [];

          tempStock.tenancyRecords = (tempStock.tenancyRecords || []).map(
            (v) => {
              v.tenants = (v.tenants || []).map(removeAllContact);
              return v;
            },
          );

          // hide update history
          // if (tempStock && tempStock.updateHistory)
          //   delete tempStock.updateHistory;
        }

        if (!stockDetail || stockDetail.length === 0) return [tempStock];
        return [...stockDetail, tempStock];
      }, []);
      resJson(res, 200, null, { data: { stocks: filteredStocks } });
    } catch (err) {
      console.error(err);
      resJson(res, 300, err.message);
      return;
    }
  }
};

const getEmployeeInfo = async (getQuery, emp_id) => {
  const query = await getQuery("LIST_EMPLOYEES_QUERY");
  const variables = {
    emp_id: [emp_id],
  };
  const body = {
    query,
    variables,
  };

  try {
    const resp = await fetch(api.employeeInternal, {
      method: "POST",
      body: JSON.stringify(body),
    });
    const data = await resp.json();

    if (data.data && data.data.employees && data.data.employees[0])
      return data.data.employees[0];
    else return {};
  } catch (e) {
    console.error(e);
    return {};
  }
};

export const getDeniedCalls = async (req, res, next) => {
  if (!req.user) {
    next();
  } else {
    try {
      const { phones } = req.body;

      const config = {
        headers: {
          Authorization: dmoToken,
        },
      };
      const promises = ["SMS", "PHONE", "FAX"].map((contactType) =>
        phones.map((phoneNo) =>
          fetch(`${api.dmo}?contact=${phoneNo}&type=${contactType}`, config)
            .then((res) => res.json())
            .then((res) => {
              if (_.has(res, "error")) {
                throw new Error(`${_.get(res, "error.msg")} - ${phoneNo}`);
              }
              return { ...res, phone: phoneNo };
            }),
        ),
      );
      const data = await Promise.all(_.flatten(promises)).then((res) => {
        const isDeniedCalls = _.groupBy(res, "phone");
        return Object.keys(isDeniedCalls).reduce(
          (result, phone) => ({
            ...result,
            [phone]: _.some(isDeniedCalls[phone], "isDeniedCall"),
          }),
          {},
        );
      });
      resJson(res, 200, null, data);
    } catch (e) {
      console.error(e);
      resJson(res, 300, e.message);
    }
  }
};
