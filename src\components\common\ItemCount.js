import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import { numberComma } from "../../helper/generalHelper";
import { injectIntl, FormattedMessage } from "react-intl";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    padding: "1vw",
  },
};

function ProposalStockCount(props) {
  const { classes, count, className, intl, ...other } = props;

  const parsedCount = count ? numberComma(count) : 0;
  let resultStr = count > 1 ? "proposals" : "proposal";

  let messageid = "proposal.list.count";

  if (props.messageid && props.messageid === "landSearch.list.count") {
    messageid = props.messageid;
    resultStr = count > 1 ? "land search records" : "land search record";
  }
  if (props.messageid && props.messageid === "company.search.count") {
    messageid = props.messageid;
    resultStr = count > 1 ? "company search records" : "company search record";
  }

  return (
    <div className={`${classes.root} ${className}`} {...other}>
      {parsedCount} <FormattedMessage id={messageid} values={{ resultStr }} />
    </div>
  );
}

ProposalStockCount.propTypes = {
  classes: PropTypes.object.isRequired,
  className: PropTypes.string,
  count: PropTypes.number,
  queryvariables: PropTypes.object,
};

export default withStyles(styles)(injectIntl(ProposalStockCount));
