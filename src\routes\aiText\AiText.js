import React, { useEffect, useRef, useState } from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import _ from "lodash";
import { FormattedMessage, injectIntl } from "react-intl";
import { makeStyles } from "@material-ui/core/styles";
import clsx from "clsx";
import TextField from "@material-ui/core/TextField";
import { getStockAiTextUrl,queryBusinessOptions } from "../../actions/stock";

const useStyles = makeStyles({
  "@global": {
    body: {
      margin: 0,
      padding: 0,
      height: "100vh",
      backgroundColor: "#F5F5F5",
    }
  },
  title: {
    width: "100%",
    padding: "3vw 2vw", 
    background: "#fede00", 
    fontSize: "1.2em", 
    fontWeight: 'bold',
    position: "fixed",
    top: "0"
  },
  tips: {
    padding: "2vw 2vw",
    fontSize: "0.8em",
    color: "#999",
  },
  tipsBg: {
    background: "#eee",
    borderBottom: "1px solid rgba(224,224,224,1)",
  },
  pickerBox: {
    padding: "2vw 2vw 0 2vw",
    "&>div:nth-child(1) > div:first-child": {
      paddingTop: "0!important",
      borderTop: "none",
    }
  },
  checkboxTitle: {
    borderTop: "1px solid rgba(224,224,224,1)",
    paddingTop: "1vw",
    color: "#666"
  },
  checkbox: {
    width: "22vw",
    height: "6vw",
    lineHeight: "6vw",
    textAlign: "center",
    borderRadius: "2px",
    marginRight: "2vw",
    marginTop: "2vw",
    fontSize: "0.8em",
    backgroundColor: "rgba(170, 170, 170, 1)",
    color: "#fff",
    "&:nth-child(4n)": {
      marginRight: "0"
    }
  },
  checkboxActive: {
    backgroundColor: "rgba(254, 222, 0, 1)",
    color: "#000",
  },
  remarkBox: {
    padding: "2vw",
    "& .MuiTextField-root": {
      marginTop: '3vw',
      background: "#eee",
      width: "100%",
      "& > div" : { padding: "1vw"}
    }
  },
  btnBox: {
    background: "#F5F5F5",
    borderTop: "1px solid rgba(224,224,224,1)",
    padding: "2vw",
    display: "flex",
    justifyContent: "flex-end",
    position: "fixed",
    bottom: 0,
    left: 0,
    right: 0,
    "& > div": {
      height: "6vw",
      lineHeight: "6vw",
      borderRadius: "2px",
      marginLeft: "2vw",
      fontSize: "0.8em",
      backgroundColor: "rgba(170, 170, 170, 1)",
      color: "#fff",
      padding: "1vw 5vw"
    },
    "& > div:last-child": {
      color: "#333",
      background: "#fede00", 
    }
  }
});


function AiText({
  stockid,
  intl,
  getStockAiTextUrl
}) {
  const classes = useStyles();
  const [formData, setFormData] = useState({
    customerObj: "投資客",
    mediaUse: "Whatsapp",
    copywritingLanguage: "zh-cn",
    useOfTone: "非正式",
    useOfToneType: "樂觀",
    emoji: "yes",
    specialRequirements: ""
  })

  const aiAssistPickerOptions = {
    customerObj: ["一手客", "二手客", "投資客", "內地客"],
    mediaUse: ["Whatsapp", "IG", "Threads", "Facebook", "小紅書"],
    copywritingLanguage: ["zh-hk", "zh-cn", "en"],
    useOfTone: ["正式", "非正式"],
    useOfToneType: ["樂觀", "感性", "理性", "權威", "自信", "熱情", "啟發", "硬性推銷", "軟性推銷"],
    emoji: ["yes", "no"]
  }

  const tagList = [
    {
      field: "customerObj",
      title: "客戶對象",
      isRequired: false
    },
    {
      field: "mediaUse",
      title: "媒體用途",
      isRequired: false
    },
    {
      field: "copywritingLanguage",
      title: "文案語言",
      isRequired: true,
      needFormatTxt: true
    },
    {
      field: "useOfTone",
      title: "語氣使用",
      isRequired: false
    },
    {
      field: "useOfToneType",
      title: "",
      isRequired: false,
      isChild: true
    },
    {
      field: "emoji",
      title: "表情符號 emoji",
      isRequired: false,
      needFormatTxt: true
    }
  ]

  const tagOptions = {
    emoji: {
      yes: "要",
      no: "不要"
    },
    copywritingLanguage: {
      "zh-hk": "香港用語",
      "zh-cn": "内地用語",
      "en": "英文"
    }
  }

  const confirmOk = async () => {
    console.log(formData);
    const { stockAiTextUrl } = await getStockAiTextUrl({
      ...formData,
      stockId: stockid,
      from: "mobile",
      specialRequirements: formData.specialRequirements.replace(/[\r\n]+/g, '')
    })
    
    if(stockAiTextUrl) {
      window.open(stockAiTextUrl, "_blank")
    }
  }
  
  return (
    <div>
      <div className={classes.title}>
        AI助手-文案自動生成
      </div>
      <div className={clsx(classes.tips, classes.tipsBg)} style={{paddingTop: "14vw"}}>
        注意：AI生成的內容可能不正確，使用前請小心校閱。
      </div>
      <div className={classes.tips}>
        請提供具體指示，AI助手會透過Whatsapp發送回覆。
      </div>
      <div className={classes.pickerBox}>
        {
          _.map(tagList, (item) => {
            return (
              <div style={{ marginTop: item.isChild ? "0" : "4px" }}>
                {
                  item.title && (
                    <div className={classes.checkboxTitle}>
                      { item.title }
                      { item.isRequired ? <span style={{color: "red"}}> * </span> : ""}
                    </div>
                  )
                }
                <div style={{padding: item.isChild ? "0 0 2vw 0" : "1vw 0 2vw 0", marginTop: item.isChild ? "-2vw" : "0", display: "flex", flexWrap: "wrap"}}>
                  {_.map(aiAssistPickerOptions[item.field], (ele)=> {
                    return (
                      <div 
                        className={
                          formData[item.field] === ele ? 
                            clsx(classes.checkboxActive, classes.checkbox) : 
                            classes.checkbox
                          }
                        onClick={()=> {
                          setFormData({
                            ...formData,
                            [`${item.field}`]: ele === formData[item.field] && !item.isRequired ? "" : ele
                          })
                        }}
                      >
                        { item.needFormatTxt ? tagOptions[item.field][ele] : ele }
                      </div>
                    )
                  })}
                </div>
              </div>
            )
          })
        }
      </div>
      <div className={classes.remarkBox}>
        <div className={classes.checkboxTitle}>
          特殊要求
        </div>
        <div className={classes.remarkInput}>
          <TextField
            id="specialRequirements-textarea"
            multiline
            rows={5}
            value={_.get(formData, "specialRequirements", "")}
            onChange={(e) => {
              setFormData({
                ...formData,
                specialRequirements: e.target.value
              })
            }}
            variant="outlined"
          />
        </div>
      </div>
      <div className={classes.btnBox}>
        <div onClick={() => {
          history.go(-1);
        }}>
          取消
        </div>
        <div onClick={confirmOk}>
          發送至AI
        </div>
      </div>
    </div>
  );
}

AiText.propTypes = {
  stockid: PropTypes.string.isRequired,
  intl: PropTypes.object.isRequired,
  getStockAiTextUrl: PropTypes.func.isRequired,
};

const mapDispatchToProps = (dispatch) => ({
  queryBusinessOptions: () => dispatch(queryBusinessOptions()),
  getStockAiTextUrl: (params) => dispatch(getStockAiTextUrl(params)),
});

export default connect(null, mapDispatchToProps)(injectIntl(AiText));
