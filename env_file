SERVICE_NAME=${SERVICE_NAME}
SOURCE_PATH=${SOURCE_PATH}

SBU=${SBU}

MODE=${MODE}
NODE_ENV=${NODE_ENV}
PORT=${PORT}
DEPLOY_PORT=${DEPLOY_PORT}
PUBLIC_HOST=${PUBLIC_HOST}
PRODUCTION_HOST=${PRODUCTION_HOST}
BASE_URL=${BASE_URL}
DEPLOY_DOMAIN=${DEPLOY_DOMAIN}
DATABASE_URL=${DATABASE_URL}
DATABASE_NAME=${DATABASE_NAME}
DATABASE_TABLE=${DATABASE_TABLE}
SESSION_DATABASE_NAME=${SESSION_DATABASE_NAME}

API_CLIENT_URL=${API_CLIENT_URL}
AUTH_CENTRAL_HOST=${AUTH_CENTRAL_HOST}
APP_NOTIFI_BOARDCAST_URL=${APP_NOTIFI_BOARDCAST_URL}

API_STOCK=${API_STOCK}
API_STOCK_INTERNAL=${API_STOCK_INTERNAL}

API_EMPLOYEE=${API_EMPLOYEE}
API_EMPLOYEE_INTERNAL=${API_EMPLOYEE_INTERNAL}

API_CONTACT=${API_CONTACT}
API_CONTACT_INTERNAL=${API_CONTACT_INTERNAL}

API_DISTRICT=${API_DISTRICT}
API_STREET=${API_STREET}
API_BUILDING=${API_BUILDING}
API_TRANSACTION=${API_TRANSACTION}
API_SUPPLEMENT=${API_SUPPLEMENT}
API_SEARCH=${API_SEARCH}
API_WWW=${API_WWW}
API_MEDIA=${API_MEDIA}
API_PROPOSAL=${API_PROPOSAL}
API_LANDSEARCH_DETAILS=${API_LANDSEARCH_DETAILS}
API_ICIMS_GENERAL=${API_ICIMS_GENERAL}

API_CLOUDWATCH_LOG_URL=${API_CLOUDWATCH_LOG_URL}
API_CLOUDWATCH_LOG_INTERNAL_URL=${API_CLOUDWATCH_LOG_INTERNAL_URL}

API_S3_MEDIA_TO_LOCAL=${API_S3_MEDIA_TO_LOCAL}
API_NOTIFICATION_CENTER_URL=${API_NOTIFICATION_CENTER_URL}

NOTIFICATION_CENTER_COMPANY=${NOTIFICATION_CENTER_COMPANY}
NOTIFICATION_CENTER_REQUESTFROM=${NOTIFICATION_CENTER_REQUESTFROM}
IT_USERS=${IT_USERS}
BANNED_USERS=${BANNED_USERS}
MEDIA_BYPASS_CODE=${MEDIA_BYPASS_CODE}
OAUTH2_URL=${OAUTH2_URL}
CLIENT_ID=${CLIENT_ID}
CLIENT_SECRET=${CLIENT_SECRET}
AUTHENTICATED_USERID=${AUTHENTICATED_USERID}
PROVISION_KEY=${PROVISION_KEY}
GRANT_TYPE=${GRANT_TYPE}
SCOPE=${SCOPE}
GOOGLE_TRACKING_ID=${GOOGLE_TRACKING_ID}
GTAG_MANAGER_ID=${GTAG_MANAGER_ID}
REQUEST_TIMEOUT=${REQUEST_TIMEOUT}
UPLOAD_REQUEST_TIMEOUT=${UPLOAD_REQUEST_TIMEOUT}
SBU=${SBU}
LOG_GROUP=${LOG_GROUP}
LOCALE=${LOCALE}
ENABLE_TRANSLATION=${ENABLE_TRANSLATION}
REFRESH_GRANT_TYPE=${REFRESH_GRANT_TYPE}
REFRESH_TOKEN_TIMEOUT=${REFRESH_TOKEN_TIMEOUT}
BACKEND_KEY=${BACKEND_KEY}
RECAPTCHA_SITE_KEY=${RECAPTCHA_SITE_KEY}
RECAPTCHA_SECRET_KEY=${RECAPTCHA_SECRET_KEY}
RESENDPIN_COUNTER=${RESENDPIN_COUNTER}
ENABLE_RECAPTCHA=${ENABLE_RECAPTCHA}
UNLOCK_QUOTA=${UNLOCK_QUOTA}
PROPOSAL_QUOTA=${PROPOSAL_QUOTA}
PP_STOCK_QUOTA=${PP_STOCK_QUOTA}
PROPOSAL_BYPASS_CODE=${PROPOSAL_BYPASS_CODE}
API_LANDSEARCH=${API_LANDSEARCH}
API_LANDSEARCH_FILEPATH=${API_LANDSEARCH_FILEPATH}
API_LANDSEARCH_LOG=${API_LANDSEARCH_LOG}
ENABLE_CONSOLID_LANDSEARCH=${ENABLE_CONSOLID_LANDSEARCH}
ENABLE_DESKTOPVIEW=${ENABLE_DESKTOPVIEW}
ENABLE_BATCH_TWO=${ENABLE_BATCH_TWO}
ENABLE_WWW_SCORE=${ENABLE_WWW_SCORE}
SSO=${SSO}
CAS_ACCESS_TOKEN_HOST=${CAS_ACCESS_TOKEN_HOST}
CAS_REFRESH_TOKEN_HOST=${CAS_REFRESH_TOKEN_HOST}
CAS_PROFILE_HOST=${CAS_PROFILE_HOST}
CAS_LOGOUT_HOST=${CAS_LOGOUT_HOST}
CAS_AUTH_CODE_HOST=${CAS_AUTH_CODE_HOST}
CAS_CLIENT_ID=${CAS_CLIENT_ID}
CAS_CLIENT_SECRET=${CAS_CLIENT_SECRET}
REFRESH_BEFORE_TIMEOUT_INTERVAL=${REFRESH_BEFORE_TIMEOUT_INTERVAL}
GATEWAY=${GATEWAY}
PERMISSION_VIEW_STOCK_QUOTA=${PERMISSION_VIEW_STOCK_QUOTA}
FIRST_HAND_STOCK_API=${FIRST_HAND_STOCK_API}
M1_PUBLIC_KEY_NAME=${M1_PUBLIC_KEY_NAME}
WB_TYPE_ID=${WB_TYPE_ID}

KOL_VIDEO_DURATION_LIMIT_SECONDS=${KOL_VIDEO_DURATION_LIMIT_SECONDS}

IMAGE_NAME=${IMAGE_NAME}
DEPLOYMENT_FILE=${DEPLOYMENT_FILE}
EKS_NAME=${EKS_NAME}
