###
GET http://baidu.com
#

###
GET https://i.midlandici.com/int-api/api.php?env=dev&sbu=mr_comm&func=building_stock&gstock_id=286100
#

###
GET https://i.midlandici.com/int-api/api.php?env=dev&sbu=mr_comm&func=building_stock&gstock_id=40920
#
{
  stockID: '40920',
  buildingID: '1263',
  stockDisplayID: '********',
  name_zh: '創紀之城第二期',
  name_en: 'Millennium City 2',
  dist_name_zh: '觀塘',
  dist_name_en: 'Kwun Tong',
  street_name_zh: '觀塘道 378號',
  street_name_en: '378 Kwun Tong Road',
  region: 'KLN'
}

###
GET https://i.midlandici.com/int-api/api.php?env=dev&sbu=mr_comm&func=buildinglist
#
{
  buildingID: '25',
  name_zh: '寶靈商業中心',
  name_en: 'Bowring Commercial Centre',
  distID: '38',
  dist_name_zh: '佐敦',
  dist_name_en: 'Jordan',
  street_name_zh: '吳松街 150-164號',
  street_name_en: '150-164 Woosung Street',
  region: 'KLN'
}

###
GET www.baidu.com HTTP/1.1
# 单个#号用来标记注释

###
POST http://localhost:5000/media-c/permission2
Content-Type: application/json

{
    "employeeId": "********"
}

###
POST http://ici-zc.cc:3004/createPreview
Content-Type: application/json

{
  "empId": "********",
  "type": "listProposal",
  "listProposal": {
    "type": "Sale",
    "showContact": true,
    "showEmployeePhoto": true,
    "proposalName": "上水廣場7樓_單盤建議書20250314",
    "termRemarks": {
      "nameZh": "買家需支付樓價之1 % 給予美聯物業代理(商業)有限公司作佣金。",
      "nameEn": "1% of purchase price shall be paid by the purchaser to Midland Realty (Comm.) Limited."
    },
    "lang": "SCHI",
    "multiImg": "FOUR",
    "fontSize": "S",
    "customTitle": {
      "value": "",
      "isShow": false
    },
    "showMainPhoto": true,
    "showTenancy": false,
    "showPossession": true,
    "showUnit": true,
    "mode": "indv",
    "useNameAsPdf": true,
    "proposals": [
      {
        "avgPrice": {
          "value": 0,
          "isShow": true
        },
        "totalPrice": {
          "value": 0,
          "isShow": true,
          "isNego": false
        },
        "bottomAvgPrice": {
          "value": 0,
          "isShow": false
        },
        "bottomTotalPrice": {
          "value": 0,
          "isShow": false
        },
        "avgRent": {
          "value": 26,
          "isShow": false
        },
        "totalRent": {
          "value": 49710,
          "isShow": false,
          "isNego": false
        },
        "bottomAvgRent": {
          "value": 0,
          "isShow": false
        },
        "bottomTotalRent": {
          "value": 0,
          "isShow": false
        },
        "floor": {
          "value": "7",
          "isShow": true
        },
        "floorInChinese": "7",
        "unit": {
          "value": "22",
          "isShow": true
        },
        "customBuilding": {
          "value": "Landmark North",
          "isShow": true
        },
        "customStreet": "Lung Sum Avenue",
        "customStreetNo": "39",
        "customDistrict": "Sheung Shui",
        "areaEfficiency": {
          "value": 0,
          "isShow": false
        },
        "possession": {
          "value": {
            "nameZh": "交吉交易",
            "nameEn": "Vacant Possession"
          },
          "isShow": true
        },
        "managementFee": {
          "value": 8.1,
          "paidBy": null,
          "paidByIsShow": null,
          "unit": "/SqFt",
          "isShow": true,
          "totalMonthlyMgtFee": 15488,
          "totalMonthlyMgtFeeIsShow": true
        },
        "gRent": {
          "value": 0,
          "paidBy": null,
          "paidByIsShow": null,
          "unit": "/Qtr",
          "isShow": false
        },
        "rates": {
          "value": 0,
          "paidBy": null,
          "paidByIsShow": null,
          "unit": "/Qtr",
          "isShow": false
        },
        "includedFee": {
          "managementFee": false,
          "rates": false,
          "gRent": false,
          "acFee": false
        },
        "decoration": {
          "value": {
            "nameZh": "無裝修",
            "nameEn": "Bare-Shell"
          },
          "isShow": true
        },
        "unitView": {
          "value": {
            "nameZh": "城市景",
            "nameEn": "City View"
          },
          "isShow": true
        },
        "availability": {
          "value": "",
          "isShow": false
        },
        "isSoleagent": false,
        "districtNameZh": "上水",
        "districtNameEn": "Sheung Shui",
        "streetNameZh": "龍琛路",
        "streetNameEn": "Lung Sum Avenue",
        "streetNo": "39",
        "useGGMapPhoto": false,
        "includeGGMap": false,
        "allInclusive": false,
        "isMgtFeeOpenAC": true,
        "yield": {
          "value": 0,
          "isShow": false
        },
        "videoUrl": {
          "value": "",
          "isShow": false
        },
        "floorType": {
          "nameZh": "",
          "nameEn": "Actual Floor"
        },
        "currentTenants": [],
        "remarks": "",
        "customTitle": {
          "value": "",
          "isShow": false
        },
        "termRemarks": {
          "nameZh": "買家需支付樓價之1 % 給予美聯物業代理(商業)有限公司作佣金。",
          "nameEn": "1% of purchase price shall be paid by the purchaser to Midland Realty (Comm.) Limited."
        },
        "stockId": 72120,
        "stockMongoId": "626d6fc885ad3354410565b7",
        "googleMap": {
          "isPP": true,
          "isMain": false,
          "isMain1": false,
          "isMain2": false
        },
        "photos": [
          {
            "id": "map",
            "multiImg": "ONE",
            "mediumRoot": "lng:114.127911,lat:22.502585",
            "photoContent": "map"
          }
        ],
        "main1Photo": null,
        "mainPhoto": null,
        "building": {
          "nameZh": "上水廣場",
          "nameEn": "Landmark North",
          "districtNameZh": "上水",
          "districtNameEn": "Sheung Shui",
          "lat": 22.502585,
          "lng": 114.127911,
          "govLat": 22.502585,
          "govLng": 114.127911,
          "usage": {
            "value": {
              "nameZh": "商業",
              "nameEn": "Commercial"
            },
            "isShow": true
          },
          "airConditioningType": {
            "value": {
              "nameZh": "中央",
              "nameEn": "Central"
            },
            "isShow": true
          },
          "airConditioningOpeningTime": {
            "value": {
              "nameZh": "@7.50 for 6-9,11,13,15-16/F\r\n@5.40 for all other levels\r\nMon-Fri : 08:00-19:00\r\nSat : 08:00-14:00",
              "nameEn": "@7.50 for 6-9,11,13,15-16/F\r\n@5.40 for all other levels\r\nMon-Fri : 08:00-19:00\r\nSat : 08:00-14:00"
            },
            "isShow": false
          },
          "airConditioningExtraCharges": {
            "value": {
              "nameZh": "",
              "nameEn": ""
            },
            "isShow": false
          },
          "managementCompany": {
            "value": {
              "nameZh": "---",
              "nameEn": "---"
            },
            "isShow": false
          },
          "haveCarPark": {
            "value": {
              "nameEn": "No",
              "nameZh": "無"
            },
            "isShow": false
          },
          "cargoLift": {
            "value": {
              "nameEn": "No",
              "nameZh": "無"
            },
            "isShow": false
          },
          "title": {
            "value": {
              "nameZh": "統一業權",
              "nameEn": "Unified"
            },
            "isShow": false
          },
          "transport": {
            "value": {
              "nameZh": "---",
              "nameEn": "---"
            },
            "isShow": false
          },
          "inTakeDate": {
            "value": "1995 (30)",
            "isShow": true
          },
          "passengerLift": {
            "value": {
              "nameZh": 8,
              "nameEn": 8
            },
            "isShow": false
          },
          "isBuildingFieldsAllHide": false
        },
        "areaGross": {
          "value": 1912,
          "isShow": true
        },
        "areaNet": {
          "value": 0,
          "isShow": false
        },
        "areaSaleable": {
          "value": 0,
          "isShow": false
        },
        "areaLettable": {
          "value": 0,
          "isShow": false
        }
      }
    ]
  }
}

###
POST http://ici-zc.cc:3000/api/logs
Content-Type: application/json

{
    "log": "test"
}

###
POST https://msds-dev.midlandici.com/authz-api/employee/view
Content-Type: application/json
Authorization: Bearer AT-14561-JUlk3Nbmov2hDtzAYgegPSxRIgI7vDEw

{
  "appId": "3009e16b84e24edfa9ba475dc849e05a",
  "lang": "eng",
  "employeeId": "********"
}

###
POST http://localhost:5000/media-c/graphql
Content-Type: application/json
Authorization: Bearer xxx
X-REQUEST-TYPE: GraphQL

query{
    media(type: "video", approval: "pending") {
        id
        stockId
        filename
        originalFilename
        type
        status
        employeeId
        availableStartTime
        availableEndTime
        tags
        processing
        approval
        lastModified
        createdDate
        operator {
            id
            branchId
            eName
            email
        }
        ... on Video {
            mediumRoot
            youtubeId
            youtubeMrId
            youtubeHkpId
            youkuId
        }
    }
}