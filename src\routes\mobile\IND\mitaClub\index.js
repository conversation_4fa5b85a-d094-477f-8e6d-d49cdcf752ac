import React from "react";

const title = "Mita Club";
async function action({ store }) {
  const { auth } = store.getState();
  if (!auth.user) {
    return { redirect: "/login" };
  }
  if (!auth.user.authorized) {
    return { redirect: "/login" };
  }

  const { default: MitaClubPage } = await import(
    /* webpackChunkName: "mita-club" */
    './MitaClubPage'
  );

  return {
    title,
    component: (<MitaClubPage />),
  };
}

export default action;
