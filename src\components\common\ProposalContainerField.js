import React from "react";
import { Field } from "redux-form";
import { injectIntl } from "react-intl";
import { withStyles } from "@material-ui/core/styles";
import Grid from "@material-ui/core/Grid";
import InputWithCheckBoxCustom from "./InputWithCheckBoxCustom";
import PillCheckBox from "./PillCheckBox";
const styles = {
  hidden: {
    visibility: "hidden",
  },
};

function ProposalContainerField(props) {
  const {
    classes,
    fields,
    label,
    intl,
    ...custom
  } = props;
  return (
    <div>
      {fields.map((item, index) => (
        <Grid container spacing={1} key={index}>
          <Grid item xs={6}>
            <InputWithCheckBoxCustom
              name={item}
              label={label + (fields.length > 1 ? " " + (index + 1) : "")}
              checkboxInFront={true}
              aligntoLabel={true}
              checkboxXs={2}
              {...custom}
            />
          </Grid>
          <Grid item xs={6}>
            <Field
              name={`${item}.haveLoadingBay`}
              component={PillCheckBox}
              text={intl.formatMessage({ id: "building.loadingbay" })}
            />
          </Grid>
        </Grid>
      ))}
    </div>
  );
}

export default withStyles(styles)(injectIntl(ProposalContainerField));