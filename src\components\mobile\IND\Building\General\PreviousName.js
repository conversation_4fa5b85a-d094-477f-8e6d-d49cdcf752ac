import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import { convertNewlineToBr } from "../../../../../helper/generalHelper";
import FieldVal from "../../../../common/FieldVal";
import { injectIntl } from "react-intl";

const styles = theme => ({
  freeText: {
    color: "#4B4B4B",
    fontSize: "0.875em"
  },
  gridContent: {
    padding: "1vw 2vw",
    gap: "25px"
  },
  gridItem: {
    backgroundColor: "#f2f2f2",
    width: "100%",
    borderRadius: "5px"
  }
});

class PreviousName extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object
  };

  render() {
    const { classes, detail, intl } = this.props;

    const previousNames = detail.previousNames ?? [];
    const previousnameMapping = previousNames.map(item => ({
      zhName: {
        label: intl.formatMessage({ id: 'building.previousname.chi' }),
        value: item.nameZh || '---'
      },
      enName: {
        label: intl.formatMessage({ id: 'building.previousname.eng' }),
        value: item.nameEn || '---'
      }
    }));

    if (previousNames.length === 0) {
      previousnameMapping.push({
        zhName: {
          label: intl.formatMessage({ id: 'building.previousname.chi' }),
          value: '---'
        },
        enName: {
          label: intl.formatMessage({ id: 'building.previousname.eng' }),
          value: '---'
        }
      });
    }

    return (
      <DetailBoxSection
        expandable={true}
        isExpanding={true}
        text={intl.formatMessage({
          id: "building.previousname"
        })}
      >
        <Grid container className={classes.gridContent}>
          {previousnameMapping && previousnameMapping.map((item) => (
            <Grid container spacing={2} className={classes.gridItem}>
              {Object.keys(item).map((v, i) => (
                <Grid item xs={12} key={v}>
                  <FieldVal field={item[v]["label"]}>{item[v].value}</FieldVal>
                </Grid>
              ))}
            </Grid>))}
        </Grid>
      </DetailBoxSection>
    );
  }
}

export default withStyles(styles)(injectIntl(PreviousName));
