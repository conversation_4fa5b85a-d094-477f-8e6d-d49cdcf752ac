import React from "react";
import PropTypes from "prop-types";
import { Checkbox, FormControlLabel } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles({
  checkbox: {
    display: "block",
    "&.Mui-checked:not(.Mui-disabled)": {
      color: "#13CE66",
    },
  },
});

// Please pass type="checkbox" into redux-form <Field />
function BooleanCheckBox({ label, input, checkboxProps, className }) {
  const classes = useStyles();

  return (
    <FormControlLabel
      className={className}
      control={
        <Checkbox
          className={classes.checkbox}
          checked={input.checked}
          onChange={input.onChange}
          {...checkboxProps}
        />
      }
      label={label}
    />
  );
}

BooleanCheckBox.defaultProps = {
  checkboxProps: {},
  className: "",
};

BooleanCheckBox.propTypes = {
  label: PropTypes.string.isRequired,
  input: PropTypes.object.isRequired,
  checkboxProps: PropTypes.object,
  className: PropTypes.string,
};

export default BooleanCheckBox;
