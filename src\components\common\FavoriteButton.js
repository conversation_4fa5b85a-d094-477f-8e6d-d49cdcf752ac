import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { withStyles } from "@material-ui/core/styles";
import { togglemyFavoriteapi } from "../../actions/stock";
import FavoriteIcon from "@material-ui/icons/Favorite";

// We can inject some CSS into the DOM.
const styles = {
  root: {
    display: "inline-block",
  },
  icon: {
    color: "#E3E3E3",
  },
  iconRed: {
    color: "red",
  },
};

function FavoriteButton(props) {
  const { classes, className, mongoid, stockid, favoriteStockIds } = props;
  const { isStockDetailPage = true, togglemyFavoriteapi } = props;

  const isFavorite = favoriteStockIds.filter((v) => v === mongoid).length > 0;

  function handleClick() {
    if (isStockDetailPage) {
      const query = {
        mongoid: mongoid,
        stockid: stockid,
      };
      togglemyFavoriteapi(query);
    }
  }

  return (
    <div className={`${classes.root} ${className}`}>
      <FavoriteIcon
        className={isFavorite ? classes.iconRed : classes.icon}
        onClick={(e) => {
          e.stopPropagation();
          handleClick();
        }}
      />
    </div>
  );
}

FavoriteButton.propTypes = {
  classes: PropTypes.object.isRequired,
  favoriteStockIds: PropTypes.array,
  mongoid: PropTypes.string,
  stockid: PropTypes.number,
};

const mapDispatchToProps = (dispatch) => {
  return {
    togglemyFavoriteapi: (args) => dispatch(togglemyFavoriteapi(args)),
  };
};

export default connect(
  null,
  mapDispatchToProps,
)(withStyles(styles)(FavoriteButton));
