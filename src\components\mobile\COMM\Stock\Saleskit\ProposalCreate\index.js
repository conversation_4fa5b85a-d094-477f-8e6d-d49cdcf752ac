/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React, { useEffect, useState } from "react";
import { withStyles } from "@material-ui/core/styles";
import PropTypes from "prop-types";
import { connect } from "react-redux";
import { submit, change } from "redux-form";
import { injectIntl } from "react-intl";
import _ from "lodash";
import TextField from "@material-ui/core/TextField";
import Checkbox from "@material-ui/core/Checkbox";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import FormGroup from "@material-ui/core/FormGroup";

import DialogFrame from "../../../../../common/DialogFrame";
import Dialog from "../../../../../common/Dialog";
import ProposalForm from "../../../Proposal/ProposalForm";
import {
  createProposal,
  clearCreateProposal,
} from "../../../../../../actions/proposal";
import {
  possessionLangIdMapping,
  floorTypeLangIdMapping,
  getTypeOptions,
  getDefaultRemarks,
} from "../../../Proposal/FormSection/selectOptions";
import langFile from "../../../../../../lang/COMM/messages";
import SubmitDialog from "../../../../../common/SubmitDialog";
import {
  getLangKey,
  goToProposalList,
  parsePeriod,
} from "../../../../../../helper/generalHelper";
import { sbu, generalProposalQuota } from "../../../../../../config";
import history from "../../../../../../core/history";
import {
  clearCreateListProposal,
  createListProposal,
} from "../../../../../../actions/listProposal";
import SelectField from "../../../../../common/SelectField";
import {
  getFloorTypeInfoFromLangFile,
} from "../../../../../Saleskit/helpers";

const CustomCheckbox = withStyles({
  root: {
    color: "white",
    "&$checked": {
      color: "#13CE66",
    },
  },
  checked: {},
})((props) => <Checkbox {...props} />);

const titleMapping = {
  unified: {
    nameEn: "Unified",
    nameZh: "統一業權",
  },
  spinoff: {
    nameEn: "Spin-off",
    nameZh: "分散業權",
  },
};

function ProposalCreate({
  stockData,
  currentStock,
  media,
  unitViews,
  decorations,
  possessions,
  buildingMedia,
  userInfo,
  createProposal,
  creatingProposal,
  createdProposal,
  createProposalError,
  clearCreateProposal,
  dispatchSubmitForm,
  createProposalDialogOpen,
  handleCloseCreateProposalDialog,
  changeFieldValue,
  form,
  exceedQuota,
  createListProposal,
  creatingListProposal,
  createdListProposal,
  createListProposalError,
  clearCreateListProposal,
  intl,
}) {
  const [proposalName, setProposalName] = useState("");
  const [listProposalType, setListProposalType] = useState("Sale");
  const [hideContact, setHideContact] = useState(false);
  const [hideEmployeePhoto, setHideEmployeePhoto] = useState(false);

  const stock = stockData[currentStock];
  const isListProposal = history.location.pathname === "/listProposal";

  useEffect(() => {
    setProposalName(parseDefaultProposalName());
  }, []);

  const parseDefaultProposalName = () => {
    const langKey = getLangKey(intl);
    return stock.building && stock.building[langKey]
      ? stock.building[langKey]
      : "";
  };

  const twoDec = (n) => {
    return Math.round((n + Number.EPSILON) * 100) / 100;
  };

  const handleCloseSubmitDialog = () => {
    handleCloseCreateProposalDialog();
    setProposalName(parseDefaultProposalName());
    setHideEmployeePhoto(false);
    setHideContact(false);
    setListProposalType("Sale");
    isListProposal ? clearCreateListProposal() : clearCreateProposal();
  };

  const submitDialogCallback = () => {
    goToProposalList();
  };

  const handleNameChange = (e) => {
    setProposalName(e.target.value);
  };

  const listProposalFields = [
    "unit",
    "floor",
    "floorType",
    "avgPrice",
    "totalPrice",
    "avgRent",
    "totalRent",
    "customBuilding",
    "customStreet",
    "customStreetNo",
    "customDistrict",
    "areaEfficiency",
    "areaGross",
    "areaNet",
    "main1Photo",
    "possession",
    "unitView",
    "decoration",
    "availability",
    "managementFee",
    "remarks",
    "stockMedia",
    "buildingMedia",
    "airConditioningType",
    "haveCarPark",
  ];

  const formStockData = (s) => {
    const langKey = getLangKey(intl);
    const buildingUsageLangKey =
      intl.locale === "zh" ? "buildingUsageZh" : "buildingUsage";
    const airConditioningTypeLangKey = intl.locale === "zh" ? "typeZh" : "type";
    const transportLangKey = getLangKey(intl, "transport");
    const area = s && s.area ? s.area : null;

    const stockMediaData =
      media?.filter((m) => m.id === s?.unicorn?.id?.toString())?.[0]?.data ||
      {};
    const buildingMediaData =
      buildingMedia?.filter(
        (m) => m.id === s?.building?.unicorn?.id?.toString(),
      )?.[0]?.data || {};

    const currTenancyRecords = s?.tenancyRecords?.filter(
      (v) => v.status === "Current" && !v.deleted,
    );
    const currentRent = currTenancyRecords?.reduce(
      (prev, curr) => prev + (curr?.rentalFee || 0),
      0,
    );

    let defaultType = "Sale";
    let defaultRemarks = getDefaultRemarks("Sale");
    switch (s?.status?.nameEn) {
      case "Lease":
        defaultType = "Lease";
        defaultRemarks = getDefaultRemarks("Lease");
        break;
      case "Sale+Lease":
        defaultType = "SaleAndLease";
        defaultRemarks = getDefaultRemarks("SaleAndLease");
        break;
    }

    const allFields = {
      floorType: "Actual Floor",
      type: defaultType,
      avgPrice: [
        {
          value: s?.askingPrice?.average,
          isShow: defaultType === "Sale" || defaultType === "SaleAndLease",
        },
      ],
      totalPrice: [
        {
          value: s?.askingPrice?.total,
          isShow: defaultType === "Sale" || defaultType === "SaleAndLease",
        },
      ],
      bottomAvgPrice: s?.askingPrice?.details?.filter(
        (v) => v.type === "average_bottom",
      )[0]?.value,
      bottomTotalPrice: s?.askingPrice?.details?.filter(
        (v) => v.type === "total_bottom",
      )[0]?.value,
      avgRent: [
        {
          value: s?.askingRent?.average,
          isShow: defaultType === "Lease" || defaultType === "SaleAndLease",
        },
      ],
      totalRent: [
        {
          value: s?.askingRent?.total,
          isShow: defaultType === "Lease" || defaultType === "SaleAndLease",
        },
      ],
      bottomAvgRent: s?.askingRent?.details?.filter(
        (v) => v.type === "average_bottom",
      )[0]?.value,
      bottomTotalRent: s?.askingRent?.details?.filter(
        (v) => v.type === "total_bottom",
      )[0]?.value,
      floor: [
        {
          value: s?.floor,
          isShow: true,
        },
      ],
      unit: [
        {
          value: s?.unit,
          isShow: !!s?.unit,
        },
      ],
      customBuilding: s?.building?.[langKey] || "---",
      customStreet: s?.building?.street?.street?.[langKey] || "---",
      customStreetNo: s?.building?.street?.number || "",
      customDistrict: s?.building?.district?.[langKey] || "---",
      areaEfficiency: [
        {
          value: area?.efficiency || "",
          isShow: false, // all net area default unselect
        },
      ],
      areaGross: [
        {
          value: area?.size || "",
          isShow: !!area?.size,
        },
      ],
      areaNet: [
        {
          value: area?.sizes?.filter((v) => v.type === "NET")[0]?.value || "",
          isShow: false, // all net area default unselect
        },
      ],
      possession: [
        {
          value: s && s.possession,
          isShow: s && s.possession ? true : false,
        },
      ],
      managementFee: [
        {
          value: (s && s.managementFee) || "",
          isShow: s && s.managementFee ? true : false,
        },
      ],
      decoration: [
        {
          value: s?.decoration?.nameEn || "",
          isShow: !!s?.decoration,
        },
      ],
      unitView: [
        {
          value: s?.unitView?.nameEn || "",
          isShow: !!s?.unitView,
        },
      ],
      availability: [
        {
          value: s?.availability || "---",
          isShow: !!s?.availability,
        },
      ],
      haveCarPark: [
        {
          value: s?.building?.haveCarPark
            ? intl.formatMessage({ id: "stock.yes" })
            : intl.formatMessage({ id: "stock.no" }),
          isShow: true,
        },
      ],
      remarks: defaultRemarks,
      currentTenants: currTenancyRecords?.map((v) => {
        const minDate = v?.expiry?.minDate;
        const maxDate = v?.expiry?.maxDate;
        return {
          tenant: v?.tenant?.[langKey] || "---",
          tenantIsShow: !!v?.tenant?.[langKey],
          rentalFee: v?.rentalFee,
          rentalFeeIsShow: !!v?.rent?.total && !!v?.tenant?.[langKey],
          period: parsePeriod(minDate, maxDate, intl),
          periodIsShow: !!(minDate || maxDate) && !!v?.tenant?.[langKey],
          tenancy: "---", // COMM db does not have isIncludeGovernmentRent, isIncludeRates, isIncludeAirConditioning data
          tenancyIsShow: false,
        };
      }),
      yield: [
        {
          // COMM db does not have yield so it need to be calculated
          value:
            currentRent && s?.askingPrice?.total
              ? twoDec(((currentRent * 12) / s?.askingPrice?.total) * 100)
              : "",
          isShow: !!(currentRent && s?.askingPrice?.total),
        },
      ],
      customTitle: [
        {
          value: "",
          isShow: false,
        },
      ],
      hideEmployeePhoto: [
        {
          value: intl.formatMessage({
            id: "proposal.form.hideemployeephoto",
          }),
          isShow: false,
        },
      ],
      hideContact: [
        {
          value: intl.formatMessage({
            id: "proposal.form.hidecontact",
          }),
          isShow: false,
        },
      ],
      companyTitle: "midlandici",
      lang: "CHI_ENG",
      stockMedia: []
        .concat(stockMediaData?.photo || [], stockMediaData?.video || [])
        ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
        ?.map((v) => v.id),
      buildingMedia: []
        .concat(buildingMediaData?.photo || [], buildingMediaData?.video || [])
        ?.filter((v) => v.tags?.indexOf("proposal") >= 0)
        ?.map((v) => v.id),
      main1Photo: "map",

      // building info
      usage: [
        {
          value: s?.building?.[buildingUsageLangKey] || "---",
          isShow: !!s?.building?.[buildingUsageLangKey],
        },
      ],
      title: [
        {
          value: titleMapping[s?.building?.title]?.[langKey] || "---",
          isShow: !!titleMapping[s?.building?.title]?.[langKey],
        },
      ],
      inTakeDate: [
        {
          value: s?.building?.completionDate || "---",
          isShow: !!s?.building?.completionDate,
        },
      ],
      developers: s?.building?.developers?.map((v) => {
        return {
          value: v[langKey] || "---",
          isShow: !!v[langKey],
        };
      }),
      managementCompany: [
        {
          value:
            s?.building?.managementCompany?.managementCompany?.[langKey] ||
            "---",
          isShow:
            !!s?.building?.managementCompany?.managementCompany?.[langKey],
        },
      ],
      transport: [
        {
          value: s?.building?.[transportLangKey] || "---",
          isShow: !!s?.building?.[transportLangKey],
        },
      ],
      passengerLift: [
        {
          value: s?.building?.passengerLift || "---",
          isShow: !!s?.building?.passengerLift,
        },
      ],
      cargoLift: [
        {
          value: s?.building?.haveCargoLift
            ? intl.formatMessage({ id: "stock.yes" })
            : intl.formatMessage({ id: "stock.no" }),
          isShow: true,
        },
      ],
      airConditioningType: [
        {
          value:
            s?.building?.airConditioning?.[airConditioningTypeLangKey] || "---",
          isShow: !!s?.building?.airConditioning?.[airConditioningTypeLangKey],
        },
      ],
    };

    if (isListProposal) {
      if (
        Object.keys(stockMediaData).length > 0 &&
        stockMediaData.photo.length > 0
      )
        allFields.main1Photo = stockMediaData.photo[0].id;
      else if (
        Object.keys(buildingMediaData).length > 0 &&
        buildingMediaData.photo.length > 0
      )
        allFields.main1Photo = buildingMediaData.photo[0].id;
    }
    return isListProposal
      ? _.omitBy(allFields, (_, key) => !listProposalFields.includes(key))
      : allFields;
  };

  const getParsedProposal = (stock, mediaData, buildingMediaData, values) => {
    const photos = [].concat(
      mediaData?.photo || [],
      buildingMediaData?.photo || [],
    );
    const videos = [].concat(
      mediaData?.video || [],
      buildingMediaData?.video || [],
    );
    const getPhotoInfoFromRedux = (id) => {
      let photo = photos?.filter((v) => v.id === id)?.[0] || null;
      if (photo)
        photo = {
          id: photo.id,
          mediumRoot: photo.mediumRoot,
          photoContent: photo.photoContent,
        };
      return photo;
    };
    const getVideoInfoFromRedux = (id) => {
      let video = videos?.filter((v) => v.id === id)?.[0] || null;
      if (video)
        video = {
          id: video.id,
          mediumRoot: video.mediumRoot,
          youtubeId: video.youtubeMrId || video.youtubeHkpId || video.youtubeId,
        };
      return video;
    };

    const parseValueFromArr = (value, arr) =>
      _.pick(
        _.defaultTo(
          _.find(arr, (item) => item.nameEn === value),
          { nameZh: "", nameEn: "" },
        ),
        ["nameEn", "nameZh"],
      );

    const currTenancyRecords = stock?.tenancyRecords?.filter(
      (v) => v.status === "Current" && !v.deleted,
    );

    // stock photo which has the main tag
    const mainStockPhotoId = mediaData?.photo?.filter(
      (v) => v.tags && v.tags.includes("main"),
    )?.[0]?.id;
    // building photo which has the main tag
    const mainBuildingPhotoId = buildingMediaData?.photo?.filter(
      (v) => v.tags && v.tags.includes("main"),
    )?.[0]?.id;
    // main stock photo has higher priority
    const mainPhoto = getPhotoInfoFromRedux(
      mainStockPhotoId || mainBuildingPhotoId || null,
    );

    let parsedData = {
      ...values,
      floorType: getFloorTypeInfoFromLangFile(values?.floorType),
      type: values?.type,
      floor: values?.floor?.[0],
      unit: values?.unit?.[0],
      avgPrice: {
        ...values?.avgPrice?.[0],
        value: values?.avgPrice?.[0]?.value || 0,
      },
      totalPrice: {
        ...values?.totalPrice?.[0],
        value: values?.totalPrice?.[0]?.value || 0,
      },
      bottomAvgPrice: values?.bottomAvgPrice || 0,
      bottomTotalPrice: values?.bottomTotalPrice || 0,
      avgRent: {
        ...values?.avgRent?.[0],
        value: values?.avgRent?.[0]?.value || 0,
      },
      totalRent: {
        ...values?.totalRent?.[0],
        value: values?.totalRent?.[0]?.value || 0,
      },
      bottomAvgRent: values?.bottomAvgRent || 0,
      bottomTotalRent: values?.bottomTotalRent || 0,
      customBuilding: {
        value: values?.customBuilding,
        isShow: true,
      },
      areaEfficiency: {
        ...values?.areaEfficiency?.[0],
        value: values?.areaEfficiency?.[0]?.value || 0,
      },
      areaGross: {
        ...values?.areaGross?.[0],
        value: values?.areaGross?.[0]?.value || 0,
      },
      areaNet: {
        ...values?.areaNet?.[0],
        value: values?.areaNet?.[0]?.value || 0,
      },
      possession: {
        ...values?.possession?.[0],
        value: parseValueFromArr(values?.possession?.[0]?.value, possessions),
      },
      unitView: {
        ...values?.unitView?.[0],
        value: parseValueFromArr(values?.unitView?.[0]?.value, unitViews),
      },
      decoration: {
        ...values?.decoration?.[0],
        value: parseValueFromArr(values?.decoration?.[0]?.value, decorations),
      },
      managementFee: {
        ...values?.managementFee?.[0],
        value: values?.managementFee?.[0]?.value || 0,
      },
      // decoration: {
      //   ...values?.decoration?.[0],
      //   value: stock?.decoration,
      // },
      // unitView: {
      //   ...values?.unitView?.[0],
      //   value: stock?.unitView,
      // },
      availability: {
        ...values?.availability?.[0],
        value: stock?.availability,
      },
      haveCarPark: {
        ...values?.haveCarPark?.[0],
        value: {
          nameEn: stock?.building?.haveCarPark ? "Yes" : "No",
          nameZh: stock?.building?.haveCarPark ? "有" : "無",
        },
      },
      currentTenants: values?.currentTenants?.map((v, i) => {
        return {
          tenant: {
            nameEn: currTenancyRecords[i]?.tenant?.nameEn,
            nameZh: currTenancyRecords[i]?.tenant?.nameZh,
            isShow: v.tenantIsShow,
          },
          rentalFee: {
            value: v.rentalFee || 0,
            isShow: v.rentalFeeIsShow,
          },
          period: {
            min: currTenancyRecords[i]?.expiry?.minDate,
            max: currTenancyRecords[i]?.expiry?.maxDate,
            isShow: v.periodIsShow,
          },
          tenancy: {
            // COMM db does not have isIncludeGovernmentRent, isIncludeRates, isIncludeAirConditioning data
            nameEn: "",
            nameZh: "",
            isShow: false,
          },
        };
      }),
      yield: {
        ...values?.yield?.[0],
        value: values?.yield?.[0]?.value || 0,
      },
      customTitle: values?.customTitle?.[0],
      hideEmployeePhoto: !!values?.hideEmployeePhoto?.[0]?.isShow,
      hideContact: !!values?.hideContact?.[0]?.isShow,
      photos: []
        .concat(values?.stockMedia || [], values?.buildingMedia || [])
        .map(getPhotoInfoFromRedux)
        .filter((v) => v !== null),
      videos: []
        .concat(values?.stockMedia || [], values?.buildingMedia || [])
        .map(getVideoInfoFromRedux)
        .filter((v) => v !== null),
      googleMap: {
        isPP: !!(values?.googleMapPhoto && values?.googleMapPhoto.length > 0),
        isMain1: values?.main1Photo === "map",
        isMain2: values?.main2Photo === "map",
      },
      main1Photo:
        values?.main1Photo && getPhotoInfoFromRedux(values?.main1Photo),
      main2Photo:
        values?.main2Photo && getPhotoInfoFromRedux(values?.main2Photo),
      mainPhoto: mainPhoto, // stock or building photo which has the main tag

      // building info
      usage: {
        ...values?.usage?.[0],
        value: {
          nameEn: stock?.building?.buildingUsage,
          nameZh: stock?.building?.buildingUsageZh,
        },
      },
      title: {
        ...values?.title?.[0],
        value: titleMapping[stock?.building?.title],
      },
      inTakeDate: {
        ...values?.inTakeDate?.[0],
        // value: moment(stockData?.building?.completionDate, "YYYY").toDate(),
        value: stock?.building?.completionDate,
      },
      developers: values?.developers?.map((v, i) => {
        return {
          ...v,
          value: {
            nameEn: stock?.building?.developers?.[i]?.nameEn,
            nameZh: stock?.building?.developers?.[i]?.nameZh,
          },
        };
      }),
      managementCompany: {
        ...values?.managementCompany?.[0],
        value: {
          nameEn: stock?.building?.managementCompany?.managementCompany?.nameEn,
          nameZh: stock?.building?.managementCompany?.managementCompany?.nameZh,
        },
      },
      transport: {
        ...values?.transport?.[0],
        value: {
          nameEn: stock?.building?.transportEn,
          nameZh: stock?.building?.transportZh,
        },
      },
      passengerLift: {
        ...values?.passengerLift?.[0],
        value: {
          nameEn: stock?.building?.passengerLift
            ? stock?.building?.passengerLift.toString()
            : "",
          nameZh: stock?.building?.passengerLift
            ? stock?.building?.passengerLift.toString()
            : "",
        },
      },
      cargoLift: {
        ...values?.cargoLift?.[0],
        value: {
          nameEn: stock?.building?.haveCargoLift ? "Yes" : "No",
          nameZh: stock?.building?.haveCargoLift ? "有" : "無",
        },
      },
      airConditioningType: {
        ...values?.airConditioningType?.[0],
        value: {
          nameEn: stock?.building?.airConditioning?.type,
          nameZh: stock?.building?.airConditioning?.typeZh,
        },
      },

      // additional data which is required by proposal but not shown in the form (or is not a field in the form)
      stockId: stock?.unicorn?.id,
      stockMongoId: stock?._id,
      sbu: sbu,
      isSoleagent: !!(
        stock.soleagent &&
        stock.soleagent.periodStart != null &&
        stock.soleagent.periodEnd != null
      ),
      districtNameZh: stock?.building?.district?.nameZh,
      districtNameEn: stock?.building?.district?.nameEn,
      streetNameZh: stock?.building?.street?.street?.nameZh,
      streetNameEn: stock?.building?.street?.street?.nameEn,
      streetNo: stock?.building?.street?.number,
      buildingNameZh: stock?.building?.nameZh,
      buildingNameEn: stock?.building?.nameEn,
      buildingDistrictNameZh: stock?.building?.district?.nameZh,
      buildingDistrictNameEn: stock?.building?.district?.nameEn,
      lng: stock?.building?.coordinates?.longitude,
      lat: stock?.building?.coordinates?.latitude,
      isBuildingFieldsAllHide: !(
        values?.usage?.[0]?.isShow ||
        values?.title?.[0]?.isShow ||
        values?.inTakeDate?.[0]?.isShow ||
        values?.developers?.filter((v) => v.isShow).length > 0 ||
        values?.managementCompany?.[0]?.isShow ||
        values?.transport?.[0]?.isShow ||
        values?.passengerLift?.[0]?.isShow ||
        values?.cargoLift?.[0]?.isShow ||
        values?.airConditioningType?.[0]?.isShow
      ),
      proposalName,
    };

    // these photo and video fields are merged into photos and videos
    delete parsedData.stockMedia;
    delete parsedData.buildingMedia;
    // googleMapPhoto is converted to includeGoogleMap field
    delete parsedData.googleMapPhoto;

    return parsedData;
  };

  const submitListProposal = () => {
    const proposalStocksId = stockData.map((s) => s._id);
    // retrieve stock proposal data
    const formArr = Object.keys(form)
      .map((id) => id.substr(5))
      .reduce((list, id) => {
        if (proposalStocksId.includes(id))
          return [...list, { id, ...form[`form_${id}`].values }];
        return list;
      }, []);

    const parsedFormArr = formArr.map((formData) => {
      const stock = stockData.find((s) => s._id === formData.id);
      const stockMediaData =
        media?.filter((m) => m.id === stock?.unicorn?.id?.toString())?.[0]
          ?.data || {};
      const buildingMediaData =
        buildingMedia?.filter(
          (m) => m.id === stock?.building?.unicorn?.id?.toString(),
        )?.[0]?.data || {};
      delete formData.id;

      const parsedProposal = getParsedProposal(
        stock,
        stockMediaData,
        buildingMediaData,
        formData,
      );
      // delete redundant fields
      // delete parsedProposal.availability;
      delete parsedProposal.developers;
      delete parsedProposal.currentTenants;
      delete parsedProposal.containers;
      delete parsedProposal.inTakeDate;
      delete parsedProposal.isSoleagent;
      delete parsedProposal.isBuildingFieldsAllHide;
      delete parsedProposal.photos;
      delete parsedProposal.videos;
      // delete parsedProposal.decoration;
      delete parsedProposal.stockType;
      delete parsedProposal.managementCompany;
      // delete parsedProposal.unitView;
      delete parsedProposal.title;
      delete parsedProposal.transport;
      delete parsedProposal.usage;
      delete parsedProposal.yield;
      delete parsedProposal.customTitle;
      delete parsedProposal.proposalName;
      delete parsedProposal.hideContact;
      delete parsedProposal.hideEmployeePhoto;
      delete parsedProposal.type;
      delete parsedProposal.sbu;
      // .....

      if (!parsedProposal.mainPhoto) {
        if (parsedProposal.main1Photo)
          parsedProposal.mainPhoto = parsedProposal.main1Photo;
        else if (parsedProposal.main2Photo)
          parsedProposal.mainPhoto = parsedProposal.main2Photo;
      }
      delete parsedProposal.main1Photo;
      delete parsedProposal.main2Photo;

      // set useGGMapPhoto to true if googleMap.isMain1 is true
      parsedProposal.useGGMapPhoto = parsedProposal.googleMap.isMain1;
      delete parsedProposal.googleMap;

      const {
        buildingNameZh: nameZh,
        buildingNameEn: nameEn,
        buildingDistrictNameZh: districtNameZh,
        buildingDistrictNameEn: districtNameEn,
        airConditioningType,
        haveCarPark,
        lat,
        lng,
      } = parsedProposal;

      // building for COMM list proposal
      parsedProposal.building = {
        nameZh,
        nameEn,
        districtNameZh,
        districtNameEn,
        haveCarPark,
        airConditioningType,
        lat,
        lng,
      };

      // delete building field in parsedProposal after
      // moved all these field into parsedProposal.building
      delete parsedProposal.buildingNameZh;
      delete parsedProposal.buildingNameEn;
      delete parsedProposal.buildingDistrictNameZh;
      delete parsedProposal.buildingDistrictNameEn;
      delete parsedProposal.passengerLift;
      delete parsedProposal.cargoLift;
      delete parsedProposal.haveCarPark;
      delete parsedProposal.airConditioningType;
      delete parsedProposal.lat;
      delete parsedProposal.lng;

      // delete undefined fields
      Object.keys(parsedProposal).forEach(
        (key) => !parsedProposal[key] && delete parsedProposal[key],
      );

      return parsedProposal;
    });

    const payload = {
      type: listProposalType,
      sbu,
      hideContact,
      hideEmployeePhoto,
      proposalName,
      proposals: parsedFormArr,
    };
    createListProposal(payload);
  };

  const submit = (stockId, buildingId, values) => {
    const stockMediaData =
      media?.filter((m) => m.id === stockId.toString())?.[0]?.data || {};
    const buildingMediaData =
      buildingMedia?.filter((m) => m.id === buildingId.toString())?.[0]?.data ||
      {};

    const parsedData = getParsedProposal(
      stock,
      stockMediaData,
      buildingMediaData,
      values,
    );

    createProposal(parsedData);
  };

  const renderForms = () =>
    stockData.map((s, idx) => (
      <ProposalForm
        key={s._id}
        stockId={s._id}
        show={currentStock === idx}
        onSubmit={(...values) =>
          submit(s.unicorn.id.toString(), s.building.unicorn.id, ...values)
        }
        form={`form_${s._id}`}
        initialValues={formStockData(s)}
        changeFieldValue={(...args) =>
          changeFieldValue(`form_${s._id}`, ...args)
        }
      />
    ));

  return (
    <div>
      <div>{renderForms()}</div>
      {exceedQuota ? (
        <Dialog
          open={createProposalDialogOpen}
          handleClose={handleCloseCreateProposalDialog}
        >
          {intl.formatMessage(
            { id: "proposal.create.exceed" },
            { quota: generalProposalQuota },
          )}
          <DialogFrame
            buttonMain={intl.formatMessage({
              id: "common.ok",
            })}
            handleMain={handleCloseSubmitDialog}
          ></DialogFrame>
        </Dialog>
      ) : (
        <SubmitDialog
          dialogOpen={createProposalDialogOpen}
          handleCloseDialog={handleCloseSubmitDialog}
          succCallback={submitDialogCallback}
          submitting={isListProposal ? creatingListProposal : creatingProposal}
          submitted={isListProposal ? createdListProposal : createdProposal}
          error={isListProposal ? createListProposalError : createProposalError}
          submit={
            isListProposal
              ? submitListProposal
              : () => dispatchSubmitForm(`form_${stock._id}`)
          }
          submitBtnText={intl.formatMessage({ id: "proposal.form.save" })}
          succMsg={intl.formatMessage(
            { id: "proposal.form.savesuccess" },
            { filename: proposalName },
          )}
        >
          <TextField
            label={intl.formatMessage({ id: "proposal.form.proposalname" })}
            value={proposalName}
            onChange={handleNameChange}
            variant="outlined"
            fullWidth
          />
          {isListProposal && (
            <>
              <SelectField
                label={intl.formatMessage({ id: "proposal.form.type" })}
                input={{
                  value: listProposalType,
                  onChange: (e) => setListProposalType(e.target.value),
                }}
                ranges={getTypeOptions(intl)}
                meta={{}}
                variant="outlined"
                fullWidth
              />
              <FormGroup>
                <FormControlLabel
                  control={
                    <CustomCheckbox
                      checked={hideContact}
                      onChange={(e) => setHideContact(e.target.checked)}
                    />
                  }
                  label={intl.formatMessage({
                    id: "proposal.form.hidecontact",
                  })}
                />
                <FormControlLabel
                  control={
                    <CustomCheckbox
                      checked={hideEmployeePhoto}
                      onChange={(e) => setHideEmployeePhoto(e.target.checked)}
                    />
                  }
                  label={intl.formatMessage({
                    id: "proposal.form.hideemployeephoto",
                  })}
                />
              </FormGroup>
            </>
          )}
        </SubmitDialog>
      )}
    </div>
  );
}

ProposalCreate.propTypes = {
  stockData: PropTypes.array,
  currentStock: PropTypes.number,
  media: PropTypes.array,
  unitViews: PropTypes.array.isRequired,
  decorations: PropTypes.array.isRequired,
  possessions: PropTypes.array.isRequired,
  buildingMedia: PropTypes.array,
  createProposal: PropTypes.func,
  creatingProposal: PropTypes.bool,
  createdProposal: PropTypes.bool,
  createProposalError: PropTypes.string,
  clearCreateProposal: PropTypes.func,
  dispatchSubmitForm: PropTypes.func,
  createProposalDialogOpen: PropTypes.bool,
  handleCloseCreateProposalDialog: PropTypes.func,
  changeFieldValue: PropTypes.func,
  form: PropTypes.object,
  exceedQuota: PropTypes.bool,
  createListProposal: PropTypes.func.isRequired,
  creatingListProposal: PropTypes.bool,
  createdListProposal: PropTypes.bool,
  createListProposalError: PropTypes.string,
  clearCreateListProposal: PropTypes.func,
  intl: PropTypes.object,
};

const mapStateToProps = (state) => ({
  userInfo:
    state.auth &&
    state.auth.user &&
    state.auth.user.login &&
    state.auth.user.login.info
      ? state.auth.user.login.info
      : {},
  stockData: state.stock.detail ? state.stock.detail : [],
  unitViews: state.stock.unitViews ?? [],
  decorations: state.stock.decorations ?? [],
  possessions: state.stock.possessions ?? [],
  currentStock: state.stock.currentDetail ? state.stock.currentDetail : 0,
  media: state.stock.media ? state.stock.media : [],
  buildingMedia: state.building.media ? state.building.media : [],
  form: state.form ? state.form : {},
  creatingProposal: state.proposal.creatingProposal
    ? state.proposal.creatingProposal
    : false,
  createdProposal: state.proposal.createdProposal
    ? state.proposal.createdProposal
    : false,
  createProposalError:
    state.proposal.createProposalError &&
    state.proposal.createProposalError.message
      ? state.proposal.createProposalError.message
      : null,
  creatingListProposal: state.listProposal.creatingListProposal
    ? state.listProposal.creatingListProposal
    : false,
  createdListProposal: state.listProposal.createdListProposal
    ? state.listProposal.createdListProposal
    : false,
  createListProposalError: state.listProposal.createListProposalError
    ? state.listProposal.createListProposalError.message
    : null,
});

const mapDispatchToProps = (dispatch) => {
  return {
    dispatchSubmitForm: (form) => dispatch(submit(form)),
    changeFieldValue: (form, field, value) =>
      dispatch(change(form, field, value)),
    createProposal: (...args) => dispatch(createProposal(...args)),
    createListProposal: (listProposal) =>
      dispatch(createListProposal(listProposal)),
    clearCreateProposal: (...args) => dispatch(clearCreateProposal(...args)),
    clearCreateListProposal: (...args) =>
      dispatch(clearCreateListProposal(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(injectIntl(ProposalCreate));
