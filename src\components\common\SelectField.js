import React from "react";
import TextField from "@material-ui/core/TextField";
import MenuItem from "@material-ui/core/MenuItem";

const renderSelectField = ({
  input,
  label,
  ranges,
  disabled = false,
  meta: { touched, error },
  children,
  ...custom
}) => (
  <TextField
    select
    variant="outlined"
    margin="normal"
    label={label}
    fullWidth
    InputLabelProps={{
      shrink: true
    }}
    SelectProps={{
      displayEmpty: true
    }}
    disabled={disabled}
    {...input}
  >
    {ranges.map(option => (
      <MenuItem key={option.value} value={option.value}>
        {option.label}
      </MenuItem>
    ))}
  </TextField>
);

export default renderSelectField;
