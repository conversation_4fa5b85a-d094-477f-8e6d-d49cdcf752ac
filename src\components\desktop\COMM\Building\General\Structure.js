import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/styles";
import Grid from "@material-ui/core/Grid";
import DetailBoxSection from "../../../../common/DetailBoxSection";
import { numberComma } from "../../../../../helper/generalHelper";
import FieldVal from "../../../../common/FieldVal";
import { injectIntl } from "react-intl";

const styles = (theme) => ({
  lmrAlign: {
    padding: "1vw 2vw",
    "& > :nth-child(3n+2)": {
      textAlign: "center",
    },
    "& > :nth-child(3n)": {
      textAlign: "right",
    },
  },
});

class Structure extends React.Component {
  static propTypes = {
    classes: PropTypes.object.isRequired,
    detail: PropTypes.object,
  };

  render() {
    const { classes, detail, intl } = this.props;

    let lowFloor = "---";
    let midFloor = "---";
    let highFloor = "---";
    const floorDefinitions = detail.floorDefinitions || [];
    floorDefinitions.forEach((v) => {
      let str =
        v.lowerLimit && v.upperLimit
          ? v.lowerLimit + " - " + v.upperLimit
          : v.lowerLimit || v.upperLimit || "---";
      switch (v.type) {
        case "Low":
          lowFloor = str;
          break;
        case "Mid":
          midFloor = str;
          break;
        case "High":
          highFloor = str;
          break;
      }
    });
    const lobby = detail.lobby || "---";
    const wall = detail.wall || "---";
    const ceiling = detail.ceiling || "---";
    const passengerLift = detail.passengerLift || "---";
    const haveCargoLift =
      detail.haveCargoLift === true
        ? "Yes"
        : detail.haveCargoLift === false
        ? "No"
        : "---";
    const totalFloor = detail.totalFloor || "---";
    const floorAreaMin =
      detail.floorArea && detail.floorArea.minArea
        ? numberComma(detail.floorArea.minArea)
        : "";
    const floorAreaMax =
      detail.floorArea && detail.floorArea.maxArea
        ? numberComma(detail.floorArea.maxArea)
        : "";
    const floorArea =
      floorAreaMin && floorAreaMax
        ? floorAreaMin + " - " + floorAreaMax + " SqFt"
        : floorAreaMin || floorAreaMax
        ? (floorAreaMin || floorAreaMax) + " SqFt"
        : "---";
    const totalFloorArea =
      detail.totalFloorArea && detail.totalFloorArea.value
        ? numberComma(detail.totalFloorArea.value) + " SqFt"
        : "---";
    const efficiency = detail.efficiency ? detail.efficiency + "%" : "---";
    const ceilHeightMin =
      detail.ceilingHeight && detail.ceilingHeight.minHeight
        ? detail.ceilingHeight.minHeight
        : "";
    const ceilHeightMax =
      detail.ceilingHeight && detail.ceilingHeight.maxHeight
        ? detail.ceilingHeight.maxHeight
        : "";
    const ceilHeight =
      ceilHeightMin && ceilHeightMax
        ? ceilHeightMin + " - " + ceilHeightMax
        : ceilHeightMin || ceilHeightMax || "---";
    const loadingKPA = detail.loadingKPA + "(KPA)" || "---";
    const haveCarParkBool = detail.carParkInfo
      ? detail.carParkInfo.haveCarPark
      : null;
    const haveCarPark =
      haveCarParkBool === true
        ? "Yes"
        : haveCarParkBool === false
        ? "No"
        : "---";
    const hourlyParking =
      detail.carParkInfo && detail.carParkInfo.hourlyParking
        ? detail.carParkInfo.hourlyParking
        : "---";
    const monthlyParkingFixed =
      detail.carParkInfo && detail.carParkInfo.monthlyParkingFixed
        ? detail.carParkInfo.monthlyParkingFixed
        : "---";
    let structureMapping = {
      [intl.formatMessage({
        id: "building.lowfloor",
      })]: { value: lowFloor, xs: 4 },
      [intl.formatMessage({
        id: "building.midfloor",
      })]: { value: midFloor, xs: 4 },
      [intl.formatMessage({
        id: "building.highfloor",
      })]: { value: highFloor, xs: 4 },
      [intl.formatMessage({
        id: "building.lobby",
      })]: { value: lobby, xs: 4 },
      [intl.formatMessage({
        id: "building.wall",
      })]: { value: wall, xs: 4 },
      [intl.formatMessage({
        id: "building.ceiling",
      })]: { value: ceiling, xs: 4 },
      [intl.formatMessage({
        id: "building.totalfloor",
      })]: { value: totalFloor, xs: 4 },
      [intl.formatMessage({
        id: "building.passengerlift",
      })]: { value: passengerLift, xs: 4 },
      [intl.formatMessage({
        id: "building.cargolift",
      })]: { value: haveCargoLift, xs: 4 },
      [intl.formatMessage({
        id: "building.floorarea",
      })]: { value: floorArea, xs: 4 },
      [intl.formatMessage({
        id: "building.totalfloorarea",
      })]: { value: totalFloorArea, xs: 4 },
      [intl.formatMessage({
        id: "building.efficiency",
      })]: { value: efficiency, xs: 4 },
      [intl.formatMessage({
        id: "building.carpark",
      })]: { value: haveCarPark, xs: 4 },
      [intl.formatMessage({
        id: "building.hourlyparking",
      })]: { value: hourlyParking, xs: 4 },
      [intl.formatMessage({
        id: "building.monthlyparking",
      })]: { value: monthlyParkingFixed, xs: 4 },
      [intl.formatMessage({
        id: "building.ceilinght",
      })]: { value: ceilHeight, xs: 4 },
      [intl.formatMessage({
        id: "building.loadingkpa",
      })]: { value: loadingKPA, xs: 4 },
    };

    return (
      <DetailBoxSection
        text={intl.formatMessage({
          id: "building.structure",
        })}
        expandable={true}
        isExpanding={true}
      >
        <Grid container spacing={2} className={classes.lmrAlign}>
          {Object.keys(structureMapping).map((v, i) => (
            <Grid item xs={structureMapping[v].xs} key={v}>
              <FieldVal field={v}>{structureMapping[v].value}</FieldVal>
            </Grid>
          ))}
        </Grid>
      </DetailBoxSection>
    );
  }
}

export default withStyles(styles)(injectIntl(Structure));
