/**
 * React Starter Kit (https://www.reactstarterkit.com/)
 *
 * Copyright © 2014-present Kriasoft, LLC. All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

import React from "react";
import {connect} from "react-redux";
import withStyles from "isomorphic-style-loader/lib/withStyles";
import InfiniteScroll from "react-infinite-scroll-component";
import {FormattedMessage, injectIntl} from "react-intl";
import s from "./index.css";
import LoadingOverlay from "../../../LoadingOverlay";
import ErrorsDialog from "../../../common/ErrorsDialog";
import {clearAnchor, fetchWWWStockList, setAnchor,} from "@/actions/wwwStock";
import ResultCard from "@/components/mobile/COMM/WWWStockList/ResultCard";
import {getUnlockCount} from "@/actions/stocklist";

class WWWStockList extends React.Component {

  componentDidMount() {
    const {intl, user, listed, listWWWStockList, getUnlockCount, currentStock, anchorY, clearAnchor} = this.props;
    if (currentStock && currentStock.stockId && listed) {
      // scroll to the clicked stock
      if (this.cardRefs[currentStock.stockId]) {
        const scrollPx = this.cardRefs[currentStock.stockId].getBoundingClientRect().top + window.scrollY - 180;
        window.scroll({
          top: scrollPx,
          behavior: "smooth",
        });
      }
    } else if (!listed) {
      // only the first time user enter this page, we do data fetch
      const variables = {
        empId: _.get(user, 'login.info.emp_id'),
        limit: 5,
        offset: 0,
      };
      listWWWStockList(variables, {isFetchingMore: false, intl});
      getUnlockCount();
    } else {

      // scroll back to the anchor position where the user left last time
      if (anchorY)
        window.scroll({
          top: anchorY,
          behavior: "smooth",
        });
      clearAnchor();
    }
  }


  componentWillUnmount() {
    const {setAnchor} = this.props;
    setAnchor(window.scrollY);
  }

  fetchMoreData = () => {
    const {intl, variables, listWWWStockList} = this.props;
    variables.offset += 5;
    listWWWStockList(variables, {isFetchingMore: true, intl});
  };


  refreshPage = () => {
    window.location.replace('/my-www-stock');
  };

  cardRefs = {};

  // using callback refs. https://stackoverflow.com/questions/52448143/dealing-with-ref-within-a-loop-in-react
  setRef = (id) => (ref) => {
    // check whether the element mount or unmount. https://stackoverflow.com/questions/41838833/issue-storing-ref-elements-in-loop
    if (ref) {
      this.cardRefs[id] = ref;
    } else {
      delete this.cardRefs[id];
    }
  };

  render() {
    const {
      listing,
      listed,
      wwwStockList,
      wwwStockListCount,
      unlockedStockIds,
      hasMore,
      error,
      intl,
    } = this.props;

    const errors = [];
    if (error)
      errors.push({
        message: error.message,
        btn: intl.formatMessage({id: "common.refresh"}),
        btnHandleClick: this.refreshPage,
      });

    return (
      <div className={s.root}>
        <div className={s.bg}/>

        <div className={s.listTop}>
          <div className={s.count}>
            {wwwStockListCount}{" "}
            <FormattedMessage id="wwwStock.wwwStockList.count"/>
          </div>
        </div>
        {listed ? (
          <div className={s.card}>
            <InfiniteScroll
              dataLength={wwwStockList.length}
              next={this.fetchMoreData}
              hasMore={hasMore}
              style={{overflowY: "hidden", marginBottom: 60}}
              endMessage={
                <p style={{textAlign: "center"}}>
                  <b>
                    <FormattedMessage id="search.noresult"/>
                  </b>
                </p>
              }
            >
              {wwwStockList.map((stock, index) => (
                <div key={index} ref={this.setRef(stock.stockId)}>
                  <ResultCard
                    result={stock}
                    isUnlocked={unlockedStockIds.indexOf(stock.stockId) > -1}
                  />
                </div>
              ))}
            </InfiniteScroll>
          </div>
        ) : null}

        {(listing || !listed) && <LoadingOverlay/>}

        <ErrorsDialog errors={errors}/>
      </div>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    user: state.auth.user ? state.auth.user : {},
    wwwStockList: state.wwwStock.wwwStockList ? state.wwwStock.wwwStockList : [],
    wwwStockListCount: state.wwwStock.wwwStockListCount ? state.wwwStock.wwwStockListCount : 0,
    unlockedStockIds: state.stocklist.unlockedStockIds ? state.stocklist.unlockedStockIds : [],
    variables: state.wwwStock.variables ? state.wwwStock.variables : {},
    listing: state.wwwStock.listing ? state.wwwStock.listing : false,
    listed: state.wwwStock.listed ? state.wwwStock.listed : false,
    hasMore: state.wwwStock.hasMore ? state.wwwStock.hasMore : false,
    error: state.wwwStock.error ? state.wwwStock.error : null,
    currentStock: state.wwwStock.currentWWWStock ? state.wwwStock.currentWWWStock : null,
    anchorY: state.wwwStock.searchResultAnchor ? state.wwwStock.searchResultAnchor : null,
  }
};

const mapDispatchToProps = (dispatch) => {
  return {
    listWWWStockList: (...args) => dispatch(fetchWWWStockList(...args)),
    getUnlockCount: () => dispatch(getUnlockCount()),
    clearAnchor: () => dispatch(clearAnchor()),
    setAnchor: (...args) => dispatch(setAnchor(...args)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withStyles(s)(injectIntl(WWWStockList)));
