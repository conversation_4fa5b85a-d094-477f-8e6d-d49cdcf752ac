export const LIST_STOCKLIST_QUERY = `query ($unicornId: [String], $sbu: [String], $status: [String], $decoration: [ID], $buildingGrade: [String], $hasCompany: Boolean $buildingUsage: [String], $includeId: [ID], $unitView: [ID], $stockIds: [ID], $sorter: [stockSorter], $buildingSourcesId: [ID], $district: [ID], $street: [ID], $streetNoMin: Int, $streetNoMax: Int, $areaMin: Int, $areaMax: Int, $searchAreaMin: Int, $searchAreaMax: Int, $priceMinAvg: Float, $priceMaxAvg: Float, $rentMinAvg: Float, $rentMaxAvg: Float, $priceMinTotal: Float, $priceMaxTotal: Float, $rentMinTotal: Float, $rentMaxTotal: Float, $isNonCentralAC: <PERSON>olean, $isRaisedFloor: Boolean, $isSaleEquity: Boolean, $isCarPark: Boolean, $isWithKey: Boolean, $withPassCodes: Boolean, $isPrivacy: Boolean, $isInvestment: Boolean, $isProjects: Boolean, $pdfPP: Boolean, $isMortgagee: Boolean, $isFacingLift: Boolean, $inspection: [String], $possession: [String], $limit: Int, $offset: Int, $ageMin: Int, $ageMax: Int, $isNew: Boolean, $isMarketable: Boolean, $isMarketableOrNew: Boolean, $isWWW: Boolean, $isNonWWW: Boolean, $floorMin: Int, $floorMax: Int, $floorLeft: String, $floorRight: String, $isSoleAgent: Boolean, $havePhoto: Boolean, $haveVideo: Boolean, $haveVR: Boolean, $haveStockPhoto: Boolean, $havePropertyAdvertisements: Boolean, $haveLandSearchDoc: Boolean, $isShop: Boolean, $isInWater: Boolean, $isOutWater: Boolean, $haveStair: Boolean, $isFlatFloor: Boolean, $haveTerrace: Boolean, $createDateMin: String, $createDateMax: String, $lastUpdateDateMin: String, $lastUpdateDateMax: String, $tenancyExpireDateMin: String, $tenancyExpireDateMax: String, $isVendor: Boolean, $isCurrent: Boolean, $isPrevious: Boolean, $contactsCompany: [ID], $contactsPerson: [String], $contactsPhone: [String], $contactsEmail: [String], $haveSurveyorProposal: Boolean) {
  stocksCount(unicornId: $unicornId, sbu: $sbu, status: $status, decoration: $decoration, includeId: $includeId, buildingGrade: $buildingGrade, hasCompany: $hasCompany, buildingUsage: $buildingUsage, unitView: $unitView, _id: $stockIds, buildingSourcesId: $buildingSourcesId, district: $district, street: $street, streetNoMin: $streetNoMin, streetNoMax: $streetNoMax, areaMin: $areaMin, areaMax: $areaMax, searchAreaMin: $searchAreaMin, searchAreaMax: $searchAreaMax, priceMinAvg: $priceMinAvg, priceMaxAvg: $priceMaxAvg, rentMinAvg: $rentMinAvg, rentMaxAvg: $rentMaxAvg, priceMinTotal: $priceMinTotal, priceMaxTotal: $priceMaxTotal, rentMinTotal: $rentMinTotal, rentMaxTotal: $rentMaxTotal, isNonCentralAC: $isNonCentralAC, isRaisedFloor: $isRaisedFloor, isSaleEquity: $isSaleEquity, isCarPark: $isCarPark, isWithKey: $isWithKey, withPassCodes: $withPassCodes, isPrivacy: $isPrivacy, isInvestment: $isInvestment, isProjects: $isProjects, pdfPP: $pdfPP, isMortgagee: $isMortgagee, isFacingLift: $isFacingLift, inspection: $inspection, possession: $possession, ageMin: $ageMin, ageMax: $ageMax, isMarketable: $isMarketable, isNew: $isNew, isMarketableOrNew: $isMarketableOrNew, isWWW: $isWWW, isNonWWW: $isNonWWW, floorMin: $floorMin, floorMax: $floorMax, floorLeft: $floorLeft, floorRight: $floorRight, isSoleAgent: $isSoleAgent, havePhoto: $havePhoto, haveVideo: $haveVideo, haveVR: $haveVR, haveStockPhoto: $haveStockPhoto, havePropertyAdvertisements: $havePropertyAdvertisements, haveLandSearchDoc: $haveLandSearchDoc, isShop: $isShop, isInWater: $isInWater, isOutWater: $isOutWater, haveStair: $haveStair, isFlatFloor: $isFlatFloor, haveTerrace: $haveTerrace, createDateMin: $createDateMin, createDateMax: $createDateMax, lastUpdateDateMin: $lastUpdateDateMin, lastUpdateDateMax: $lastUpdateDateMax, tenancyExpireDateMin: $tenancyExpireDateMin, tenancyExpireDateMax: $tenancyExpireDateMax, isCurrVendor: $isVendor, isCurrent: $isCurrent, isPrevious: $isPrevious, contactsCompany: $contactsCompany, contactsPerson: $contactsPerson, contactsPhone: $contactsPhone, contactsEmail: $contactsEmail, haveSurveyorProposal: $haveSurveyorProposal)
  stocks(unicornId: $unicornId, sbu: $sbu, status: $status, decoration: $decoration, includeId: $includeId, buildingGrade: $buildingGrade, hasCompany: $hasCompany, buildingUsage: $buildingUsage, unitView: $unitView, _id: $stockIds, sort: $sorter, buildingSourcesId: $buildingSourcesId, district: $district, street: $street, streetNoMin: $streetNoMin, streetNoMax: $streetNoMax, areaMin: $areaMin, areaMax: $areaMax, searchAreaMin: $searchAreaMin, searchAreaMax: $searchAreaMax, priceMinAvg: $priceMinAvg, priceMaxAvg: $priceMaxAvg, rentMinAvg: $rentMinAvg, rentMaxAvg: $rentMaxAvg, priceMinTotal: $priceMinTotal, priceMaxTotal: $priceMaxTotal, rentMinTotal: $rentMinTotal, rentMaxTotal: $rentMaxTotal, isNonCentralAC: $isNonCentralAC, isRaisedFloor: $isRaisedFloor, isSaleEquity: $isSaleEquity, isCarPark: $isCarPark, isWithKey: $isWithKey, withPassCodes: $withPassCodes, isPrivacy: $isPrivacy, isInvestment: $isInvestment, isProjects: $isProjects, pdfPP: $pdfPP, isMortgagee: $isMortgagee, isFacingLift: $isFacingLift, inspection: $inspection, possession: $possession, limit: $limit, offset: $offset, ageMin: $ageMin, ageMax: $ageMax, isMarketable: $isMarketable, isNew: $isNew, isMarketableOrNew: $isMarketableOrNew, isWWW: $isWWW, isNonWWW: $isNonWWW, floorMin: $floorMin, floorMax: $floorMax, floorLeft: $floorLeft, floorRight: $floorRight, isSoleAgent: $isSoleAgent, havePhoto: $havePhoto, haveVideo: $haveVideo, haveVR: $haveVR, haveStockPhoto: $haveStockPhoto, havePropertyAdvertisements: $havePropertyAdvertisements, haveLandSearchDoc: $haveLandSearchDoc, isShop: $isShop, isInWater: $isInWater, isOutWater: $isOutWater, haveStair: $haveStair, isFlatFloor: $isFlatFloor, haveTerrace: $haveTerrace, createDateMin: $createDateMin, createDateMax: $createDateMax, lastUpdateDateMin: $lastUpdateDateMin, lastUpdateDateMax: $lastUpdateDateMax, tenancyExpireDateMin: $tenancyExpireDateMin, tenancyExpireDateMax: $tenancyExpireDateMax, isCurrVendor: $isVendor, isCurrent: $isCurrent, isPrevious: $isPrevious, contactsCompany: $contactsCompany, contactsPerson: $contactsPerson, contactsPhone: $contactsPhone, contactsEmail: $contactsEmail, haveSurveyorProposal: $haveSurveyorProposal) {
    unitViews{
      nameZh
      nameEn
    }
    decorations{
      nameZh
      nameEn
    }
    unit
    building {
      nameEn
      nameZh
      haveStair
      isFlatFloor
    }
    district {
      abbr
      nameZh
    }
    askingRent {
      average
      total
      trend
    }
    askingPrice {
      average
      total
      trend
    }
    area
    _id
    sbu
    status
    floor {
      input
    }
    isNew
    isMarketable
    isSoleAgent
    isWWW
    mortgagee
    isSaleEquity
    isWithKey
    withPassCodes
    isFacingLift
    isRaisedFloor
    isNonCentralAC
    isCarPark
    isInvestment
    isProjects
    pdfPP
    haveVR
    haveVideo
    havePhoto
    haveStockPhoto
    havePropertyAdvertisements
    haveLandSearchDoc
    isShop
    isInWater
    isOutWater
    haveTerrace
    unicorn {
      stock
    }
    tenancyCurrentTenant
    tenancyCurrentTenantZh
    tenancyExpireDate
    searchArea
    searchAreaType
    haveSurveyorProposal
    wwwScore {
      eaaOwner
      soleagent
      stockInformation
      newStock
      video
      kolVideo
      photo
    }
    wwwScoreTotal
    isOnline
    wwwAgentInfos {
      wwwBy
      wwwByName
      wwwByDept
      wwwDate
      wwwDesc
      scoreTotal
      score {
        eaaOwner
        soleAgent
        photo
        video
        kolVideo
      }
      eaaOwner
      soleAgent
      video
      videoId
      kolVideo
      kolVideoId
      photo
      photoIds
    }
    wwwChannelFull {
      eaaOwner
      soleagent
      video
      kolVideo
      photo
    }
  }
}
`;

export const LIST_STOCKLIST_BY_KEYWORDS_QUERY = `
query ($building: [ID], $district: [ID], $street: [ID], $sbu: [String], $limit: Int, $offset: Int, $sorter: [stockOrSorter]) {
  stocksOrCount(building: $building, district: $district, street: $street, sbu: $sbu)
  stocksOr(building: $building, district: $district, street: $street, sbu: $sbu, limit: $limit, offset: $offset, sort: $sorter) {
    unit
    building {
      nameEn
      nameZh
    }
    district {
      abbr
    }
    askingRent {
      average
      total
      trend
    }
    askingPrice {
      average
      total
      trend
    }
    area
    _id
    sbu
    status
    floor {
      input
    }
    isMarketable
    mortgagee
    isCarPark
    isRaisedFloor
    isWWW
    isSaleEquity
    isSoleAgent
    isNew
    isFacingLift
    havePropertyAdvertisements
    wwwScore {
      eaaOwner
      soleagent
      stockInformation
      newStock
      video
      kolVideo
      photo
    }
    wwwScoreTotal
    isOnline
    wwwAgentInfos {
      wwwBy
      wwwByName
      wwwByDept
      wwwDate
      wwwDesc
      scoreTotal
      score {
        eaaOwner
        soleAgent
        photo
        video
        kolVideo
      }
      eaaOwner
      soleAgent
      video
      videoId
      kolVideo
      kolVideoId
      photo
      photoIds
    }
    wwwChannelFull {
      eaaOwner
      soleagent
      video
      kolVideo
      photo
    }
  }
}
`;

export const LIST_DECORATIONS_QUERY = `
{
  decorations {
    _id
    nameEn
    nameZh
  }
}
`;

export const LIST_POSSESSIONS_QUERY = `
{
  possessions(sort: [{ field: _id, order: ASC }]) {
    _id
    nameEn
    nameZh
  }
}
`;

export const LIST_USAGES_QUERY = `
{
  usages {
    _id
    nameEn
    nameZh
  }
}
`;
