import React, { useState, memo, useEffect } from "react";
import { connect } from "react-redux";
import PropTypes from "prop-types";
import _ from "lodash";
import { makeStyles } from "@material-ui/core/styles";
import { injectIntl } from "react-intl";

import DetailTabPanelFrame from "../common/DetailTabPanelFrame";
import LoadingOverlay from "../LoadingOverlay";
import UploadMediaDialog from "../common/UploadMedia/UploadMediaDialog";
import { listStockMedia } from "../../actions/stock";
import { listBuildingMedia } from "../../actions/building";
import { listStreetMedia } from "../../actions/street";
import MediaView from "./MediaView";
import { sbu } from "../../config";
import { getFullAddress, getICSCode, getKolAddress } from "../Saleskit/helpers";
import { listEmployees, listValidEmployees } from "@/actions/employee";

import {
  getLang<PERSON>ey,
} from "../../helper/generalHelper";
import UploadMediaTypeSelectedDialog from "../common/UploadMedia/UploadMediaTypeSelectedDialog";
import { PERMISSIONS } from "@/constants/auth";
import { formValueSelector } from "redux-form";

const useStyles = makeStyles(() => ({
  wrapper: {
    padding: " 1vh 2vw",
  },
  notFound: {
    margin: "1em 0",
    textAlign: "center",
    fontWeight: "bold",
  },
  circularLoader: {
    textAlign: "center",
    marginTop: "18vh",
  },
}));

const { VIEW_KOL } = PERMISSIONS;

function Media({
  empId,
  employee,
  getEmployee,
  validEmployees,
  getValidEmployees,
  detail,
  stockId,
  buildingId,
  streetId,
  media,
  buildingMedia,
  streetMedia,
  listingData,
  listedData,
  listStockMedia,
  listBuildingMedia,
  listStreetMedia,
  formValues,
  intl,
  permissions,
}) {
  const classes = useStyles();
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [uploadTypeDialogOpen, setUploadTypeDialogOpen] = useState(false);
  const [dialogValues, setDialogValues] = useState({
    uploadTo: "stock", // "Unit",
    type: 0,
    photoContent: "interior",
    documentContent: "Surveyor PP",
    employeeId: employee?.emp_id,
    description: undefined,
    mediaType: undefined,
  });

  const getAddress = (detail) => {
    const { street, building } = detail;
    //return `${building?.district?.["nameZh"] || "---"} ${building?.street?.street?.["nameZh"] || street?.street?.["nameZh"] || "---"}${sbu === "SHOPS" ? ' '+ (building?.street?.number || street?.number || ""):''} ${building?.["nameZh"] || "---"} ${detail?.floor || '---'} ${detail?.unit || '---'}`;
    return getKolAddress({ ...intl, locale: "zh" }, detail)
  };

  useEffect(() => {
    if (_.isNil(employee)) {
      getEmployee({ emp_id: [empId] });
    }
    if (_.isEmpty(validEmployees)) {
      getValidEmployees({
        "name": "",
        "limit": 0,
        "offset": 0,
        "hasTeam": true,
        "sbu": getICSCode(),
        "valid_emp": true,
        "isNew": true
      });
    }
  }, []);

  const getBuildingName = (detail) => {
    const { street, building } = detail;
    if (sbu === "SHOPS") {
      return `${building?.street?.street?.["nameZh"] || street?.street?.["nameZh"] || "---"} ${building?.street?.number || street?.number || "---"}號`;
    } else {
      return `${building?.["nameZh"] || "---"}`;
    }
  };

  const stockMediaData = stockId
    ? _.get(
      _.find(media, (m) => m.id === stockId.toString()),
      "data",
      {},
    )
    : {};
  const buildingMediaData = buildingId
    ? _.get(
      _.find(buildingMedia, (m) => m.id === buildingId.toString()),
      "data",
      {},
    )
    : {};
  const streetMediaData = streetId
    ? _.get(
      _.find(streetMedia, (m) => m.id === streetId.toString()),
      "data",
      {},
    )
    : {};
  const hasData = !_.isEmpty(stockMediaData) || !_.isEmpty(buildingMediaData) || !_.isEmpty(streetMediaData);

  const uploadCallback = () => {
    if (stockId && formValues.uploadTo === "stock") {
      listStockMedia({
        empId,
        sid: [stockId.toString()],
      });
    } else if (buildingId && formValues.uploadTo === "building") {
      listBuildingMedia({
        empId,
        sid: [buildingId.toString()],
      });
    } else if (streetId && formValues.uploadTo === "street") {
      listStreetMedia({
        empId,
        sid: [streetId.toString()],
      });
    }
  };

  // const dialogValues = {
  //   uploadTo: "Unit",
  //   type: 0,
  //   photoContent: "interior",
  // };
  const bottomButtons = [
    {
      label: intl.formatMessage({ id: "stock.upload.all" }),
      onClick: () => {
        setUploadTypeDialogOpen(true);
      },
    },
    /* {
      label: intl.formatMessage({ id: "stock.upload.kol" }),
      onClick: () => {
        const localStorageKOLFileInfo = localStorage.getItem(`${stockId}_KOLFileInfo`)
        ? JSON.parse(localStorage.getItem(`${stockId}_KOLFileInfo`))
        : {};
        setDialogValues({
          ...dialogValues,
          photoContent: "kol_video",
          address: localStorageKOLFileInfo.address || getAddress(detail),
          description: localStorageKOLFileInfo.description || `KOL - ${_.get(employee, "dept_code")} ${_.get(employee, "firstname_en")} ${_.get(employee, "surname_en")}`,
          characteristicZh: localStorageKOLFileInfo.characteristicZh || "",
          buildingName: getBuildingName(detail),
        });
        setUploadDialogOpen(true);
      },
    },
    {
      label: intl.formatMessage({ id: "stock.upload" }),
      onClick: () => {
        setDialogValues({
          ...dialogValues,
          photoContent: "interior",
        });
        setUploadDialogOpen(true);
      },
    }, */
  ];

  const onSelected = React.useCallback((type) => {
    const newValues = { ...dialogValues, mediaType: type, documentContent: undefined, photoContent: undefined, };
    switch (type) {
      case "photo":
      case "document":
        if (type === "document") {
          newValues.documentContent = "Surveyor PP";
        } else {
          newValues.photoContent = "interior";
        }
        break;
      case "video":
      case "kol_video":
        const localStorageKOLFileInfo = localStorage.getItem(`${stockId}_KOLFileInfo`)
          ? JSON.parse(localStorage.getItem(`${stockId}_KOLFileInfo`))
          : {};
        _.merge(newValues, {
          photoContent: type === "kol_video" ? "kol_video" : "interior",
          address: localStorageKOLFileInfo.address || getAddress(detail),
          ...(type === "kol_video" && {
            description: localStorageKOLFileInfo.description ||
              `KOL - ${_.get(employee, "dept_code")} ${_.get(employee, "firstname_en")} ${_.get(employee, "surname_en")}`,
          }),
          ...(type === "video" && {
            description: "",
          }),
          characteristicZh: localStorageKOLFileInfo.characteristicZh || "",
          buildingName: getBuildingName(detail),
          bgm: "A Relax.mp3",
          isShowContact: type === "kol_video" ? true : false,
        });
        break;
    }
    setDialogValues(newValues);
    setUploadTypeDialogOpen(false);
    setUploadDialogOpen(true);
  }, []);

  const handleUpdateTypeCloseDialog = React.useCallback(() => {
    setUploadTypeDialogOpen(false);
  }, [setUploadTypeDialogOpen]);

  const PERMISSION_ALLOW_TO_VIEW_KOL = React.useMemo(() => {
    return _.get(permissions, VIEW_KOL);
  }, [permissions, VIEW_KOL]);
  const groupSections = React.useMemo(() => {
    if (PERMISSION_ALLOW_TO_VIEW_KOL) {
      return ['document', 'kol_video', 'video', 'photo'];
    }
    return ['document', 'kol_video', 'video', 'photo'];
  }, [PERMISSION_ALLOW_TO_VIEW_KOL]);

  return (
    <>
      <DetailTabPanelFrame
        wrapperProps={{ className: classes.wrapper }}
        hasData={hasData}
        listing={listingData}
        listed={listedData}
        bottomButtons={bottomButtons}
      >
        {!listingData && listedData && hasData && (
          <MediaView
            stockMedia={stockMediaData}
            buildingMedia={buildingMediaData}
            streetMedia={streetMediaData}
            groupSections={groupSections}
          />
        )}
        {(listingData || !listedData) && (
          <div className={classes.circularLoader}>
            <LoadingOverlay />
          </div>
        )}
        {!listingData && listedData && !hasData && (
          <div className={classes.notFound}>
            {"Stock/Building media not found"}
          </div>
        )}
      </DetailTabPanelFrame>
      {uploadDialogOpen && (
        <UploadMediaDialog
          initialValues={dialogValues}
          dialogOpen={uploadDialogOpen}
          handleCloseDialog={() => setUploadDialogOpen(false)}
          buildingId={buildingId}
          stockId={stockId}
          streetId={streetId}
          callback={uploadCallback}
        />
      )}

      {(
        <UploadMediaTypeSelectedDialog
          dialogOpen={uploadTypeDialogOpen}
          handleCloseDialog={handleUpdateTypeCloseDialog}
          mediaTypes={groupSections}
          onSelected={onSelected}
        />
      )}
    </>
  );
}

Media.propTypes = {
  empId: PropTypes.string.isRequired,
  employee: PropTypes.object.isRequired,
  getEmployee: PropTypes.func.isRequired,
  validEmployees: PropTypes.array.isRequired,
  getValidEmployees: PropTypes.func.isRequired,
  detail: PropTypes.object.isRequired,
  stockId: PropTypes.number.isRequired,
  buildingId: PropTypes.number,
  streetId: PropTypes.string,
  media: PropTypes.array,
  buildingMedia: PropTypes.array,
  streetMedia: PropTypes.array,
  listingData: PropTypes.bool.isRequired,
  listedData: PropTypes.bool.isRequired,
  listStockMedia: PropTypes.func.isRequired,
  listBuildingMedia: PropTypes.func.isRequired,
  listStreetMedia: PropTypes.func.isRequired,
  intl: PropTypes.object.isRequired,
};

const selector = formValueSelector('uploadMediaForm');
const mapStateToProps = (state) => ({
  permissions: _.get(state, "employee.permissions") || {},
  empId: _.get(state, "auth.user.login.info.emp_id", ""),
  employee: _.get(state, "employee.employees.0"),
  validEmployees: _.get(state, "employee.validEmployees", []),
  detail: _.get(state, "stock.detail[0]", {}),
  media: _.get(state, "stock.media", []),
  buildingMedia: _.get(state, "building.media", []),
  streetMedia: _.get(state, "street.media", []),
  formValues: selector(state, 'uploadTo', 'employeeId'),
  listingData: _.some(
    [
      _.get(state, "stock.listing"),
      _.get(state, "stock.listingMedia"),
      _.get(state, "building.listingMedia"),
      _.get(state, "street.listingMedia"),
    ],
    Boolean,
  ),
  listedData:
    _.get(state, "stock.listed") &&
    (_.get(state, "stock.listedMedia") ||
      _.get(state, "building.listedMedia", false) ||
      _.get(state, "street.listedMedia", false)),
});

const mapDispatchToProps = (dispatch) => ({
  listStockMedia: (...args) => dispatch(listStockMedia(...args)),
  listBuildingMedia: (...args) => dispatch(listBuildingMedia(...args)),
  listStreetMedia: (...args) => dispatch(listStreetMedia(...args)),
  getEmployee: (variable) => dispatch(listEmployees(variable)),
  getValidEmployees: (variable) => dispatch(listValidEmployees(variable)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(memo(injectIntl(Media)));
